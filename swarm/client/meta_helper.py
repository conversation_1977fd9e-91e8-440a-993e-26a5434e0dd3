from se_elasticsearch.repository.static import MetaPrefix


class Meta:
    """
    Helper class to access SteelEye meta fields based on the meta prefix.
    """

    def __init__(self, prefix: str):

        if prefix not in (MetaPrefix.AMPERSAND, MetaPrefix.META):
            raise ValueError(f"Unsupported prefix {prefix}")

        self._relation = "_relation"
        self._all = "all"
        self._ancestor = "ancestor"
        self._approval_roles = "approvalRoles"
        self._cascade_id = "cascadeId"
        self._comment = "comment"
        self._expiry = "expiry"
        self._hash = "hash"
        self._id = "id"
        self._invocation_id = "invocationId"
        self._key = "key"
        self._link = "link"
        self._model = "model"
        self._parent = "parent"
        self._realm = "realm"
        self._sequence = "sequence"
        self._status = "status"
        self._task_id = "taskId"
        self._timestamp = "timestamp"
        self._trait_fqn = "traitFqn"
        self._ttl_expiry = "ttlExpiry"
        self._unique_props = "uniqueProps"
        self._updater = "updater"
        self._user = "user"
        self._validation_errors = "validationErrors"
        self._version = "version"

        self._prefix = prefix

    @property
    def all(self):
        return f"{self._prefix}{self._all}"

    @property
    def ancestor(self):
        return f"{self._prefix}{self._ancestor}"

    @property
    def approval_roles(self):
        return f"{self._prefix}{self._approval_roles}"

    @property
    def cascade_id(self):
        return f"{self._prefix}{self._cascade_id}"

    @property
    def comment(self):
        return f"{self._prefix}{self._comment}"

    @property
    def expiry(self):
        return f"{self._prefix}{self._expiry}"

    @property
    def hash(self):
        return f"{self._prefix}{self._hash}"

    @property
    def id(self):
        return f"{self._prefix}{self._id}"

    @property
    def invocation_id(self):
        return f"{self._prefix}{self._invocation_id}"

    @property
    def key(self):
        return f"{self._prefix}{self._key}"

    @property
    def link(self):
        return f"{self._prefix}{self._link}"

    @property
    def model(self):
        return f"{self._prefix}{self._model}"

    @property
    def parent(self):
        return f"{self._prefix}{self._parent}"

    @property
    def prefix(self):
        return self._prefix

    @property
    def realm(self):
        return f"{self._prefix}{self._realm}"

    @property
    def sequence(self):
        return f"{self._prefix}{self._sequence}"

    @property
    def status(self):
        return f"{self._prefix}{self._status}"

    @property
    def task_id(self):
        return f"{self._prefix}{self._task_id}"

    @property
    def timestamp(self):
        return f"{self._prefix}{self._timestamp}"

    @property
    def trait_fqn(self):
        return f"{self._prefix}{self._trait_fqn}"

    @property
    def ttl_expiry(self):
        return f"{self._prefix}{self._ttl_expiry}"

    @property
    def unique_props(self):
        return f"{self._prefix}{self._unique_props}"

    @property
    def updater(self):
        return f"{self._prefix}{self._updater}"

    @property
    def user(self):
        return f"{self._prefix}{self._user}"

    @property
    def validation_errors(self):
        return f"{self._prefix}{self._validation_errors}"

    @property
    def version(self):
        return f"{self._prefix}{self._version}"
