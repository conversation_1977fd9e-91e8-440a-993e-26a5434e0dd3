import logging
from abc import ABC
from typing import Dict
from typing import Optional

from se_elasticsearch.repository import elasticsearch6
from se_elasticsearch.repository.static import MetaPrefix

from swarm.client.record_handler.abstract_record_handler import AbstractR<PERSON>ordHandler

log = logging.getLogger(__name__)


class Es5RecordHandler(AbstractRecordHandler, ABC):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def get_meta_prefix(self) -> str:
        return MetaPrefix.AMPERSAND

    def get(
        self,
        index: str,
        doc_id: str,
        version: Optional[int] = None,
        source_only: bool = True,
        model: str = None,
    ) -> dict:

        if model is None:
            raise TypeError(
                "The `model` argument is mandatory for the ES5 record handler get method."
            )

        return super().get(
            index=index,
            doc_id=doc_id,
            version=version,
            source_only=source_only,
            model=model,
        )

    def set_request_doc_type(self, params: Dict[str, str], model: str):
        params["doc_type"] = self.get_request_doc_type(model=model)

    def get_request_doc_type(self, model: str) -> str:
        return model

    def get_es_not_found_error(self, *args, **kwargs):
        return elasticsearch6.exceptions.NotFoundError
