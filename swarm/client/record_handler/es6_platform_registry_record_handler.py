import logging
from abc import ABC
from typing import Dict
from typing import Optional

import elasticsearch6
from se_elasticsearch.repository.static import MetaPrefix

from swarm.client.record_handler.abstract_record_handler import AbstractRecordHandler

log = logging.getLogger(__name__)


class Es6PlatformRegistryRecordHandler(AbstractRecordHandler, ABC):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def get_meta_prefix(self) -> str:
        return MetaPrefix.META

    def get(
        self,
        index: str,
        doc_id: str,
        version: Optional[int] = None,
        source_only: bool = True,
        model: Optional[str] = None,
    ) -> dict:

        return super().get(
            index=index, doc_id=doc_id, version=version, source_only=source_only
        )

    def set_request_doc_type(self, params: Dict[str, str], model: str):
        params["doc_type"] = self.get_request_doc_type()

    def get_request_doc_type(self, model: Optional[str] = None) -> str:
        return "_doc"

    def get_es_not_found_error(self, *args, **kwargs):
        return elasticsearch6.exceptions.NotFoundError
