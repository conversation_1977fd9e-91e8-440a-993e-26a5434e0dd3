import json
import logging
import os
from typing import Dict
from typing import List
from typing import Optional

from elasticsearch6 import NotFoundError
from se_elasticsearch.repository import ResourceConfig
from se_elasticsearch.repository.elasticsearch6 import ElasticsearchRepository
from se_schema_meta import EXPIRY

from swarm.client.utilities import CLIENT_MAP
from swarm.flow.static import FlowEnvVar
from swarm.platform.vault import fetch_elastic_secrets_from_vault
from swarm.platform.vault import SeVaultKV2
from swarm.schema.flow.bundle.components import Resource
from swarm.schema.static import Environment

logger = logging.getLogger(__name__)

EFDH_ES_SECRETS_PATH = "efdh/elasticsearch-secrets"


class Registry:
    def __init__(self, config: ResourceConfig):
        self._config: ResourceConfig = config
        self._es: ElasticsearchRepository = None

    @property
    def es(self) -> ElasticsearchRepository:
        if self._es is None:
            self._es = ElasticsearchRepository(config=self._config)
        return self._es

    def get_flow(self, flow_id: str, stack: Optional[str] = None) -> dict:
        try:
            response = self.es_client_get(flow_id)
            flow = response.get("_source")
        except NotFoundError as e:
            tenant, remainder = flow_id.split(".", 1)
            # If the flow id itself starts with platform and it wasn't found, raise an NotFoundError exception
            if flow_id.startswith("platform"):
                raise e
            if stack and flow_id.startswith(stack):
                raise e
            try:
                # Try fetching the flow as a stack-level flow
                response = self.stack_or_platform_flow_id(
                    stack_or_platform_prefix=stack, flow_id_remainder=remainder
                )
                logger.info(f"Stack level flow for {flow_id} found")
            except (ValueError, NotFoundError):
                # Try fetching the flow as a platform flow
                logger.info(
                    f"stack level flow not found for stack {stack}, with flow_id {flow_id} , will try as platform flow"
                )
                response = self.stack_or_platform_flow_id(
                    stack_or_platform_prefix="platform", flow_id_remainder=remainder
                )
                logger.info(f"Platform flow for {flow_id} found")
            flow = response.get("_source")
            # overwrite flow realm with realm embedded in flow id
            # this reflects a stack/platform flow running as a specific client
            flow["realm"], _ = flow_id.split(":", 1)
            logger.info(
                f"overlaying realm {flow['realm']} in bundle"
                f"{flow['bundle']['_meta']['id']}"
            )

        flow["bundle"] = self.without_meta(flow["bundle"])
        return self.without_meta(flow)

    def es_client_get(self, flow_id):
        response = self.es.client.get(index="flow", id=flow_id, doc_type="_doc")
        return response

    def stack_or_platform_flow_id(
        self, stack_or_platform_prefix: str, flow_id_remainder: str
    ) -> dict:
        if stack_or_platform_prefix:
            stack_or_platform_flow_id = ".".join(
                [stack_or_platform_prefix, flow_id_remainder]
            )
            return self.es_client_get(flow_id=stack_or_platform_flow_id)
        else:
            raise ValueError("No prefix provided")

    def get_client(self, client_id: str) -> dict:
        client = self.es.client.get(index="client", id=client_id, doc_type="_doc")
        return client.get("_source")

    def get_platform_resource_configs(
        self, resource_id: str, env: str, resource_type: str = "elasticsearch"
    ) -> List[ResourceConfig]:
        body = {
            "query": {
                "bool": {
                    "filter": [
                        {"term": {"env": env.upper()}},
                        {"term": {"id": resource_id}},
                    ]
                }
            }
        }
        resource_index = f"{resource_type.lower()}_resource"
        responses: List[dict] = self.es.scroll(
            index=resource_index, query=body, as_dataframe=False
        )

        results = []
        for response in responses:
            results.extend([ResourceConfig(**self.without_meta(response))])
        return results

    def get_platform_resources(
        self, resource_id: str, env: str, resource_type: str = "elasticsearch"
    ) -> List[Resource]:
        resource_configs = self.get_platform_resource_configs(
            resource_id=resource_id, env=env, resource_type=resource_type
        )
        r = Resource(
            name=resource_id,
            type=resource_type.upper(),
            config=resource_configs,
        )
        return r

    def get_resource(self, resource_id: str, resource_type: str, stack: str) -> dict:

        tokens = stack.split("-")
        env = tokens[0].upper() if len(tokens) > 1 else "PROD"
        self._evaluate_reference_data_against_env(resource_id=resource_id, env=env)

        resource_index = f"{resource_type.lower()}_resource"
        if resource_type == "ELASTICSEARCH":
            doc_id = ":".join([resource_id, stack])
        elif resource_type == "SFTP":
            doc_id = ":".join([resource_id, env])
        else:
            raise ValueError(f"unsupported resource type: {resource_type}")

        try:
            resource = self.es.client.get(
                index=resource_index, id=doc_id, doc_type="_doc"
            )
            return self.without_meta(resource.get("_source"))
        except NotFoundError:
            body = {"query": {"term": {"id": resource_id}}}
            result = self.es.client.search(
                index=resource_index, doc_type="_doc", body=body
            )
            if not result["hits"]["total"]:
                raise ValueError(f"No resource records found for `{resource_id}`.")
            if result["hits"]["total"] != 1:
                raise ValueError(
                    f"More than one record found for resource id `{resource_id}`: {result['hits']['hits']}."
                )

            return self.without_meta(result["hits"]["hits"][0]["_source"])

    @staticmethod
    def _evaluate_reference_data_against_env(resource_id: str, env: str):
        """
        This method verifies that we are not connecting to reference-data dev|uat resources
        from the prod environment or to reference data dev from uat environment.

        NOTE: it assumes that the reference-data resources are:
            - reference-data (PROD)
            - reference-data-uat (UAT)
            - reference-data-dev (DEV)

        :param resource_id: Resource id.
        :type resource_id: str
        """

        if env.lower() == Environment.PROD and (
            resource_id.startswith("reference-data-dev")
            or resource_id.startswith("reference-data-uat")
        ):
            raise ValueError(
                "Flows running on PROD env cannot cannot to reference-data in neither DEV or UAT."
            )

        if env.lower() == Environment.UAT and (
            resource_id.startswith("reference-data-dev")
        ):
            raise ValueError(
                "Flows running on UAT env cannot cannot to reference-data in neither DEV"
            )

    @staticmethod
    def without_meta(rec: dict):
        item = dict([item for item in rec.items() if not item[0].startswith("_meta")])
        return item

    def get_stack_infrastructure(
        self, stack: str, env: str
    ) -> Dict[str, Resource]:  # pragma no cover
        infrastructure = {}
        desired_stacks = ["platform", "srp"]

        # For local runs fetch secret from env vars rather than vault
        if bool(os.environ.get("DEV", False)):
            tenant_elastic_secrets = {
                "id": "tenant-data",
                "host": os.environ.get("ELASTIC_HOST"),
                "port": os.environ.get("ELASTIC_PORT"),
                "scheme": os.environ.get("ELASTIC_SCHEME"),
                "api_key": os.environ.get("ELASTIC_API_KEY"),
            }
        else:
            tenant_elastic_secrets = fetch_elastic_secrets_from_vault()

        for resource_type in CLIENT_MAP.keys():
            resource_index = f"{resource_type.lower()}_resource"
            body = {"query": {"bool": {"must_not": {"exists": {"field": EXPIRY}}}}}

            if resource_type == "ELASTICSEARCH":
                body["query"]["bool"]["filter"] = {"terms": {"stack": desired_stacks}}
            else:
                body["query"]["bool"]["filter"] = {"term": {"env": env.upper()}}

            results = self.es.scroll(
                query=body, index=resource_index, as_dataframe=False
            )

            # Append elastic results obtained from Vault
            if resource_type == "ELASTICSEARCH":
                results.append(tenant_elastic_secrets)

            for result in results:

                if "stack" in result.keys() and result["stack"] not in desired_stacks:
                    continue

                resource = Resource.validate(
                    {
                        "name": result["id"],
                        "type": resource_type,
                        "config": self.without_meta(result),
                    }
                )
                infrastructure[result["id"]] = resource

        # TODO: override platform es resource for EFDH
        if resource_type == "ELASTICSEARCH" and bool(int(os.getenv("USE_EFDH", 0))):
            # replace `reference-data` key with `efdh` resource values in the infrastructure dict
            # replace the `id of platform` resources with `efdh` resource values in the infrastructure dict
            vault_client = SeVaultKV2()
            efdh_es_secrets = vault_client.get_secrets(path=EFDH_ES_SECRETS_PATH)
            result = {
                "id": "reference-data",
                "host": efdh_es_secrets["ELASTIC_HOST"],
                "port": efdh_es_secrets["ELASTIC_PORT"],
                "scheme": efdh_es_secrets["ELASTIC_SCHEME"],
                "api_key": efdh_es_secrets["ELASTIC_API_KEY"],
            }
            infrastructure["reference-data"] = Resource.validate(
                {
                    "name": "reference-data",
                    "type": "ELASTICSEARCH",
                    "config": self.without_meta(result),
                }
            )

        return infrastructure


class DevRegistry(Registry):
    def __init__(self, config: ResourceConfig):
        self.local_ports = {}
        try:
            env_local_ports = os.environ.get(FlowEnvVar.SWARM_LOCAL_PORTS)
            if env_local_ports:
                self.local_ports = json.loads(env_local_ports)
        except Exception:
            pass

        new_port = os.environ.get(
            "DEV_REGISTRY_PORT", self.local_ports.get(config.host, 443)
        )
        config.port = new_port
        config.url = f"{config.scheme}://{config.host}:{config.port}"

        super().__init__(config=config)

    def get_resource(self, resource_id: str, resource_type: str, stack: str) -> dict:
        result = super().get_resource(
            resource_id=resource_id, resource_type=resource_type, stack=stack
        )
        if result["host"] in self.local_ports.keys():
            result["port"] = self.local_ports.get(result["host"])
        return result

    def get_stack_infrastructure(self, stack: str, env: str) -> Dict[str, Resource]:
        infrastructure = super().get_stack_infrastructure(stack=stack, env=env)
        for k, v in infrastructure.items():
            if v.config.host in self.local_ports.keys():
                new_port = self.local_ports.get(v.config.host)
                v.config.port = new_port
                v.config.url = f"{v.config.scheme}://{v.config.host}:{v.config.port}"
        return infrastructure
