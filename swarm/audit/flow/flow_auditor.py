import logging
import os
from dataclasses import dataclass
from typing import List
from typing import Optional

import psutil
import yaml
from prefect.engine.state import State
from prefect.utilities.collections import DotDict

from swarm.audit.result.elastic import ElasticResult
from swarm.audit.result.elastic import ElasticResultAggregator
from swarm.audit.result.s3 import S3Result
from swarm.audit.result.s3 import S3ResultAggregator
from swarm.audit.result.sftp import SftpResult
from swarm.audit.result.sftp import SftpResultAggregator
from swarm.audit.result.task_audit import TaskAuditAggregator
from swarm.audit.result.task_audit import TaskAuditResult

logger = logging.getLogger(__name__)


@dataclass
class FailedTask:
    name: str
    message: str
    error: Optional[Exception] = None


@dataclass
class AuditResult:
    state: str
    failed: bool = False
    failed_tasks: Optional[List[FailedTask]] = None
    task_audit_result: Optional[TaskAuditResult] = None
    elastic_result: Optional[ElasticResult] = None
    s3_result: Optional[S3Result] = None
    sftp_result: Optional[SftpResult] = None
    empty: bool = False

    def get_failed_tasks(self) -> dict:
        return dict([(ft.name, ft.message) for ft in self.failed_tasks])


class FlowAuditor:
    @staticmethod
    def execute(state: State, context: DotDict) -> AuditResult:
        # capture any failed tasks
        failed_tasks = list()
        for task, result in state.result.items():
            if result.is_successful():
                continue
            failed_tasks.append(
                FailedTask(
                    name=task.__class__.__name__,
                    message=result.message,
                    error=result.result,
                )
            )

        # task audit result
        task_audit_result = TaskAuditAggregator.execute(context=context)

        # elastic result
        elastic_result = ElasticResultAggregator.execute(context=context)

        # s3 result
        s3_result = S3ResultAggregator.execute(context=context)

        # sftp result
        sftp_result = SftpResultAggregator.execute(context=context)

        # audit result empty when no results and not failed
        empty = (
            elastic_result.empty
            and task_audit_result.empty
            and s3_result.empty
            and sftp_result.empty
            and state.is_successful()
        )

        result = AuditResult(
            state=state.message,
            failed=state.is_failed(),
            failed_tasks=failed_tasks,
            task_audit_result=task_audit_result,
            elastic_result=elastic_result,
            s3_result=s3_result,
            sftp_result=sftp_result,
            empty=empty,
        )

        # add memory metrics to logger
        process = psutil.Process(os.getpid())
        memory = process.memory_info()
        cpu = {
            "cpu_count": psutil.cpu_count(),
            "cpu_percent": process.cpu_percent(),
            "cpu_times": process.cpu_times(),
            "cpu_freq": psutil.cpu_freq(percpu=False),
        }

        # add aws data to message
        aws = FlowAuditor.aws_env_vars()

        message = dict(
            state=result.state,
            failed_tasks=result.get_failed_tasks(),
            memory=memory,
            cpu=cpu,
            aws=aws,
        )
        logger.info(f"\n{yaml.dump(message)}")

        return result

    @staticmethod
    def aws_env_vars():
        aws = dict(
            batch_ce_name=os.environ.get("AWS_BATCH_CE_NAME", ""),
            batch_job_array_index=os.environ.get("AWS_BATCH_JOB_ARRAY_INDEX", ""),
            batch_job_attempt=os.environ.get("AWS_BATCH_JOB_ATTEMPT", ""),
            batch_job_id=os.environ.get("AWS_BATCH_JOB_ID", ""),
            batch_job_main_node_index=os.environ.get(
                "AWS_BATCH_JOB_MAIN_NODE_INDEX", ""
            ),
            batch_job_main_node_private_ipv4_address=os.environ.get(
                "AWS_BATCH_JOB_MAIN_NODE_PRIVATE_IPV4_ADDRESS", ""
            ),
            batch_jon_node_index=os.environ.get("AWS_BATCH_JOB_NODE_INDEX", ""),
            batch_job_num_nodes=os.environ.get("AWS_BATCH_JOB_NUM_NODES", ""),
            batch_jq_name=os.environ.get("AWS_BATCH_JQ_NAME", ""),
        )
        return aws
