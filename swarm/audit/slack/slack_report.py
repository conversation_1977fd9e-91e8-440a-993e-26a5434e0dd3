import logging
import os
from dataclasses import asdict
from typing import Any
from typing import List
from typing import Optional

import humanize
import pendulum
import stringcase
from se_schema.models.swarm.bundle.model import Bundle

from swarm.audit.flow.flow_auditor import AuditResult
from swarm.conf import Settings
from swarm.flow.workflow import Workflow
from swarm.task.io.write.s3.result import S3File
from swarm.task.io.write.sftp.result import SftpFile
from swarm.utilities.slack import send_raw_message
from swarm.utilities.slack import TOKEN

logger = logging.getLogger(__name__)


class SlackReport:
    divider_block = {"type": "divider"}

    def __init__(
        self,
        audit_result: AuditResult,
        workflow: Workflow,
        parameters: Optional[dict] = None,
    ):
        self.audit_result = audit_result
        self.workflow = workflow
        self.parameters = parameters or dict()

    def publish(self) -> Any:
        bundle = self.workflow.config.bundle
        blocks = []

        elapsed = pendulum.now().utcnow().diff(self.workflow.start_time).in_words()

        client = self.workflow.client.get("name")

        # TODO: specific for AWS
        batch_job_id = os.environ.get("AWS_BATCH_JOB_ID")

        blocks.extend(
            self.compose_header(
                bundle=bundle,
                env=Settings.STACK,
                client=client,
                tenant=Settings.tenant,
                elapsed=elapsed,
                batch_job_id=batch_job_id,
            )
        )

        if self.audit_result.failed:
            failed_block = self.compose_failed_state()
            if failed_block:
                blocks.append(self.divider_block)
                blocks.extend(failed_block)

        if self.parameters:
            params_block = self.compose_parameters(parameters=self.parameters)
            if params_block:
                blocks.append(self.divider_block)
                blocks.extend(params_block)

        if (
            self.audit_result.elastic_result
            and not self.audit_result.elastic_result.empty
        ):
            elastic_block = self.compose_elastic_blocks(result=self.audit_result)
            if elastic_block:
                blocks.append(self.divider_block)
                blocks.extend(elastic_block)

        if self.audit_result.s3_result and not self.audit_result.s3_result.empty:
            s3_block = self.compose_s3_blocks(result=self.audit_result)
            if s3_block:
                blocks.append(self.divider_block)
                blocks.extend(s3_block)

        if self.audit_result.sftp_result and not self.audit_result.sftp_result.empty:
            sftp_block = self.compose_sftp_blocks(result=self.audit_result)
            if sftp_block:
                blocks.append(self.divider_block)
                blocks.extend(sftp_block)

        if (
            self.audit_result.task_audit_result
            and not self.audit_result.task_audit_result.empty
        ):
            audit_block = self.compose_task_audit_blocks(result=self.audit_result)
            if audit_block:
                blocks.append(self.divider_block)
                blocks.extend(audit_block)

        channel = (
            "#swarm-flow-dev-watcher"
            if Settings.env != "prod"
            else "#swarm-flow-watcher"
        )

        channel = os.environ.get("SWARM_AUDITS_CHANNEL", channel)

        message = {
            "token": TOKEN,
            "channel": channel,
            "text": f"{bundle.name} Audit Report",
            "username": bundle.name,
            "blocks": blocks,
        }

        if Settings.DEV:
            logger.info(f"Not sending slack report in DEV: {message}")
            return
        send_raw_message(message)

    @staticmethod
    def compose_header(
        bundle: Bundle,
        env: str,
        client: str,
        tenant: str,
        elapsed: str,
        batch_job_id: Optional[str],
    ):
        context_args = [tenant, bundle.id, env, elapsed, bundle.image]

        if batch_job_id:
            context_args.append(f"batch job id: {batch_job_id}")

        job_attempt = os.environ.get("AWS_BATCH_JOB_ATTEMPT", 0)
        if job_attempt:
            context_args.append(f"attempt: {job_attempt}")

        ctx = "  |  ".join(context_args)

        blocks = [
            {"type": "section", "text": {"type": "mrkdwn", "text": f"*{client}*"}},
            {"type": "context", "elements": [{"text": ctx, "type": "mrkdwn"}]},
        ]

        return blocks

    def compose_failed_state(self):
        fields = [
            dict(
                type="mrkdwn",
                text=f"*{len(self.audit_result.failed_tasks)}* task(s) failed.",
            )
        ]
        heading = self.audit_result.state.upper().replace(".", "")

        blocks = [
            {
                "type": "section",
                "text": {"type": "mrkdwn", "text": f" :memo: *{heading}*"},
            },
            {"type": "section", "fields": fields},
        ]
        return blocks if fields else None

    @staticmethod
    def compose_parameters(parameters: dict):
        fields = list()
        for param, value in parameters.items():
            field = dict(type="mrkdwn", text=f"*{param}*\n{value}")
            fields.append(field)

        blocks = [
            {
                "type": "section",
                "text": {"type": "mrkdwn", "text": " :memo: *PARAMETERS*"},
            },
        ]
        sections = [{"type": "section", "text": fld} for fld in fields]
        blocks.extend(sections)
        return blocks if fields else None

    @staticmethod
    def compose_s3_fields(header: str, actions: List[S3File]):
        fields = list()
        for file_action in actions:
            nat_bytes = humanize.naturalsize(file_action.bytes)
            text = (
                f"*{header}*\n_BUCKET_: {file_action.bucket_name}"
                f"\n_KEY_: {file_action.key_name}"
                f"\n_SIZE_: {nat_bytes}"
            )
            field = dict(type="mrkdwn", text=text)
            fields.append(field)
        return fields

    @staticmethod
    def compose_s3_blocks(result: AuditResult):
        fields = list()

        if result.s3_result.uploaded:
            uploaded_fields = SlackReport.compose_s3_fields(
                header="Uploaded", actions=result.s3_result.uploaded
            )
            fields.extend(uploaded_fields)

        if result.s3_result.downloaded:
            downloaded_fields = SlackReport.compose_s3_fields(
                header="Downloaded", actions=result.s3_result.downloaded
            )
            fields.extend(downloaded_fields)

        if len(fields) > 10:
            count = len(fields)
            fields = fields[:9]
            fields.append(
                dict(
                    type="mrkdwn",
                    text=f"*Plus*\n{count - 9} more upload(s) we can't fit here...",
                )
            )

        blocks = [
            {"type": "section", "text": {"type": "mrkdwn", "text": " :memo: *S3*"}},
        ]
        sections = [{"type": "section", "text": fld} for fld in fields]
        blocks.extend(sections)
        return blocks if fields else None

    @staticmethod
    def compose_sftp_fields(header: str, actions: List[SftpFile]):
        fields = list()
        for file_action in actions:
            nat_bytes = humanize.naturalsize(file_action.bytes)
            text = (
                f"*{header}*\n_REMOTE_DIR_: {file_action.remote_dir}"
                f"\n_FILE_NAME_: {file_action.file_name}"
                f"\n_SIZE_: {nat_bytes}"
            )
            field = dict(type="mrkdwn", text=text)
            fields.append(field)
        return fields

    @staticmethod
    def compose_sftp_blocks(result: AuditResult):
        fields = list()

        if result.sftp_result.uploaded:
            uploaded_fields = SlackReport.compose_sftp_fields(
                header="Uploaded", actions=result.sftp_result.uploaded
            )
            fields.extend(uploaded_fields)

        if result.s3_result.downloaded:
            downloaded_fields = SlackReport.compose_sftp_fields(
                header="Downloaded", actions=result.sftp_result.downloaded
            )
            fields.extend(downloaded_fields)

        if len(fields) > 10:
            count = len(fields)
            fields = fields[:9]
            fields.append(
                dict(
                    type="mrkdwn",
                    text=f"*Plus*\n{count - 9} more upload(s) we can't fit here...",
                )
            )

        blocks = [
            {"type": "section", "text": {"type": "mrkdwn", "text": " :memo: *SFTP*"}},
        ]
        sections = [{"type": "section", "text": fld} for fld in fields]
        blocks.extend(sections)
        return blocks if fields else None

    @staticmethod
    def compose_task_audit_blocks(result: AuditResult):
        fields = list()
        for summary in result.task_audit_result.summary:
            text = f"*{summary.task}*\n_MSG_: {summary.message}\n_ERR_: {summary.error}"
            if len(text) > 2000:
                logger.warning(f"truncating audit message for slack:\n{text}")
                text = text[:2000]
            field = dict(type="mrkdwn", text=text)
            fields.append(field)
            if summary.traceback is not None:
                text = f"*{summary.task} Traceback*\n{summary.traceback}"
                if len(text) > 2000:
                    logger.warning(f"truncating audit traceback for slack:\n{text}")
                    text = text[-2000:]
                field = dict(type="mrkdwn", text=text)
                fields.append(field)

        if len(fields) > 10:
            count = len(fields)
            fields = fields[:9]
            fields.append(
                dict(
                    type="mrkdwn",
                    text=f"*Plus*\n{count - 9} more error(s) we can't fit here...",
                )
            )

        blocks = [
            {
                "type": "section",
                "text": {"type": "mrkdwn", "text": " :memo: *TASK AUDITS*"},
            },
        ]
        sections = [{"type": "section", "text": fld} for fld in fields]
        blocks.extend(sections)
        return blocks if fields else None

    @staticmethod
    def compose_elastic_blocks(result: AuditResult):
        blocks = [
            {
                "type": "section",
                "text": {"type": "mrkdwn", "text": " :memo: *ELASTIC*"},
            },
        ]

        for model_stat in result.elastic_result.model_stats:
            blocks.append(
                dict(
                    type="section",
                    text=dict(type="mrkdwn", text=f"*{model_stat.model_name}*"),
                )
            )
            total_counts = dict(
                [(k, v) for k, v in asdict(model_stat.counts).items() if v > 0]
            )
            fields = list()
            for category, count in total_counts.items():
                category = stringcase.capitalcase(category)
                field = dict(type="mrkdwn", text=f"*{category}*\n{count}")
                fields.append(field)

            blocks.append({"type": "section", "fields": fields})

        if result.elastic_result.payload:
            payload = humanize.naturalsize(result.elastic_result.payload)
            blocks.append(
                dict(
                    type="section",
                    text=dict(type="mrkdwn", text=f"*Payload*\n{payload}"),
                )
            )

        return blocks if len(blocks) > 1 else None
