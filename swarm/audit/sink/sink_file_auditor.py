import json
import logging
import textwrap
from datetime import datetime
from typing import Any
from typing import List
from typing import Optional

import pandas as pd
from schema_sdk.steeleye_model.base.schema import SchemaExtra
from se_elastic_schema.models import SinkFileAudit
from se_elasticsearch.repository import get_repository_by_cluster_version
from se_elasticsearch.repository import ResourceConfig
from se_schema.models.provenance.sink_file_audit.model import (
    SinkFileAudit as SeSchemaSinkFileAudit,
)

from swarm.audit.flow.flow_auditor import AuditResult
from swarm.audit.result.elastic import WriteCounts
from swarm.client.record_handler_helper import init_record_handler
from swarm.schema.task.auditor.model import AGGREGATION_DELIMITER
from swarm.task.io.write.elastic.static import BulkWriterColumns

logger = logging.getLogger(__name__)


class SinkFileAuditor:
    model = "SinkFileAudit"

    def __init__(
        self, config: ResourceConfig, tenant: str, audit_id: Optional[str] = None
    ):
        self.audit_id = audit_id
        self.record_handler = init_record_handler(
            client=get_repository_by_cluster_version(resource_config=config).client,
            tenant=tenant,
        )

        self.tenant = tenant

        # maintain SFA support for ES5 clients that still use se-schema models and alias naming conventions
        if (
            self.record_handler.client.info()
            .get("version", {})
            .get("number", "")
            .startswith("5")
        ):
            self.write_alias = (
                SeSchemaSinkFileAudit.schema()
                .get(SchemaExtra.ALIAS)
                .format(company=tenant)
            )
        else:
            self.write_alias = SinkFileAudit.get_elastic_index_alias(tenant=tenant)

    def create_record(
        self,
        bucket: str,
        key: str,
        queued: datetime,
        task_name: str,
        task_version: str,
        data_source: dict,
        status: str = "QUEUED",
        triggered_by: str = "S3",
        constrained: bool = False,
        target_indices: List[int] = None,
    ):
        audit_record = dict(
            tenant=self.tenant,
            bucket=bucket,
            key=key,
            queued=queued.isoformat() + "Z",
            status=status,
            triggeredBy=triggered_by,
            dataSource=data_source,
            taskName=task_name,
            taskVersion=task_version,
            constrained=constrained,
            targetIndices=target_indices,
        )

        result = self.record_handler.create(
            doc=audit_record, alias=self.write_alias, model=self.model
        )
        self.audit_id = result.id
        return self.audit_id

    def get_record(self, record_id: str) -> dict:
        result = self.record_handler.get(
            index=self.write_alias, model=self.model, doc_id=record_id
        )
        return result

    def update_record(self, record_id: str, version: int, **fields):
        self.record_handler.update_in_place(
            doc=fields,
            alias=self.write_alias,
            model=self.model,
            doc_id=record_id,
            version=version,
        )

    def on_processing(self):
        self.update_record(
            record_id=self.audit_id,
            version=1,
            status="PROCESSING",
            started=datetime.utcnow().isoformat() + "Z",
        )

    def on_errored(self, result: AuditResult):
        duration, completed = self.calculate_duration()

        self.update_record(
            record_id=self.audit_id,
            version=2,
            status="ERRORED",
            taskError=result.state,
            completed=completed.isoformat() + "Z",
            duration=str(duration),
        )

    # flake8: noqa: C901
    def on_processed(self, result: AuditResult) -> Any:
        duration, completed = self.calculate_duration()

        audit_record = dict(
            status="PROCESSED",
            completed=completed.isoformat() + "Z",
            duration=str(duration),
        )

        if not result.elastic_result.empty:
            total_counts = result.elastic_result.total_counts
            model_stats = [
                model_stat
                for model_stat in result.elastic_result.model_stats
                if model_stat.model_name != "SinkRecordAudit"
            ]
            quarantined_model_stats = [
                model_stat
                for model_stat in model_stats
                if model_stat.model_name.startswith("Quarantine")
            ]
            for quarantined_model_stat in quarantined_model_stats:
                primary_model_name = quarantined_model_stat.model_name.replace(
                    "Quarantined", ""
                )
                primary_model_stat = [
                    model_stat
                    for model_stat in model_stats
                    if model_stat.model_name == primary_model_name
                ]
                if primary_model_stat:
                    primary_model_stat = primary_model_stat[0]
                    model_stats.remove(quarantined_model_stat)
                    model_stats.remove(primary_model_stat)
                    counts = primary_model_stat.counts
                    counts.version_conflict = (
                        quarantined_model_stat.counts.version_conflict
                    )
                    counts.duplicate += quarantined_model_stat.counts.duplicate
                    counts.quarantined += quarantined_model_stat.counts.created
                    counts.errored += quarantined_model_stat.counts.errored
                    counts.updated += quarantined_model_stat.counts.updated
                    model_stats.append(primary_model_stat)

            if quarantined_model_stats:
                total_counts = self.update_total_counts(
                    write_counts=[ms.counts for ms in model_stats]
                )

            imported = total_counts.updated + total_counts.created
            audit_record.update(
                dict(
                    imported=imported,
                    duplicate=total_counts.duplicate,
                    iterable=total_counts.total,
                    errored=total_counts.errored,
                    quarantined=total_counts.quarantined,
                )
            )

            record_type_stats = list()
            for model_stat in model_stats:
                imported = model_stat.counts.updated + model_stat.counts.created
                stat = dict(
                    recordType=model_stat.model_name,
                    imported=imported,
                    quarantined=model_stat.counts.quarantined,
                    duplicate=model_stat.counts.duplicate,
                    errored=model_stat.counts.errored,
                )
                if model_stat.counts.duplicate > 0:
                    dupe_model_mask = (
                        result.elastic_result.duplicates[BulkWriterColumns.MODEL]
                        == model_stat.model_name
                    )
                    if dupe_model_mask.any():
                        dupes_for_model = result.elastic_result.duplicates.loc[
                            dupe_model_mask,
                            [BulkWriterColumns.ID, BulkWriterColumns.RAW_INDEX],
                        ]
                        if not dupes_for_model.empty:
                            stat["duplicateRecords"] = dupes_for_model.rename(
                                columns={
                                    BulkWriterColumns.ID: "id",
                                    BulkWriterColumns.RAW_INDEX: "sourceFileIndex",
                                }
                            ).to_dict(orient="records")
                    else:
                        logger.warning(
                            f"model stats for {model_stat.model_name} show {model_stat.counts.duplicate} "
                            f"duplicate(s) but no dupes found in mask."
                        )

                record_type_stats.append(stat)
            audit_record["recordTypeStats"] = record_type_stats

        if not result.task_audit_result.empty:
            # aggregate error types
            error_class_stats = list()
            if len(result.task_audit_result.errors.index) > 0:

                # Automatically aggregates errors which follow the aggregation pattern
                agg_record_mask = (
                    result.task_audit_result.errors.get(
                        "ctx.error", default=pd.Series(dtype="str")
                    )
                    .fillna("")
                    .astype("string")
                    .str.contains(rf".*{AGGREGATION_DELIMITER}.*", regex=True)
                )
                if not agg_record_mask.empty:
                    to_be_agg_records = result.task_audit_result.errors[agg_record_mask]
                    normal_records = result.task_audit_result.errors[~agg_record_mask]
                else:
                    to_be_agg_records = pd.DataFrame()
                    normal_records = result.task_audit_result.errors

                if not to_be_agg_records.empty:
                    if "ctx.error" in to_be_agg_records.columns:
                        for error_message, frame in to_be_agg_records.groupby(
                            by=["message"]
                        ):
                            exploded_df = pd.DataFrame(
                                frame["ctx.error"]
                                .str.replace(
                                    rf"\s+{AGGREGATION_DELIMITER}\s+",
                                    AGGREGATION_DELIMITER,
                                    regex=True,
                                )
                                .fillna("")
                                .str.split(AGGREGATION_DELIMITER)
                                .values.tolist()
                            )
                            cols = exploded_df.columns.tolist()
                            # We group-by all cols of the split message and aggregate the last one
                            agg_df = exploded_df.groupby(cols[:-1], as_index=False).agg(
                                {cols[-1]: list}
                            )
                            error_class_stat = dict(
                                errorClass=error_message,
                                values=agg_df.astype("str")
                                .agg(" : ".join, axis=1)
                                .apply(lambda x: textwrap.wrap(x, width=30000)[0])
                                .values.tolist(),
                            )
                            error_class_stats.append(error_class_stat)

                if not normal_records.empty:
                    for message, frame in normal_records.groupby(by=["message"]):
                        if "ctx.error" in frame.columns:
                            if "ctx.columns" in frame.columns:
                                # enrich audit with column details if present
                                error_values = (
                                    frame["ctx.error"].fillna("").astype(str)
                                    + " "
                                    + frame["ctx.columns"].fillna("").astype(str)
                                )
                                error_values = error_values.unique().tolist()
                            else:
                                error_values = frame["ctx.error"].unique().tolist()
                        else:
                            error_values = [message]
                        error_class_stat = dict(errorClass=message, values=error_values)
                        error_class_stats.append(error_class_stat)

            if result.elastic_result and not result.elastic_result.errors.empty:
                writer_error_stats = list()
                for error_type, frame in result.elastic_result.errors.groupby(
                    by=[BulkWriterColumns.ERROR_TYPE]
                ):
                    error_reasons = (
                        frame[BulkWriterColumns.ERROR_REASON].unique().tolist()
                    )
                    error_class_stat = dict(
                        errorClass=f"Elasticsearch errors: {error_type}",
                        values=error_reasons,
                    )
                    writer_error_stats.append(error_class_stat)

                error_class_stats.extend(writer_error_stats)

            # Aggregate counts from individual task counts
            task_record_error_counts = (
                result.task_audit_result.errors.get(
                    "ctx.error_count", default=pd.Series(dtype="int")
                )
                .fillna(0)
                .astype("int")
                .sum()
            )
            task_record_skip_count = (
                result.task_audit_result.errors.get(
                    "ctx.skip_count", default=pd.Series(dtype="int")
                )
                .fillna(0)
                .astype("int")
                .sum()
            )
            task_duplicate_count = (
                result.task_audit_result.errors.get(
                    "ctx.duplicate_count", default=pd.Series(dtype="int")
                )
                .fillna(0)
                .astype("int")
                .sum()
            )
            task_total_count = (
                result.task_audit_result.errors.get(
                    "ctx.input_total_count", default=pd.Series(dtype="int")
                )
                .fillna(0)
                .astype("int")
                .sum()
            )

            audit_record["errored"] = (
                audit_record.get("errored", 0) + task_record_error_counts
            )
            audit_record["duplicate"] = (
                audit_record.get("duplicate", 0) + task_duplicate_count
            )
            audit_record["skipped"] = task_record_skip_count

            # Overriding iterable (total count) logic to point to the count of the
            # input file rather than total count of records which were ingested.
            audit_record["iterable"] = (
                task_total_count if task_total_count else audit_record.get("iterable")
            )

            audit_record["errorClassStats"] = error_class_stats

        # Serialise to avoid NaNs, etc.
        # TODO: this should move to sdk update_record
        audit_record = json.loads(pd.Series(audit_record).to_json(date_format="iso"))

        try:
            self.update_record(record_id=self.audit_id, version=2, **audit_record)
        except Exception as e:
            logger.error(f"{e} exception caught for audit record:\n{audit_record}")
            raise e

    @staticmethod
    def update_total_counts(write_counts: List[WriteCounts]) -> WriteCounts:
        total_counts = WriteCounts(
            created=sum([wc.created for wc in write_counts]),
            updated=sum([wc.updated for wc in write_counts]),
            errored=sum([wc.errored for wc in write_counts]),
            duplicate=sum([wc.duplicate for wc in write_counts]),
            quarantined=sum([wc.quarantined for wc in write_counts]),
            version_conflict=sum([wc.version_conflict for wc in write_counts]),
        )
        total_counts.total = (
            total_counts.created
            + total_counts.updated
            + total_counts.errored
            + total_counts.duplicate
            + total_counts.quarantined
            + total_counts.version_conflict
        )
        return total_counts

    def calculate_duration(self):
        # fetch current sink file audit record for this flow
        current_record = self.get_record(record_id=self.audit_id)

        # set completed and calculate duration
        # todo handle properly as fromisoformat doesn't like Z
        started = datetime.fromisoformat(current_record.get("started")[:-1])
        completed = datetime.utcnow()
        duration = completed - started
        return duration, completed
