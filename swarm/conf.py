import logging
import os
from typing import List

from se_elasticsearch.repository import ResourceConfig

from swarm.client.base import ClientBase
from swarm.client.utilities import instantiate_client
from swarm.client.utilities import instantiate_platform_clients
from swarm.flow.static import FlowEnvVar
from swarm.flow.static import RegistryCluster
from swarm.platform.registry import DevRegistry
from swarm.platform.registry import Registry
from swarm.platform.secrets import _SeLocalSecretsStore
from swarm.platform.secrets import A<PERSON>SecretsStore
from swarm.platform.secrets import Se<PERSON><PERSON>retsStore
from swarm.platform.vault import _SeVaultLocalSecretsStore
from swarm.platform.vault import AbstractVault
from swarm.platform.vault import SeVaultKV2
from swarm.schema.workflow.model import PrefectContextSwarmParams

logger = logging.getLogger(__name__)


class SettingsCls:
    """
    This class is supposed to act as a singleton for a flow run.
    It centralizes behaviours and adds a level of abstraction.
    """

    def __init__(self):
        # If DEV is defined in the environment we consider we're developing locally.
        # Should only be used on local development systems (developer local machine)
        self.DEV = bool(os.environ.get("DEV", False))
        self.FLOW_ID = os.environ.get(FlowEnvVar.SWARM_FLOW_ID)
        self.CLIENT_SECRETS = os.environ.get(FlowEnvVar.SWARM_CLIENT_SECRETS)

        # we need to cater for local flow running env var for now.
        if self.FLOW_ID is None and self.DEV:
            self.FLOW_ID = os.environ.get(FlowEnvVar.SWARM_LOCAL_FLOW_ID)

        self.STACK = os.environ.get("STACK")
        self.CI = os.environ.get("CI") == "true"

        self._vault_secrets_store = None
        self._secrets_store = None
        self._registry = None
        self._connections = None
        self._context = None

    @property
    def env(self) -> str:
        """
        STACK env var should hold: dev-blue, uat-blue, prod-i3
        """
        return self.STACK.split("-")[0] if self.STACK != "srp" else "prod"

    @property
    def secrets_store(self) -> AbsSecretsStore:
        """
        :return: a secrets store instance
        """
        if self._secrets_store is None:
            if self.CI:
                return _SeLocalSecretsStore()
            self._secrets_store = SeSecretsStore()
        return self._secrets_store

    @property
    def vault_secrets_store(self) -> AbstractVault:
        """
        :return: a hashicorp vault secrets store instance
        """
        if self._vault_secrets_store is None:
            if self.CI:
                return _SeVaultLocalSecretsStore()
            self._vault_secrets_store = SeVaultKV2()
        return self._vault_secrets_store

    @property
    def job_id(self) -> str:
        """
        :return: the current running job_id
        TODO will need chaning for when running outside AWS Batch
        """
        return os.environ.get("AWS_BATCH_JOB_ID", self.DEV and "DEV" or "")

    @property
    def realm(self) -> str:
        """
        As per our convention on flow id returns the realm
        :return:  realm for current running flow
        """
        return self.FLOW_ID.split(":", 1)[0]

    @property
    def client_secrets(self) -> str:
        """
        Optional. A client secret refers to a parameter in Aws System Manager,
        Parameter Store.
        These secrets are specific for a tenant and do not interfere with the infrastructure
        managed by Terraform.
        It refers to a parameter named self.CLIENT_SECRETS.tenant on AWS.
        :return: client secret name
        """
        return (
            self.CLIENT_SECRETS
            if self.CLIENT_SECRETS
            else f"client_secrets.{self.realm}"
        )

    @property
    def bundle(self) -> str:
        """
        :return: Bundle for the currently running flow
        """
        return self.FLOW_ID.split(":", 1)[1]

    @property
    def tenant(self) -> str:
        """
        :return: Tenant for the currently running flow
        stacks (prod-i3, uat-blue, dev-blue...) and platform are viewed as tenants
        """
        return self.realm.split(".", 1)[0]

    @property
    def domain(self) -> str:
        """
        :return: the current domain (steeleye.co, uat.steeleye.co, dev.steeleye.co)
        """
        return self.realm.split(".", 1)[1]

    @property
    def registry(self) -> Registry:
        """
        Method to get the Registry object.
        Has different logic for development environment.
        :return: Registry object to abstract all communication with our registry.
        """
        if self._registry:
            return self._registry
        config = ResourceConfig(
            host=RegistryCluster.HOST,
            scheme=RegistryCluster.SCHEME,
            port=RegistryCluster.PORT,
        )
        if self.DEV:
            self._registry = DevRegistry(config=config)
        else:
            self._registry = Registry(config=config)
        return self._registry

    @property
    def connections(self) -> "SwarmConnections":
        """
        The connections are the way a flow can access external resources.
        :return: an object that can create resource clients on demand.
        """
        if self._connections is None:
            self._connections = SwarmConnections()
        return self._connections

    @property
    def context(self) -> PrefectContextSwarmParams:
        if self._context is None:
            self._context = PrefectContextSwarmParams(
                audits_dir="audits",
                results_dir="results",
                sources_dir="sources",
                targets_dir="targets",
                quarantines_dir="quarantines",
            )
        return self._context


class SwarmConnections:
    """
    This class handles the caching and creation of connections to resources
    """

    def __init__(self):
        self._stack_infra = None
        self._platform_es_resources = None

    @property
    def stack_infra(self):
        if self._stack_infra is None:
            self._stack_infra = Settings.registry.get_stack_infrastructure(
                stack=Settings.STACK, env=Settings.env
            )
        return self._stack_infra

    def get_config(self, key: str) -> ResourceConfig:
        """
        :param key: key for the resource (tenant-data, reference-data, sftp-unavista)
        :return: the ResourceConfig to be used all over
        """
        return self.stack_infra.get(key).config

    def get(self, key: str) -> ClientBase:
        """
        :param key: key for the resource (tenant-data, reference-data, sftp-unavista)
        :return: a single Client for that resource
        """
        resource = self.stack_infra.get(key)
        return instantiate_client(resource)

    def get_platform_resources(self, key: str, resource_type: str) -> List[ClientBase]:
        """
        This method is supposed to be used by "platform" flows where we want to get
        connections to all of the stacks elasticsearch clusters.

        :param key: key for the resources to search for (teanant-data, reference-data)
        :param resource_type: the type of resource (elasticsearch, sftp)
        :return: a list of Clients connected to each of the resources found.
        """
        if self._platform_es_resources is None:
            self._platform_es_resources = Settings.registry.get_platform_resources(
                key, Settings.env, resource_type=resource_type
            )
        results = instantiate_platform_clients(self._platform_es_resources)
        return results


Settings = SettingsCls()
