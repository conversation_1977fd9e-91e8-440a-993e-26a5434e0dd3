import logging
from typing import List
from typing import Type
from typing import Union

import pandas as pd
import pendulum
from addict import Dict
from schema_sdk.steeleye_model.gamma import SchemaGammaExtra
from se_schema.base.steeleye_schema_model import SteeleyeSchemaModel
from se_schema.utilities.inspector import find_model

log = logging.getLogger(__name__)


class SchemaReader:
    @staticmethod
    def id_props_for(model_name: str) -> List[str]:
        model = SchemaReader.model_for(model_name=model_name)
        id_props = model.schema().get(SchemaGammaExtra.ID_PROPERTIES)
        return id_props

    @staticmethod
    def hash_props_for(model_name: str) -> List[str]:
        model = SchemaReader.model_for(model_name=model_name)
        hash_props = model.schema().get(SchemaGammaExtra.HASH_PROPERTIES)
        return hash_props

    @staticmethod
    def unique_fields_props_for(model_name: str) -> List[str]:
        model = SchemaReader.model_for(model_name=model_name)
        unique_fields = model.schema().get(SchemaGammaExtra.UNIQUE_FIELDS)
        return unique_fields

    @staticmethod
    def model_for(model_name: str) -> Type[SteeleyeSchemaModel]:
        # hack for abaci
        model = find_model("UserSession" if model_name == "Session" else model_name)
        return model

    @staticmethod
    def should_hash_id_for(model_name: str) -> bool:
        """
        Returns the GEN_ID_AS_HASH flag from the model schema.
        """
        model = SchemaReader.model_for(model_name=model_name)
        id_as_hash_flag = model.schema().get(SchemaGammaExtra.GEN_ID_AS_HASH, False)
        return id_as_hash_flag

    @staticmethod
    def index_for(model_name: str, doc: Union[dict, pd.Series], tenant: str):
        model = find_model(model_name)
        alias = model.schema().get(SchemaGammaExtra.ALIAS)
        if "{company}" in alias:
            alias = alias.format(company=tenant)
        if not model.schema().get(SchemaGammaExtra.IS_TEMPLATE, False):
            return alias

        template_strategy = model.schema().get(SchemaGammaExtra.TEMPLATE_STRATEGY)
        idx = SchemaReader.__make_index(
            alias=alias, tpl_strategy=template_strategy, doc=doc
        )
        return idx

    @staticmethod
    def __make_index(alias: str, tpl_strategy: Dict, doc: pd.Series) -> str:
        try:
            strategy = tpl_strategy.get("strategy", "")
            if strategy.lower() == "date_attribute":
                date_val = doc.get(tpl_strategy.get("attribute"))
                return tpl_strategy.get("pattern").replace(
                    "*", pendulum.parse(date_val).format(tpl_strategy.get("format"))
                )

            elif strategy.lower() == "auto_date":
                return tpl_strategy.get("pattern").replace(
                    "*", pendulum.now().format(tpl_strategy.get("format"))
                )
        except Exception as e:
            raise SchemaError(f"failed to make index for {alias} - {str(e)}")

        raise SchemaError(f"unable to get template strategy for {alias}")


class SchemaError(Exception):
    def __init__(self, message: str):
        super().__init__(message)
