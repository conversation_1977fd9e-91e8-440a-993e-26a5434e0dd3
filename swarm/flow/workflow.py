import importlib
import logging
from pathlib import Path
from typing import Optional

import addict
import pendulum
import prefect
from indict import Indict
from pendulum import DateTime
from prefect import flatten
from prefect import Flow
from prefect import Parameter
from prefect.engine.results import LocalResult
from prefect.executors.base import Executor
from prefect.tasks.control_flow import ifelse
from prefect.tasks.control_flow import merge
from prefect.tasks.control_flow import switch
from prefect.utilities.collections import DotDict
from se_schema.components.swarm.flow.if_else import IfElse
from se_schema.components.swarm.flow.switch import Switch
from se_schema.models.swarm.flow.model import Flow as SwarmFlowModel

from swarm.conf import Settings
from swarm.schema.task.components import PrefectParams
from swarm.schema.task.static import ResultCategory

logger = logging.getLogger(__name__)


class Workflow:
    def __init__(
        self,
        flow_config: dict,
        client: dict,
        tenant_configuration: Optional[addict.Dict] = None,
    ):
        self._client = client

        # Validate flow
        logger.info("Validating flow configuration ...")

        flow_config = flatten_flow_source_schema(flow_config=flow_config)

        self._config = SwarmFlowModel.validate(flow_config)

        self._prefect_context_params = create_prefect_context_params(
            config=self._config, tenant_configuration=tenant_configuration
        )

        if "." in self._config.bundle.id:
            # this is a python flow, import
            logger.info("Bundleless flow detected, importing python flow.")
            self._flow = getattr(
                importlib.import_module(self._config.bundle.id), "flow"
            )
        else:
            # this is a bundle flow, compose
            logger.info("Bundle flow detected, running flow_factory")
            self._flow = flow_factory(
                swarm_flow=self._config, results_dir=self.context.get("results_dir")
            )

        self._start_time = None

    @property
    def flow(self) -> Flow:
        return self._flow

    @property
    def config(self) -> SwarmFlowModel:
        return self._config

    @property
    def client(self) -> dict:
        return self._client

    @property
    def context(self) -> DotDict:
        return self._prefect_context_params

    @property
    def start_time(self) -> DateTime:
        return self._start_time

    def run(
        self,
        parameters: Optional[dict] = None,
        executor: Executor = None,
    ):
        self._start_time = pendulum.now().utcnow()

        with prefect.context(swarm=self.context):
            state = self._flow.run(parameters=parameters, executor=executor)

            return state


def flow_factory(swarm_flow: SwarmFlowModel, results_dir: Path) -> Flow:
    """During the instantiation of this class, this method creates the necessary Prefect Task instances
    and binds them to the Prefect FlowConfig object.
    """

    # prepare the flow container for the tasks
    flow = Flow(name=swarm_flow.get_name())

    # construct task override cache
    task_overrides = dict(
        [(to.name, to.params) for to in swarm_flow.taskOverrides or list()]
    )

    all_tasks = {}
    all_control_flows = {}

    # get all the parameters defined in the config bundle
    for param in swarm_flow.bundle.get_parameters():
        logger.info(f"Validating parameter for {param.name} ...")
        all_tasks[param.name] = {
            "task": Parameter(name=param.name, required=param.required),
            "upstreamTasks": [],
        }

    # get all the tasks defined in the config bundle
    for task in swarm_flow.bundle.tasks:
        logger.info(f"Validating task {task.name} ...")

        module, importable = task.path.split(":")
        task_class = getattr(importlib.import_module(module), importable)

        # params
        if task.name in task_overrides:
            task_params = task.params or dict()
            task_params.update(task_overrides.get(task.name))
            task_params = task_class.params_class.validate(task_params)
        else:
            task_params = (
                task_class.params_class.validate(task.params) if task.params else None
            )

        # params list
        task_params_list = (
            [task_class.params_class.validate(x) for x in task.params_list]
            if task.params_list
            else None
        )

        # clients
        task_resources = (
            task_class.resources_class.validate(task.resources)
            if task.resources
            else None
        )

        # task config
        task_config = task.config and PrefectParams(**task.config) or PrefectParams()

        # optional result
        result_category = getattr(task_class, "result_category")
        if result_category != ResultCategory.NA:
            result_path = results_dir.joinpath(result_category.value).as_posix()
            task_config.result = LocalResult(dir=result_path)

        task_instance = task_class(
            name=task.name,
            params=task_params,
            params_list=task_params_list,
            config=task_config,
            resources=task_resources,
            platform=swarm_flow.bundle.platform,
        )

        all_tasks[task_instance.name] = {
            "task": task_instance,
            "upstreamTasks": task.upstream_tasks,
        }

    # get all the control flows defined in the config bundle
    for control_flow in swarm_flow.bundle.get_control_flows():
        logger.info(f"Validating control flow for {control_flow.name} ...")
        # switch to functional api to integrate control flows
        if isinstance(control_flow, IfElse):
            with flow:
                required_tasks = (
                    control_flow.conditionTaskName,
                    control_flow.trueTaskName,
                    control_flow.falseTaskName,
                )
                missing = set(required_tasks) - set(all_tasks)
                if missing:
                    raise ValueError(
                        f"Control flow IfElse `{control_flow.name}` "
                        f"references undefined tasks: {missing}"
                    )
                condition = all_tasks[control_flow.conditionTaskName]["task"]
                true_task = all_tasks[control_flow.trueTaskName]["task"]
                false_task = all_tasks[control_flow.falseTaskName]["task"]
                ifelse(
                    condition=condition,
                    true_task=true_task,
                    false_task=false_task,
                    mapped=control_flow.mapped,
                )
                if control_flow.merge:
                    merge_result = merge(
                        true_task, false_task, mapped=control_flow.mapped
                    )
                    all_control_flows[control_flow.name] = merge_result

        elif isinstance(control_flow, Switch):
            with flow:
                required_tasks = list(control_flow.cases.values()) + [
                    control_flow.conditionTaskName
                ]
                missing = set(required_tasks) - set(all_tasks)
                if missing:
                    raise ValueError(
                        f"Control flow Switch `{control_flow.name}` "
                        f"references undefined tasks: {missing}"
                    )
                condition = all_tasks[control_flow.conditionTaskName]["task"]
                cases = dict(
                    [(k, all_tasks[v]["task"]) for k, v in control_flow.cases.items()]
                )
                switch(condition=condition, cases=cases, mapped=control_flow.mapped)
                if control_flow.merge:
                    merge_result = merge(*cases.values(), mapped=control_flow.mapped)
                    all_control_flows[control_flow.name] = merge_result

    # bind tasks to flow
    for task_name in all_tasks.keys():
        task_details = all_tasks[task_name]

        task_instance = task_details["task"]

        # if task has no bindings, simply add to the flow
        upstream_tasks = task_details.get("upstreamTasks")
        if not upstream_tasks:
            flow.add_task(task_instance)
            continue

        for bind in upstream_tasks:
            input_task = (
                all_tasks.get(bind.taskName).get("task")
                if bind.taskName in all_tasks
                else all_control_flows.get(bind.taskName)
            )

            if bind.flatten:
                input_task = flatten(input_task)

            task_instance.set_upstream(
                input_task, flow=flow, mapped=bind.mapped, key=bind.key
            )

    if task_overrides:
        missing = set(task_overrides.keys()).difference(all_tasks.keys())
        if missing:
            raise ValueError(f"task overrides not found: {missing}")

    return flow


def create_prefect_context_params(
    config: SwarmFlowModel, tenant_configuration: Optional[dict]
) -> DotDict:

    prefect_context_params = (
        Settings.context.dict()
    )  # compatibility with what's already in Settings
    prefect_context_params.update(
        dict(
            bundle=config.bundle,
            tenant_configuration=tenant_configuration,
            source_schema=config.bundle.source_schema,
        )
    )
    prefect_context_params = DotDict(prefect_context_params)
    return prefect_context_params


def flatten_flow_source_schema(flow_config: dict) -> dict:
    """
    This method flattens the schema in flow.bundle.schema in case it
    is unflatten (therefore field names contain `.` originally.

    :param flow_config: (dict). Flow record
    :return:
    """
    source_schema = flow_config.get("bundle", {}).get("schema")

    if source_schema is None:
        return flow_config

    flow_config["bundle"]["schema"] = Indict(source_schema).flatten().to_dict()

    return flow_config
