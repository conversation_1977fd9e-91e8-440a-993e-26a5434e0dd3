import copy
import json
import logging
import os
from datetime import datetime
from datetime import <PERSON><PERSON><PERSON>
from pathlib import Path
from typing import Optional

import boto3
import pendulum
import yaml
from elasticsearch6.exceptions import NotFoundError

from swarm.audit.sink.sink_file_auditor import SinkFile<PERSON>uditor
from swarm.conf import Settings
from swarm.flow.flow_meta import FlowMeta
from swarm.flow.static import FlowEnvVar
from swarm.platform.registry import Registry

logger = logging.getLogger(__name__)

COMPUTE = "compute"
QUEUE = "queue"
RETRY = "retry"
ENVIRONMENT = "environment"

# These are the same keys used in the AWS Batch API
MEMORY = "memory"
VCPUS = "vcpus"
TIMEOUT = "attemptDurationSeconds"

BATCH_JOB_QUEUE_TPL = "{stack}-batch-compute-queue"
ANALYTICS_QUEUE = "{stack}-analytics-queue"
BACKLOAD_QUEUE = "{stack}-refinitiv-backload-queue"
S3_COMPUTE_QUEUE = "{stack}-s3-batch-queue"

SMALL_CONTAINER = {VCPUS: 1, MEMORY: 1024 * 7}
MEDIUM_CONTAINER = {VCPUS: 3, MEMORY: 1024 * 15}
LARGE_CONTAINER = {VCPUS: 7, MEMORY: 1024 * 31}
EXTRA_LARGE_CONTAINER = {VCPUS: 7, MEMORY: 1024 * 60}

DEFAULT_ATTEMPT_DURATION = 86400  # 24 hours in seconds max timeout
FLOW_SPECS_INTERVAL = 10  # minutes
FLOW_SPECS = "FLOW_SPECS"

DEFAULT_FLOW_SPECS = {
    "reference-refinitiv-orders-to-tca": {  # tenant stack
        QUEUE: ANALYTICS_QUEUE,
        COMPUTE: SMALL_CONTAINER,
    },
    "reference-refinitiv-tca-metrics": {
        QUEUE: ANALYTICS_QUEUE,
        COMPUTE: LARGE_CONTAINER,
    },  # tenant stack
    "reference-refinitiv-map-ric": {  # srp stack
        QUEUE: BACKLOAD_QUEUE,
        COMPUTE: MEDIUM_CONTAINER,
    },
    "reference-refinitiv-annual-tick-data-download": {  # srp stack
        QUEUE: BACKLOAD_QUEUE,
        COMPUTE: MEDIUM_CONTAINER,
    },
    "reference-refinitiv-ric-backload": {  # srp stack
        QUEUE: S3_COMPUTE_QUEUE,
        COMPUTE: EXTRA_LARGE_CONTAINER,
    },
    "reference-refinitiv-daily-extract": {  # srp stack
        QUEUE: BATCH_JOB_QUEUE_TPL,
        COMPUTE: EXTRA_LARGE_CONTAINER,
    },
    "reference-refinitiv-instrument-poll": {  # srp stack
        QUEUE: BATCH_JOB_QUEUE_TPL,
        COMPUTE: SMALL_CONTAINER,
    },
    "reference-instrument-eurex": {  # tenant stack
        QUEUE: BATCH_JOB_QUEUE_TPL,
        COMPUTE: MEDIUM_CONTAINER,
    },
    "order-feed-reddeer-processor": {  # tenant stack
        QUEUE: BATCH_JOB_QUEUE_TPL,
        COMPUTE: MEDIUM_CONTAINER,
    },
    "emir-universal-cme": {  # tenant stack
        QUEUE: BATCH_JOB_QUEUE_TPL,
        COMPUTE: LARGE_CONTAINER,
    },
    "tr-workflow-unavista-generate-report": {COMPUTE: MEDIUM_CONTAINER},
}


class SeBatchJobSubmitter:
    def __init__(self):
        self._batch_client = None
        self._secrets_store = None
        self._last_specs_fetch = None
        self._flow_specs = self.get_flow_specs_configuration()

    @property
    def batch_client(self):
        if self._batch_client is None:
            params = {}
            role_arn = os.environ.get("SE_BATCH_ARN_ROLE")
            if role_arn is not None:
                sts_client = boto3.client("sts")
                session_name = f"SeBatchSubmitterSession-{pendulum.now().timestamp()}"
                assumed_role_object = sts_client.assume_role(
                    RoleArn=role_arn, RoleSessionName=session_name
                )
                credentials = assumed_role_object["Credentials"]
                params = dict(
                    aws_access_key_id=credentials["AccessKeyId"],
                    aws_secret_access_key=credentials["SecretAccessKey"],
                    aws_session_token=credentials["SessionToken"],
                )
            self._batch_client = boto3.client("batch", **params)
        return self._batch_client

    @staticmethod
    def get_flow_meta(
        registry: Registry,
        s3_key: str,
        s3_bucket: str,
        flow_args: Optional[dict] = None,
        origin: Optional[str] = "NA",
    ) -> FlowMeta:
        """
        Constructs a FlowMeta object by fetching it's data from the Registry.
        :param registry: the SteelEye Registry elastic resource client
        :param s3_key: the s3 key for the file to process
        :param s3_bucket: the s3 bucket where the file lives at
        :param flow_args: additional flow args for the target Swarm Flow
        :param origin: where this flow was triggered from
        :return:
        """
        if "/" in s3_key:
            _, bundle_id, *_ = s3_key.split("/")
        else:
            bundle_id = s3_key
        if flow_args is None:
            flow_args = dict()
        if s3_bucket.startswith("s3://"):
            # a defense if a bucket comes in with the s3:// protocol prefix
            s3_bucket = s3_bucket[5:]
        flow_id = ":".join([s3_bucket, bundle_id])
        file_url = f"s3://{s3_bucket}/{s3_key}"

        logger.info(f"use flow id {flow_id}")

        if flow_id.startswith("refinitiv"):
            # refinitiv flows are really platform flows
            flow_id = flow_id.replace("refinitiv", "platform", 1)

        # extract client id from realm
        client_id, _ = flow_id.split(".", 1)
        # fetch client given client id
        client = registry.get_client(client_id=client_id)
        # extract stack based on client realm
        realm, _ = flow_id.split(":", 1)
        stack = client["stacks"].get(realm)

        # fetch flow given flow id or local flow if passed
        flow = registry.get_flow(flow_id=flow_id, stack=stack)

        # assign platform flow flag
        platform_flow = flow["bundle"].get("platform", False) and flow[
            "realm"
        ].startswith("platform")

        if platform_flow:
            logger.info(f"operating {flow['bundle']['name']} as platform flow")

        flow_meta = FlowMeta(
            flow_id=flow_id,
            flow_args=flow_args,
            bundle_id=bundle_id,
            flow=flow,
            client=client,
            stack=stack,
            s3_bucket=s3_bucket,
            s3_key=s3_key,
            file_url=file_url,
            origin=origin,
        )
        return flow_meta

    @staticmethod
    def _get_audit_key(flow_meta: FlowMeta) -> str:
        """
        Determine the audit key based on flow_meta.
        :param: flow_meta the complete Flow Meta representing the flow
        :returns: the audit_key used for the audit record (str)
        """
        prefix = None
        prefix_args = flow_meta.flow_args.get("prefix")

        if prefix_args:

            delta_in_days = flow_meta.flow_args.get("delta_in_days")
            date_prefix = ""
            if delta_in_days:
                date_prefix = (
                    datetime.today() - timedelta(days=int(delta_in_days))
                ).strftime("%Y%m%d")

            prefix = Path(prefix_args).joinpath(date_prefix).as_posix()

        if flow_meta.s3_key == flow_meta.bundle_id and prefix:
            audit_key = prefix
        else:
            audit_key = flow_meta.file_url

        if audit_key.startswith("s3:"):
            audit_key = audit_key.replace(f"s3://{flow_meta.s3_bucket}/", "")

        return audit_key

    def audit_flow(self, flow_meta: FlowMeta) -> Optional[str]:
        """
        Uses SinkFileAuditor to create an audit record for the flow.

        :param flow_meta: the whole FlowMeta object representing the flow.
        :return: the audit_id for the created record

        """
        bundle = flow_meta.flow["bundle"]
        audit = bundle.get("audit", dict())
        if not audit:
            return None

        infra_resources = Settings.registry.get_stack_infrastructure(
            stack=flow_meta.stack, env=flow_meta.env
        )

        if audit["resourceId"] not in infra_resources:
            raise ValueError(
                f"Unable to find resource {flow_meta.stack} {flow_meta.env} {audit['resourceId']}"
            )

        audit_client_config = infra_resources.get(audit["resourceId"]).config

        task_name, task_version = bundle["image"].split(":")
        writer = SinkFileAuditor(
            config=audit_client_config, tenant=flow_meta.client["id"]
        )
        audit_key = self._get_audit_key(flow_meta)

        audit_id = None
        try:
            audit_id = writer.create_record(
                bucket=flow_meta.s3_bucket,
                key=audit_key,
                queued=datetime.utcnow(),
                task_name=task_name,
                task_version=task_version,
                data_source=dict(name=bundle["name"]),
            )
        except NotFoundError as e:
            if e.error == "index_not_found_exception":
                # don't crash if the index doesn't exist.
                logger.exception(f"SinkAuditor index not found: {flow_meta.client}")
            else:
                raise e
        return audit_id

    def get_job_specs(
        self, flow_meta: FlowMeta, audit_id: Optional[str] = None
    ) -> dict:
        """
        Constructs the Batch job dictionary
        :param flow_meta: the FlowMeta object representing the flow
        :param audit_id: the audit id
        :return: dictionary with full AWS Batch job definition
        """

        if ":" in flow_meta.flow["bundle"]["image"]:
            image, version = flow_meta.flow["bundle"]["image"].split(":")
            version = version.replace(".", "-")
            image = image.replace("_", "-")
        else:
            image = "swarm-tasks"
            version = flow_meta.flow["bundle"]["image"].split(":")[-1].replace(".", "-")

        batch_job_name_tpl = "{tenant}-{bundle}-{version}"
        batch_job_definition_tpl = "{stack}-{image}-v{version}"

        job_definition = batch_job_definition_tpl.format(
            stack=flow_meta.stack, version=version, image=image
        )

        job_name = batch_job_name_tpl.format(
            tenant=flow_meta.client["id"],
            bundle=flow_meta.bundle_id,
            version=version,
        ).replace(".", "-")

        # get the environment variables for PROD/UAT/DEV
        params_dict = self.get_environment_variables(stack=flow_meta.stack)
        params_dict.update(
            {
                FlowEnvVar.SWARM_FLOW_ID: flow_meta.flow_id,
                FlowEnvVar.SWARM_FLOW_ARGS: json.dumps(flow_meta.flow_args),
                FlowEnvVar.SWARM_EMAIL_NOTIFICATION: "true",
                FlowEnvVar.SWARM_ORIGIN: flow_meta.origin,
            }
        )

        if flow_meta.file_url:
            params_dict[FlowEnvVar.SWARM_FILE_URL] = flow_meta.file_url
        if audit_id:
            params_dict[FlowEnvVar.SWARM_AUDIT_ID] = audit_id

        flow_specs = self.get_flow_specs_configuration()
        bundle_cfg = flow_specs.get(flow_meta.bundle_id, {})

        if ENVIRONMENT in bundle_cfg:
            params_dict.update(bundle_cfg.get(ENVIRONMENT))

        params = [dict(name=x, value=str(y)) for x, y in params_dict.items()]
        container_overrides = {"environment": params}

        # default queue logic
        job_queue = BATCH_JOB_QUEUE_TPL.format(stack=flow_meta.stack)

        # Update compute requirements into container overrides
        container_overrides.update(bundle_cfg.get(COMPUTE, {}))

        if QUEUE in bundle_cfg:
            target_queue = bundle_cfg[QUEUE].format(stack=flow_meta.stack)
            if self.does_queue_exist(target_queue):
                job_queue = target_queue
        # end of queue or compute specific logic

        # support different timeouts per bundle_id
        timeout = {TIMEOUT: bundle_cfg.get(TIMEOUT, DEFAULT_ATTEMPT_DURATION)}

        specs = dict(
            jobName=job_name,
            jobQueue=job_queue,
            jobDefinition=job_definition,
            containerOverrides=container_overrides,
            timeout=timeout,
        )

        if os.environ.get("MAX_WORKERS"):
            specs["parameters"] = {"MAX_WORKERS": os.environ.get("MAX_WORKERS")}

        if RETRY in bundle_cfg:
            specs["retryStrategy"] = {
                "attempts": bundle_cfg.get(RETRY),
            }

        return specs

    def submit_job(
        self, flow_meta: FlowMeta, audit_id: Optional[str] = None, depends=None
    ) -> str:
        """
        Submits a job to AWS Batch
        :param flow_meta: the complete Flow Meta representing the flow
        :param audit_id: the id of the flow audit record
        :return: the id of the job created in AWS Batch
        """

        # compose job specs to pass along to submit job
        job_specs = self.get_job_specs(flow_meta=flow_meta, audit_id=audit_id)

        if depends:
            job_specs["dependsOn"] = [
                {"jobId": depends, "type": "SEQUENTIAL"},
            ]

        logger.info(f"submit batch job with specs:\n{yaml.dump(job_specs)}")
        response = self.batch_client.submit_job(**job_specs)

        logger.info(f"batch response:\n{response.get('jobId')}")
        return response.get("jobId")

    def does_queue_exist(self, name: str) -> bool:
        response = self.batch_client.describe_job_queues(jobQueues=[name])
        return len(response["jobQueues"]) > 0

    @staticmethod
    def get_environment_variables(stack: str) -> dict:
        env = stack.split("-")[0]
        env_var_secret = f"SWARM_{env.upper()}_ENV"
        try:
            value = Settings.secrets_store.get_secret(env_var_secret, decrypt=False)
            return json.loads(value)
        except Settings.secrets_store.client.exceptions.ParameterNotFound:
            return {}
        except Exception:
            logger.exception(f"Failed to fetch {env_var_secret}")
            return {}

    def get_flow_specs_configuration(self) -> dict:
        try:
            ten_min_ago = pendulum.now().subtract(minutes=FLOW_SPECS_INTERVAL)
            if not self._last_specs_fetch or self._last_specs_fetch <= ten_min_ago:
                specs = copy.deepcopy(DEFAULT_FLOW_SPECS)
                fetched_specs = json.loads(
                    Settings.secrets_store.get_secret(FLOW_SPECS)
                )
                specs.update(fetched_specs)
                self._flow_specs = specs
                self._last_specs_fetch = pendulum.now()
                logger.info(f"Using specs: {self._flow_specs}")
            return self._flow_specs
        except Settings.secrets_store.client.exceptions.ParameterNotFound:
            logger.info(
                f"Flow specs parametre {FLOW_SPECS} not found, using hardcoded defaults."
            )
            return DEFAULT_FLOW_SPECS
        except Exception:
            logger.exception("Fetching flow specs failed, using hardcoded defaults.")
            return DEFAULT_FLOW_SPECS


def start_batch_job(s3_bucket: str, s3_key: str, flow_args: dict, previous=None) -> str:
    job_submitter = SeBatchJobSubmitter()
    flow_meta = job_submitter.get_flow_meta(
        registry=Settings.registry,
        s3_key=s3_key,
        s3_bucket=s3_bucket,
        flow_args=flow_args,
    )
    audit_id = job_submitter.audit_flow(flow_meta=flow_meta)
    job_id = job_submitter.submit_job(
        flow_meta=flow_meta, audit_id=audit_id, depends=previous
    )
    return job_id
