from datetime import datetime


def get_utc_time_diff(then: datetime) -> str:
    """
    Get the utc time diff in more friendly/human format

    :param then: start time
    :return: the total time diff in seconds
    """
    now = get_date_time_utcnow()
    diff = now - then

    return pretty_time_delta(diff.total_seconds())


def get_date_time_utcnow() -> datetime:
    """
    Get the current utc time now, so it can be easily mocked on testing

    :return: utc date time now
    """
    return datetime.utcnow()


def pretty_time_delta(seconds) -> str:
    """
    Converts a timedelta into a more human/friendly format
    e.g.: pretty_time_delta(85) will become '01m25s'

    :param seconds: the time in seconds to format
    :return: the convert time in format: '%sd%sh%sm%ss'
    """
    seconds = int(seconds)
    days, seconds = divmod(seconds, 86400)
    hours, seconds = divmod(seconds, 3600)
    minutes, seconds = divmod(seconds, 60)
    if days > 0:
        return "%sd%sh%sm%ss" % (
            add_leading_zero(days),
            add_leading_zero(hours),
            add_leading_zero(minutes),
            add_leading_zero(seconds),
        )
    elif hours > 0:
        return "%sh%sm%ss" % (
            add_leading_zero(hours),
            add_leading_zero(minutes),
            add_leading_zero(seconds),
        )
    elif minutes > 0:
        return "%sm%ss" % (add_leading_zero(minutes), add_leading_zero(seconds))
    else:
        return "%ss" % (seconds,)


def add_leading_zero(value: int) -> str:
    """
    Add a leading zero to a number less than 10

    :param value: the value to add a leading zero
    :return: the number with a leading zero (if applicable)
    """
    return f"0{value}" if int(value) < 10 else str(value)
