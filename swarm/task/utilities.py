from functools import wraps
from traceback import format_exc
from typing import Any
from typing import Callable

from prefect.engine import signals

from swarm.conf import Settings
from swarm.schema.task.auditor.static import TaskMessage
from swarm.task.auditor import Auditor


def pre_run(run_method: Callable) -> Callable:
    """
    Helper decorator to execute some logic before task.run() is executed, namely:
    - instantiate the infrastructure clients_configs

    Returns:
        - Callable: the decorated / altered `Task.run` method after executing the pre logic

    Example:
    ```python
    TO BE DONE
    ```
    """

    @wraps(run_method)
    def method(self: Any, *args: Any, **kwargs: Any) -> Any:
        batch_index = None
        for arg in kwargs.values():
            batch_index = getattr(arg, "batch_index", None)
            batch_index = (
                None if batch_index == {} else batch_index
            )  # handle Addict instances in kwargs
            if batch_index is not None:
                break

        if self._auditor is None:
            self._auditor = Auditor(task_name=self.name, batch_id=batch_index)
        else:
            self._auditor.batch_id = batch_index

        if self.resources:
            self.clients = Settings.connections

        try:
            result = run_method(self, *args, **kwargs)
            return result

        except (signals.SKIP, signals.RETRY, signals.ENDRUN):
            raise

        except Exception as e:
            self.auditor.add(
                message=TaskMessage.EXECUTION_FAILED.format(error=e.__class__.__name__),
                ctx={"error": str(e), "traceback": format_exc()},
            )
            raise e
        finally:
            self.auditor.reset()

    return method
