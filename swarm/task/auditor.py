import textwrap
from typing import List
from typing import Optional

import pandas as pd

from swarm.conf import Settings
from swarm.schema.task.auditor.model import AuditCtx
from swarm.schema.task.auditor.model import AuditLog


class Auditor:
    def __init__(self, task_name: str, batch_id: Optional[int] = None):
        self._task_name = task_name
        self._batch_id = batch_id
        self._data: List[dict] = []

    @property
    def task_name(self) -> str:
        return self._task_name

    @property
    def batch_id(self) -> Optional[int]:
        return self._batch_id

    @batch_id.setter
    def batch_id(self, batch_id: int):
        self._batch_id = batch_id

    def add(self, message: str, ctx: Optional[dict] = None):
        if ctx:
            ctx = AuditCtx.validate(ctx)
            # Keep only 30000 characters as Lucene fields can be at most 32766 bytes long
            if ctx.error:
                ctx.error = textwrap.shorten(text=ctx.error, width=30000)
        message = textwrap.shorten(text=message, width=30000)
        self._data.append(AuditLog(message=message, ctx=ctx).dict())

    def to_dataframe(self) -> pd.DataFrame:
        df = pd.json_normalize(self._data)
        df["task_name"] = self._task_name
        df["batch_id"] = self._batch_id

        return df

    def reset(self):
        self._dump_audit_to_csv()
        self._data = []
        self._batch_id = None

    def _dump_audit_to_csv(self):

        filename = (
            f"auditor_{self._batch_id}_{self._task_name}.csv"
            if self._batch_id
            else f"auditor_{self._task_name}.csv"
        )
        file_path = Settings.context.audits_dir.joinpath(filename)
        df = self.to_dataframe()

        # Only store audit csv if not empty
        if not df.empty:
            df.to_csv(file_path, index=False, encoding="utf-8", sep=",")
