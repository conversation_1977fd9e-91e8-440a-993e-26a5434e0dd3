from dataclasses import dataclass
from enum import Enum
from pathlib import Path
from typing import List
from typing import Optional


class SftpAction(str, Enum):
    PUT = "put"
    GET = "get"


@dataclass
class SftpFile:
    file_name: str
    remote_dir: str
    action: SftpAction
    bytes: Optional[int] = 0
    local_path: Optional[Path] = None


@dataclass
class SftpTargetResult:
    targets: List[SftpFile]
