from dataclasses import dataclass
from enum import Enum
from pathlib import Path
from typing import List
from typing import Optional


class S3Action(str, Enum):
    UPLOAD = "upload"
    DOWNLOAD = "download"


@dataclass
class S3File:
    file_path: Path
    bucket_name: str
    key_name: str
    action: S3Action
    bytes: Optional[int] = 0


@dataclass
class S3TargetResult:
    targets: List[S3File]
