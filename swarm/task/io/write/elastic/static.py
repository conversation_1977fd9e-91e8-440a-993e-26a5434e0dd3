RECORD_INDEX_DELIMITER = "|"


class WriteStatus:
    CREATED = "created"
    UPDATED = "updated"
    ERRORED = "errored"
    VERSION_CONFLICT = "version_conflict"
    DUPLICATE = "duplicate"


class BulkWriterColumns:
    FILE_INDEX = "file_index"
    RAW_INDEX = "raw_index"
    MODEL = "model"
    HASH = "hash"
    ID = "id"
    ES_INDEX = "es_index"
    STATUS = "status"
    ERROR_TYPE = "error_type"
    ERROR_REASON = "error_reason"
    ERROR_CAUSED_BY_TYPE = "error_caused_by_type"
    ERROR_CAUSED_BY_REASON = "error_caused_by_reason"
