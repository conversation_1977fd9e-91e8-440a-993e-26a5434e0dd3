import logging
from typing import Dict
from typing import Optional

import boto3
import pandas as pd

LOG_GROUP = ""  # AWS Log group
LOG_STREAM = ""  # the specific stream within the log group mentioned above
LOG_LOCAL_FILEPATH = ""  # local path to save the logs to
logging.basicConfig(level=logging.INFO)


def get_aws_logs_client():
    return boto3.client("logs")


def write_logs_to_disk(file_path, log_events: dict):
    try:
        log_messages = "\n".join(
            pd.DataFrame(log_events["events"])["message"].to_list()
        )
        with open(file_path, "a") as log_writer:
            log_writer.write(log_messages)
    except KeyError:
        logging.warning("No messages to be written in this iteration")


def start_from_head_and_get_next_token(
    aws_log_client: boto3,
    log_group: str,
    log_stream: str,
    next_token: Optional[str] = None,
) -> Dict:
    if not next_token:
        logging.info(f"Capturing the beginning of stream {log_stream}")
        return aws_log_client.get_log_events(
            logGroupName=log_group,
            logStreamName=log_stream,
            startFromHead=True,
            limit=10000,
        )
    else:
        logging.info(f"Looking for next token {next_token}")
        return aws_log_client.get_log_events(
            logGroupName=log_group,
            logStreamName=log_stream,
            nextToken=next_token,
            startFromHead=True,
            limit=10000,
        )


if __name__ == "__main__":

    client = get_aws_logs_client()
    logs = start_from_head_and_get_next_token(
        aws_log_client=client, log_group=LOG_GROUP, log_stream=LOG_STREAM
    )

    write_logs_to_disk(file_path=LOG_LOCAL_FILEPATH, log_events=logs)
    next_forward_token = logs["nextForwardToken"]
    while next_forward_token:
        logs = start_from_head_and_get_next_token(
            aws_log_client=client,
            log_group=LOG_GROUP,
            log_stream=LOG_STREAM,
            next_token=next_forward_token,
        )
        if logs["nextForwardToken"] == next_forward_token:
            logging.info("Congratulations!! You've reached the end of this logstream")
            break
        next_forward_token = logs["nextForwardToken"]
        write_logs_to_disk(file_path=LOG_LOCAL_FILEPATH, log_events=logs)
