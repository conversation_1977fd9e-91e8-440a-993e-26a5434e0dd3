import os
from pathlib import Path
from typing import Any
from typing import Dict

import yaml

base_path = Path(__file__).parent.parent.parent.joinpath("bundles")
traversal_generator = os.walk(base_path)

result = {}

for nested_paths_tuple in traversal_generator:

    relative_path = nested_paths_tuple[0]
    nested_files = nested_paths_tuple[2]

    for file_name in nested_files:

        if file_name == "bundle.yaml":
            full_path = Path(relative_path).joinpath(file_name).as_posix()
            bundle_content: Dict[str, Any] = yaml.load(
                open(full_path, "r"), Loader=yaml.FullLoader
            )
            result[bundle_content["id"]] = bundle_content["name"]

print(result)
