# Bundle path -> position/universal/steeleye-blotter
# Confluence: https://steeleye.atlassian.net/wiki/spaces/PRODUCT/pages/2171404391/Position+-+Model
# GitHub docs: swarm-flows/docs/bundles/position/universal/position-universal-steeleye-blotter.md

id: position-universal-steeleye-blotter
name: Positions Universal Steeleye Blotter
infra:
  - name: tenant-data
    type: ELASTICSEARCH
  - name: reference-data
    type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
  - name: file_url
    envVar: SWARM_FILE_URL
    description: url of the file to process
controlFlows:
  - name: S3OrLocalFile
    conditionTaskName: ParametersFlowController
    trueTaskName: S3DownloadFile
    falseTaskName: LocalFile
    merge: true
  - name: FileTooBig # The input file if greater than chunksize * max_chunk_size, will be split into different files and re-uploaded to S3
    conditionTaskName: HorizontalBatchController
    trueTaskName: BatchProducer
    falseTaskName: MergeAndChunkCsvFiles
    mapped: false
    merge: false
tasks:
  - path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
    name: ParametersFlowController
    params:
      arg_name: file_url
      starts_with: s3://
      condition: AND
      ignore_case: true
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
    name: S3DownloadFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.local_file:LocalFile
    name: LocalFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
    name: CsvFileSplitter
    params:
      chunksize: 10000
      delimiter: ','
      normalise_columns: true
    upstreamTasks:
      - taskName: S3OrLocalFile
        mapped: false
        key: extractor_result
  - path: swarm_tasks.io.read.horizontal_batch_controller:HorizontalBatchController
    name: HorizontalBatchController
    params:
      max_chunk_size: 7
    upstreamTasks:
      - taskName: CsvFileSplitter
        mapped: false
        key: list_of_batches
  - path: swarm_tasks.io.read.merge_and_chunk_csv_files:MergeAndChunkCsvFiles
    name: MergeAndChunkCsvFiles
    params:
      max_chunk_size: 7
    upstreamTasks:
      - taskName: CsvFileSplitter
        mapped: false
        key: file_splitter_result_list
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.transform.extract_path.s3_file_list_from_frame_splitter_result_list:S3FileListFileSplitterResultList
    name: S3FileListFileSplitterResultList
    params:
      cloud_key_prefix: "flows/position-universal-steeleye-blotter/batches"
      datetime_field_in_file_path: false
    upstreamTasks:
      - taskName: MergeAndChunkCsvFiles
        mapped: false
        key: file_splitter_result_list
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.write.aws.s3_upload_file:S3UploadFile
    name: S3UploadOrderFile
    upstreamTasks:
      - taskName: S3FileListFileSplitterResultList
        mapped: false
        key: upload_target
  - path: swarm_tasks.io.read.batch_producer:BatchProducer
    name: BatchProducer
    params:
      source_schema:
        "ACCOUNTID": string
        "ADDITIONALINFORMATION": string
        "AMOUNT": float
        "CURRENCY": string
        "CLIENTID": string
        "DATE": string
        "DIRECTION": string
        "FUNDID": string
        "ISIN": string
        "LEVEL": string
        "PORTFOLIOMANAGERID": string
        "PNLAMOUNT": float
        "QUANTITY": float
        "QUANTITYNOTATION": string
        "RIC": string
        "RISKENTITYID": string
        "TRADERID": string
    upstreamTasks:
      - taskName: CsvFileSplitter
        mapped: true
        key: file_splitter_result
  # Skip logic - skip records where ISIN is missing
  - path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
    name: FilterRowsInSourceFile
    params:
      query: "`ISIN`.notnull() | `ISIN`.str.len == 12"
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
  # Primary transformations
  - path: swarm_tasks.primary_transformations.get_primary_transformations:GetPrimaryTransformations
    name: PrimaryTransformation
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: result
  # LinkParties
  - path: swarm_tasks.transform.steeleye.link.parties:LinkParties
    name: LinkParties
    params:
      identifiers_path: marketIdentifiers.parties
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: PrimaryTransformation
        mapped: true
        key: result
  # LinkInstrument
  - path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
    name: LinkInstrument
    params:
      identifiers_path: marketIdentifiers.instrument
    resources:
      ref_data_key: reference-data
      tenant_data_key: tenant-data
    upstreamTasks:
      - taskName: PrimaryTransformation
        mapped: true
        key: result
  - path: swarm_tasks.position.identifiers.position_parties_fallback:PositionPartiesFallback
    name: PositionPartiesFallback
    params:
      source_attribute: __party__
    upstreamTasks:
      - taskName: PrimaryTransformation
        mapped: true
        key: result
      - taskName: LinkParties
        mapped: true
        key: link_parties
  - path: swarm_tasks.transform.map.map_conditional:MapConditional
    name: InstrumentFallbackConditional
    paramsList:
      - target_attribute: __instrument_id_code_type__
        cases:
          - query: "`__isin__`.str.len() == 12"
            value: ID
    upstreamTasks:
      - taskName: PrimaryTransformation
        mapped: true
        key: result
  # Instrument Fallback
  - path: swarm_tasks.generic.instrument_fallback.instrument_fallback:InstrumentFallback
    name: InstrumentFallback
    params:
      instrument_fields_map:
        - source_field: __isin__
          target_field: instrumentFullName
        - source_field: __instrument_id_code_type__
          target_field: ext.instrumentIdCodeType
      str_to_bool_dict:
        "true": True
        "false": False
    upstreamTasks:
      - taskName: PrimaryTransformation
        mapped: true
        key: result
      - taskName: LinkInstrument
        mapped: true
        key: link_instrument
      - taskName: InstrumentFallbackConditional
        mapped: true
        key: position_parties
  # MapToNested
  - path: swarm_tasks.transform.map.map_to_nested:MapToNested
    name: InstrumentOverridesRIC
    params:
      source_attribute: __ric__
      nested_path: ext.pricingReferences.RIC
      target_attribute: instrumentDetails.instrument
    upstreamTasks:
      - taskName: InstrumentFallback
        mapped: true
        key: link_instrument
      - taskName: PrimaryTransformation
        mapped: true
        key: result
  # Frame concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: PrimaryFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __isin__
        - __ric__
        - __party__
        - __instrument_id_code_type__
        - marketIdentifiers.instrument
        - marketIdentifiers.parties
        - asset_class_attribute # Instrument Identifiers columns
        - bbg_figi_id_attribute
        - currency_attribute
        - eurex_id_attribute
        - exchange_symbol_attribute
        - expiry_date_attribute
        - interest_rate_start_date_attribute
        - isin_attribute
        - notional_currency_1_attribute
        - notional_currency_2_attribute
        - option_strike_price_attribute
        - option_type_attribute
        - swap_near_leg_date_attribute
        - underlying_index_name_attribute
        - underlying_index_name_leg_2_attribute
        - underlying_index_series_attribute
        - underlying_index_term_attribute
        - underlying_index_term_value_attribute
        - underlying_index_version_attribute
        - underlying_isin_attribute
        - underlying_symbol_attribute
        - underlying_symbol_expiry_code_attribute
        - underlying_index_term_leg_2_attribute
        - underlying_index_term_value_leg_2_attribute
        - venue_attribute
        - venue_financial_instrument_short_name_attribute
        - instrument_classification_attribute
    upstreamTasks:
      - taskName: PrimaryTransformation
        mapped: true
        key: primary_frame_concatenator
      - taskName: PositionPartiesFallback
        mapped: true
        key: parties
      - taskName: InstrumentOverridesRIC
        mapped: true
        key: instruments
  # Assign Meta
  - path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
    name: AssignMeta
    params:
      model_attribute: __meta_model__
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
  # Final Frame Concatenate with PositionRecords and AssignMeta
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: FinalFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __meta_model__
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
      - taskName: AssignMeta
        mapped: true
        key: assign_meta
  # Elastic Bulk Transformer
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: ElasticBulkTransformer
    params:
      action_type: index
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: FinalFrameConcatenator
        mapped: true
        key: transform_result
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: producer_result
  # Write Call records with Attachment data included
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: ElasticBulkWriter
    params:
      action_type: index
      payload_size: 10000000
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: ElasticBulkTransformer
        mapped: true
        key: result
