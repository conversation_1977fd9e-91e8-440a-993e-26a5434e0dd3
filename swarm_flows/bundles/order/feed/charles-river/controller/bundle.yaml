# This flow checks for the corresponding 4 files required for ingesting record and then creates a file for
# 'order-feed-charles-river-processor' flow if the pair file is present and raise SKIP if not found.
# Confluence page: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2450587649/Order+Charles+River#Controller-Task
# Bundle path -> order/feed/charles-river/controller
id: order-feed-charles-river-controller
name: Order Feed Charles River

infra:
  - name: tenant-data
    type: ELASTICSEARCH

audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data

parameters:
  - name: file_url
    envVar: SWARM_FILE_URL
    description: url of the file to process

# This Flow can trigger up to 2 Processor flows downstream:
# - order-feed-charles-river-allocation-processor
# - order-feed-charles-river-order-processor
# The flow will process the allocations by default unless the client has a flow override enabled
controlFlows:
  - name: ProcessOrSkipAllocations
    conditionTaskName: AllocationsProcessingController
    merge: false
    mapped: false
    cases:
      ProcessAllocations: AllocationFileController # The flow processes allocations by default
      SkipAllocations: SkipAllocationsTask # FlowOverride target to skip allocations for some clients

tasks:

# This task's purpose is to propagate the "params.value" field. Whatever it receives is whatever it outputs.
# By default, it will return "ProcessAllocations". Then, the "ProcessOrSkipAllocations"
# controller will check this value and trigger the "AllocationFileController" task by default.
# If a tenant has a flow override for this task "AllocationsProcessingController", returning the
# value "SkipAllocations", then the "AllocationsProcessingController" controller will instead
# trigger a Noop Task (which does nothing), essentially skipping allocations processing.
  - path: swarm_tasks.generic.value_proxy:ValueProxy
    name: AllocationsProcessingController
    params:
      value: ProcessAllocations
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url

  - path: swarm_tasks.control_flow.noop:Noop
    name: SkipAllocationsTask

  - path: swarm_tasks.control_flow.multiple_files_input_controller:MultipleFilesInputController
    name: AllocationFileController
    params:
      list_of_files_regex:
        - "(CRIMS_allocation)"
        - "(CRIMS_order)"
        - "(CRIMS_place)"
      unique_identifier_regex:
        - "\\d{4}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])" # i.e "20220226 - YYYYMMDD"
      list_of_files_regex_for_which_empty_frame_returned:
        - "(CRIMS_fills)"
        - "(CRIMS_orderhistory)"
      file_links_in_output: true
    upstreamTasks:
      - taskName: file_url
        key: file_url

  # Pre Process data by reading all the files present in S3 for the flow run
  - path: swarm_tasks.io.read.aws.s3_download_multiple_files:S3DownloadMultipleFiles
    name: PreProcessCharlesRiverAllocationData
    params:
      file_columns:
        - s3_crims_order_file_url
        - s3_crims_place_file_url
        - s3_crims_allocation_file_url
      suffix_identifier_regex: s3_crims_(.*)_file_url
      detect_file_separator: True
    upstreamTasks:
      - taskName: AllocationFileController
        mapped: false
        key: producer_result

  - path: swarm_tasks.order.feed.charles_river.processor.record_transformer:CharlesRiverFrameTransformer
    name: CharlesRiverAllocationFrameTransformer
    params:
      trigger_file_type: "allocation"
    upstreamTasks:
      - taskName: PreProcessCharlesRiverAllocationData
        mapped: false
        key: pre_process_result
      - taskName: file_url
        mapped: false
        key: file_url

  - path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
    name: CsvAllocationFileSplitter
    params:
      chunksize: 20000
      detect_encoding: true
      target_dir_name: allocation
    upstreamTasks:
      - taskName: CharlesRiverAllocationFrameTransformer
        mapped: false
        key: extractor_result

  - path: swarm_tasks.transform.extract_path.s3_file_list_from_frame_splitter_result_list:S3FileListFileSplitterResultList
    name: AllocationS3FileListFileSplitterResultList
    params:
      datetime_field_in_file_path: 2
      cloud_key_prefix: "flows/order-feed-charles-river-allocation-processor"
    upstreamTasks:
      - taskName: CsvAllocationFileSplitter
        mapped: false
        key: file_splitter_result_list

  - path: swarm_tasks.io.write.aws.s3_upload_file:S3UploadFile
    name: S3UploadAllocationFile
    upstreamTasks:
      - taskName: AllocationS3FileListFileSplitterResultList
        mapped: true
        key: upload_target

  - path: swarm_tasks.control_flow.multiple_files_input_controller:MultipleFilesInputController
    name: OrderFileController
    params:
      list_of_files_regex:
        - "(CRIMS_allocation)"
        - "(CRIMS_fills)"
        - "(CRIMS_order)"
        - "(CRIMS_place)"
      unique_identifier_regex:
        - "\\d{4}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])" # i.e "20220226 - YYYYMMDD"
      list_of_files_regex_for_which_empty_frame_returned:
        - "(CRIMS_orderhistory)"
      file_links_in_output: true
    upstreamTasks:
      - taskName: file_url
        key: file_url

  # Pre Process data by reading all the files present in S3 for the flow run
  - path: swarm_tasks.io.read.aws.s3_download_multiple_files:S3DownloadMultipleFiles
    name: PreProcessCharlesRiverOrderData
    params:
      file_columns:
        - s3_crims_order_file_url
        - s3_crims_place_file_url
        - s3_crims_fills_file_url
        - s3_crims_allocation_file_url
      suffix_identifier_regex: s3_crims_(.*)_file_url
      detect_file_separator: True
    upstreamTasks:
      - taskName: OrderFileController
        mapped: false
        key: producer_result

  - path: swarm_tasks.order.feed.charles_river.processor.record_transformer:CharlesRiverFrameTransformer
    name: CharlesRiverFrameOrderTransformer
    params:
      trigger_file_type: "order"
    upstreamTasks:
      - taskName: PreProcessCharlesRiverOrderData
        mapped: false
        key: pre_process_result
      - taskName: file_url
        mapped: false
        key: file_url

  - path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
    name: CsvOrderFileSplitter
    params:
      chunksize: 20000
      detect_encoding: true
      target_dir_name: order
    upstreamTasks:
      - taskName: CharlesRiverFrameOrderTransformer
        mapped: false
        key: extractor_result

  - path: swarm_tasks.transform.extract_path.s3_file_list_from_frame_splitter_result_list:S3FileListFileSplitterResultList
    name: OrderS3FileListFileSplitterResultList
    params:
      datetime_field_in_file_path: 2
      cloud_key_prefix: "flows/order-feed-charles-river-order-processor"
    upstreamTasks:
      - taskName: CsvOrderFileSplitter
        mapped: false
        key: file_splitter_result_list

  - path: swarm_tasks.io.write.aws.s3_upload_file:S3UploadFile
    name: S3UploadOrderFile
    upstreamTasks:
      - taskName: OrderS3FileListFileSplitterResultList
        mapped: true
        key: upload_target