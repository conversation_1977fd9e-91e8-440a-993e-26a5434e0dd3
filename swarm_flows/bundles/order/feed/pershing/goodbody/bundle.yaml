# https://steeleye.atlassian.net/wiki/spaces/IN/pages/2161639429/Order+Pershing+-+Goodbody
# This flow always populates Order records as NEWO and OrderState records as FILL
id: order-feed-pershing-goodbody
name: Order Feed Pershing
infra:
  - name: tenant-data
    type: ELASTICSEARCH
  - name: reference-data
    type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
  - name: file_url
    envVar: SWARM_FILE_URL
    description: url of the file to process
controlFlows:
  - name: S3OrLocalFile
    conditionTaskName: ParametersFlowController
    trueTaskName: S3DownloadFile
    falseTaskName: LocalFile
    merge: true
  - name: NeedsQuarantine
    conditionTaskName: QuarantineCondition
    trueTaskName: QuarantinedElasticBulkTransformer
    falseTaskName: Noop
    mapped: true
    merge: false
tasks:
  - path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
    name: ParametersFlowController
    params:
      arg_name: file_url
      starts_with: s3://
      condition: AND
      ignore_case: true
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
    name: S3DownloadFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.local_file:LocalFile
    name: LocalFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
    name: CsvFileSplitter
    params:
      chunksize: 5000
    upstreamTasks:
      - taskName: S3OrLocalFile
        mapped: false
        key: extractor_result

  # Batch Producer
  - path: swarm_tasks.io.read.batch_producer:BatchProducer
    name: BatchProducer
    params:
      source_schema:
        "AGT_PRIN": string
        "BUY_SELL": string
        "CLIENT_CLNO": string
        "CLT_CONS": string
        "CLT_SETT_CCY": string
        "COMM_REF": string
        "COMORDERREF": string
        "COUNTERPARTY1": string
        "COUNTERPARTY2": string
        "DEAL_TIME": string
        "DEALT_BY": string
        "IMPORT_DT": string
        "INPUT_DT": string
        "ISIN": string
        "MIFID_CAT": string
        "PRICE": float
        "PRICE_CCY": string
        "RESP_CODE": string
        "RECORD_STATE": string
        "SEDOL": string
        "STK_NM_1": string
        "STK_QTY": string
        "STOCK_TYPE": string
        "SETT_DT": string
        "TRAN_DT": string
        "TRAN_REF": string
        "USER_CD": string
        "VENUE": string
    upstreamTasks:
      - taskName: CsvFileSplitter
        mapped: true
        key: file_splitter_result

  # Test file contains an invalid record at the end
  - path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
    name: FilterRowsInSourceFile
    params:
      query: "COMM_REF.notnull()"
      skip_on_empty: true
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result

  # Map Static
  - path: swarm_tasks.transform.map.map_static:MapStatic
    name: MapStatic
    paramsList:
      - target_attribute: executionDetails.orderType
        target_value: Market
      - target_attribute: sourceIndex
        from_index: true
        cast_to_str: true
      - target_attribute: sourceKey
        from_env_var: SWARM_FILE_URL
      - target_attribute: _orderState.__meta_model__
        target_value: OrderState
      - target_attribute: _order.__meta_model__
        target_value: Order
      - target_attribute: dataSourceName
        target_value: Pershing
      - target_attribute: transactionDetails.priceNotation
        target_value: MONE
      - target_attribute: transactionDetails.quantityNotation
        target_value: UNIT
      - target_attribute: tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator
        target_value: false
      - target_attribute: _order.executionDetails.orderStatus
        target_value: NEWO
      - target_attribute: _orderState.executionDetails.orderStatus
        target_value: FILL
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: result

  # Additional Info
  - path: swarm_tasks.transform.map.map_attribute:MapAttribute
    name: OutgoingOrderInfoFields
    paramsList:
      - source_attribute: SEDOL
        target_attribute: __sedol_with_prefix__
        prefix: "sedol: "
      - source_attribute: STOCK_TYPE
        target_attribute: __stock_type_with_prefix__
        prefix: "stock type: "
      - source_attribute: USER_CD
        target_attribute: __user_with_prefix__
        prefix: "last user: "
      - source_attribute: CLT_CONS
        target_attribute: __clt_cons_with_prefix__
        prefix: "client consideration: "
      - source_attribute: STK_NM_1
        target_attribute: __stk_nm_with_prefix__
        prefix: "stock name: "
      - source_attribute: MIFID_CAT
        target_attribute: __mifid_cat_with_prefix__
        prefix: "client type: "
      - source_attribute: COUNTERPARTY2
        target_attribute: __counterparty2_with_prefix__
        prefix: "counterparty 2: "
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: result

  - path: swarm_tasks.transform.concat.concat_attributes:ConcatAttributes
    name: ConcatAddlInfoAttributes
    paramsList:
      - source_attributes:
          - __sedol_with_prefix__
          - __stock_type_with_prefix__
          - __user_with_prefix__
          - __clt_cons_with_prefix__
          - __stk_nm_with_prefix__
          - __mifid_cat_with_prefix__
          - __counterparty2_with_prefix__
        target_attribute: executionDetails.outgoingOrderAddlInfo
        delimiter: ", "
      - source_attributes:
          - __comorderref__
          - __tran_ref__
        target_attribute: orderIdentifiers.orderIdCode
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: result
      - taskName: OutgoingOrderInfoFields
        mapped: true
        key: outgoing_info_fields
      - taskName: MapValue
        mapped: true
        key: map_value_result

  # Get Tenant LEI
  - path: swarm_tasks.steeleye.generic.get_tenant_lei:GetTenantLEI
    name: GetTenantLEI
    params:
        target_lei_column: __executing_entity__
        target_column_prefix: "lei:"
    upstreamTasks:
      - taskName: MapStatic
        mapped: true
        key: result

  # Map Values
  - path: swarm_tasks.transform.map.map_value:MapValue
    name: MapValue
    paramsList:
      - source_attribute: BUY_SELL
        target_attribute: __buysell__
        case_insensitive: true
        value_map:
          b: "1"
          s: "2"
      - source_attribute: BUY_SELL
        target_attribute: __buysell_indicator__
        case_insensitive: true
        value_map:
          b: BUYI
          s: SELL
      - source_attribute: AGT_PRIN
        target_attribute: executionDetails.tradingCapacity
        value_map:
          A: AOTC
          P: DEAL
      - source_attribute: COMORDERREF
        target_attribute: __comorderref__
        case_insensitive: true
        regex_replace_map:
          - regex: "\/"
            replace_value: "|"
        regex_replace_only: true
      - source_attribute: TRAN_REF
        target_attribute: __tran_ref__
        case_insensitive: true
        regex_replace_map:
          - regex: "\/"
            replace_value: "|"
        regex_replace_only: true
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: result

  # Convert prices and currencies to major
  - path: swarm_tasks.generic.currency.convert_minor_to_major:ConvertMinorToMajor
    name: ConvertMinorToMajor
    paramsList:
      - source_ccy_attribute: PRICE_CCY
        target_ccy_attribute: transactionDetails.priceCurrency
      - source_price_attribute: PRICE
        source_ccy_attribute: PRICE_CCY
        target_price_attribute: priceFormingData.price
        cast_to: abs
      - source_price_attribute: PRICE
        source_ccy_attribute: PRICE_CCY
        target_price_attribute: transactionDetails.price
        cast_to: abs
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: result

  # Convert dates and times
  - path: swarm_tasks.transform.datetime.convert_datetime:ConvertDatetime
    name: ChangeTimeFormat
    paramsList:
      - source_attribute: DEAL_TIME
        target_attribute: __deal_time_temp__
        source_attribute_format: "%H%M%S"
        convert_to: time
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: filter_result
      - taskName: MapStatic
        mapped: true
        key: result

  - path: swarm_tasks.transform.datetime.join_date_and_time:JoinDateAndTimeFormat
    name: JoinDateAndTimeFormat
    paramsList:
      - source_date_attribute: TRAN_DT
        source_time_attribute: __deal_time_temp__
        target_attribute: transactionDetails.tradingDateTime
        source_format: "%d/%m/%Y%H:%M:%S.%fZ"
        timezone_info: &ireland_timezone "Europe/Dublin"
      - source_date_attribute: TRAN_DT
        source_time_attribute: __deal_time_temp__
        target_attribute: timestamps.tradingDateTime
        source_format: "%d/%m/%Y%H:%M:%S.%fZ"
        timezone_info: *ireland_timezone
      - source_date_attribute: INPUT_DT
        source_time_attribute: __deal_time_temp__
        target_attribute: timestamps.orderReceived
        source_format: "%d/%m/%Y%H:%M:%S.%fZ"
        timezone_info: *ireland_timezone
      - source_date_attribute: INPUT_DT
        source_time_attribute: __deal_time_temp__
        target_attribute: timestamps.orderSubmitted
        source_format: "%d/%m/%Y%H:%M:%S.%fZ"
        timezone_info: *ireland_timezone
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: filter_rows_result
      - taskName: ChangeTimeFormat
        mapped: true
        key: change_time_format
      - taskName: MapStatic
        mapped: true
        key: result

  - path: swarm_tasks.transform.datetime.convert_datetime:ConvertDatetime
    name: ConvertDateTime
    paramsList:
      - source_attribute: timestamps.orderReceived
        target_attribute: date
        source_attribute_format: "%Y-%m-%dT%H:%M:%S.%fZ"
        convert_to: date
      - source_attribute: SETT_DT
        target_attribute: transactionDetails.settlementDate
        source_attribute_format: "%d/%m/%Y"
        convert_to: date
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: filter_rows
      - taskName: JoinDateAndTimeFormat
        mapped: true
        key: result

  # Map Attributes
  - path: swarm_tasks.transform.map.map_attribute:MapAttribute
    name: MapAttributes
    paramsList:
      # Transaction Details
      - source_attribute: STK_QTY
        target_attribute: transactionDetails.quantity
      - source_attribute: VENUE
        target_attribute: transactionDetails.venue
      - source_attribute: PRICE
        target_attribute: transactionDetails.priceAverage
      - source_attribute: __buysell_indicator__
        target_attribute: transactionDetails.buySellIndicator
      - source_attribute: executionDetails.tradingCapacity
        target_attribute: transactionDetails.tradingCapacity
      # Price Forming data
      - source_attribute: STK_QTY
        target_attribute: priceFormingData.initialQuantity
      - source_attribute: STK_QTY
        target_attribute: _orderState.priceFormingData.tradedQuantity
      # Buy/Sell Indicator
      - source_attribute: __buysell__
        target_attribute: _orderState.buySell
      - source_attribute: __buysell__
        target_attribute: _order.buySell
      - source_attribute: __buysell_indicator__
        target_attribute: executionDetails.buySellIndicator
      # Order ID fields
      - source_attribute: orderIdentifiers.orderIdCode
        target_attribute: _order.id
      - source_attribute: orderIdentifiers.orderIdCode
        target_attribute: _orderState.id
      - source_attribute: COMM_REF
        target_attribute: orderIdentifiers.aggregatedOrderId
      - source_attribute: RESP_CODE
        target_attribute: orderIdentifiers.internalOrderIdCode
      # Transaction Ref. No fields
      - source_attribute: __tran_ref__
        target_attribute: reportDetails.transactionRefNo
      - source_attribute: __tran_ref__
        target_attribute: _orderState.orderIdentifiers.transactionRefNo
      # Dates and Times
      - source_attribute: transactionDetails.tradingDateTime
        target_attribute: _order.timestamps.orderStatusUpdated
      - source_attribute: transactionDetails.tradingDateTime
        target_attribute: _orderState.timestamps.orderStatusUpdated
      - source_attribute: DEALT_BY
        target_attribute: __trader_with_id__
        prefix: "id:"
      - source_attribute: CLIENT_CLNO
        target_attribute: __client_with_id__
        prefix: "id:"
      # Party identifiers fields
      - source_attribute: COUNTERPARTY1
        target_attribute: __counter_party_with_id__
        prefix: "id:"
    upstreamTasks:
      - taskName: JoinDateAndTimeFormat
        mapped: true
        key: join_datetime
      - taskName: MapValue
        mapped: true
        key: map_value
      - taskName: GetTenantLEI
        mapped: true
        key: tenant_lei
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: result
      - taskName: ConcatAddlInfoAttributes
        mapped: true
        key: concat_ids


  # Instrument and Party identifiers
  - path: swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers:InstrumentIdentifiers
    name: InstrumentIdentifiers
    params:
      currency_attribute: transactionDetails.priceCurrency
      isin_attribute: ISIN
    upstreamTasks:
      - taskName: FilterRowsInSourceFile
        mapped: true
        key: filter_rows
      - taskName: ConvertMinorToMajor
        mapped: true
        key: result

  - path: swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers:GenericOrderPartyIdentifiers
    name: PartyIdentifiers
    params:
      target_attribute: marketIdentifiers.parties
      executing_entity_identifier: __executing_entity__
      counterparty_identifier: __counter_party_with_id__
      buyer_identifier: __executing_entity__
      client_identifier: __client_with_id__
      seller_identifier: __counter_party_with_id__
      trader_identifier: __trader_with_id__
      buy_sell_side_attribute: __buysell__
      use_buy_mask_for_buyer_seller: true
    upstreamTasks:
      - taskName: GetTenantLEI
        mapped: true
        key: tenant_lei
      - taskName: MapValue
        mapped: true
        key: map_value
      - taskName: MapAttributes
        mapped: true
        key: result

  - path: swarm_tasks.transform.steeleye.link.merge_market_identifiers:MergeMarketIdentifiers
    name: MergeMarketIdentifiers
    params:
      identifiers_path: marketIdentifiers
      instrument_path: marketIdentifiers.instrument
      parties_path: marketIdentifiers.parties
    upstreamTasks:
      - taskName: InstrumentIdentifiers
        mapped: true
        key: instrument_identifiers
      - taskName: PartyIdentifiers
        mapped: true
        key: result

  # Primary frame Concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: PrimaryFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __executing_entity__
        - __trader_with_id__
        - __buysell__
        - __buysell_indicator__
        - __counter_party_with_id__
        - __deal_time_temp__
        - __input_time_temp__
        - __import_time_temp__
        - __sett_time_temp__
        - __client_with_id__
        - __sedol_with_prefix__
        - __stock_type_with_prefix__
        - __user_with_prefix__
        - __clt_cons_with_prefix__
        - __stk_nm_with_prefix__
        - __mifid_cat_with_prefix__
        - __counterparty2_with_prefix__
        - __comorderref__
        - __tran_ref__
    upstreamTasks:
      - taskName: MapStatic
        mapped: true
        key: map_static
      - taskName: ConcatAddlInfoAttributes
        mapped: true
        key: concat_attributes_addl_info
      - taskName: GetTenantLEI
        mapped: true
        key: tenant_lei
      - taskName: MapAttributes
        mapped: true
        key: map_attributes
      - taskName: MapValue
        mapped: true
        key: map_value
      - taskName: ConvertMinorToMajor
        mapped: true
        key: ConvertMinorToMajor
      - taskName: ChangeTimeFormat
        mapped: true
        key: change_time_format
      - taskName: JoinDateAndTimeFormat
        mapped: true
        key: join_datetime
      - taskName: ConvertDateTime
        mapped: true
        key: convert_datetime
      - taskName: MergeMarketIdentifiers
        mapped: true
        key: market_identifiers
      - taskName: InstrumentIdentifiers
        mapped: true
        key: instrument_identifiers
      - taskName: PartyIdentifiers
        mapped: true
        key: party_identifiers

  # Parties and Instruments linking
  - path: swarm_tasks.transform.steeleye.link.parties:LinkParties
    name: LinkParties
    params:
      identifiers_path: marketIdentifiers.parties
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result

  - path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
    name: LinkInstrument
    params:
      identifiers_path: marketIdentifiers.instrument
      currency_attribute: transactionDetails.priceCurrency
    resources:
      ref_data_key: reference-data
      tenant_data_key: tenant-data
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result

  - path: swarm_tasks.transform.steeleye.meta.assign_meta_parent:AssignMetaParent
    name: ParentId
    params:
      parent_model_attribute: _order.__meta_model__
      parent_attributes_prefix: _order.
      target_attribute: _orderState.__meta_parent__
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result

  # Auxiliary frame concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: AuxiliaryFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
        - marketIdentifiers.instrument
        - marketIdentifiers.parties
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: primary_frame_concatenator
      - taskName: LinkParties
        mapped: true
        key: link_parties
      - taskName: LinkInstrument
        mapped: true
        key: link_instruments
      - taskName: ParentId
        mapped: true
        key: result

  # Splitting and prefix removal
  - path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
    name: OrderRecords
    params:
      except_prefix: _orderState.
      strip_prefix: true
    upstreamTasks:
      - taskName: AuxiliaryFrameConcatenator
        mapped: true
        key: result

  - path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
    name: OrderStateRecords
    params:
      except_prefix: _order.
      strip_prefix: true
    upstreamTasks:
      - taskName: AuxiliaryFrameConcatenator
        mapped: true
        key: result

  - path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
    name: StripPrefixOrder
    params:
      action: strip
      prefix: _order.
    upstreamTasks:
      - taskName: OrderRecords
        mapped: true
        key: result

  - path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
    name: StripPrefixOrderState
    params:
      action: strip
      prefix: _orderState.
    upstreamTasks:
      - taskName: OrderStateRecords
        mapped: true
        key: result

  # Vertical Concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: VerticalConcatenator
    params:
      orient: vertical
      reset_index: true
      drop_index: true
    upstreamTasks:
      - taskName: StripPrefixOrder
        mapped: true
        key: order_records
      - taskName: StripPrefixOrderState
        mapped: true
        key: result

  # Best-execution tasks
  - path: swarm_tasks.transform.steeleye.orders.best_execution.best_execution:BestExecution
    name: BestExecution
    resources:
      es_client_key: reference-data
    upstreamTasks:
      - taskName: VerticalConcatenator
        mapped: true
        key: result

  # Best-Ex results concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: BestExecutionConcatenator
    params:
      orient: horizontal
    upstreamTasks:
      - taskName: BestExecution
        mapped: true
        key: best_ex_result
      - taskName: VerticalConcatenator
        mapped: true
        key: result

  # Assign Meta
  - path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
    name: AssignMeta
    params:
      model_attribute: __meta_model__
      parent_attribute: __meta_parent__
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: BestExecutionConcatenator
        mapped: true
        key: result

  # Post-meta concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: PostMetaConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __meta_model__
        - __meta_parent__
    upstreamTasks:
      - taskName: BestExecutionConcatenator
        mapped: true
        key: result
      - taskName: AssignMeta
        mapped: true
        key: meta_results

  # Elastic Bulk Transformer
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: ElasticBulkTransformer
    params:
      action_type: create
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: transform_result
    - taskName: FilterRowsInSourceFile
      mapped: true
      key: producer_result

  # Elastic Bulk Writer
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: PutIfAbsent
    params:
      payload_size: 10000000
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: ElasticBulkTransformer
      mapped: true
      key: result

  # Instrument Mapper
  - path: swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper:InstrumentMapper
    name: InstrumentMapper
    upstreamTasks:
      - taskName: PostMetaConcatenator
        mapped: true
        key: source_frame
      - taskName: PutIfAbsent
        mapped: true
        key: bulk_writer_result

  # Quarantine Condition
  - path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
    name: QuarantineCondition
    upstreamTasks:
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result

  # Quarantined Elastic Bulk Transformer
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: QuarantinedElasticBulkTransformer
    params:
      action_type: create
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: PostMetaConcatenator
      mapped: true
      key: transform_result
    - taskName: FilterRowsInSourceFile
      mapped: true
      key: producer_result
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result

  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: QuarantinedPutIfAbsent
    params:
      payload_size: 10000000
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
    - taskName: QuarantinedElasticBulkTransformer
      mapped: true
      key: result

  - path: swarm_tasks.control_flow.noop:Noop
    name: Noop