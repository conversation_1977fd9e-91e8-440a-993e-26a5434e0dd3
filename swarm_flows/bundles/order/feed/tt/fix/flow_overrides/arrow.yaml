taskOverrides:
  all_environments:
    # ICE trades are already captured in ICE POF FIX for Arrow
    - name: IceExchangeSkipLogic
      params:
        query: "~((`LegSecurityExchange`.astype('str').str.contains('ICE',case=True,na=False)) or (`SecurityExchange`.astype('str').str.contains('ICE',case=True,na=False)))"
        skip_on_empty: true

    - name: ClientMapConditional
      params:
        target_attribute: CLIENTID
        cases:
          - query: "index == index"
            attribute: Account