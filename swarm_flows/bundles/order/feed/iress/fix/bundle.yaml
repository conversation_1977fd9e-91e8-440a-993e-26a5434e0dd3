# Bundle path: order/feed/iress/fix
# Confluence page: https://steeleye.atlassian.net/wiki/spaces/IN/pages/1737032162/Order+IRESS
# Input: CSV file which contains S3 links to a batch of IRESS fix files.
# All the fix files are downloaded locally, and processed together in one flow run.

# Methodology - Parse FIX messages, build Pandas DataFrame from them with relevant data.
# Models populated: Order, OrderState, QuarantinedOrder

id: order-feed-iress-fix
name: Order Feed IRESS FIX

infra:
- name: tenant-data
  type: ELASTICSEARCH
- name: reference-data
  type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
- name: file_url
  envVar: SWARM_FILE_URL
  description: url of the file to process
controlFlows:
- name: S3OrLocalFile
  conditionTaskName: ParametersFlowController
  trueTaskName: S3DownloadFile
  falseTaskName: LocalFile
  merge: true
- name: NeedsQuarantine
  conditionTaskName: QuarantineCondition
  trueTaskName: QuarantinedElasticBulkTransformer
  falseTaskName: Noop
  merge: false
tasks:
  # ParametersFlowController
- path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
  name: ParametersFlowController
  params:
    arg_name: file_url
    starts_with: s3://
    condition: AND
    ignore_case: true
  upstreamTasks:
  - taskName: file_url
    key: file_url
  # S3DownloadFile
- path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
  name: S3DownloadFile
  upstreamTasks:
  - taskName: file_url
    key: file_url
  # LocalFile
- path: swarm_tasks.io.read.local_file:LocalFile
  name: LocalFile
  upstreamTasks:
  - taskName: file_url
    key: file_url
  # Download fix files and put their content in a data frame column
- path: swarm_tasks.io.read.fix.fix_batch_csv_downloader:FixBatchCsvDownloader
  name: FixBatchCsvDownloader
  upstreamTasks:
    - taskName: S3OrLocalFile
      key: extractor_result
  # Read .fix file, parse, validate and convert to FixParserResult dataclass
  # Note: the key has to be fix_dataframe as that's the argument name in execute()
- path: swarm_tasks.io.read.fix.fix_parser:FixParser
  name: FixParser
  upstreamTasks:
    - taskName: FixBatchCsvDownloader
      key: fix_dataframe
  # Convert FixParserResult to Pandas DataFrame
  # Note: the key has to be fix_parsed_data as that's the argument name in execute()
- path: swarm_tasks.io.read.fix.fix_parser_result_to_frame:FixParserResultToFrame
  name: FixParserResultToFrame
  params:
    dataframe_columns:
      - Account
      - AvgPx
      - CFICode
      - ClientID
      - CumQty
      - Currency
      - ExecID
      - ExecType
      - ExpireDate
      # ff_ columns not in the fix protocol (these are specific to this flow)
      - ff_5001 # ff_5001: OrderIDInternal
      - ff_5002
      - ff_5006  # ff_5006: OrderIDParentRoot
      - ff_5100
      - ff_6751
      - ff_6777
      - ff_7201
      - ff_7207
      - ff_7208
      - ff_7733
      - LastCapacity
      - LastMkt
      - LastPx
      - LastQty
      - LeavesQty
      - MaturityDay
      - MaturityMonthYear
      - MsgSeqNum
      - OrderID
      - OrderQty
      - OrdType
      - Price
      - PutOrCall
      - S3FileURL
      - SecondaryOrderID
      - SecurityExchange
      - SecurityID
      - SecurityIDSource
      - SecurityType
      - SenderCompID
      - SendingTime
      - Side
      - StopPx
      - StrikePrice
      - Symbol
      - TimeInForce
      - TransactTime
  upstreamTasks:
    - taskName: FixParser
      key: fix_parsed_data
  # Filter out records where ExecType not in [0,1,2,3,4,F,H]
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: FilterFixMessagesByExecType
  params:
    query: "`ExecType`.isin(['0', '1', '2', '3', '4', 'F', 'H'])"
    skip_on_empty: true
  upstreamTasks:
    - taskName: FixParserResultToFrame
      key: result
  # Primary Transformations
  # Convert currencies and prices
- path: swarm_tasks.generic.currency.convert_minor_to_major:ConvertMinorToMajor
  name: ConvertMinorToMajor
  paramsList:
    # Currencies
    - source_ccy_attribute: Currency
      target_ccy_attribute: transactionDetails.priceCurrency
    # Prices
    - source_price_attribute: LastPx
      source_ccy_attribute: Currency
      target_price_attribute: transactionDetails.price
      cast_to: abs
    - source_price_attribute: LastPx
      source_ccy_attribute: Currency
      target_price_attribute: priceFormingData.price
      cast_to: abs
    - source_price_attribute: AvgPx
      source_ccy_attribute: Currency
      target_price_attribute: transactionDetails.priceAverage
      cast_to: abs
    - source_price_attribute: Price
      source_ccy_attribute: Currency
      target_price_attribute: __price__
      cast_to: abs
    - source_price_attribute: StopPx
      source_ccy_attribute: Currency
      target_price_attribute: executionDetails.stopPrice
      cast_to: abs
    - source_price_attribute: StrikePrice
      source_ccy_attribute: Currency
      target_price_attribute: __strike_price_fallback__
      cast_to: abs
  upstreamTasks:
    - taskName: FilterFixMessagesByExecType
      key: result
- path: swarm_tasks.transform.map.map_attribute:MapAttribute
  name: OutgoingOrderInfoFields
  paramsList:
  - source_attribute: Symbol
    target_attribute: __symbol_with_prefix__
    prefix: "Symbol: "
  - source_attribute: Symbol
    target_attribute: __text_with_prefix__
    prefix: "Text: "
  - source_attribute: Account
    target_attribute: __account_with_prefix__
    prefix: "Account ID (Fix Tag 1): "
  - source_attribute: ClientID
    target_attribute: __clientid_with_prefix__
    prefix: "Client ID (Fix Tag 109): "
  - source_attribute: ff_7207
    target_attribute: __mgf_candidate_with_prefix__
    prefix: "MGF Candidate: "
  - source_attribute: ff_7208
    target_attribute: __jitney_broker_id_with_prefix__
    prefix: "Jitney Broker ID: "
  - source_attribute: ff_7201
    target_attribute: __account_type_with_prefix__
    prefix: "Account Type: "
  upstreamTasks:
    - taskName: FilterFixMessagesByExecType
      key: result
  # Concatenate Attributes
- path: swarm_tasks.transform.concat.concat_attributes:ConcatAttributes
  name: ConcatAttributes
  paramsList:
  - source_attributes:
      - Side
      - ExecID
      - OrderID
    target_attribute: reportDetails.transactionRefNo
    delimiter: "|"
  - source_attributes:
      - ff_5001
      - ff_5006
      - OrderID
    target_attribute: orderIdentifiers.orderIdCode
    delimiter: "|"
  - source_attributes:
      - __symbol_with_prefix__
      - __text_with_prefix__
      - __clientid_with_prefix__
      - __mgf_candidate_with_prefix__
      - __jitney_broker_id_with_prefix__
      - __account_type_with_prefix__
    target_attribute: executionDetails.outgoingOrderAddlInfo
    delimiter: "; "
  - source_attributes:
      - MaturityMonthYear
      - MaturityDay
    target_attribute: __maturity_date_if_not_null__
    mask: "(`MaturityMonthYear`.notnull()) & `MaturityDay`.notnull()"
  upstreamTasks:
    - taskName: FilterFixMessagesByExecType
      key: result
    - taskName: OutgoingOrderInfoFields
      key: outgoing_order_info_fields
  # Convert Datetime columns to the expected format
- path: swarm_tasks.transform.datetime.convert_datetime:ConvertDatetime
  name: ConvertDatetime
  paramsList:
    - source_attribute: TransactTime
      target_attribute: __transact_time__
      source_attribute_format: "%Y%m%d-%H:%M:%S.%f"
      convert_to: datetime
    - source_attribute: SendingTime
      target_attribute: __sending_time__
      source_attribute_format: "%Y%m%d-%H:%M:%S.%f"
      convert_to: datetime
    - source_attribute: ExpireDate
      target_attribute: __expire_date__
      source_attribute_format: "%Y%m%d"
      mask: "`ExpireDate`.notnull()"
      convert_to: datetime
    - source_attribute: __maturity_date_if_not_null__
      target_attribute: __maturity_date_fallback__
      source_attribute_format: "%Y%m%d"
      mask: "`__maturity_date_if_not_null__`.notnull()"
      convert_to: date
  upstreamTasks:
    - taskName: FilterFixMessagesByExecType
      key: result
    - taskName: ConcatAttributes
      key: concat_attributes
- path: swarm_tasks.generic.string.extract_part_from_delimited_text:ExtractPartFromDelimitedText
  name: ExtractPartFromDelimitedText
  paramsList:
  - source_attribute: Symbol
    target_attribute: __symbol_first_string__
    delimiter: "-"
    index_of_part_to_extract: 0
  - source_attribute: SecurityID
    target_attribute: __security_id_first_string__
    delimiter: "-"
    index_of_part_to_extract: 0
  upstreamTasks:
    - taskName: FilterFixMessagesByExecType
      key: result
  # Map Static
- path: swarm_tasks.transform.map.map_static:MapStatic
  name: MapStatic
  paramsList:
  # Metadata fields
  - target_attribute: _order.__meta_model__
    target_value: Order
  - target_attribute: _orderState.__meta_model__
    target_value: OrderState
  - target_attribute: sourceIndex
    from_index: true
    cast_to_str: true
  - target_attribute: dataSourceName
    target_value: IRESS FIX
  # Transaction Details and Execution Details
  - target_attribute: transactionDetails.quantityNotation
    target_value: UNIT
  - target_attribute: transactionDetails.priceNotation
    target_value: MONE
  - target_attribute:  _order.executionDetails.orderStatus
    target_value: NEWO
  # Instrument Fallback fields
  - target_attribute:  __is_created_through_fallback__
    target_value: true
  upstreamTasks:
    - taskName: FilterFixMessagesByExecType
      key: result

  # Map Attribute for Option Type. Needed before MapConditional
- path: swarm_tasks.transform.map.map_attribute:MapAttribute
  name: OptionTypeMapAttribute
  paramsList:
  - source_attribute: CFICode
    target_attribute: __cficode_second_character__
    mask: "(`CFICode`.notnull()) & (`CFICode`.str.len() > 1)"
    start_index: 1
    end_index: 2
  upstreamTasks:
    - taskName: FilterFixMessagesByExecType
      key: result

  # Map Conditional
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: MapConditional
  paramsList:
    - target_attribute: __ultimate_venue__
      cases:
        - query: "`SecurityExchange`.notnull()"
          attribute: SecurityExchange
        - query: "`SecurityExchange`.isnull()"
          attribute: LastMkt
    - target_attribute: __buysell__
      cases:
        - query: "`Side`=='1'"
          value: BUYI
        - query: "`Side`.isin(['2', '5', '6'])"
          value: SELL
    - target_attribute: _order.buySell
      cases:
        - query: "`Side`=='1'"
          value: BUYI
        - query: "`Side`.isin(['2', '5', '6'])"
          value: SELL
    - target_attribute: executionDetails.shortSellingIndicator
      cases:
        - query: "`Side`=='2'"
          value: SELL
        - query: "`Side`=='5'"
          value: SESH
        - query: "`Side`=='6'"
          value: SSEX
    - target_attribute: transactionDetails.tradingCapacity
      cases:
        - query: "(`LastCapacity`=='1') | LastCapacity.isnull()"
          value: AOTC
        - query: "`LastCapacity`=='2'"
          value: MTCH
        - query: "`LastCapacity`.isin(['3', '4', '5'])"
          value: DEAL
    - target_attribute: __validity_period__
      cases:
        - query: "`TimeInForce`=='0'"
          value: DAVY
        - query: "`TimeInForce`=='1'"
          value: GTCV
        - query: "`TimeInForce`=='3'"
          value: IOCV
        - query: "`TimeInForce`=='4'"
          value: FOKV
        - query: "`TimeInForce`=='5'"
          value: GTXV
        - query: "`TimeInForce`=='6'"
          value: GTDV
    - target_attribute: executionDetails.orderType
      cases:
        - query: "`OrdType`=='1'"
          value: Market
        - query: "`OrdType`=='2'"
          value: Limit
        - query: "`OrdType`=='3'"
          value: Stop / Stop Loss
        - query: "`OrdType`=='4'"
          value: Stop Limit
        - query: "`OrdType`=='5'"
          value: Market On Close
        - query: "`OrdType`=='6'"
          value: With or Without
        - query: "`OrdType`=='7'"
          value: Limit or Better
        - query: "`OrdType`=='8'"
          value: Limit With or Without
        - query: "`OrdType`=='9'"
          value: On Basis
        - query: "`OrdType`=='D'"
          value: Previously Quoted
        - query: "`OrdType`=='E'"
          value: Previously Indicated
        - query: "`OrdType`=='G'"
          value: Forex Swap
        - query: "`OrdType`=='I'"
          value: Funari
        - query: "`OrdType`=='J'"
          value: Market If Touched
        - query: "`OrdType`=='K'"
          value: Market With Left Over as Limit
        - query: "`OrdType`=='L'"
          value: Previous Fund Valuation Point
        - query: "`OrdType`=='M'"
          value: Next Fund Valuation Point
        - query: "`OrdType`=='P'"
          value: Pegged
        - query: "`OrdType`=='Q'"
          value: Counter-order selection
    - target_attribute: _orderState.executionDetails.orderStatus
      cases:
        # ExecType = '0' indicates NEWO
        - query: "`ExecType`.isin(['1', 'F'])"
          value: PARF
        - query: "`ExecType` == '2'"
          value: FILL
        - query: "`ExecType` == '3'"
          value: DNFD
        - query: "`ExecType`.isin(['4', 'H'])"
          value: CAME
        - query: "`ExecType`.isin(['5', 'D', 'G'])"
          value: REME
        - query: "`ExecType` == '6'"
          value: PNDC
        - query: "`ExecType`.isin(['7', '9'])"
          value: REMA
        - query: "`ExecType` == '8'"
          value: REMO
        - query: "`ExecType` == 'C'"
          value: EXPI
    - target_attribute: __transact_or_sending_time__
      cases:
        - query: "index == index"
          attribute: __transact_time__
        - query: "`TransactTime`.isnull()"
          attribute: __sending_time__
    - target_attribute: __instrument_classification_fallback__
      cases:
        - query: "`CFICode`.notnull()"
          attribute: CFICode
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('OPT', case=False)) & (`PutOrCall` == 0)"
          value: OPXXXX
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('OPT', case=False)) & (`PutOrCall` == 1)"
          value: OCXXXX
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('ETN', case=False))"
          value: CEXXXX
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('MF', case=False))"
          value: CIXXXX
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('CTB|CAN|TB|TBILL|USTB|TNOTE|UST|CORP', case=False))"
          value: DBXXTX
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('CORP', case=False))"
          value: DBXXXX
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('CB', case=False))"
          value: DCXXXX
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('ABS', case=False))"
          value: DDQXXX
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('DR', case=False))"
          value: DDXXXX
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('STRUCT', case=False))"
          value: DEXXXX
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('CS', case=False))"
          value: ECXXXX
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('PS', case=False))"
          value: EPXXXX
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('FUT', case=False))"
          value: FFXXXX
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('FXSPOT', case=False))"
          value: IFXXXX
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('EQBSKT', case=False))"
          value: JEBXXX
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('EQFWD', case=False))"
          value: JEXXXX
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('FXNDF', case=False))"
          value: JFXXXC
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('FXFWD', case=False))"
          value: JFXXXX
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('CFD', case=False))"
          value: JXXXCX
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('SPREADBET', case=False))"
          value: JXXXSX
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('SECLOAN', case=False))"
          value: LLEXXX
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('LOANLEASE', case=False))"
          value: LLXXXX
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('REPO', case=False))"
          value: LRXXXX
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('WAR', case=False))"
          value: RWXXXX
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('CDS', case=False))"
          value: SCXXXX
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('FXSWAP', case=False))"
          value: SFXXXX
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('FWDSWAP', case=False))"
          value: SXXXXX
        - query: "`CFICode`.isnull() & (`SecurityType`.fillna('').str.fullmatch('FUTSWAP|SWAPTION', case=False))"
          value: SXXXXX
        - query: "`CFICode`.isnull() & (`SecurityType`.isnull())"
          value: ECXXXX
    - target_attribute: __security_id__
      cases:
        - query: "`SecurityIDSource` == '4'"
          attribute: SecurityID
        - query: "`SecurityIDSource`.isnull()"
          attribute: __symbol_first_string__
    - target_attribute: __instr_id_code_type_fallback__
      cases:
        - query: "`SecurityIDSource` == '4'"
          value: ID
    - target_attribute: __instrument_fullname_fallback__
      cases:
        - query: "`SecurityID`.notnull()"
          attribute: SecurityID
        - query: "`SecurityID`.isnull()"
          attribute: Symbol
    - target_attribute: __strike_price_curr_fallback__
      cases:
        - query: "`__strike_price_fallback__`.notnull()"
          attribute: transactionDetails.priceCurrency
    - target_attribute: __strike_price_type_fallback__
      cases:
        - query: "`__strike_price_fallback__`.notnull()"
          value: MntryVal
    - target_attribute: __option_type__
      cases:
        - query: "`__cficode_second_character__`.notnull()"
          attribute: __cficode_second_character__
        - query: "`PutOrCall` == 0"
          value: PUTO
        - query: "`PutOrCall` == 1"
          value: CALL
    - target_attribute: __option_type_fallback__
      cases:
        - query: "`PutOrCall` == 0"
          value: PUTO
        - query: "`PutOrCall` == 1"
          value: CALL
    - target_attribute: __underlying_symbol_attribute__
      cases:
        - query: "(`SecurityType`.fillna('').str.upper().isin(['OPT', 'FUT'])) & (`__symbol_first_string__`.notnull())"
          attribute: __symbol_first_string__
        - query: "(`SecurityType`.fillna('').str.upper().isin(['OPT', 'FUT'])) & (`__symbol_first_string__`.isnull()) & (`__security_id_first_string__`.notnull())"
          attribute: __security_id_first_string__
    - target_attribute: __asset_class__
      cases:
        # Equities: set to pd.NA
        - query: "`SecurityType`.fillna('').str.upper().isin(['CS', 'PS'])"
          as_empty: true
        - query: "`SecurityType`.fillna('').str.upper() == 'FUT'"
          value: future
        - query: "`SecurityType`.fillna('').str.upper() == 'OPT'"
          value: option
    - target_attribute: __newo_in_file__
      cases:
        - query: "`ExecType` == '0'"
          value: true
        - query: "`ExecType` != '0'"
          value: false
    - target_attribute: executionDetails.passiveOnlyIndicator
      cases:
        - query: "`ff_7733` == 'Y'"
          value: true
        - query: "`ff_7733` != 'Y'"
          value: false
    - target_attribute: executionDetails.passiveAggressiveIndicator
      cases:
        - query: "`ff_7733` == 'Y'"
          value: "AGRE"
        - query: "`ff_7733` != 'Y'"
          value: "PASV"
    - target_attribute: marDetails.isPersonalAccountDealing
      cases:
        - query: "`ff_6777` == 'Y'"
          value: true
        - query: "`ff_6777` != 'Y'"
          value: false
  upstreamTasks:
    - taskName: FilterFixMessagesByExecType
      key: result
    - taskName: MapStatic
      key: map_static
    - taskName: ConvertDatetime
      key: convert_datetime
    - taskName: ExtractPartFromDelimitedText
      key: extract_from_delimited
    - taskName: ConvertMinorToMajor
      key: convert_currencies
    - taskName: OptionTypeMapAttribute
      key: option_type_cfi
  # Lookup DataFrame from S3 to get the ultimate venue
- path: swarm_tasks.order.feed.iress.fix.lookup_mic_codes_in_s3:LookupMicCodesInS3
  name: LookupMicCodesInS3
  params:
  # Metadata fields
    lookup_file_s3_key: mapping_tables/order-feed-iress-fix/iress_mic_mapping.csv
    source_venue_column: LastMkt
    source_ultimate_venue_column: __ultimate_venue__
    target_venue_column: transactionDetails.venue
    target_ultimate_venue_column: transactionDetails.ultimateVenue
  upstreamTasks:
    - taskName: FilterFixMessagesByExecType
      key: result
    - taskName: MapConditional
      key: map_conditional
  # Map Attributes
- path: swarm_tasks.transform.map.map_attribute:MapAttribute
  name: MapAttributes
  paramsList:
  # Order Identifiers
  - source_attribute: orderIdentifiers.orderIdCode
    target_attribute: _order.id
  - source_attribute: orderIdentifiers.orderIdCode
    target_attribute: _orderState.id
  - source_attribute: SecondaryOrderID
    target_attribute: orderIdentifiers.internalOrderIdCode
  - source_attribute: reportDetails.transactionRefNo
    target_attribute: orderIdentifiers.transactionRefNo
  - source_attribute: MsgSeqNum
    target_attribute: orderIdentifiers.sequenceNumber
  - source_attribute: ff_5100
    target_attribute: orderIdentifiers.tradingVenueTransactionIdCode
  - source_attribute: ff_5002
    target_attribute: orderIdentifiers.parentOrderId
  # Transaction Details
  - source_attribute: CumQty
    target_attribute: transactionDetails.cumulativeQuantity
  - source_attribute: __transact_or_sending_time__
    target_attribute: _orderState.transactionDetails.tradingDateTime
  - source_attribute: LastQty
    target_attribute: transactionDetails.quantity
  # Price Forming Data
  - source_attribute: LastQty
    target_attribute: priceFormingData.tradedQuantity
  - source_attribute: OrderQty
    target_attribute: priceFormingData.initialQuantity
  - source_attribute: LeavesQty
    target_attribute: priceFormingData.remainingQuantity
  # Parties fields
  - source_attribute: SenderCompID
    target_attribute: __executing_entity_with_id__
    prefix: "id:"
  - source_attribute: Account
    target_attribute: __account_with_id__
    prefix: "id:"
  - source_attribute: ClientID
    target_attribute: __client_with_id__
    prefix: "id:"
  - source_attribute: transactionDetails.ultimateVenue
    target_attribute: __counterparty_with_id__
    prefix: "id:"
  - source_attribute: transactionDetails.ultimateVenue
    target_attribute: __counterparty_without_id__
  - source_attribute: ClientID
    target_attribute: __investment_dm_with_id__
    prefix: "id:"
  - source_attribute: ClientID
    target_attribute: __investment_dm_without_id__
  - source_attribute: ff_6751
    target_attribute: __trader_with_id__
    prefix: "id:"
  - source_attribute: ff_6751
    target_attribute: __trader_without_id__
  # Source Key: Add the sourceKey from the S3FileURL column created in FixParserResultToFrame
  - source_attribute: S3FileURL
    target_attribute: sourceKey
  # Temp column so we don't need to pass FilterFixMessagesByExecType in AuxiliaryMapConditional
  - source_attribute: SecurityIDSource
    target_attribute: __security_id_source__
  upstreamTasks:
    - taskName: FilterFixMessagesByExecType
      key: result
    - taskName: ConcatAttributes
      key: concat_attributes
    - taskName: MapConditional
      key: map_conditional
    - taskName: LookupMicCodesInS3
      key: lookup_mic_codes
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: MapClientIDConditionally
  paramsList:
    - target_attribute: __client_conditional_with_id__
      cases:
        - query: "`Account`.notnull() & (`ClientID`.fillna('').str.upper()!='ODLUM')"
          attribute: __account_with_id__
        - query: "`Account`.isnull() | (`ClientID`.fillna('').str.upper()=='ODLUM')"
          attribute: __client_with_id__
    - target_attribute: __client_conditional_without_id__
      cases:
        - query: "`Account`.notnull() & (`ClientID`.fillna('').str.upper()!='ODLUM')"
          attribute: Account
        - query: "`Account`.isnull() | (`ClientID`.fillna('').str.upper()=='ODLUM')"
          attribute: ClientID
  upstreamTasks:
    - taskName: FilterFixMessagesByExecType
      key: result
    - taskName: MapAttributes
      key: map_attributes
- path: swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers:GenericOrderPartyIdentifiers
  name: PartyIdentifiers
  params:
    target_attribute: marketIdentifiers.parties
    executing_entity_identifier: __executing_entity_with_id__
    client_identifier: __client_conditional_with_id__
    counterparty_identifier: __counterparty_with_id__
    investment_decision_within_firm_identifier: __investment_dm_with_id__
    trader_identifier: __trader_with_id__
    buyer_identifier: __client_conditional_with_id__
    seller_identifier: __counterparty_with_id__
    buy_sell_side_attribute: __buysell__
    use_buy_mask_for_buyer_seller: true
  upstreamTasks:
    - taskName: MapAttributes
      key: result
    - taskName: MapConditional
      key: map_conditional
    - taskName: MapClientIDConditionally
      key: map_client
- path: swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers:InstrumentIdentifiers
  name: InstrumentIdentifiers
  params:
    asset_class_attribute: __asset_class__
    currency_attribute: transactionDetails.priceCurrency
    isin_attribute: __security_id__
    venue_attribute: transactionDetails.ultimateVenue
    expiry_date_attribute: __maturity_date_fallback__
    option_strike_price_attribute: __strike_price_fallback__
    option_type_attribute: __option_type__
    underlying_symbol_attribute: __underlying_symbol_attribute__
    retain_task_inputs: true
  upstreamTasks:
    - taskName: MapConditional
      key: result
    - taskName: ConvertMinorToMajor
      key: convert_currencies
    - taskName: LookupMicCodesInS3
      key: lookup_mic_codes
    - taskName: ConvertDatetime
      key: dates
- path: swarm_tasks.transform.steeleye.link.merge_market_identifiers:MergeMarketIdentifiers
  name: MergeMarketIdentifiers
  params:
    identifiers_path: marketIdentifiers
    instrument_path: marketIdentifiers.instrument
    parties_path: marketIdentifiers.parties
  upstreamTasks:
    - taskName: InstrumentIdentifiers
      key: result
    - taskName: PartyIdentifiers
      key: party_identifiers
# Primary Frame Concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PrimaryFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
    - __transact_time__
    - __sending_time__
    - __account_with_id__
    - __executing_entity_with_id__
    - __client_with_id__
    - __ultimate_venue__
    - __maturity_date_if_not_null__
    - __cficode_second_character__
  upstreamTasks:
    - taskName: ConvertDatetime
      key: convert_date_time
    - taskName: ConvertMinorToMajor
      key: convert_minor_major
    - taskName: MapAttributes
      key: map_attribute
    - taskName: MapStatic
      key: map_static
    - taskName: MapConditional
      key: map_conditional
    - taskName: MapClientIDConditionally
      key: map_client_id_cond
    - taskName: ConcatAttributes
      key: concat_attributes
    - taskName: PartyIdentifiers
      key: party_identifiers
    - taskName: InstrumentIdentifiers
      key: instrument_identifiers
    - taskName: MergeMarketIdentifiers
      key: merge_market_ids
    - taskName: LookupMicCodesInS3
      key: lookup_mic_codes
- path: swarm_tasks.transform.steeleye.link.parties:LinkParties
  name: LinkParties
  params:
    identifiers_path: marketIdentifiers.parties
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      key: result
- path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
  name: LinkInstrument
  params:
    identifiers_path: marketIdentifiers.instrument
    asset_class_attribute: __asset_class__
    currency_attribute: transactionDetails.priceCurrency
    venue_attribute: transactionDetails.ultimateVenue
  resources:
    ref_data_key: reference-data
    tenant_data_key: tenant-data
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      key: result
  # Parties Fallback
- path: swarm_tasks.order.generic.parties_fallback:PartiesFallback
  name: PartiesFallback
  resources:
    es_client_key: tenant-data
  params:
    buyer_attribute: __client_conditional_without_id__
    seller_attribute: __counterparty_without_id__
    use_buy_mask_for_buyer_seller: true
    investment_decision_maker_attribute: __investment_dm_without_id__
    trader_attribute: __trader_without_id__
    buy_sell_side_attribute: __buysell__
    counterparty_attribute: __counterparty_without_id__
    client_attribute: __client_conditional_without_id__
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      key: result
    - taskName: LinkParties
      key: link_parties_result
 # Auxiliary Map Attributes
- path: swarm_tasks.transform.map.map_attribute:MapAttribute
  name: AuxiliaryMapAttributes
  paramsList:
  # Datetime fields
  - source_attribute: __transact_or_sending_time__
    target_attribute: timestamps.orderReceived
  - source_attribute: __transact_or_sending_time__
    target_attribute: timestamps.orderSubmitted
  # For orders, orderStatusUpdated = orderReceived
  - source_attribute: __transact_or_sending_time__
    target_attribute: _order.timestamps.orderStatusUpdated
  - source_attribute: _orderState.transactionDetails.tradingDateTime
    target_attribute: _orderState.timestamps.tradingDateTime
  # Validity period
  - source_attribute: __validity_period__
    target_attribute: executionDetails.validityPeriod
    cast_to: string.list
    list_delimiter: ;
  - source_attribute: __expire_date__
    target_attribute: timestamps.validityPeriod
    cast_to: string.list
    list_delimiter: ;
  # Buy/Sell Indicator
  - source_attribute: __buysell__
    target_attribute: _orderState.buySell
  - source_attribute: __buysell__
    target_attribute: executionDetails.buySellIndicator
  - source_attribute: __buysell__
    target_attribute: transactionDetails.buySellIndicator
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      key: result
  # Concat Attributes for fallback fields
- path: swarm_tasks.transform.concat.concat_attributes:ConcatAttributes
  name: ConcatAttributesForFallback
  paramsList:
  - source_attributes:
      - __security_id__
      - transactionDetails.ultimateVenue
      - transactionDetails.priceCurrency
    target_attribute: __aii_id_code__
  - source_attributes:
      - transactionDetails.ultimateVenue
      - __security_id__
      - __maturity_date_fallback__
      - __strike_price_fallback__
      - transactionDetails.priceCurrency
    target_attribute: __aii_id_symbol__
    delimiter: "|"
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      key: result
  # Auxiliary Map Conditional
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: AuxiliaryMapConditional
  paramsList:
    - target_attribute: _orderState.timestamps.orderStatusUpdated
      cases:
        - query: "`_orderState.executionDetails.orderStatus`.isin(('FILL', 'PARF'))"
          attribute: _orderState.transactionDetails.tradingDateTime
        - query: "~`_orderState.executionDetails.orderStatus`.isin(('FILL', 'PARF'))"
          attribute: timestamps.orderReceived
    - target_attribute: executionDetails.limitPrice
      cases:
        - query: "`executionDetails.orderType`.str.contains(r'.*Limit.*')"
          attribute: __price__
    - target_attribute: __iui_fallback__
      cases:
        - query: "`__security_id_source__` == '4'"
          attribute: __aii_id_code__
        - query: "(`__security_id_source__` == '8') & __maturity_date_fallback__.notnull()"
          attribute: __aii_id_symbol__
        - query: "`__security_id_source__`.isnull()"
          attribute: __aii_id_code__
    - target_attribute: __aii_fallback__
      cases:
        - query: "`__security_id_source__` == '4'"
          attribute: __aii_id_code__
        - query: "(`__security_id_source__` == '8') & __maturity_date_fallback__.notnull()"
          attribute: __aii_id_symbol__
        - query: "`__security_id_source__`.isnull()"
          attribute: __aii_id_symbol__
  upstreamTasks:
  - taskName: PrimaryFrameConcatenator
    key: result
  - taskName: AuxiliaryMapAttributes
    key: aux_map_attributes
  - taskName: ConcatAttributesForFallback
    key: concat_attr_fallback
- path: swarm_tasks.generic.instrument_fallback.instrument_fallback:InstrumentFallback
  name: InstrumentFallback
  params:
    instrument_fields_map:
      # Ext
      - source_field: __instr_id_code_type_fallback__
        target_field: ext.instrumentIdCodeType
      - source_field: __underlying_symbol_attribute__
        target_field: ext.exchangeSymbolRoot
      - source_field: __underlying_symbol_attribute__
        target_field: ext.exchangeSymbol
      - source_field: __strike_price_type_fallback__
        target_field: ext.strikePriceType
      - source_field: __aii_fallback__
        target_field: ext.alternativeInstrumentIdentifier
      - source_field: __iui_fallback__
        target_field: ext.instrumentUniqueIdentifier
      # Derivative
      - source_field: __maturity_date_fallback__
        target_field: derivative.expiryDate
      - source_field: __option_type_fallback__
        target_field: derivative.optionType
      - source_field: __strike_price_fallback__
        target_field: derivative.strikePrice
      - source_field: __strike_price_curr_fallback__
        target_field: derivative.strikePriceCurrency
      # Venue
      - source_field: transactionDetails.venue
        target_field: venue.tradingVenue
      # Top-level fields
      - source_field: __instrument_classification_fallback__
        target_field: instrumentClassification
      - source_field: __instrument_fullname_fallback__
        target_field: instrumentFullName
      - source_field: __is_created_through_fallback__
        target_field: isCreatedThroughFallback
    str_to_bool_dict:
      "true": True
      "false": False
    cfi_and_bestex_from_instrument_classification: true
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      key: result
    - taskName: LinkInstrument
      key: link_instrument
    - taskName: AuxiliaryMapConditional
      key: aux_map_conditional
# Get instrumentClassification from the instrument
- path: swarm_tasks.transform.map.map_from_nested:MapFromNested
  name: MapNestedFromInstrument
  paramsList:
  - source_attribute: instrumentDetails.instrument
    nested_path: instrumentClassification
    target_attribute: __instrument_classification__
  upstreamTasks:
    - taskName: InstrumentFallback
      key: result
  # Map tradersAlgosWaiversIndicators.commodityDerivativeIndicator
- path: swarm_tasks.transform.map.map_value:MapValue
  name: MapCommodityDerivativeIndicator
  paramsList:
  - source_attribute: __instrument_classification__
    target_attribute: tradersAlgosWaiversIndicators.commodityDerivativeIndicator
    case_insensitive: true
    regex_replace_map:
      - regex: "^JT[A-Z]{2}C[A-Z]{1}$"
        replace_value: F
      - regex: "^JT[A-Z]{2}F[A-Z]{1}$"
        replace_value: F
      - regex: "^FC[A-Z]{4}$"
        replace_value: F
      - regex: "^HT[A-Z]{4}$"
        replace_value: F
      - regex: "^O[A-Z]{2}T[A-Z]{2}$"
        replace_value: F
      - regex: "^ST[A-Z]{1}T[A-Z]{2}$"
        replace_value: F
      - regex: "^ST[A-Z]{1}C[A-Z]{2}$"
        replace_value: F
    value_map:
      F: false
  upstreamTasks:
  - taskName: MapNestedFromInstrument
    key: result
- path: swarm_tasks.transform.steeleye.meta.assign_meta_parent:AssignMetaParent
  name: ParentId
  params:
    parent_model_attribute: _order.__meta_model__
    parent_attributes_prefix: _order.
    target_attribute: _orderState.__meta_parent__
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      key: result
  # Add date column from OrderStatusUpdated
- path: swarm_tasks.transform.datetime.convert_datetime:ConvertDatetime
  name: PopulateOrderDate
  paramsList:
    - source_attribute: _order.timestamps.orderStatusUpdated
      target_attribute: _order.date
      source_attribute_format: "%Y-%m-%dT%H:%M:%S.%fZ"
      convert_to: date
    - source_attribute: _orderState.timestamps.orderStatusUpdated
      target_attribute: _orderState.date
      source_attribute_format: "%Y-%m-%dT%H:%M:%S.%fZ"
      convert_to: date
  upstreamTasks:
    - taskName: AuxiliaryMapConditional
      key: result
    - taskName: AuxiliaryMapAttributes
      key: aux_map_attributes
# Auxiliary Frame Concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: AuxiliaryFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
    - __instr_id_code_type_fallback__
    - __instrument_classification_fallback__
    - __is_created_through_fallback__
    - __instrument_id_code_type_fallback__
    - __underlying_symbol_attribute__
    - __instrument_fullname_fallback__
    - __maturity_date_fallback__
    - __aii_fallback__
    - __iui_fallback__
    - __strike_price_fallback__
    - __strike_price_curr_fallback__
    - __strike_price_type_fallback__
    - __option_type__
    - __option_type_fallback__
    - __transact_or_sending_time__
    - __validity_period__
    - __buysell__
    - __price__
    - __security_id_source__
    - __security_id__
    - __expire_date__
    - marketIdentifiers.instrument
    - marketIdentifiers.parties
    - __client_conditional_with_id__
    - __client_conditional_without_id__
    - __counterparty_with_id__
    - __counterparty_without_id__
    - __investment_dm_without_id__
    - __investment_dm_with_id__
    - __trader_without_id__
    - __trader_with_id__
    - __asset_class__
    - asset_class_attribute # Instrument Identifiers columns
    - bbg_figi_id_attribute
    - currency_attribute
    - eurex_id_attribute
    - exchange_symbol_attribute
    - expiry_date_attribute
    - interest_rate_start_date_attribute
    - isin_attribute
    - notional_currency_1_attribute
    - notional_currency_2_attribute
    - option_strike_price_attribute
    - option_type_attribute
    - swap_near_leg_date_attribute
    - underlying_index_name_attribute
    - underlying_index_name_leg_2_attribute
    - underlying_index_series_attribute
    - underlying_index_term_attribute
    - underlying_index_term_value_attribute
    - underlying_index_version_attribute
    - underlying_isin_attribute
    - underlying_symbol_attribute
    - underlying_symbol_expiry_code_attribute
    - underlying_index_term_leg_2_attribute
    - underlying_index_term_value_leg_2_attribute
    - venue_attribute
    - venue_financial_instrument_short_name_attribute
    - instrument_classification_attribute
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      key: result
    - taskName: PartiesFallback
      key: parties_fallback
    - taskName: InstrumentFallback
      key: instrument_fallback
    - taskName: MapCommodityDerivativeIndicator
      key: map_commodity_derivative_indicator
    - taskName: ParentId
      key: parent_id
    - taskName: AuxiliaryMapAttributes
      key: aux_map_attributes
    - taskName: AuxiliaryMapConditional
      key: aux_map_conditional
    - taskName: PopulateOrderDate
      key: order_date
  # Split Order records
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderRecords
  params:
    except_prefix: _orderState.
    strip_prefix: true
  upstreamTasks:
  - taskName: AuxiliaryFrameConcatenator
    key: result
  # Split OrderState records
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderStateRecords
  params:
    except_prefix: _order.
    strip_prefix: true
  upstreamTasks:
  - taskName: AuxiliaryFrameConcatenator
    key: result
  # Strip "_order." prefix from "Order" records
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrder
  params:
    action: strip
    prefix: _order.
  upstreamTasks:
  - taskName: OrderRecords
    key: result
  # Strip "_orderState." prefix from "OrderState" records
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrderState
  params:
    action: strip
    prefix: _orderState.
  upstreamTasks:
  - taskName: OrderStateRecords
    key: result
  # Vertical concatenator of "Order" and "OrderState" records
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: VerticalConcatenator
  params:
    orient: vertical
    reset_index: true
    drop_index: true
  upstreamTasks:
  - taskName: StripPrefixOrder
    key: order_records
  - taskName: StripPrefixOrderState
    key: order_state_records
  # Remove OrderStates for NEWO records
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: RemoveInvalidOrderStates
  params:
    query: "`executionDetails.orderStatus`.notnull()"
  upstreamTasks:
  - taskName: VerticalConcatenator
    key: result
# Remove duplicate new orders
- path: swarm_tasks.order.generic.remove_duplicate_newo:RemoveDuplicateNEWO
  name: RemoveDupNEWO
  params:
    newo_in_file_col: __newo_in_file__
    drop_newo_in_file_col: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: RemoveInvalidOrderStates
    key: result
# Best-execution tasks
- path: swarm_tasks.transform.steeleye.orders.best_execution.best_execution:BestExecution
  name: BestExecution
  resources:
    es_client_key: reference-data
  upstreamTasks:
  - taskName: RemoveDupNEWO
    key: result
# Best-Ex results concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: BestExecutionConcatenator
  params:
    orient: horizontal
  upstreamTasks:
  - taskName: BestExecution
    key: best_ex_result
  - taskName: RemoveDupNEWO
    key: orders_and_orderstates_final
  # Assign Meta
- path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
  name: AssignMeta
  params:
    model_attribute: __meta_model__
    parent_attribute: __meta_parent__
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: BestExecutionConcatenator
    key: result
 # Post-meta concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PostMetaConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __meta_model__
      - __meta_parent__
  upstreamTasks:
  - taskName: BestExecutionConcatenator
    key: result
  - taskName: AssignMeta
    key: meta
# Elastic Bulk Transformer
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: ElasticBulkTransformer
  params:
    action_type: create
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: PostMetaConcatenator
    key: transform_result
  - taskName: FilterFixMessagesByExecType
    key: producer_result
# Elastic Bulk Writer
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: PutIfAbsent
  params:
    payload_size: 10000000
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: ElasticBulkTransformer
    key: result
# Instrument Mapper
- path: swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper:InstrumentMapper
  name: InstrumentMapper
  upstreamTasks:
    - taskName: PostMetaConcatenator
      key: source_frame
    - taskName: PutIfAbsent
      key: bulk_writer_result
# Quarantine Condition
- path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
  name: QuarantineCondition
  upstreamTasks:
  - taskName: PutIfAbsent
    key: bulk_writer_result
# Quarantined Elastic Bulk Transformer
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: QuarantinedElasticBulkTransformer
  params:
    action_type: create
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: PostMetaConcatenator
    key: transform_result
  - taskName: FilterFixMessagesByExecType
    key: producer_result
  - taskName: PutIfAbsent
    key: bulk_writer_result
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: QuarantinedPutIfAbsent
  params:
    payload_size: 10000000
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: QuarantinedElasticBulkTransformer
    key: result
- path: swarm_tasks.control_flow.noop:Noop
  name: Noop