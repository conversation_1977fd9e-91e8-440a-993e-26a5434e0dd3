# Bundle path -> order/feed/flextrade/exe-processor
# Confluence page: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2473590785/Order+FlexTrade
id: order-feed-flextrade-exe-processor
name: Order Feed FlexTrade EXE
infra:
- name: tenant-data
  type: ELASTICSEARCH
- name: reference-data
  type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
- name: file_url
  envVar: SWARM_FILE_URL
  description: url of the file to process
controlFlows:
- name: S3OrLocalFile
  conditionTaskName: ParametersFlowController
  trueTaskName: S3DownloadFile
  falseTaskName: LocalFile
  merge: true
- name: NeedsQuarantine
  conditionTaskName: QuarantineCondition
  trueTaskName: QuarantinedElasticBulkTransformer
  falseTaskName: Noop
  mapped: true
  merge: false
tasks:
  - path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
    name: ParametersFlowController
    params:
      arg_name: file_url
      starts_with: s3://
      condition: AND
      ignore_case: true
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
    name: S3DownloadFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  - path: swarm_tasks.io.read.local_file:LocalFile
    name: LocalFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
  # CSV File splitter
  - path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
    name: CsvFileSplitter
    params:
      chunksize: 10000
    upstreamTasks:
      - taskName: S3OrLocalFile
        mapped: false
        key: extractor_result
  # Batch Producer
  - path: swarm_tasks.io.read.batch_producer:BatchProducer
    name: BatchProducer
    params:
      remove_unknown_columns: true
      source_schema:
        "exe_broker-orderid": string
        "exe_destination": string
        "exe_exec-id": string
        "exe_exec-price": float
        "exe_exec-shares": string
        "exe_order-id": string
        "exe_portfolio": string
        "exe_portfolio-side": string
        "exe_prim-orderid": string
        "exe_symbol": string
        "exe_status": string
        "exe_transaction-time": string
        "exe_userfixtag": string
        "exe_user-initials": string
        "prim_rpl_fixtags": string
        "prim_rpl_incoming-fix-orderid/strategyid": string
        "prim_rpl_isin": string
        "prim_rpl_limit-price": string
        "prim_rpl_order-time": string
    upstreamTasks:
      - taskName: CsvFileSplitter
        mapped: true
        flatten: true
        key: file_splitter_result
  # Extract batch number from FrameProducer Result
  - path: swarm_tasks.generic.extract_batch_from_frame_producer_result:ExtractBatchFromFrameProducerResult
    name: ExtractBatchFromFrameProducerResult
    params:
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: frame_producer_result
  - path: swarm_tasks.io.read.fix.inline_fix_parser:InlineFixParser
    name: InlineFixParserExeUserFixtag
    params:
      fix_content_column: exe_userfixtag
      fix_delimiter: ";"
      required_tags:
        - "15" # Currency
        - "22" # SecurityIDSource
        - "30" # LastMkt
        - "48" # SecurityID
        - "60" # TransactTime
        - "151" # LeavesQty
        - "167" # SecurityType
        - "455" # SecurityAltID
        - "456" # SecurityAltIDSource
        - "20001" # ff_20001
      additional_column_prefix: exe_user_fix_tag_
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: source_frame
  # Add columns from
  - path: swarm_tasks.io.read.fix.inline_fix_parser:InlineFixParser
    name: InlineFixParserPrimRplFixtags
    params:
      fix_content_column: prim_rpl_fixtags
      fix_delimiter: ";"
      required_tags:
        - "15" # Currency
      additional_column_prefix: prim_rpl_fix_tag_
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: source_frame
  # Concatenate source frame with parsed fix content
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: SourceFrameConcatenator
    params:
      orient: horizontal
    upstreamTasks:
      - taskName: BatchProducer
        mapped: true
        key: main_frame
      - taskName: InlineFixParserExeUserFixtag
        mapped: true
        key: exe_user_fixtag
      - taskName: InlineFixParserPrimRplFixtags
        mapped: true
        key: prim_rpl_fixtags
  # Primary transformations
  - path: swarm_tasks.primary_transformations.get_primary_transformations:GetPrimaryTransformations
    name: FlextradeExeTransformations
    upstreamTasks:
      - taskName: SourceFrameConcatenator
        mapped: true
        key: result
  # Assign Meta Parent
  - path: swarm_tasks.transform.steeleye.meta.assign_meta_parent:AssignMetaParent
    name: MetaParent
    params:
      parent_model_attribute: _order.__meta_model__
      parent_attributes_prefix: _order.
      target_attribute: _orderState.__meta_parent__
    upstreamTasks:
      - taskName: FlextradeExeTransformations
        mapped: true
        key: result
  # Link Parties
  - path: swarm_tasks.transform.steeleye.link.parties:LinkParties
    name: LinkParties
    params:
      identifiers_path: marketIdentifiers.parties
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: FlextradeExeTransformations
        mapped: true
        key: result
  # Link Instruments
  - path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
    name: LinkInstrument
    params:
      identifiers_path: marketIdentifiers.instrument
      venue_attribute: transactionDetails.ultimateVenue
      currency_attribute: transactionDetails.priceCurrency
    resources:
      ref_data_key: reference-data
      tenant_data_key: tenant-data
    upstreamTasks:
      - taskName: FlextradeExeTransformations
        mapped: true
        key: result
  # Instrument Fallback
  - path: swarm_tasks.generic.instrument_fallback.instrument_fallback:InstrumentFallback
    name: InstrumentFallback
    params:
      instrument_fields_map:
        # Ext
        - source_field: __asset_class_main__
          target_field: ext.bestExAssetClassMain
        - source_field: bbg_figi_id_attribute
          target_field: ext.exchangeSymbolBbg
        # Top-level fields
        - source_field: isin_attribute
          target_field: instrumentIdCode
        - source_field: __is_created_through_fallback__
          target_field: isCreatedThroughFallback
      str_to_bool_dict:
        "true": True
        "false": False
    upstreamTasks:
      - taskName: FlextradeExeTransformations
        mapped: true
        key: result
      - taskName: LinkInstrument
        mapped: true
        key: link_instrument

    # Primary Frame Conatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: PrimaryFrameConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __asset_class_main__
        - marketIdentifiers.instrument
        - marketIdentifiers.parties
        - __instr_expiry_date__
        - __instr_unique_identifier__
        - __is_created_through_fallback__
        - asset_class_attribute # Instrument Identifiers columns
        - bbg_figi_id_attribute
        - currency_attribute
        - eurex_id_attribute
        - exchange_symbol_attribute
        - expiry_date_attribute
        - interest_rate_start_date_attribute
        - isin_attribute
        - notional_currency_1_attribute
        - notional_currency_2_attribute
        - option_strike_price_attribute
        - option_type_attribute
        - swap_near_leg_date_attribute
        - underlying_index_name_attribute
        - underlying_index_name_leg_2_attribute
        - underlying_index_series_attribute
        - underlying_index_term_attribute
        - underlying_index_term_value_attribute
        - underlying_index_version_attribute
        - underlying_isin_attribute
        - underlying_symbol_attribute
        - underlying_symbol_expiry_code_attribute
        - underlying_index_term_leg_2_attribute
        - underlying_index_term_value_leg_2_attribute
        - venue_attribute
        - venue_financial_instrument_short_name_attribute
        - instrument_classification_attribute
    upstreamTasks:
      - taskName: FlextradeExeTransformations
        mapped: true
        key: result
      - taskName: MetaParent
        mapped: true
        key: meta_parent
      - taskName: LinkParties
        mapped: true
        key: link_parties
      - taskName: InstrumentFallback
        mapped: true
        key: instrument_fallback

  # Filter only OrderRecords into a frame
  - path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
    name: OrderRecords
    params:
      except_prefix: _orderState.
      strip_prefix: true
    upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      mapped: true
      key: result
  # Filter only OrderStateRecords into a frame
  - path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
    name: OrderStateRecords
    params:
      except_prefix: _order.
      strip_prefix: true
    upstreamTasks:
      - taskName: PrimaryFrameConcatenator
        mapped: true
        key: result
  # Strip prefix `_order.` from the column names of OrderRecords frame.
  - path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
    name: StripPrefixOrder
    params:
      action: strip
      prefix: _order.
    upstreamTasks:
      - taskName: OrderRecords
        mapped: true
        key: result
  # Strip prefix `orderState.` from the column names of OrderState Records frame.
  - path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
    name: StripPrefixOrderState
    params:
      action: strip
      prefix: _orderState.
    upstreamTasks:
      - taskName: OrderStateRecords
        mapped: true
        key: result
  # Vertically concatenate Order and OrderState Record frames created above.
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: VerticalConcatenator
    params:
      orient: vertical
      reset_index: true
      drop_index: true
    upstreamTasks:
      - taskName: StripPrefixOrder
        mapped: true
        key: order_records
      - taskName: StripPrefixOrderState
        mapped: true
        key: order_state_records

  # We shouldn't create synthetic NEWOs. So any synthetic NEWOs created should
  # be dropped
  - path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
    name: RemoveSyntheticNEWOs
    params:
      query: "~(`executionDetails.orderStatus` == 'NEWO')"
    upstreamTasks:
      - taskName: VerticalConcatenator
        mapped: true
        key: result
  # Best-execution tasks
  - path: swarm_tasks.transform.steeleye.orders.best_execution.best_execution:BestExecution
    name: BestExecution
    resources:
      es_client_key: reference-data
    upstreamTasks:
      - taskName: RemoveSyntheticNEWOs
        mapped: true
        key: result
  # Best-Ex results concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: BestExecutionConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __newo_in_file__
    upstreamTasks:
      - taskName: BestExecution
        mapped: true
        key: best_ex_result
      - taskName: RemoveSyntheticNEWOs
        mapped: true
        key: orders_and_orderstates_final
  # Assign Meta
  - path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
    name: AssignMeta
    params:
      model_attribute: __meta_model__
      parent_attribute: __meta_parent__
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: BestExecutionConcatenator
        mapped: true
        key: result
  # Post-meta concatenator
  - path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
    name: PostMetaConcatenator
    params:
      orient: horizontal
      drop_columns:
        - __meta_model__
        - __meta_parent__
    upstreamTasks:
      - taskName: BestExecutionConcatenator
        mapped: true
        key: result
      - taskName: AssignMeta
        mapped: true
        key: meta
  # Elastic Bulk Transformer
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: ElasticBulkTransformer
    params:
      action_type: create
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: PostMetaConcatenator
        mapped: true
        key: transform_result
      - taskName: ExtractBatchFromFrameProducerResult
        mapped: true
        key: batch_index
  # ElasticBulkWriter
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: ElasticBulkWriter
    params:
      payload_size: 10000000
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: ElasticBulkTransformer
        mapped: true
        key: result
  # Instrument Mapper
  - path: swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper:InstrumentMapper
    name: InstrumentMapper
    upstreamTasks:
      - taskName: PostMetaConcatenator
        mapped: true
        key: source_frame
      - taskName: ElasticBulkWriter
        mapped: true
        key: bulk_writer_result
  - path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
    name: QuarantineCondition
    upstreamTasks:
      - taskName: ElasticBulkWriter
        mapped: true
        key: bulk_writer_result
  - path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
    name: QuarantinedElasticBulkTransformer
    params:
      action_type: create
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: PostMetaConcatenator
        mapped: true
        key: transform_result
      - taskName: ExtractBatchFromFrameProducerResult
        mapped: true
        key: batch_index
      - taskName: ElasticBulkWriter
        mapped: true
        key: bulk_writer_result
  - path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
    name: QuarantinedElasticBulkWriter
    params:
      payload_size: 10000000
      quarantined: true
    resources:
      es_client_key: tenant-data
    upstreamTasks:
      - taskName: QuarantinedElasticBulkTransformer
        mapped: true
        key: result
  - path: swarm_tasks.control_flow.noop:Noop
    name: Noop