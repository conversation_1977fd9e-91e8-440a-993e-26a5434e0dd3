# Bundle path -> tr/feed/unavista/xml-to-unavista-apollo-converter
# Jira page: https://steeleye.atlassian.net/browse/ON-3305
#
# ----------------------- Documentation ----------------------------#
#    This is an intermediary task, that converts a report for MiFIR,
# which is an XML in the UV format, to a CSV format. This CSV format,
# is the format that the Apollo handler flow ("tr-feed-unavista")
# is expecting.
#
#    In short, this flow converts the XML format source data into CSV
# so that it can be ingested by the Apollo handler.
# ------------------------------------------------------------------#
#
id: tr-feed-xml-to-unavista-converter
name: XML to Unavista Converter
infra:
  - name: tenant-data
    type: ELASTICSEARCH
  - name: reference-data
    type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
  - name: file_url
    envVar: SWARM_FILE_URL
    description: url of the file to process
controlFlows:
  - name: S3OrLocalFile
    conditionTaskName: ParametersFlowController
    trueTaskName: S3DownloadFile
    falseTaskName: LocalFile
    merge: true
tasks:
  # ParametersFlowController to detect input file from S3 or local machine
  - path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
    name: ParametersFlowController
    params:
      arg_name: file_url
      starts_with: s3://
      condition: AND
      ignore_case: true
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url

  # Retrieve input file from S3
  - path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
    name: S3DownloadFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url

  # Or read local file
  - path: swarm_tasks.io.read.local_file:LocalFile
    name: LocalFile
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url

  # The original xml file has some data that we need on xpath "UVMiFIRDocument.UVHeader" xml tag
  # The header data is retrieved by the `GetUVHeader` task in chunks,
  # this task will always generate only one chunk with one row, because
  # only one "UVMiFIRDocument.UVHeader" xml tag
  - path: swarm_tasks.io.read.xml_file_splitter:XMLFileSplitter
    name: GetUVHeader
    params:
      chunksize: 10000
      target_dir_name: xml_header_chunks
      nested_data_path: UVMiFIRDocument.UVHeader
    upstreamTasks:
      - taskName: S3OrLocalFile
        mapped: false
        key: extractor_result

  # Read input XML file, parse it into a Pandas DataFrame and split it in batches
  - path: swarm_tasks.io.read.xml_file_splitter:XMLFileSplitter
    name: XMLFileSplitter
    params:
      chunksize: 10000
      target_dir_name: xml_transaction_chunks
      nested_data_path: UVMiFIRDocument.Document.Document.FinInstrmRptgTxRpt.Tx
    upstreamTasks:
      - taskName: S3OrLocalFile
        mapped: false
        key: extractor_result

  # Read CSV batches and dispatch the associated dataframes to downstream tasks
  - path: swarm_tasks.tr.feed.unavista.xml_to_unavista_converter:XmlToUnavistaConverter
    name: XmlToUnavistaConverter
    params:
      data_path: UVMiFIRDocument.Document.Document.FinInstrmRptgTxRpt.Tx
      header_data_path: UVMiFIRDocument.UVHeader
      target_s3_path: flows/tr-feed-unavista/
      input_csv_schema:
        "UVMiFIRDocument|Document|Document|FinInstrmRptgTxRpt|Tx|Cxl|TxId": "string"
        "UVMiFIRDocument|Document|Document|FinInstrmRptgTxRpt|Tx|New|TxId": "string"
        "UVMiFIRDocument|Document|Document|FinInstrmRptgTxRpt|Tx|New|Tx|Qty|NmnlVal": "string"
        "UVMiFIRDocument|Document|Document|FinInstrmRptgTxRpt|Tx|New|Tx|Qty|Unit": "string"
        "UVMiFIRDocument|Document|Document|FinInstrmRptgTxRpt|Tx|New|Tx|Qty|MntryVal": "string"
        "UVMiFIRDocument|Document|Document|FinInstrmRptgTxRpt|Tx|Cxl|Tx|Qty|NmnlVal": "string"
        "UVMiFIRDocument|Document|Document|FinInstrmRptgTxRpt|Tx|Cxl|Tx|Qty|Unit": "string"
        "UVMiFIRDocument|Document|Document|FinInstrmRptgTxRpt|Tx|Cxl|Tx|Qty|MntryVal": "string"
        "UVMiFIRDocument|Document|Document|FinInstrmRptgTxRpt|Tx|New|Tx|Pric|Pric|MntryVal|Amt": "string"
      dot_separated_input_schema_keys_pattern: '\|'
    upstreamTasks:
      - taskName: file_url
        mapped: false
        key: file_url
      - taskName: S3OrLocalFile
        mapped: false
        key: source_xml_file_path
      - taskName: XMLFileSplitter
        mapped: true
        key: file_splitter_result
      - taskName: GetUVHeader
        mapped: false
        key: header_file_splitter_result

  - path: swarm_tasks.io.write.aws.s3_upload_file:S3UploadFile
    name: S3UploadOutputFile
    upstreamTasks:
      - taskName: XmlToUnavistaConverter
        mapped: false
        key: upload_target