from swarm.flow.workflow import flatten_flow_source_schema


class TestWorkflow:
    def test_flatten_flow_source_schema(self):
        flow_config = {
            "bundle": {
                "schema": {
                    "root": "string",
                    "foo": {"bar": {"hello": "string", "world": "float"}},
                }
            }
        }
        result = flatten_flow_source_schema(flow_config=flow_config)

        assert result == {
            "bundle": {
                "schema": {
                    "root": "string",
                    "foo.bar.hello": "string",
                    "foo.bar.world": "float",
                }
            }
        }
