from typing import Optional
from unittest import mock

from swarm.flow.runner import FlowRunner


class FakeRegistry:
    def get_flow(self, flow_id: str, stack: Optional[str] = None) -> dict:
        flow = {
            "bundle": {
                "id": flow_id.split(":")[-1],
                "name": "Fake Bundle",
                "platform": False,
                "image": "fakeImage",
                "infra": [{"name": "tenant-data", "type": "ELASTICSEARCH"}],
                "tasks": [],
            },
            "realm": "tenant.dev.steeleye.co",
            "taskOverrides": [{"name": "Task1", "params": {}}],
        }
        return flow

    def get_client(self, client_id: str) -> dict:
        result = {
            "id": client_id,
            "stacks": {"tenant.dev.steeleye.co": "test-stack"},
        }
        return result


class FakeFlow:
    pass


flow = FakeFlow()


class TestBundlelessWorkflow:
    def test_bundleless_runner_with_registry(self):
        registry = FakeRegistry()
        tenant_config = {}
        flow_id = "tenant.dev.steeleye.co:tests.flow.test_bundleless_workflow"
        with mock.patch(
            "swarm.flow.runner.FlowRunner._fetch_tenant_config",
            return_value=tenant_config,
        ):
            runner = FlowRunner(flow_id=flow_id, registry=registry)
            assert isinstance(runner.workflow.flow, FakeFlow)
