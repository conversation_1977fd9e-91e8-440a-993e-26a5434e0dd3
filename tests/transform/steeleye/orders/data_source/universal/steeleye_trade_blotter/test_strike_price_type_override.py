from se_trades_tasks.order_and_tr.feed.order_and_tr_universal_steeleye_trade_blotter.strike_price_type_override import (
    run_strike_price_type_override,
)
from se_trades_tasks.order_and_tr.feed.order_and_tr_universal_steeleye_trade_blotter.strike_price_type_override import (
    StrikePriceTypeOverride,
)


class TestStrikePriceTypeOverride(object):
    """
    Test cases for "VenueOverride" class
    """

    def test_strike_price_type_override(self):
        assert StrikePriceTypeOverride
        assert run_strike_price_type_override
