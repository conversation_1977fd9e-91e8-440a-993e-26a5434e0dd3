import numpy as np
import pandas as pd
import pytest
from se_elastic_schema.components.market.identifier import Identifier
from se_elastic_schema.static.market import IdentifierType

from swarm_tasks.transform.steeleye.orders.data_source.adss.mt4.emir.identifiers import (
    instrument_identifiers,
)
from swarm_tasks.transform.steeleye.orders.data_source.adss.mt4.emir.identifiers.static import (
    ADSSEmirMT4Columns,
)


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """
    creates empty dataframe
    """
    return pd.DataFrame()


@pytest.fixture()
def all_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            ADSSEmirMT4Columns.INSTRUMENT_CLASSIFICATION: [
                "CD",
                "SB",
                pd.NA,
                "CD",
                "SB",
            ],
            ADSSEmirMT4Columns.INSTRUMENT_ID: ["A", "B", pd.NA, "CU", "CO"],
            ADSSEmirMT4Columns.INSTRUMENT: ["CD", "SB", pd.NA, "GBPEUR", "GBPEUR"],
            ADSSEmirMT4Columns.NOTIONAL_CURRENCY_1: ["USD", "GBP", pd.NA, "EUR", "EUR"],
            ADSSEmirMT4Columns.NOTIONAL_CURRENCY_2: ["EUR", "NZD", "NZD", "USD", "USD"],
        }
    )
    return df


@pytest.fixture()
def missing_some_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            ADSSEmirMT4Columns.INSTRUMENT_CLASSIFICATION: [
                "CFD",
                "SB",
                pd.NA,
                "JFTXSC",
            ],
            ADSSEmirMT4Columns.NOTIONAL_CURRENCY_2: ["EUR", "NZD", pd.NA, pd.NA],
        }
    )
    return df


class TestInstrumentIdentifiers(object):
    """
    Test cases for "TestInstrumentIdentifiers" class
    """

    def test_empty_input_df_without_source_columns(self, empty_source_df: pd.DataFrame):
        task = instrument_identifiers.InstrumentIdentifiers(
            name="test-instrument-identifiers"
        )
        result = task.execute(empty_source_df)
        assert result.empty

    def test_all_cols_in_source_df(self, all_col_in_source_df: pd.DataFrame):
        task = instrument_identifiers.InstrumentIdentifiers(
            name="test-instrument-identifiers"
        )
        result = task.execute(all_col_in_source_df)
        expected_data = pd.DataFrame(
            {
                "target": [
                    "XXXXEURUSDFXCFD",
                    "XXXXNZDGBPFXSB",
                    np.nan,
                    "XXXXGBPEURFXCFD",
                    "XXXXGBPEURFXSB",
                ]
            }
        )
        expected_result = pd.DataFrame(index=all_col_in_source_df.index)
        expected_result["marketIdentifiers.instrument"] = (
            expected_data["target"]
            .dropna()
            .apply(
                lambda x: [
                    Identifier(
                        labelId=x,
                        path="instrumentDetails.instrument",
                        type=IdentifierType.OBJECT,
                    ).dict(by_alias=True)
                ]
            )
        )
        assert result.equals(expected_result)

    def test_missing_some_cols_in_source_df(
        self, missing_some_col_in_source_df: pd.DataFrame
    ):
        task = instrument_identifiers.InstrumentIdentifiers(
            name="test-instrument-identifiers"
        )
        result = task.execute(missing_some_col_in_source_df)
        assert result.dropna().empty
