import pandas as pd
import pytest

from swarm_tasks.transform.steeleye.orders.data_source.adss.mt4.emir.identifiers import (
    party_identifiers,
)
from swarm_tasks.transform.steeleye.orders.data_source.adss.mt4.emir.identifiers.static import (
    ADSSEmirMT4Columns,
)


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """
    creates empty dataframe
    """
    df = pd.DataFrame()
    return df


@pytest.fixture()
def all_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            ADSSEmirMT4Columns.BENEFICIARY_ID: ["test1"],
            ADSSEmirMT4Columns.OTHER_COUNTERPARTY_ID: ["test1"],
            "transactionDetails.buySellIndicator": ["BUYI"],
        }
    )
    return df


@pytest.fixture()
def missing_some_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            ADSSEmirMT4Columns.BENEFICIARY_ID: ["test1"],
            "transactionDetails.buySellIndicator": ["BUYI"],
        }
    )
    return df


class TestEmirMT4PartyIdentifiers(object):
    """
    Test cases for "TestPartyIdentifiers" class
    """

    def test_empty_input_df_without_source_columns(self, empty_source_df):
        task = party_identifiers.PartyIdentifiers(name="test-party-identifiers")
        result = task.execute(empty_source_df)
        assert result.empty

    def test_all_col_in_source_df(self, all_col_in_source_df: pd.DataFrame):
        task = party_identifiers.PartyIdentifiers(name="test-party-identifiers")
        expected_result = pd.DataFrame(
            {
                "marketIdentifiers.parties": [
                    "[{'labelId': 'lei:test1', 'path': 'parties.buyer', "
                    "'type': <IdentifierType.ARRAY>}, {'labelId': 'lei:test1', "
                    "'path': 'parties.executingEntity', "
                    "'type': <IdentifierType.OBJECT>}, {'labelId': 'id:test1', "
                    "'path': 'parties.seller', 'type': <IdentifierType.ARRAY>}, "
                    "{'labelId': 'id:test1', 'path': 'parties.trader', "
                    "'type': <IdentifierType.ARRAY>}, {'labelId': 'id:test1', "
                    "'path': 'parties.counterparty', 'type': <IdentifierType.OBJECT>}]"
                ],
                "executingEntityFileIdentifier": "lei:test1",
                "buyerFileIdentifier": "lei:test1",
                "sellerFileIdentifier": "id:test1",
                "traderFileIdentifier": "id:test1",
                "counterpartyFileIdentifier": "id:test1",
            }
        )

        result = task.execute(all_col_in_source_df)
        assert result.astype("str").equals(expected_result.astype("str"))

    def test_missing_some_col_in_source_df(
        self, missing_some_col_in_source_df: pd.DataFrame
    ):
        task = party_identifiers.PartyIdentifiers(name="test-party-identifiers")
        expected_result = pd.DataFrame(
            {
                "marketIdentifiers.parties": [
                    "[{'labelId': 'lei:test1', 'path': 'parties.buyer', "
                    "'type': <IdentifierType.ARRAY>}, {'labelId': 'lei:test1', "
                    "'path': 'parties.executingEntity', "
                    "'type': <IdentifierType.OBJECT>}]"
                ],
                "executingEntityFileIdentifier": "lei:test1",
                "buyerFileIdentifier": "lei:test1",
                "sellerFileIdentifier": "<NA>",
                "traderFileIdentifier": "<NA>",
                "counterpartyFileIdentifier": "<NA>",
            }
        )

        result = task.execute(missing_some_col_in_source_df)
        assert result.astype("str").equals(expected_result.astype("str"))
