import pandas as pd
import pytest

from swarm_tasks.transform.steeleye.orders.data_source.atfx.mt4.html.identifiers import (
    instrument_identifiers,
)


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """
    creates empty dataframe
    """
    df = pd.DataFrame()
    return df


@pytest.fixture()
def all_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame({"Symbol": ["SpotXX", "SpotXY"]})
    return df


@pytest.fixture()
def missing_some_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame({"Symbol": ["SpotXX", "SpotXY", pd.NA]})
    return df


class TestInstrumentIdentifiers(object):
    """
    Test cases for "TestInstrumentIdentifiers" class
    """

    def test_empty_input_df_without_source_columns(self, empty_source_df: pd.DataFrame):
        params = instrument_identifiers.Params(
            asset_class_identifier="FXSPOTCFD", venue_identifier="XXXX"
        )
        task = instrument_identifiers.InstrumentIdentifiers(
            name="test-instrument", params=params
        )
        result = task.execute(empty_source_df, params=params)
        assert result.empty

    def test_all_col_in_source_df(self, all_col_in_source_df: pd.DataFrame):
        params = instrument_identifiers.Params(
            asset_class_identifier="FXSPOTCFD", venue_identifier="XXXX"
        )
        task = instrument_identifiers.InstrumentIdentifiers(
            name="test-instrument", params=params
        )
        data = [
            "[{'labelId': 'XXXXSPOTXXFXSPOTCFD', 'path': 'instrumentDetails.instrument', "
            "'type': <IdentifierType.OBJECT>}]",
            "[{'labelId': 'XXXXSPOTXYFXSPOTCFD', 'path': 'instrumentDetails.instrument', "
            "'type': <IdentifierType.OBJECT>}]",
        ]
        expected_result = pd.Series(data)
        result = task.execute(all_col_in_source_df, params=params)
        assert result.astype("str").equals(expected_result)

    def test_missing_some_col_in_source_df(
        self, missing_some_col_in_source_df: pd.DataFrame
    ):
        data = [
            "[{'labelId': 'XXXXSPOTXXFXSPOTCFD', 'path': 'instrumentDetails.instrument', "
            "'type': <IdentifierType.OBJECT>}]",
            "[{'labelId': 'XXXXSPOTXYFXSPOTCFD', 'path': 'instrumentDetails.instrument', "
            "'type': <IdentifierType.OBJECT>}]",
        ]
        expected_result = pd.Series(data)
        params = instrument_identifiers.Params(
            asset_class_identifier="FXSPOTCFD", venue_identifier="XXXX"
        )
        task = instrument_identifiers.InstrumentIdentifiers(
            name="test-instrument", params=params
        )
        result = task.execute(missing_some_col_in_source_df, params=params)
        assert result.astype("str").equals(expected_result)
