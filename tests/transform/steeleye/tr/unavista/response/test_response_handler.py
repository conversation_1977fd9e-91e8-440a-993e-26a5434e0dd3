from datetime import datetime

import pytest

from swarm_tasks.transform.steeleye.tr.unavista.response.response_handler import Params
from swarm_tasks.transform.steeleye.tr.unavista.response.response_handler import (
    UnavistaResponseHandler,
)


@pytest.fixture()
def params():
    return Params(**{"chunksize": 0, "response_type": "ARM"})


@pytest.fixture()
def all_date_time_values():
    return {
        "submitted": "2022-12-20T13:48:08.447718Z",
        "arm": {"responseReceived": "2022-12-20T14:48:08.447718Z"},
    }


@pytest.fixture()
def submitted_date_none_data():
    return {
        "submitted": None,
        "arm": {"responseReceived": "2022-12-20T14:48:08.447718Z"},
    }


@pytest.fixture()
def arm_response_received_none_data():
    return {
        "submitted": "2022-12-20T13:48:08.447718Z",
        "arm": {"responseReceived": None},
    }


class TestResponseHandler:
    def test_with_datetime_fields(self, params, all_date_time_values):
        task = UnavistaResponseHandler(name="Test", params=params)
        result = task.get_datetime_fields(all_date_time_values)

        # We know the expected values
        assert result == [
            datetime(2022, 12, 20, 13, 48, 8, 447718),
            datetime(2022, 12, 20, 14, 48, 8, 447718),
        ]

    def test_without_submitted_date_time(
        self, mocker, params, submitted_date_none_data
    ):
        task = UnavistaResponseHandler(name="Test", params=params)
        mocker.patch(
            "swarm_tasks.transform.steeleye.tr.unavista.response.response_handler.UnavistaResponseHandler.auditor",
            new_callable=mocker.PropertyMock,
        )
        report_submission_datetime, arm_submission_datetime = task.get_datetime_fields(
            submitted_date_none_data
        )

        # since the reponse process will take sometime, the received
        # value will alwaus be less than the current time
        assert report_submission_datetime < datetime.utcnow()
        assert arm_submission_datetime == datetime(2022, 12, 20, 14, 48, 8, 447718)

    def test_without_arm_response_received_date_time(
        self, mocker, params, arm_response_received_none_data
    ):
        task = UnavistaResponseHandler(name="Test", params=params)
        mocker.patch(
            "swarm_tasks.transform.steeleye.tr.unavista.response.response_handler.UnavistaResponseHandler.auditor",
            new_callable=mocker.PropertyMock,
        )
        report_submission_datetime, arm_submission_datetime = task.get_datetime_fields(
            arm_response_received_none_data
        )

        # since the reponse process will take sometime, the received
        # value will alwaus be less than the current time
        assert arm_submission_datetime < datetime.utcnow()
        assert report_submission_datetime == datetime(2022, 12, 20, 13, 48, 8, 447718)
