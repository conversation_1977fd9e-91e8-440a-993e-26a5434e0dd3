from typing import List
from unittest.mock import patch
from unittest.mock import PropertyMock

import pandas as pd
import pytest
from prefect import context
from prefect.utilities.collections import DotDict
from swarm.conf import SettingsCls
from swarm.task.io.write.elastic.result import ElasticBulkWriterResult

from swarm_tasks.transform.steeleye.tr.unavista.response.update_nca_producer import (
    Params,
)
from swarm_tasks.transform.steeleye.tr.unavista.response.update_nca_producer import (
    Resources,
)
from swarm_tasks.transform.steeleye.tr.unavista.response.update_nca_producer import (
    UpdateNcaProducer,
)


@pytest.fixture()
def params():
    return Params(**{"chunksize": 1000})


@pytest.fixture()
def resources():
    return Resources(**{"es_client_key": "tenant-data"})


@pytest.fixture()
def elastic_bulk_response_df() -> List[ElasticBulkWriterResult]:
    return [
        ElasticBulkWriterResult(
            frame=pd.DataFrame(
                {
                    "file_index": [
                        0,
                        1,
                        2,
                    ],
                    "model": [
                        "RTS22Transaction",
                        "RTS22Transaction",
                        "RTS22Transaction",
                    ],
                    "id": [
                        "12345:2024-03-05:NEWT",
                        "23456:2024-03-05:NEWT",
                        "34567:2024-03-05:NEWT",
                    ],
                }
            ),
            quarantined=False,
            total_bytes=0,
        )
    ]


class TestUpdateNCAProducer:
    @patch.object(
        SettingsCls,
        "realm",
        new_callable=PropertyMock,
        return_value="test.dev.steeleye.co",
    )
    @patch.object(
        SettingsCls,
        "env",
        new_callable=PropertyMock,
        return_value="dev-blue",
    )
    def test_it_can_handle_empty_kwargs(self, mocker, params, resources):
        context.swarm = DotDict()
        context.swarm.file_id = "some_file_id"

        es_mock = mocker.Mock()
        es_mock.meta.id = "&id"
        es_mock.meta.model = "&model"
        es_mock.meta.hash = "&hash"
        es_mock.meta.version = "&version"
        es_mock.scroll.side_effect = es_client_scroll

        with patch.object(UpdateNcaProducer, "get_es_client") as mock_es_client:
            mock_es_client.return_value = es_mock
            task = UpdateNcaProducer(name="Test", params=params, resources=resources)
            result = task.execute(params=params, resources=resources)

        assert result == []

    @patch.object(
        SettingsCls,
        "realm",
        new_callable=PropertyMock,
        return_value="test.dev.steeleye.co",
    )
    @patch.object(
        SettingsCls,
        "env",
        new_callable=PropertyMock,
        return_value="dev-blue",
    )
    def test_it_can_handle_empty_df(self, mocker, params, resources):
        context.swarm = DotDict()
        context.swarm.file_id = "some_file_id_2"

        es_mock = mocker.Mock()
        es_mock.meta.id = "&id"
        es_mock.meta.model = "&model"
        es_mock.meta.hash = "&hash"
        es_mock.meta.version = "&version"
        es_mock.scroll.side_effect = es_client_scroll

        with patch.object(UpdateNcaProducer, "get_es_client") as mock_es_client:
            mock_es_client.return_value = es_mock
            task = UpdateNcaProducer(name="Test", params=params, resources=resources)
            result = task.execute(
                params=params, resources=resources, **{"results": pd.DataFrame()}
            )

        assert result == []

    @patch.object(
        SettingsCls,
        "realm",
        new_callable=PropertyMock,
        return_value="test.dev.steeleye.co",
    )
    @patch.object(
        SettingsCls,
        "env",
        new_callable=PropertyMock,
        return_value="dev-blue",
    )
    def test_it_can_handle_some_data(
        self, mocker, params, resources, elastic_bulk_response_df
    ):
        context.swarm = DotDict()
        context.swarm.file_id = "some_file_id_2"

        es_mock = mocker.Mock()
        es_mock.meta.id = "&id"
        es_mock.meta.model = "&model"
        es_mock.meta.hash = "&hash"
        es_mock.meta.version = "&version"
        es_mock.scroll.side_effect = es_client_scroll

        with patch.object(UpdateNcaProducer, "get_es_client") as mock_es_client:
            mock_es_client.return_value = es_mock
            task = UpdateNcaProducer(name="Test", params=params, resources=resources)
            result = task.execute(
                params=params,
                resources=resources,
                **{"results": elastic_bulk_response_df}
            )

        assert result == []


def es_client_scroll(index: str, **kwargs):
    return pd.DataFrame()
