from pathlib import Path

import pandas as pd
import pytest
from mock import patch
from prefect.engine.signals import FAIL

from swarm_tasks.transform.steeleye.tr.unavista.submit_report.submit_driver import (
    SubmitDriver,
)

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")

NO_TRANSACTION_REPORT = TEST_FILES_DIR.joinpath("tr_report_with_no_transaction.xml")
ONE_TRANSACTION_REPORT = TEST_FILES_DIR.joinpath("tr_report_with_one_transaction.xml")
TWO_TRANSACTION_REPORT = TEST_FILES_DIR.joinpath("tr_report_with_two_transactions.xml")


class TestSubmitDriver:
    def test_get_transaction_record_from_es_returns_df(self, mocker) -> None:
        task = SubmitDriver(name="Test")
        elastic_result_df = pd.DataFrame({"reportDetails.transactionRefNo": ["1"]})
        mocker.patch.object(
            SubmitDriver,
            "get_records_from_elastic",
            return_value=elastic_result_df,
        )

        # es=None doesn't matter because it's mocked
        with patch("time.sleep", return_value=None) as patched_time_sleep:
            result = task.get_transaction_record_from_es_and_reconcile(
                es=None, query={}, index="test", total_transaction_ref_numbers_count=1
            )

        assert result.equals(elastic_result_df)
        assert patched_time_sleep.call_count == 0

    def test_get_transaction_record_from_es_raises_fail(self, mocker) -> None:
        task = SubmitDriver(name="Test")
        mocker.patch.object(
            SubmitDriver,
            "get_records_from_elastic",
            return_value=pd.DataFrame({"reportDetails.transactionRefNo": ["1"]}),
        )

        # es=None doesn't matter because it's mocked
        # the test will attempt to find the record in ES for 10 times and fail
        with patch("time.sleep", return_value=None) as patched_time_sleep:
            with pytest.raises(FAIL) as e:
                task.get_transaction_record_from_es_and_reconcile(
                    es=None,
                    query={},
                    index="test",
                    total_transaction_ref_numbers_count=2,
                )

        assert e.match("Failed to find total Transactions match in Elastic vs In File")
        assert patched_time_sleep.call_count == 10

    def test_parse_xml_and_extract_transaction_ref_no_end_to_end(self) -> None:
        # Case 1: Test with No Transaction in report. Should raise fail
        with pytest.raises(FAIL) as result_one:
            SubmitDriver.parse_xml_and_extract_transaction_ref_no_count(
                NO_TRANSACTION_REPORT.as_posix()
            )
        assert result_one.match("No transaction record found in the report")

        # Case 2: Test with One Transaction
        result_two = SubmitDriver.parse_xml_and_extract_transaction_ref_no_count(
            ONE_TRANSACTION_REPORT.as_posix()
        )
        assert result_two == 1

        # Case 3: Test with Two Transactions
        result_three = SubmitDriver.parse_xml_and_extract_transaction_ref_no_count(
            TWO_TRANSACTION_REPORT.as_posix()
        )
        assert result_three == 2
