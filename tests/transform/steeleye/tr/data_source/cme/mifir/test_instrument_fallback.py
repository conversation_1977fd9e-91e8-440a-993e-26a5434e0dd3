import pandas as pd
import pytest
from se_elastic_schema.static.mifid2 import RTS22TransactionStatus

from swarm_tasks.transform.steeleye.tr.data_source.cme.mifir import (
    instrument_fallback as fb,
)
from swarm_tasks.transform.steeleye.tr.data_source.cme.mifir.instrument_fallback import (
    InstrumentFallback,
)
from swarm_tasks.transform.steeleye.tr.data_source.cme.mifir.static import (
    CmeMifirColumns,
)


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """
    creates empty dataframe
    """
    result = pd.DataFrame()

    return result


@pytest.fixture()
def all_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            CmeMifirColumns.NOTIONAL_CURRENCY_1: ["EUR", "EUR"],
            CmeMifirColumns.NOTIONAL_CURRENCY_2: [pd.NA, pd.NA],
            CmeMifirColumns.STRIKE_PRICE_CURRENCY: [pd.NA, "USD"],
            CmeMifirColumns.INSTRUMENT_ID: [pd.NA, pd.NA],
            CmeMifirColumns.INSTRUMENT_NAME: [
                "GERMANY 30 ROLLING BUX CFD",
                "EU 50 ROLLING BUX CFD",
            ],
            CmeMifirColumns.STRIKE_PRICE_TYPE: [pd.NA, pd.NA],
            CmeMifirColumns.UNDERLYING_INDEX_NAME: [
                "Dax 30 Index",
                "Eurostoxx 50 Index",
            ],
            CmeMifirColumns.UNDERLYING_INDEX_TERM: [pd.NA, pd.NA],
            CmeMifirColumns.UNDERLYING_INDEX_ID: [pd.NA, pd.NA],
            CmeMifirColumns.UNDERLYING_INSTRUMENT_ID: [pd.NA, pd.NA],
            CmeMifirColumns.DELIVERY_TYPE: ["CASH", "CASH"],
            CmeMifirColumns.PRICE_TYPE: ["MONE", "MONE"],
            CmeMifirColumns.QUANTITY_TYPE: ["UNIT", "UNIT"],
            CmeMifirColumns.EXPIRY_DATE: [pd.NA, pd.NA],
            CmeMifirColumns.PRICE_MULTIPLIER: ["25", "10"],
            CmeMifirColumns.STRIKE_PRICE: [pd.NA, pd.NA],
            CmeMifirColumns.INSTRUMENT_CLASSIFICATION: ["JEIXCC", "JEIXCC"],
            CmeMifirColumns.VENUE: ["XXXX", "XXXX"],
            "instrumentDetails.instrument": ["instrument1", pd.NA],
            "reportDetails.reportStatus": ["NEWT", "NEWT"],
            "transactionDetails.priceNotation": ["MONE", "MONE"],
            "transactionDetails.quantityNotation": ["UNIT", "UNIT"],
            "workflow.eligibility": [pd.NA, pd.NA],
            "workflow.status": [pd.NA, pd.NA],
        }
    )
    return df


@pytest.fixture()
def instrument_class_regex_override_status_series() -> pd.Series:
    transaction_status = [
        RTS22TransactionStatus.REPORTABLE,
        RTS22TransactionStatus.REPORTABLE,
        RTS22TransactionStatus.NON_REPORTABLE,
        RTS22TransactionStatus.REPORTABLE_USER_OVERRIDE,
        RTS22TransactionStatus.REPORTABLE_USER_OVERRIDE,
        RTS22TransactionStatus.REPORTABLE_USER_OVERRIDE,
    ]

    return pd.Series(transaction_status)


@pytest.fixture()
def missing_some_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            CmeMifirColumns.NOTIONAL_CURRENCY_1: ["EUR", "EUR"],
            CmeMifirColumns.INSTRUMENT_NAME: [
                "GERMANY 30 ROLLING BUX CFD",
                "EU 50 ROLLING BUX CFD",
            ],
            CmeMifirColumns.STRIKE_PRICE_TYPE: [pd.NA, pd.NA],
            CmeMifirColumns.UNDERLYING_INDEX_NAME: [
                "Dax 30 Index",
                "Eurostoxx 50 Index",
            ],
            CmeMifirColumns.UNDERLYING_INDEX_TERM: [pd.NA, pd.NA],
            CmeMifirColumns.UNDERLYING_INDEX_ID: [pd.NA, pd.NA],
            CmeMifirColumns.UNDERLYING_INSTRUMENT_ID: [pd.NA, pd.NA],
            CmeMifirColumns.PRICE_TYPE: ["MONE", "MONE"],
            CmeMifirColumns.QUANTITY_TYPE: ["UNIT", "UNIT"],
            CmeMifirColumns.EXPIRY_DATE: [pd.NA, pd.NA],
            CmeMifirColumns.PRICE_MULTIPLIER: ["25", "10"],
            CmeMifirColumns.STRIKE_PRICE: [pd.NA, pd.NA],
            CmeMifirColumns.INSTRUMENT_CLASSIFICATION: ["JEIXCC", "JEIXCC"],
            CmeMifirColumns.VENUE: ["XXXX", "XXXX"],
            "instrumentDetails.instrument": ["instrument1", pd.NA],
            "reportDetails.reportStatus": ["NEWT", "NEWT"],
            "transactionDetails.priceNotation": ["MONE", "MONE"],
            "transactionDetails.quantityNotation": ["UNIT", "UNIT"],
            "workflow.eligibility": [pd.NA, pd.NA],
            "workflow.status": [pd.NA, pd.NA],
        }
    )
    return df


@pytest.fixture()
def underlying_instrument_data_df() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "NOTIONALCURRENCY1": ["EUR"],
            "NOTIONALCURRENCY2": [pd.NA],
            "STRIKEPRICECURRENCY": ["USD"],
            "INSTRUMENTIDENTIFICATIONCODE": [pd.NA],
            "INSTRUMENTFULLNAME": ["EU 50 ROLLING BUX CFD"],
            "STRIKEPRICETYPE": [pd.NA],
            "UNDERLYINGINDEXNAME": ["-ESTRON"],
            "TERMOFTHEUNDERLYINGINDEX": ["1DAYS"],
            "UNDERLYINGINSTRUMENTCODE": [pd.NA],
            "DELIVERYTYPE": ["CASH"],
            "PRICETYPE": ["MONE"],
            "QUANTITYTYPE": ["UNIT"],
            "EXPIRYDATE": [pd.NA],
            "PRICEMULTIPLIER": ["10"],
            "STRIKEPRICE": [pd.NA],
            "INSTRUMENTCLASSIFICATION": ["JEIXCC"],
            "VENUE": ["XXXX"],
            "instrumentDetails.instrument": [pd.NA],
            "reportDetails.reportStatus": ["NEWT"],
            "transactionDetails.priceNotation": ["MONE"],
            "transactionDetails.quantityNotation": ["UNIT"],
            "temp.maturityDate": [pd.NA],
            "OPTIONEXERCISESTYLE": [pd.NA],
            "OPTIONTYPE": [pd.NA],
        }
    )


@pytest.fixture()
def swap_data_df() -> pd.DataFrame:
    return pd.DataFrame(
        {
            CmeMifirColumns.UNDERLYING_INDEX_NAME: [
                ",",
                ",+ABC",
                "+ABC;-ABC;+ABC",
                pd.NA,
                "+ABC;-ABC;+ABC",
            ],
            CmeMifirColumns.UNDERLYING_INDEX_ID: [
                ",,",
                pd.NA,
                pd.NA,
                "-ABC,+ABC;-ANC",
                pd.NA,
            ],
            CmeMifirColumns.UNDERLYING_INDEX_TERM: [",,", pd.NA, ",,", pd.NA, ",,,,"],
        }
    )


@pytest.fixture()
def underlying_instrument_data_expected_result() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "ext.underlyingInstruments": [
                [
                    {
                        "derivative.underlyingIndexName": "ESTRON",
                        "derivative.underlyingIndexTerm": "1DAYS",
                    }
                ]
            ],
            "derivative.underlyingInstruments": [[]],
            "derivative.underlyingIndexName": ["ESTRON"],
            "derivative.underlyingIndexTerm": ["1DAYS"],
        }
    )


class TestInstrumentFallBack(object):
    """
    Test cases for "InstrumentFallBack" class
    """

    def test_empty_input_df_without_source_columns(self, empty_source_df: pd.DataFrame):
        params = fb.Params()
        task = fb.InstrumentFallback(name="test-instrument", params=params)
        result = task.execute(empty_source_df, params)
        assert result.empty

    def test_all_col_in_source_df(self, all_col_in_source_df: pd.DataFrame):
        data = {
            "instrumentDetails.instrument": [
                "instrument1",
                {
                    "derivative.deliveryType": "CASH",
                    "instrumentClassification": "JEIXCC",
                    "instrumentFullName": "EU 50 ROLLING BUX CFD",
                    "notionalCurrency1": "EUR",
                    "ext.quantityNotation": "UNIT",
                    "derivative.strikePriceCurrency": "USD",
                    "ext.priceNotation": "MONE",
                    "derivative.priceMultiplier": "10",
                    "ext.onFIRDS": False,
                    "commoditiesOrEmissionAllowanceDerivativeInd": False,
                    "ext.underlyingInstruments": [
                        {"derivative.underlyingIndexName": "Eurostoxx 50 Index"}
                    ],
                    "derivative.underlyingInstruments": [],
                    "derivative.underlyingIndexName": "Eurostoxx 50 Index",
                    "ext.mifirEligible": True,
                    "isCreatedThroughFallback": True,
                },
            ],
            "workflow.eligibility": [
                pd.NA,
                {
                    "eligible": True,
                    "onFirds": False,
                    "underlyingOnFirds": False,
                    "totv": False,
                    "utotv": True,
                    "executionVenue": "XXXX",
                },
            ],
            "workflow.status": [
                pd.NA,
                RTS22TransactionStatus.REPORTABLE_USER_OVERRIDE,
            ],
            "transactionDetails.swapDirectionalities": [["Other"], ["Other"]],
        }
        params = fb.Params()
        expected_result = pd.DataFrame(data)
        task = fb.InstrumentFallback(name="test-instrument", params=params)
        result = task.execute(all_col_in_source_df, params)
        assert result.equals(expected_result)

    def test_instrument_class_regex_override_status_series(
        self, instrument_class_regex_override_status_series: pd.Series
    ):
        df = pd.DataFrame(
            {
                "instrumentDetails.instrument": [
                    {
                        "instrumentIdCode": "549300W3IAAAAR2OUL22",
                        "instrumentClassification": "FFIC42",
                    },
                    {
                        "instrumentIdCode": "549300W3IAAAAR2OUL22",
                        "instrumentClassification": "ES44ABCD",
                    },
                    pd.NA,
                    pd.NA,
                    pd.NA,
                    pd.NA,
                ],
                "workflow.status": [
                    RTS22TransactionStatus.REPORTABLE,
                    RTS22TransactionStatus.REPORTABLE,
                    pd.NA,
                    RTS22TransactionStatus.REPORTABLE,
                    pd.NA,
                    pd.NA,
                ],
                CmeMifirColumns.INSTRUMENT_CLASSIFICATION: [
                    "FFIC42",
                    "ES44ABCD",
                    "ES44ABCD",
                    "ES44ABCD",
                    "ES44ABCD",
                    pd.NA,
                ],
                CmeMifirColumns.INSTRUMENT_ID: [
                    "549300W3IAAAAR2OUL22",
                    "549300W3IVAAAA2OUL22",
                    "549300W3IVAAAA2OUL22",
                    pd.NA,
                    pd.NA,
                    pd.NA,
                ],
                CmeMifirColumns.UNDERLYING_INDEX_ID: [
                    pd.NA,
                    pd.NA,
                    pd.NA,
                    pd.NA,
                    pd.NA,
                    pd.NA,
                ],
                CmeMifirColumns.UNDERLYING_INDEX_NAME: [
                    pd.NA,
                    pd.NA,
                    pd.NA,
                    pd.NA,
                    pd.NA,
                    pd.NA,
                ],
                CmeMifirColumns.UNDERLYING_INDEX_TERM: [
                    pd.NA,
                    pd.NA,
                    pd.NA,
                    pd.NA,
                    pd.NA,
                    pd.NA,
                ],
            }
        )

        params = fb.Params(instrument_classification_regex="^ES[a-zA-Z0-9_]*$")
        task = fb.InstrumentFallback(name="test-instrument", params=params)
        result = task.execute(df, params)
        assert result[fb.WorkflowFields.STATUS].equals(
            instrument_class_regex_override_status_series
        )

    def test_missing_some_col_in_source_df(
        self, missing_some_col_in_source_df: pd.DataFrame
    ):
        params = fb.Params()
        task = fb.InstrumentFallback(name="test-instrument", params=params)
        data = {
            "instrumentDetails.instrument": [
                "instrument1",
                {
                    "instrumentClassification": "JEIXCC",
                    "instrumentFullName": "EU 50 ROLLING BUX CFD",
                    "notionalCurrency1": "EUR",
                    "ext.quantityNotation": "UNIT",
                    "ext.priceNotation": "MONE",
                    "derivative.priceMultiplier": "10",
                    "ext.onFIRDS": False,
                    "commoditiesOrEmissionAllowanceDerivativeInd": False,
                    "ext.underlyingInstruments": [
                        {"derivative.underlyingIndexName": "Eurostoxx 50 Index"}
                    ],
                    "derivative.underlyingInstruments": [],
                    "derivative.underlyingIndexName": "Eurostoxx 50 Index",
                    "ext.mifirEligible": True,
                    "isCreatedThroughFallback": True,
                },
            ],
            "workflow.eligibility": [
                pd.NA,
                {
                    "eligible": True,
                    "onFirds": False,
                    "underlyingOnFirds": False,
                    "totv": False,
                    "utotv": True,
                    "executionVenue": "XXXX",
                },
            ],
            "workflow.status": [
                pd.NA,
                RTS22TransactionStatus.REPORTABLE_USER_OVERRIDE,
            ],
            "transactionDetails.swapDirectionalities": [["Other"], ["Other"]],
        }
        expected_result = pd.DataFrame(data)
        result = task.execute(missing_some_col_in_source_df, params)
        assert result.equals(expected_result)

    def test_underlying_instrument_data(
        self, underlying_instrument_data_df, underlying_instrument_data_expected_result
    ) -> None:
        result = InstrumentFallback._get_underlying_instrument_data(
            underlying_instrument_data_df
        )
        assert result.equals(underlying_instrument_data_expected_result)

    def test_get_transaction_details_swap_data(self, swap_data_df):
        result = InstrumentFallback._get_transaction_details_swap_data(swap_data_df)
        expected_result = pd.Series(
            [
                ["Other", "Other", "Other"],
                ["Other", "Swap In"],
                ["Swap In", "Swap Out", "Swap In"],
                ["Swap Out", "Swap In", "Swap Out"],
                ["Swap In", "Swap Out", "Swap In", "Other", "Other"],
            ]
        )
        assert expected_result.equals(result)
