from pathlib import Path

import numpy as np
import pandas as pd
import pytest

from swarm_tasks.transform.steeleye.tr.data_source.cme.mifir.parties_fallback import (
    PartiesFallback,
)
from swarm_tasks.transform.steeleye.tr.data_source.cme.mifir.parties_fallback import (
    PartyField,
)
from swarm_tasks.transform.steeleye.tr.data_source.cme.mifir.parties_fallback import (
    PASS_THROUGH_COLUMNS,
)
from swarm_tasks.transform.steeleye.tr.data_source.cme.mifir.parties_fallback import (
    PersonField,
)
from swarm_tasks.transform.steeleye.tr.data_source.cme.mifir.parties_fallback import (
    Resources,
)
from swarm_tasks.transform.steeleye.tr.data_source.cme.mifir.static import (
    CmeMifirColumns,
)


SCRIPT_PATH = Path(__file__).parent
TESTFILE_PATH = SCRIPT_PATH.joinpath(r"data/party_fallback_anonymised_test_data.csv")
ES_CLIENT_KEY = "tenant-data"


@pytest.fixture
def empty_source_df() -> pd.DataFrame:
    result = pd.DataFrame()
    return result


@pytest.fixture
def source_frame():
    df = pd.read_csv(TESTFILE_PATH)
    return df


@pytest.fixture
def resource() -> Resources:
    resource = Resources(es_client_key=ES_CLIENT_KEY)
    return resource


@pytest.fixture
def task_with_mocked_es_client(mocker, resource):
    es_mock = mocker.Mock()
    es_mock.meta.key = "&key"
    task = PartiesFallback(name="PartiesFallback", resources=resource)
    task.clients = {ES_CLIENT_KEY: es_mock}
    return task


class MifirIdType:
    N = "N"
    L = "L"


class TestPartyFallback:
    def test_empty_input_without_source_columns_returns_empty_result(
        self, empty_source_df
    ):
        task = PartiesFallback(name="PartyFallback")
        result = task.execute(empty_source_df)
        assert result.empty

    def test_input_missing_some_required_columns_returns_nan_party_columns(
        self, source_frame, task_with_mocked_es_client, resource
    ):
        keep_cols = [
            CmeMifirColumns.BUYER_FIRST_NAMES,
            CmeMifirColumns.BUYER_SURNAMES,
            CmeMifirColumns.BUYER_ID,
            CmeMifirColumns.BUYER_IDENTIFICATION_CODE_TYPE,
            CmeMifirColumns.BUYER_DATE_OF_BIRTH,
            CmeMifirColumns.BUYER_COUNTRY_OF_THE_BRANCH,
        ]
        source_frame = source_frame.drop(
            columns=[c for c in source_frame.columns if c not in keep_cols]
        )
        result = task_with_mocked_es_client.execute(source_frame, resources=resource)

        assert result[PartyField.BUYER].notna().any()
        for column in PartyField:
            if column == PartyField.BUYER:
                continue

            assert result[column].isna().all()

    def test_nan_values_in_all_required_columns_returns_empty_pass_through_columns(
        self, source_frame, task_with_mocked_es_client, resource
    ):
        for column in source_frame.columns:
            source_frame[column] = pd.NA

        # task = PartiesFallback(name="PartyFallback")
        result = task_with_mocked_es_client.execute(source_frame, resources=resource)
        # check that all pass through columns have all nan
        assert result[PASS_THROUGH_COLUMNS].isna().all().all()

    def test_format_party(self, source_frame):
        task = PartiesFallback(name="PartyFallback")

        result = task._format_party(
            source_frame,
            CmeMifirColumns.BUYER_FIRST_NAMES,
            CmeMifirColumns.BUYER_SURNAMES,
            CmeMifirColumns.BUYER_ID,
            CmeMifirColumns.BUYER_IDENTIFICATION_CODE_TYPE,
            CmeMifirColumns.BUYER_DATE_OF_BIRTH,
            CmeMifirColumns.BUYER_COUNTRY_OF_THE_BRANCH,
        )

        assert (
            result[PersonField.MIFIR_ID_SUB_TYPE]
            .dropna()
            .isin(["LEI", "CONCAT", "NIDN"])
            .all()
        )
        assert result[PersonField.MIFIR_ID_TYPE].dropna().isin(["L", "N"]).all()

    def test_format_party_within_firm(
        self, source_frame, task_with_mocked_es_client, resource
    ):
        task = PartiesFallback(name="PartyFallback")
        id_types = np.random.choice(
            ["LEI", "CONCAT", "NIDN", "ALGO"], len(source_frame)
        )
        source_frame[CmeMifirColumns.INVESTMENT_DECISION_WITHIN_FIRM_TYPE] = pd.Series(
            id_types
        )

        result = task._format_party_within_firm(
            source_frame,
            CmeMifirColumns.INVESTMENT_DECISION_ID,
            CmeMifirColumns.INVESTMENT_DECISION_ID,
            CmeMifirColumns.INVESTMENT_DECISION_WITHIN_FIRM_TYPE,
            CmeMifirColumns.INVESTMENT_DECISION_COUNTRY_OF_BRANCH,
            meta_key="&key",
        )

        assert (
            result.loc[result[PersonField.MIFIR_ID].notna(), PersonField.UNIQUE_IDS]
            .notna()
            .all()
        )
        assert (
            result[PersonField.MIFIR_ID_TYPE]
            .dropna()
            .isin([MifirIdType.L, MifirIdType.N])
            .all()
        )
        if (
            source_frame[CmeMifirColumns.INVESTMENT_DECISION_WITHIN_FIRM_TYPE]
            .isin(["LEI", "CONCAT", "NIDN"])
            .any()
        ):
            assert (
                result[PersonField.MIFIR_ID_SUB_TYPE]
                .dropna()
                .isin(["LEI", "CONCAT", "NIDN"])
                .all()
            )
