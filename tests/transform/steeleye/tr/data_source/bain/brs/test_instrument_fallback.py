from typing import Type

import numpy as np
import pandas as pd
import pytest

from swarm_tasks.transform.steeleye.tr.data_source.bain.brs import (
    instrument_fallback as fb,
)
from swarm_tasks.transform.steeleye.tr.data_source.bain.brs.instrument_fallback import (
    Params,
)
from swarm_tasks.transform.steeleye.tr.data_source.bain.brs.static import BainBRSColumns


@pytest.fixture()
def params() -> Type[Params]:
    Params = fb.Params
    Params.price_notation_map = {
        "MNTRYVALAMT": "MONE",
        "PCTG": "PERC",
        "BSISPTS": "BAPO",
        "YLD": "YIEL",
    }
    Params.quantity_notation_map = {
        "MONETARYVALUE": "MONE",
        "NOMINALVALUE": "NOML",
        "UNIT": "UNIT",
    }
    return Params


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """
    creates empty dataframe
    """
    result = pd.DataFrame()

    return result


@pytest.fixture()
def all_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            BainBRSColumns.DELIVERY_TYPE: ["CASH", "CASH"],
            BainBRSColumns.EXPIRY_DATE: ["20201101", "20201102"],
            BainBRSColumns.INSTRUMENT_CLASSIFICATION: ["JEIXCC", "JEIXCC"],
            BainBRSColumns.INSTRUMENT_ID: ["ID1", pd.NA],
            BainBRSColumns.INSTRUMENT_NAME: [
                "GERMANY 30 ROLLING BUX CFD",
                "EU 50 ROLLING BUX CFD",
            ],
            BainBRSColumns.NOTIONAL_CURRENCY_1: ["EUR", "EUR"],
            BainBRSColumns.PRICE_MULTIPLIER: ["25", "10"],
            BainBRSColumns.PRICE_TYPE: ["MONE", "MONE"],
            BainBRSColumns.QUANTITY_TYPE: ["UNIT", "UNIT"],
            BainBRSColumns.STRIKE_PRICE: [4, 3],
            BainBRSColumns.STRIKE_PRICE_CURRENCY: [pd.NA, "USD"],
            BainBRSColumns.STRIKE_PRICE_TYPE: [pd.NA, pd.NA],
            BainBRSColumns.UNDERLYING_INDEX_ID: [pd.NA, pd.NA],
            BainBRSColumns.UNDERLYING_INDEX_NAME: [
                "Dax 30 Index",
                "Eurostoxx 50 Index",
            ],
            BainBRSColumns.UNDERLYING_INDEX_TERM: [pd.NA, pd.NA],
            BainBRSColumns.UNDERLYING_INSTRUMENT_ID: [pd.NA, pd.NA],
            "instrumentDetails.instrument": ["instrument1", pd.NA],
        }
    )
    return df


@pytest.fixture()
def missing_some_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            BainBRSColumns.DELIVERY_TYPE: ["CASH", "CASH"],
            BainBRSColumns.EXPIRY_DATE: ["20201101", "20201102"],
            BainBRSColumns.INSTRUMENT_CLASSIFICATION: ["JEIXCC", "JEIXCC"],
            BainBRSColumns.INSTRUMENT_ID: ["ID1", pd.NA],
            BainBRSColumns.NOTIONAL_CURRENCY_1: ["EUR", "EUR"],
            BainBRSColumns.PRICE_MULTIPLIER: ["25", "10"],
            BainBRSColumns.PRICE_TYPE: ["MONE", "MONE"],
            BainBRSColumns.QUANTITY_TYPE: ["UNIT", "UNIT"],
            BainBRSColumns.STRIKE_PRICE: [4, 3],
            BainBRSColumns.STRIKE_PRICE_CURRENCY: [pd.NA, "USD"],
            BainBRSColumns.STRIKE_PRICE_TYPE: [pd.NA, pd.NA],
            BainBRSColumns.UNDERLYING_INDEX_ID: [pd.NA, pd.NA],
            "instrumentDetails.instrument": ["instrument1", pd.NA],
        }
    )
    return df


class TestInstrumentFallBack(object):
    """
    Test cases for "InstrumentFallBack" class
    """

    def test_empty_input_df_without_source_columns(
        self, empty_source_df: pd.DataFrame, params: Params
    ):
        task = fb.InstrumentFallback(name="test-instrument", params=params)
        result = task.execute(empty_source_df, params)
        assert result.empty

    def test_all_col_in_source_df(
        self, all_col_in_source_df: pd.DataFrame, params: Params
    ):
        expected_result = pd.DataFrame(
            {
                "instrumentDetails.instrument": [
                    "instrument1",
                    {
                        "derivative.deliveryType": "CASH",
                        "derivative.expiryDate": "20201102",
                        "instrumentClassification": "JEIXCC",
                        "instrumentFullName": "EU 50 ROLLING BUX CFD",
                        "notionalCurrency1": "EUR",
                        "derivative.priceMultiplier": "10",
                        "ext.quantityNotation": "UNIT",
                        "derivative.strikePrice": 3,
                        "derivative.strikePriceCurrency": "EUR",
                        "ext.onFIRDS": False,
                        "ext.underlyingInstruments": [
                            {"derivative.underlyingIndexName": "Eurostoxx 50 Index"}
                        ],
                        "derivative.underlyingInstruments": [],
                        "derivative.underlyingIndexName": "Eurostoxx 50 Index",
                        "isCreatedThroughFallback": True,
                    },
                ],
                "workflow.eligibility": [
                    np.nan,
                    {
                        "eligible": True,
                        "onFirds": False,
                        "totv": False,
                        "utotv": True,
                        "underlyingOnFirds": True,
                    },
                ],
                "workflow.status": [np.nan, "REPORTABLE_USER_OVERRIDE"],
            }
        )

        task = fb.InstrumentFallback(name="test-instrument", params=params)
        result = task.execute(all_col_in_source_df, params)
        assert result.equals(expected_result)

    def test_missing_some_col_in_source_df(
        self, missing_some_col_in_source_df: pd.DataFrame, params: Params
    ):
        task = fb.InstrumentFallback(name="test-instrument", params=params)
        expected_result = pd.DataFrame(
            {
                "instrumentDetails.instrument": [
                    "instrument1",
                    {
                        "derivative.deliveryType": "CASH",
                        "derivative.expiryDate": "20201102",
                        "instrumentClassification": "JEIXCC",
                        "notionalCurrency1": "EUR",
                        "derivative.priceMultiplier": "10",
                        "ext.quantityNotation": "UNIT",
                        "derivative.strikePrice": 3,
                        "derivative.strikePriceCurrency": "EUR",
                        "ext.onFIRDS": False,
                        "ext.underlyingInstruments": [],
                        "derivative.underlyingInstruments": [],
                        "isCreatedThroughFallback": True,
                    },
                ],
                "workflow.eligibility": [
                    np.nan,
                    {
                        "eligible": True,
                        "onFirds": False,
                        "totv": False,
                        "utotv": True,
                        "underlyingOnFirds": True,
                    },
                ],
                "workflow.status": [np.nan, "REPORTABLE_USER_OVERRIDE"],
            }
        )
        result = task.execute(missing_some_col_in_source_df, params)
        assert result.equals(expected_result)
