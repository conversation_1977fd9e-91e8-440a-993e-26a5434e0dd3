import numpy as np
import pandas as pd
import pytest

from swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter import static
from swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter import (
    up_front,
)


SteelEyeTradeBlotterColumns = static.SteelEyeTradeBlotterColumns
TRX_DTL_UP_FRONT_PAYMENT = up_front.TRX_DTL_UP_FRONT_PAYMENT
TRX_DTL_UP_FRONT_PAYMENT_CCY = up_front.TRX_DTL_UP_FRONT_PAYMENT_CCY


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """
    creates empty dataframe
    """
    df = pd.DataFrame()
    return df


@pytest.fixture()
def missing_source_col_in_source_df() -> pd.DataFrame:
    """
    creates dataframe without following source columns -
        SteelEyeTradeBlotterColumns.INSTRUMENT_ASSET_CLASS
        SteelEyeTradeBlotterColumns.INSTRUMENT_CLASSIFICATION
    """
    df = pd.DataFrame(
        {"test_col1": ["test1", "test2"], "test_col2": ["test1", "test2"]}
    )
    return df


@pytest.fixture()
def missing_transform_col_in_source_df() -> pd.DataFrame:
    """
    creates dataframe without following transform columns -
        SteelEyeTradeBlotterColumns.UPFRONT_PAYMENT
        SteelEyeTradeBlotterColumns.NET_AMOUNT
        SteelEyeTradeBlotterColumns.PRICE_CURRENCY
    """
    df = pd.DataFrame(
        {
            SteelEyeTradeBlotterColumns.INSTRUMENT_ASSET_CLASS: [
                "CDX",
                "test1",
                "test2",
            ],
            SteelEyeTradeBlotterColumns.INSTRUMENT_CLASSIFICATION: [
                "test1",
                "test2",
                "SC",
            ],
        }
    )
    return df


@pytest.fixture()
def nan_in_source_col_in_source_df() -> pd.DataFrame:
    """
    creates dataframe with nan in source columns
    """
    df = pd.DataFrame(
        {
            SteelEyeTradeBlotterColumns.INSTRUMENT_ASSET_CLASS: [
                np.nan,
                "test1",
                "test2",
            ],
            SteelEyeTradeBlotterColumns.INSTRUMENT_CLASSIFICATION: [
                "test1",
                "test2",
                np.nan,
            ],
        }
    )
    return df


@pytest.fixture()
def all_combination_of_data_in_source_df() -> pd.DataFrame:
    """
    creates dataframe with following required columns and data
        SteelEyeTradeBlotterColumns.INSTRUMENT_ASSET_CLASS
        SteelEyeTradeBlotterColumns.INSTRUMENT_CLASSIFICATION
        SteelEyeTradeBlotterColumns.UPFRONT_PAYMENT
        SteelEyeTradeBlotterColumns.NET_AMOUNT
        SteelEyeTradeBlotterColumns.PRICE_CURRENCY
    """
    df = pd.DataFrame(
        {
            SteelEyeTradeBlotterColumns.INSTRUMENT_ASSET_CLASS: [
                "CDX",
                "test1",
                "test2",
                np.nan,
                np.nan,
                np.nan,
            ],
            SteelEyeTradeBlotterColumns.INSTRUMENT_CLASSIFICATION: [
                "test1",
                "test2",
                "SC",
                np.nan,
                np.nan,
                np.nan,
            ],
            up_front.ASSET_CLASS_COLUMN: [
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
                "FX OPTION",
                "FX OPTION",
            ],
            SteelEyeTradeBlotterColumns.UPFRONT_PAYMENT: [
                0.205,
                np.nan,
                0.345,
                None,
                101,
                None,
            ],
            SteelEyeTradeBlotterColumns.NET_AMOUNT: [
                np.nan,
                1228500,
                None,
                np.nan,
                None,
                102,
            ],
            SteelEyeTradeBlotterColumns.PRICE_CURRENCY: [
                "USD",
                "GBP",
                "EUR",
                np.nan,
                np.nan,
                np.nan,
            ],
            SteelEyeTradeBlotterColumns.UPFRONT_PAYMENT_CURRENCY: [
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
                "USc",
                pd.NA,
            ],
            SteelEyeTradeBlotterColumns.QUANTITY_CURRENCY: [
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
                "USD",
            ],
        }
    )
    return df


class TestTrxDtlUpFront(object):
    """
    Test cases for "TrxDtlUpFront" class
    """

    def test_empty_input_df_without_source_columns(self, empty_source_df, mocker):
        task = up_front.TrxDtlUpFront(name="test-upfront")
        result = task.execute(empty_source_df)
        assert result.empty

    def test_missing_source_col_in_source_df(
        self, missing_source_col_in_source_df, mocker
    ):
        task = up_front.TrxDtlUpFront(name="test-upfront")
        expected_result = pd.DataFrame(
            {
                TRX_DTL_UP_FRONT_PAYMENT: [np.nan, np.nan],
                TRX_DTL_UP_FRONT_PAYMENT_CCY: [np.nan, np.nan],
            }
        )

        result = task.execute(missing_source_col_in_source_df)
        assert result.equals(expected_result)

    def test_nan_in_source_col_in_source_df(
        self, nan_in_source_col_in_source_df, mocker
    ):
        task = up_front.TrxDtlUpFront(name="test-upfront")
        expected_result = pd.DataFrame(
            {
                TRX_DTL_UP_FRONT_PAYMENT: [np.nan, np.nan, np.nan],
                TRX_DTL_UP_FRONT_PAYMENT_CCY: [np.nan, np.nan, np.nan],
            }
        )
        result = task.execute(nan_in_source_col_in_source_df)
        assert result.equals(expected_result)

    def test_missing_transform_col_in_source_df(
        self, missing_transform_col_in_source_df, mocker
    ):
        task = up_front.TrxDtlUpFront(name="test-upfront")
        # result = task.execute(empty_source_df)
        with pytest.raises(KeyError) as _:
            _ = task.execute(missing_transform_col_in_source_df)

    def test_1_all_combination_of_data_in_source_df(
        self, all_combination_of_data_in_source_df, mocker
    ):
        task = up_front.TrxDtlUpFront(name="test-upfront")
        result = task.execute(all_combination_of_data_in_source_df)
        expected_result = pd.DataFrame(
            {
                TRX_DTL_UP_FRONT_PAYMENT: [0.205, np.nan, 0.345, np.nan, 101, 102],
                TRX_DTL_UP_FRONT_PAYMENT_CCY: [
                    "USD",
                    np.nan,
                    "EUR",
                    np.nan,
                    "USD",
                    "USD",
                ],
            }
        )
        assert result.equals(expected_result)
