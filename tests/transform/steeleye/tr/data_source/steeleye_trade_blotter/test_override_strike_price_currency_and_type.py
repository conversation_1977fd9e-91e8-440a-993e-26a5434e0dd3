import numpy as np
import pandas as pd
import pytest
from prefect.engine import signals

from swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.override_strike_price_currency_and_type import (  # noqa E501
    OverrideStrikePriceCurrencyAndType,
)
from swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.override_strike_price_currency_and_type import (  # noqa E501
    Params,
)

INSTRUMENT_DETAILS_INSTRUMENT = "instrumentDetails.instrument"
DERIVATIVE_STRIKE_PRICE_CURRENCY = "derivative.strikePriceCurrency"
EXT_STRIKE_PRICE_TYPE = "ext.strikePriceType"

SOURCE_STRIKE_PRICE_TYPE = "source_strike_price_type"
SOURCE_STRIKE_PRICE_CURRENCY = "source_strike_price_currency"


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """Represents an empty source frame"""
    df = pd.DataFrame()
    return df


@pytest.fixture()
def source_dataframe() -> pd.DataFrame:
    """Represents a source dataframe with data"""

    df = pd.DataFrame(
        [
            # Record 1: strike price currency not null, strike price type ""
            {
                INSTRUMENT_DETAILS_INSTRUMENT: {
                    DERIVATIVE_STRIKE_PRICE_CURRENCY: "USD",
                    EXT_STRIKE_PRICE_TYPE: "",
                },
                SOURCE_STRIKE_PRICE_TYPE: "MntryVal",
                SOURCE_STRIKE_PRICE_CURRENCY: "EUR",
            },
            # Record 2: strike price currency not null, strike price type np.nan
            {
                INSTRUMENT_DETAILS_INSTRUMENT: {
                    DERIVATIVE_STRIKE_PRICE_CURRENCY: "USD",
                    EXT_STRIKE_PRICE_TYPE: np.nan,
                },
                SOURCE_STRIKE_PRICE_TYPE: "MntryVal",
                SOURCE_STRIKE_PRICE_CURRENCY: "EUR",
            },
            # Record 3: strike price currency pd.NA, strike price type not null
            {
                INSTRUMENT_DETAILS_INSTRUMENT: {
                    DERIVATIVE_STRIKE_PRICE_CURRENCY: pd.NA,
                    EXT_STRIKE_PRICE_TYPE: "MntryVal",
                },
                SOURCE_STRIKE_PRICE_TYPE: "MntryValOverride",
                SOURCE_STRIKE_PRICE_CURRENCY: "EUR",
            },
            # Record 4: strike price currency "", strike price type not null
            {
                INSTRUMENT_DETAILS_INSTRUMENT: {
                    DERIVATIVE_STRIKE_PRICE_CURRENCY: "",
                    EXT_STRIKE_PRICE_TYPE: "MntryVal",
                },
                SOURCE_STRIKE_PRICE_TYPE: "MntryValOverride",
                SOURCE_STRIKE_PRICE_CURRENCY: "EUR",
            },
            # Record 5: strike price currency and strike price type not null
            {
                INSTRUMENT_DETAILS_INSTRUMENT: {
                    DERIVATIVE_STRIKE_PRICE_CURRENCY: "USD",
                    EXT_STRIKE_PRICE_TYPE: "MntryVal",
                },
                SOURCE_STRIKE_PRICE_TYPE: "MntryValOverride",
                SOURCE_STRIKE_PRICE_CURRENCY: "EUR",
            },
            # Record 6: strike price currency "" and strike price type np.nan
            {
                INSTRUMENT_DETAILS_INSTRUMENT: {
                    DERIVATIVE_STRIKE_PRICE_CURRENCY: "",
                    EXT_STRIKE_PRICE_TYPE: np.nan,
                },
                SOURCE_STRIKE_PRICE_TYPE: "MntryValOverride",
                SOURCE_STRIKE_PRICE_CURRENCY: "EUR",
            },
        ]
    )

    return df


@pytest.fixture()
def source_frame_with_currency_col_missing() -> pd.DataFrame:
    """
    Represents a source dataframe with one of the required columns
    missing. Here, the source strike price currency column is missing
    """

    df = pd.DataFrame(
        [
            # Record 1: strike price currency not null, strike price type ""
            {
                INSTRUMENT_DETAILS_INSTRUMENT: {
                    DERIVATIVE_STRIKE_PRICE_CURRENCY: "USD",
                    EXT_STRIKE_PRICE_TYPE: "",
                },
                SOURCE_STRIKE_PRICE_TYPE: "MntryVal",
            },
            # Record 2: strike price currency not null, strike price type np.nan
            {
                INSTRUMENT_DETAILS_INSTRUMENT: {
                    DERIVATIVE_STRIKE_PRICE_CURRENCY: "USD",
                    EXT_STRIKE_PRICE_TYPE: np.nan,
                },
                SOURCE_STRIKE_PRICE_TYPE: "MntryVal",
            },
        ]
    )

    return df


@pytest.fixture()
def expected_result_for_override() -> pd.DataFrame:
    """Represents the expected result when override is True"""

    df = pd.DataFrame(
        [
            # Record 1: strike price currency not null, strike price type ""
            {
                INSTRUMENT_DETAILS_INSTRUMENT: {
                    DERIVATIVE_STRIKE_PRICE_CURRENCY: "USD",
                    EXT_STRIKE_PRICE_TYPE: "MntryVal",
                },
            },
            # Record 2: strike price currency not null, strike price type np.nan
            {
                INSTRUMENT_DETAILS_INSTRUMENT: {
                    DERIVATIVE_STRIKE_PRICE_CURRENCY: "USD",
                    EXT_STRIKE_PRICE_TYPE: "MntryVal",
                },
            },
            # Record 3: strike price currency pd.NA, strike price type not null
            {
                INSTRUMENT_DETAILS_INSTRUMENT: {
                    DERIVATIVE_STRIKE_PRICE_CURRENCY: "EUR",
                    EXT_STRIKE_PRICE_TYPE: "MntryVal",
                },
            },
            # Record 4: strike price currency "", strike price type not null
            {
                INSTRUMENT_DETAILS_INSTRUMENT: {
                    DERIVATIVE_STRIKE_PRICE_CURRENCY: "EUR",
                    EXT_STRIKE_PRICE_TYPE: "MntryVal",
                },
            },
            # Record 5: strike price currency and strike price type not null
            {
                INSTRUMENT_DETAILS_INSTRUMENT: {
                    DERIVATIVE_STRIKE_PRICE_CURRENCY: "USD",
                    EXT_STRIKE_PRICE_TYPE: "MntryVal",
                },
            },
            # Record 6: strike price currency "" and strike price type np.nan
            {
                INSTRUMENT_DETAILS_INSTRUMENT: {
                    DERIVATIVE_STRIKE_PRICE_CURRENCY: "EUR",
                    EXT_STRIKE_PRICE_TYPE: "MntryValOverride",
                },
            },
        ]
    )

    return df


@pytest.fixture()
def params_fixture_override_true():
    params = Params(
        **{
            "override_strike_price_currency_and_type": True,
            "source_strike_price_type_column": SOURCE_STRIKE_PRICE_TYPE,
            "source_strike_price_currency_column": SOURCE_STRIKE_PRICE_CURRENCY,
        }
    )
    return params


@pytest.fixture()
def params_fixture_override_false():
    params = Params(
        **{
            "override_strike_price_currency_and_type": False,
            "source_strike_price_type_column": None,
            "source_strike_price_currency_column": None,
        }
    )
    return params


class TestOverrideStrikePriceCurrencyAndType:
    """Test Suite for OverrideStrikePriceCurrencyAndType"""

    def test_empty_source_frame(self, empty_source_df, params_fixture_override_true):
        """Test for an empty source frame"""
        task = OverrideStrikePriceCurrencyAndType(
            name="OverrideStrikePriceCurrencyAndType",
            params=params_fixture_override_true,
        )
        with pytest.raises(signals.FAIL):
            task.execute(
                source_frame=empty_source_df, params=params_fixture_override_true
            )

    def test_pydantic_validation_error_params(self):
        """
        Test for the case where the override param is True and the source strike price columns
        are None. A ValueError is expected to be raised
        """
        with pytest.raises(ValueError):
            Params(
                **{
                    "override_strike_price_currency_and_type": True,
                    "source_strike_price_type_column": None,
                    "source_strike_price_currency_column": None,
                }
            )

    def test_required_columns_missing_in_source_frame_when_override_true(
        self, source_frame_with_currency_col_missing, params_fixture_override_true
    ):
        """
        Test for the case where one or more of the required columns are missing in the
        source frame. Note that this will raise FAIL only when override is True
        """
        task = OverrideStrikePriceCurrencyAndType(
            name="OverrideStrikePriceCurrencyAndType",
            params=params_fixture_override_true,
        )
        with pytest.raises(signals.FAIL):
            task.execute(
                source_frame=source_frame_with_currency_col_missing,
                params=params_fixture_override_true,
            )

    def test_required_columns_missing_in_source_frame_when_override_false(
        self, source_frame_with_currency_col_missing, params_fixture_override_false
    ):
        """
        Test for the case where one or more of the required columns are missing in the
        source frame. Note that this will return the source frame
        """
        task = OverrideStrikePriceCurrencyAndType(
            name="OverrideStrikePriceCurrencyAndType",
            params=params_fixture_override_false,
        )
        result = task.execute(
            source_frame=source_frame_with_currency_col_missing,
            params=params_fixture_override_false,
        )
        assert result.equals(source_frame_with_currency_col_missing)

    def test_override_false_with_valid_source_frame(
        self, source_dataframe, params_fixture_override_false
    ):
        """
        Test for the case where we have a source frame with valid instrument data, and override is false.
        The source frame itself is returned in this case
        """
        task = OverrideStrikePriceCurrencyAndType(
            name="OverrideStrikePriceCurrencyAndType",
            params=params_fixture_override_false,
        )
        result = task.execute(
            source_frame=source_dataframe, params=params_fixture_override_false
        )
        assert result.equals(source_dataframe)

    def test_override_true_with_valid_source_frame(
        self,
        source_dataframe,
        params_fixture_override_true,
        expected_result_for_override,
    ):
        """
        Test for the case where we have a source frame with valid instrument data, and override is true.
        The strike price type and strike price currency values in instrumentDetails.instrument are updated
        if they are null (from the source strike price currency and type columns)
        """
        task = OverrideStrikePriceCurrencyAndType(
            name="OverrideStrikePriceCurrencyAndType",
            params=params_fixture_override_true,
        )
        result = task.execute(
            source_frame=source_dataframe, params=params_fixture_override_true
        )
        assert result.equals(expected_result_for_override)
