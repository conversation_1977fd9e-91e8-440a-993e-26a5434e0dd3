from unittest.mock import patch

import pandas as pd
from swarm.task.auditor import Auditor

from swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.branch_membership_country import (
    BranchMembershipCountry,
)
from swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.branch_membership_country import (
    Params,
)


class TestBranchMembershipCountry:
    def test_without_valid_cases_to_assign(self):
        data = pd.DataFrame(
            {
                "transactionDetails.venue": [
                    pd.NA,
                    "XOFF",
                    "XXXX",
                    "4AXE",  # Non EEA venue
                    "ABNA",  # SI Code
                    "VVV",
                    "AAAAA",
                ]
            }
        )

        task = BranchMembershipCountry(name="test")

        result = task.execute(source_frame=data)

        outcome_df = pd.DataFrame(
            {
                "transactionDetails.branchMembershipCountry": [
                    pd.NA,
                    pd.NA,
                    pd.NA,
                    pd.NA,
                    pd.NA,
                    pd.NA,
                    pd.NA,
                ]
            }
        )
        assert result.equals(outcome_df)

    def test_venue_mask_to_assign_country_code(self):

        data = pd.DataFrame(
            {
                "transactionDetails.venue": [
                    pd.NA,
                    "XOFF",
                    "XXXX",
                    "4AXE",  # Non EEA venue
                    "ABNA",  # SI Code
                    "XLON",
                ]
            }
        )

        mask = BranchMembershipCountry._get_venue_mask(df=data)

        expected_mask = pd.Series([False, False, False, False, False, True])

        assert mask.equals(expected_mask)

    def test_with_missing_executing_entity_data(self):
        data = pd.DataFrame(
            {
                "transactionDetails.venue": ["XLON"],
            }
        )

        task = BranchMembershipCountry(name="test")

        result = task.execute(source_frame=data)

        outcome_df = pd.DataFrame(
            {"transactionDetails.branchMembershipCountry": [pd.NA]}
        )
        assert result.equals(outcome_df)

    def test_mixed_venues_for_branch_membership_country(self):
        data = pd.DataFrame(
            {
                "transactionDetails.venue": ["XXXX", "XLON", "XOFF", "XLON"],
                "parties.executingEntity": [
                    {"firmIdentifiers.branchCountry": pd.NA},
                    {"firmIdentifiers.branchCountry": "GB"},
                    {"firmIdentifiers.branchCountry": pd.NA},
                    pd.NA,
                ],
            }
        )

        task = BranchMembershipCountry(name="test")

        result = task.execute(source_frame=data)

        outcome_df = pd.DataFrame(
            {"transactionDetails.branchMembershipCountry": [pd.NA, "GB", pd.NA, pd.NA]}
        )
        assert result.equals(outcome_df)

    def test_venues_in_si_and_noneea_lists(self):
        data = pd.DataFrame(
            {
                "transactionDetails.venue": ["4AXE", "ABNA", "XLON"],
                "parties.executingEntity": [
                    {"firmIdentifiers.branchCountry": "FR"},
                    {"firmIdentifiers.branchCountry": "NL"},
                    {"firmIdentifiers.branchCountry": "GB"},
                ],
            }
        )

        task = BranchMembershipCountry(name="test")

        result = task.execute(source_frame=data)

        outcome_df = pd.DataFrame(
            {"transactionDetails.branchMembershipCountry": [pd.NA, pd.NA, "GB"]}
        )
        assert result.equals(outcome_df)

    @patch.object(BranchMembershipCountry, "auditor")
    def test_with_branch_membership_country_column(self, mock_auditor):
        mock_auditor.return_value = Auditor(task_name="LinkLeiRecord")
        data = pd.DataFrame(
            {
                "transactionDetails.venue": [
                    "AXE",
                    "ABNA",
                    "XLON",
                    "XLON",
                    "XLON",
                    "XLON",
                    "XLON",
                ],
                "parties.executingEntity": [
                    {"firmIdentifiers.branchCountry": "FR"},
                    {"firmIdentifiers.branchCountry": "NL"},
                    {"firmIdentifiers.branchCountry": "GB"},
                    pd.NA,
                    pd.NA,
                    {"firmIdentifiers.branchCountry": "GB"},
                    pd.NA,
                ],
                "__branch_membership_country__": [
                    "FR",
                    "New Zealand",
                    pd.NA,
                    "Austria",
                    "New Zealand",
                    "FR",
                    "INVALID!",
                ],
            }
        )
        params = Params(
            branch_membership_country_column="__branch_membership_country__"
        )
        task = BranchMembershipCountry(name="test", params=params)

        result = task.execute(source_frame=data, params=params)

        outcome_df = pd.DataFrame(
            {
                "transactionDetails.branchMembershipCountry": [
                    pd.NA,
                    pd.NA,
                    "GB",
                    "AT",
                    "NZ",
                    "FR",
                    pd.NA,
                ]
            }
        )
        pd.testing.assert_frame_equal(left=result, right=outcome_df)
