��?      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�FinInstrm.Id��)FinInstrm.Othr.FinInstrmGnlAttrbts.FullNm��-FinInstrm.Othr.FinInstrmGnlAttrbts.ClssfctnTp��*FinInstrm.Othr.FinInstrmGnlAttrbts.NtnlCcy��)FinInstrm.Othr.DerivInstrmAttrbts.DlvryTp��(FinInstrm.Othr.DerivInstrmAttrbts.XpryDt��,FinInstrm.Othr.DerivInstrmAttrbts.PricMltplr��(FinInstrm.Othr.DerivInstrmAttrbts.OptnTp��/FinInstrm.Othr.DerivInstrmAttrbts.OptnExrcStyle��(FinInstrm.Othr.DebtInstrmAttrbts.MtrtyDt��>FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Sngl.Indx��CFinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Sngl.ISIN��CFinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Sngl.Indx��CFinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Bskt.Indx��>FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Bskt��DFinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpOut.Sngl.ISIN��DFinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpOut.Sngl.Indx��DFinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpOut.Bskt.Indx��?FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpOut.Bskt��>FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Sngl.ISIN��LFinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Sngl.Indx.Nm.RefRate.Nm��HFinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Sngl.Indx.Term.Unit��IFinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Sngl.Indx.Term.Value��9FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Sngl.Indx��9FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Bskt��>FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Bskt.Indx��EFinInstrm.Othr.DerivInstrmAttrbts.AsstClssSpcfcAttrbts.FX.OthrNtnlCcy��5FinInstrm.Othr.DerivInstrmAttrbts.StrkPric.NoPric.Pdg��5FinInstrm.Othr.DerivInstrmAttrbts.StrkPric.NoPric.Ccy�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK	��h�i8�����R�(K�<�NNNJ����J����K t�b�CH                                                                �t�bhCNu��R�e]�(hhK ��h��R�(KKK	��h�f8�����R�(KhQNNNJ����J����K t�b�CH      �      �      �      �      �      �      �      �      ��t�bhhK ��h��R�(KKK	��h!�]�(�Instrument Full Name�hghghghghghghghg�FFICSX��SEIPXC��SEBPXC�hjhhhihjhjhj�USD�hkhkhkhkhkhkhkhk�CASH�hlhlhlhlhlhlhlhl�
2027-12-31�hmhmhmhmhmhmhmhmet�bhhK ��h��R�(KKK	��h�f8�����R�(KhQNNNJ����J����K t�b�B         �?      �?      �?      �?      �?      �?      �?      �?      �?      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      ��t�bhhK ��h��R�(KKK	��h!�]�(]�}�(�ISIN�G�      �
Nm.RefRate.Nm��Underlying Index Name��	Term.Unit��pandas._libs.missing��NA����
Term.Value�h�uaG�      G�      G�      ]�}�(h�G�      h�h�h�h�h�h�uaG�      G�      G�      G�      et�bhhK ��h��R�(KKK	��hu�CH      �      �      �      �      �      �      �      �      ��t�bhhK ��h��R�(KKK	��h!�]�(G�      ]�}�(h�G�      h��[{'instrumentIdCode': 'IS�h�h�h�h�uaG�      G�      G�      ]�}�(h�G�      h��[{'instrumentIdCode': 'IS�h�h�h�h�uaG�      G�      G�      G�      G�      ]�}�(h�G�      h��[{'instrumentIdCode': 'IS�h�h�h�h�ua]�}�(h�G�      h��[{'instrumentIdCode': 'IS�h�h�h�h�uaG�      G�      ]�}�(h�G�      h��[{'instrumentIdCode': 'IS�h�h�h�h�ua]�}�(h�G�      h��[{'instrumentIdCode': 'IS�h�h�h�h�ua]�}�(h�G�      h��[{'instrumentIdCode': 'IS�h�h�h�h�uaet�bhhK ��h��R�(KKK	��hu�C�      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      ��t�bhhK ��h��R�(KKK	��h!�]�(G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      et�bhhK ��h��R�(KKK	��hu�C�      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      ��t�bhhK ��h��R�(KKK	��h!�]�(G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      et�bhhK ��h��R�(KKK	��hu�CH      �      �      �      �      �      �      �      �      ��t�bhhK ��h��R�(KKK	��h!�]�(G�      G�      G�      G�      G�      G�      G�      G�      G�      et�bhhK ��h��R�(KKK	��hu�CH      �      �      �      �      �      �      �      �      ��t�bhhK ��h��R�(KKK	��h!�]�(G�      G�      G�      G�      �100.0��1000.0��10000.0��PNDG�h�G�      G�      G�      G�      G�      G�      G�      G�      �USD�et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h&h'h(h)h*et�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h+h,h-h.et�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h0at�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h1h2et�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h3h4et�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h5h6et�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h7h8et�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h9h:h;h<et�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h=at�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h?at�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h@hAet�bhCNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hZ�mgr_locs��builtins��slice���K KK��R�u}�(jg  hdjh  jk  KKK��R�u}�(jg  hqjh  jk  KK
K��R�u}�(jg  h{jh  jk  K
KK��R�u}�(jg  h�jh  jk  KKK��R�u}�(jg  h�jh  jk  KKK��R�u}�(jg  h�jh  jk  KKK��R�u}�(jg  h�jh  jk  KKK��R�u}�(jg  h�jh  jk  KKK��R�u}�(jg  h�jh  jk  KKK��R�u}�(jg  h�jh  jk  KKK��R�u}�(jg  h�jh  jk  KKK��R�u}�(jg  h�jh  jk  KKK��R�u}�(jg  h�jh  jk  KKK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.