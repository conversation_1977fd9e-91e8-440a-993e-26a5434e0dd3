��*      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�FinInstrm.Id��)FinInstrm.Othr.FinInstrmGnlAttrbts.FullNm��-FinInstrm.Othr.FinInstrmGnlAttrbts.ClssfctnTp��*FinInstrm.Othr.FinInstrmGnlAttrbts.NtnlCcy��)FinInstrm.Othr.DerivInstrmAttrbts.DlvryTp��(FinInstrm.Othr.DerivInstrmAttrbts.XpryDt��,FinInstrm.Othr.DerivInstrmAttrbts.PricMltplr��(FinInstrm.Othr.DerivInstrmAttrbts.OptnTp��/FinInstrm.Othr.DerivInstrmAttrbts.OptnExrcStyle��(FinInstrm.Othr.DebtInstrmAttrbts.MtrtyDt��9FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Bskt��>FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Bskt��CFinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Bskt.Indx��?FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpOut.Bskt��DFinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpOut.Bskt.Indx��>FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Bskt.Indx��CFinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Sngl.ISIN��CFinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Sngl.Indx��DFinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpOut.Sngl.ISIN��DFinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpOut.Sngl.Indx��>FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Sngl.ISIN��>FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Sngl.Indx��EFinInstrm.Othr.DerivInstrmAttrbts.AsstClssSpcfcAttrbts.FX.OthrNtnlCcy�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C@                                                         �t�bh=Nu��R�e]�(hhK ��h��R�(KKK��h!�]�(G�      G�      G�      G�      G�      G�      G�      G�      et�bhhK ��h��R�(KKK��h!�]�(�*MLD1LMOM EQUITY BASKET SWAPS DECEMBER 2029��*MLD1SMOM EQUITY BASKET SWAPS DECEMBER 2029��*MLD1SMOM EQUITY BASKET SWAPS DECEMBER 2029��*MLD1LMOM EQUITY BASKET SWAPS DECEMBER 2029��*GSTHVISP EQUITY BASKET SWAPS DECEMBER 2023��*GSTHHVIP EQUITY BASKET SWAPS FEBRUARY 2024��*GSTHVISP EQUITY BASKET SWAPS DECEMBER 2022��*GSTHHVIP EQUITY BASKET SWAPS DECEMBER 2022��SEBPXC��SEBPXC��SEBPXC��SEBPXC��SEBPXC��SEBPXC��SEBPXC��SEBPXC��EUR��EUR��EUR��EUR��USD��USD��USD��USD��CASH��CASH��CASH��CASH��CASH��CASH��CASH��CASH��
2029-12-31��
2029-12-31��
2029-12-31��
2029-12-31��
2023-12-29��
2024-02-28��
2022-12-29��
2022-12-19�et�bhhK ��h��R�(KKK��h�f8�����R�(KhKNNNJ����J����K t�b�C@      �?      �?      �?      �?      �?      �?      �?      �?�t�bhhK ��h��R�(KKK��h!�]�(NNNNNNNNNNNNNNNNNNNNNNNN�pandas._libs.missing��NA���h�h�h�h�h�h�h�}��ISIN�]�(�DE000CBK1001��DE0005810055��DE0005557508��DE0007037129��NL0000009082��FR0000121329��FR0000120271��GB0009895292��DK0060534915��NO0010096985��SE0017768716��IT0003856405��LU0156801721��GB0002634946��GB0002875804��GB0007980591��GB00B033F229��GB00BD6K4575��GB0005405286��GB0004544929��GB0006776081��GB00BM8PJY71��GB0007188757��GB0004082847��ES0167050915��ES0125220311��ES0113679I37��FI0009014377��ES0173516115��ES0113860A34��FI0009003305��FI0009005987��GB00B0SWJX34��ES0140609019��DE000KSAG888��DE0005200000��BMG4593F1389��GB00BMBVGQ36��JE00B4T3BW64��SE0011090018��FR0010908533��FR0010533075��FR0000044448��DE0007030009��DE000A0WMPJ6��SE0000114837��PTGAL0AM0009��NO0010345853��NL0010558797��SE0000120669��SE0000112385��FR0010259150��FR0011726835��NL0010832176��GB00BN4HT335��GB00BYQ0JC66��NL0011872643��IE00BD1RP616��ES0105563003��GB00BP6MXD84�es}�h�]�(�CH0012138530��DE000A1EWWW0��DE0005785802��DE0007500001��NL0000009538��IT0003497168��GB0030913577��SE0000108656��GB0033986497��GB0031274896��GB00BH4HKS39��FI0009007132��GB0006825383��GB00B5ZN1N88��DK0060738599��DE000PAH0038��SE0000202624��GB0001859296��GB0009633180��GB00BYT1DJ19��GB00B3MBS747��GB00BK9RKT01��DE0005089031��CH0012453913��FR0000051807��DE0006969603��CH0024608827��SE0015810247��SE0001662230��SE0000379190��FR0010313833��CH0016440353��IE0004927939��DK0010219153��FR0000054470��FR0000121147��NO0010310956��IE0002424939��DE000EVNK013��DE000LEG1110��DE000A1ML7J1��NL0011821392��DE0006452907��DE000KGX8881��DE000ZAL1111��DE0005664809��DE0005158703��NL0012015705��LU1673108939��AT0000A18XM4��NL0012969182��IT0005366767��DE000A161408��SE0017832488��NO0003055501��DE000KBX1006��DK0060634707��SE0012853455��CH0012549785�esG�      G�      }�h�]�(�US00724F1012��US0079031078��US0311621009��US0605051046��US1101221083��US20030N1019��US20825C1045��US22160K1051��US17275R1023��US2546871060��US3453708600��US4370761029��US4592001014��US4581401001��US4781601046��US46625H1005��US1912161007��US5486611073��US5801351017��US88579Y1010��US58933Y1055��US5951121038��US6541061031��US6745991058��US7134481081��US7170811035��US7427181091��US7475251036��AN8068571086��US00206R1023��US87612E1064��US8825081040��US9113121068��US9311421039��US30231G1022��US56585A1025��US1667641005��US6174464486��US46120E6023��US74144T1088��US7237871071��US1696561059��US69331C1080��US09247X1019��US7561091049��US25278X1090��US00287Y1091��US16119P1084��US11135F1012��US60770K1079�esG�      G�      }�h�]�(�US0378331005��US0079031078��US0231351067��US00507V1098��US0605051046��US1729674242��US2358511028��US2546871060��US3377381088��US4448591028��US5949181045��US64110L1061��US09857L1089��US8725901040��US91324P1021��US9497461015��US0367521038��US2283681060��US8085131055��US79466L3024��US16411R2085��US92826C8394��US57636Q1040��US26884L1098��US58733R1023��US15135B1017��US37940X1028��US0846707026��US88160R1014��US12008R1077��US30303M1027��US6974351057��US81762P1021��US74967X1037��US01609W1027��US3802371076��US70450Y1038��US02079K3059��US9713781048��US16119P1084��US3383071012��US90353T1007��US22788C1053��US1651677353��NL0013056914��US03769M1062��US0494681010�esh�h�G�      G�      h�G�      G�      h�G�      G�      }�h�]�(�DE000A1EWWW0��DE0005439004��DE0005785802��DE0007500001��NL0000009538��DK0010272632��IT0003497168��FR0010220475��GB0033986497��GB0006825383��GB00B63H8491��GB00B5ZN1N88��FI0009007132��GB0000811801��ES0171996087��FI0009003727��SE0000202624��DK0060252690��GB0001859296��GB0009633180��GB0005576813��GB00BYT1DJ19��GB00BM8Q5M07��GB00B3MBS747��GB00BK9RKT01��CH0024608827��DE0006969603��SE0015810247��CH1175448666��CH0012453913��SE0000379190��SE0001662230��IE0004927939��DK0010219153��FR0000121147��IE00B00MZ448��SE0012454072��SE0017832488��DE000LEG1110��DE000A1ML7J1��SE0005127818��NO0003055501��DE0005158703��AT0000A18XM4��DE000KGX8881��PLPEKAO00016��GB00BDVZYZ77��LU1072616219��DE000ZAL1111��LU1673108939��CH0311864901��NL0012015705��DE000A2E4K43��DE000A161408��NL0012969182��DE000KBX1006��SE0012853455��GB00BNYK8G86�es}�h�]�(�DE0008430026��FR0000121329��FR0000120271��ES0173516115��GB0007980591��GB0002634946��GB0004544929��GB0006776081��ES0113860A34��GB0004082847��IT0003856405��FI0009013296��DK0060534915��DK0061539921��NO0010096985��ES0113679I37��GB0009895292��ES0125220311��DK0010272202��GB00BZ4BQC70��FI0009014377��FI0009003305��ES0167050915��GB0007188757��GB0000456144��IT0004176001��DK0010307958��DE0005200000��LU0156801721��GB00B033F229��ES0140609019��GB00B17BBQ50��FR0010908533��FR0000052292��BE0974259880��BMG4593F1389��JE00B4T3BW64��DE0007030009��PTGAL0AM0009��DE000A0WMPJ6��NL0010558797��CH0130293662��GB00BYQ0JC66��BE0003816338��FR0011726835��DE0006095003��GB00BD3VFW73��DK0015998017��ES0105563003��NL0010832176��GB00BP6MXD84��IE00BD1RP616��SE0006993770��SE0000120669��SE0000112385�esG�      }�h�]�(�US0378331005��US0231351067��US00507V1098��US2358511028��US3377381088��SG9999000020��US4448591028��US5324571083��US5949181045��US64110L1061��US67066G1040��US09857L1089��US8725901040��US7433151039��US91324P1021��US9497461015��US8085131055��US78409V1044��US8835561023��US79466L3024��US91307C1027��US16411R2085��US92826C8394��US57636Q1040��US9285634021��US58733R1023��US8936411003��US0846707026��US88160R1014��US12008R1077��US29273V1008��US30303M1027��US6974351057��US81762P1021��US98138H1014��US74967X1037��US01609W1027��US3802371076��US5303073051��US03674X1063��US70450Y1038��US02079K3059��US9713781048��US5312296073��US90353T1007��US1651677353��US00187Y1001��US03769M1062��US21037T1097�es}�h�]�(�US0028241000��US00724F1012��US0311621009��US0970231058��US1101221083��US20030N1019��US20825C1045��US22160K1051��US17275R1023��US1266501006��US3453708600��US4370761029��US40434L1052��US4592001014��US4581401001��US4781601046��US46625H1005��US4824801009��US1912161007��US5324571083��US5486611073��US5801351017��US5951121038��US6541061031��US67066G1040��US6745991058��US7134481081��US7170811035��US7427181091��US7475251036��US8825081040��US9113121068��US92343V1044��US9311421039��US30231G1022��US1667641005��US6174464486��US65339F1012��US78409V1044��US74144T1088��US8835561023��US1696561059��US09247X1019��US75886F1075��US74340W1036��US00287Y1091��US9256521090��US11135F1012��US1255231003��US60770K1079�esG�      G�      G�      h�h�G�      h�h�G�      h�h�h�h�h�h�h�h�G�      G�      }�h�h�s}�h�h�sG�      }�h�h�s}�h�h�sG�      G�      G�      ]�}�(h��EU000A2X2A25��
Nm.RefRate.Nm��ESTR��Nm.RefRate.Indx�N�Nm.Term.Unit��DAYS��Nm.Term.Val��1�ua]�}�(h��EU000A2X2A25�j\  �ESTR�j^  Nj_  �DAYS�ja  �1�uaG�      ]�}�(h�Nj\  �EFFR�j^  Nj_  �DAYS�ja  �1�ua]�}�(h�Nj\  �EFFR�j^  Nj_  �DAYS�ja  �1�uaG�      }�h�h�s}�h�h�sG�      G�      }�h�h�sG�      G�      }�h�h�s]�}�(h��EU000A2X2A25�j\  �ESTR�j^  Nj_  �DAYS�ja  �1�ua]�}�(h��EU000A2X2A25�j\  �ESTR�j^  Nj_  �DAYS�ja  �1�uaG�      G�      ]�}�(h�Nj\  �EFFR�j^  Nj_  �DAYS�ja  �1�uaG�      G�      ]�}�(h�Nj\  �EFFR�j^  Nj_  �DAYS�ja  �1�uah�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�G�      G�      G�      G�      G�      G�      G�      G�      et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bh=Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h&h'h(h)h*et�bh=Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h+at�bh=Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h,h-h.h/h0h1h2h3h4h5h6h7h8h9h:h;et�bh=Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hT�mgr_locs��builtins��slice���K KK��R�u}�(j�  hZj�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.