import numpy as np
import pandas as pd

from swarm_tasks.transform.steeleye.comms.voice.transcription.static import (
    TranscriptionFields,
)
from swarm_tasks.transform.steeleye.comms.voice.translation.translate import (
    AddTranslation,
)
from swarm_tasks.transform.steeleye.comms.voice.translation.translate import Params


class MockClient:
    @staticmethod
    def translate_text(Text, SourceLanguageCode, TargetLanguageCode):
        if Text == "mock_error":
            return ["Yes, Yes"]
        return {TranscriptionFields.TRANSLATED_TEXT: "Yes, Yes, Yes"}


def _init_task():
    params = Params()
    task = AddTranslation(name="AddTranslation", params=params)
    return task, params


class TestAddTranslation:
    def test_translate_no_translation_needed(self):
        task, params = _init_task()

        row = pd.Series(
            {
                TranscriptionFields.RECORD_ID: "fake_dummy_id",
                TranscriptionFields.TRANSCRIPTION: "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>",
                TranscriptionFields.LANGUAGE: "fr-FR",
            }
        )

        translated = task._translate(
            row=row, languages=["fr-FR"], client=None, param=params
        )

        assert translated == "<PERSON>ui, <PERSON>ui, <PERSON>ui"

    def test_translate_empty_transcription(self):
        task, params = _init_task()

        row = pd.Series(
            {
                TranscriptionFields.RECORD_ID: "fake_dummy_id",
                TranscriptionFields.TRANSCRIPTION: np.nan,
                TranscriptionFields.LANGUAGE: "en-GB",
            }
        )

        translated = task._translate(
            row=row, languages=["en-GB"], client=MockClient(), param=params
        )

        assert pd.isna(translated)

    def test_translate_catch_exception(self):
        task, params = _init_task()

        row = pd.Series(
            {
                TranscriptionFields.RECORD_ID: "fake_dummy_id",
                TranscriptionFields.TRANSCRIPTION: "mock_error",
                TranscriptionFields.LANGUAGE: "fr-FR",
            }
        )

        translated = task._translate(
            row=row, languages=["en-GB"], client=MockClient(), param=params
        )

        assert (
            translated
            == "mock_error\n---------- en-GB ----------\n\n\nTranslation unavailable for record fake_dummy_id."
        )

    def test_translate(self):
        task, params = _init_task()

        row = pd.Series(
            {
                TranscriptionFields.RECORD_ID: "fake_dummy_id",
                TranscriptionFields.TRANSCRIPTION: "Oui, Oui, Oui",
                TranscriptionFields.LANGUAGE: "fr-FR",
            }
        )

        translated = task._translate(
            row=row, languages=["en-GB"], client=MockClient(), param=params
        )

        assert (
            translated
            == "Oui, Oui, Oui\n---------- en-GB ----------\n\n\nYes, Yes, Yes"
        )
