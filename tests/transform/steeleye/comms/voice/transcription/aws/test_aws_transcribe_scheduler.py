import pandas as pd

from swarm_tasks.transform.steeleye.comms.voice.transcription.aws_transcribe_scheduler import (
    AwsTranscribeScheduler,
)
from swarm_tasks.transform.steeleye.comms.voice.transcription.aws_transcribe_scheduler import (
    Params,
)
from swarm_tasks.transform.steeleye.comms.voice.transcription.static import (
    TranscriptionRules,
)


class TestAWSTranscribeScheduler:
    def test_queue_calls_pd_na_bucket_key(self):
        """
        Test case when the CSV contains some calls without recordings
        """
        task = AwsTranscribeScheduler(name="test")

        voice = pd.Series(
            {
                "voiceFile.fileInfo.location.bucket": pd.NA,
                "voiceFile.fileInfo.location.key": pd.NA,
                "identifiers.fromId": "+353879759192",
                "identifiers.toIds": ["+447866222289"],
                "callDuration": pd.Timedelta("0 days 00:00:05"),
                "tmp": pd.Timedelta("0 days 00:00:05"),
            }
        )
        config = TranscriptionRules()
        client = ""
        params = Params()

        queued = task._queue_calls(voice, config, client, params)
        assert not queued

    def test_queue_calls_no_bucket_key(self):
        """
        Test case when the CSV only contains calls without recordings
        """
        task = AwsTranscribeScheduler(name="test")

        voice = pd.Series(
            {
                "identifiers.fromId": "+353879759192",
                "identifiers.toIds": ["+447866222289"],
                "callDuration": pd.Timedelta("0 days 00:00:05"),
                "tmp": pd.Timedelta("0 days 00:00:05"),
            }
        )
        config = TranscriptionRules()
        client = ""
        params = Params()

        queued = task._queue_calls(voice, config, client, params)
        assert not queued
