import numpy as np
import pandas as pd
import pytest

from swarm_tasks.transform.steeleye.reference.instrument.lme.tif.alternative_instrument_identifier import (
    AlternativeInstrumentIdentifier,
)
from swarm_tasks.transform.steeleye.reference.instrument.lme.tif.alternative_instrument_identifier import (
    Column,
)
from swarm_tasks.transform.steeleye.reference.instrument.lme.tif.alternative_instrument_identifier import (
    TARGET_ATTRIBUTE,
)


@pytest.fixture()
def source_frame() -> pd.DataFrame:
    """
    Represents a source dataframe parsed from input file
    """
    df = pd.DataFrame(
        {
            Column.VENUE: ["XXXX", "XXXX", "XXXX", "XXXX", "XXXX", np.nan],
            Column.CONTRACT_CODE: [
                "TEST_CC",
                "TEST_CC",
                "TEST_CC",
                "TEST_CC",
                np.nan,
                "TEST_CC",
            ],
            Column.NOTIONAL_CURRENCY: ["USD", "USD", "USD", "USD", "USD", "USD"],
            Column.CFI: ["OPCFID", "OPCFID", "OPCFID", "OPCFID", "OPCFID", "OPCFID"],
            Column.MATURITY: [
                "2020/01/01",
                "2020/01/01",
                "2020/01/01",
                np.nan,
                "2020/01/01",
                "2020/01/01",
            ],
            Column.CONTRACT_TYPE: ["OPTN", "FALSE", "OPTN", "OPTN", "OPTN", "OPTN"],
            Column.STRIKE_PRICE: [0.1, 0.1, np.nan, 0.1, 0.1, 0.1],
        }
    )
    return df


class TestTransactionRefNo:
    def test_alternative_instrument_identifier(self, source_frame: pd.DataFrame):
        mapping = AlternativeInstrumentIdentifier(
            name="test-alternative-instrument-identifier"
        )
        outcome_df = mapping.execute(source_frame)
        expected_df = pd.DataFrame(
            {
                TARGET_ATTRIBUTE: [
                    "XXXXTEUSDOP2020-01-01 00:00:000.10000000",
                    "XXXXTEUSDOP2020-01-01 00:00:00",
                    np.nan,
                    np.nan,
                    np.nan,
                    np.nan,
                ]
            }
        )
        assert outcome_df.equals(expected_df)
