from pathlib import Path

import pandas as pd
import pytest
from prefect import context
from prefect.utilities.collections import DotDict
from se_elasticsearch.repository import get_repository_by_cluster_version
from swarm.schema.flow.bundle.components import ResourceConfig

from swarm_tasks.transform.steeleye.meta.assign_meta import AssignMeta


@pytest.mark.skip(reason="needs realignment")
def test_benchmark_assign_meta():
    """
    Original:

    [100%][2020-05-30 08:18:34] INFO - prefect.assign meta | assign meta id
    [2020-05-30 08:18:34] INFO - prefect.assign meta | assign meta key
    [2020-05-30 08:18:34] INFO - prefect.assign meta | assign meta hash
    [2020-05-30 08:18:35] INFO - prefect.assign meta | unflatten records
    [2020-05-30 08:18:35] INFO - prefect.assign meta | assign meta validation errors
    [2020-05-30 08:18:52] INFO - prefect.assign meta | prepare result
    19.00s call

    # Improvement 30th May 2020
    [100%][2020-05-30 09:34:33] INFO - prefect.assign meta | assign meta id
    [2020-05-30 09:34:33] INFO - prefect.assign meta | assign meta key
    [2020-05-30 09:34:33] INFO - prefect.assign meta | assign meta hash
    [2020-05-30 09:34:34] INFO - prefect.assign meta | unflatten records
    [2020-05-30 09:34:34] INFO - prefect.assign meta | assign meta validation errors
    [2020-05-30 09:34:40] INFO - prefect.assign meta | prepare result
    7.87s call

    """
    df = pd.read_csv(Path(__file__).parent.joinpath("data", "meta.csv").as_posix())

    from swarm_tasks.transform.steeleye.meta.assign_meta import Params

    params = Params(
        model="RTS22Transaction",
        model_path="se_schema.models.mifid2.rts22_transaction.model",
    )

    assign_meta_task = AssignMeta(name="assign meta", params=params)

    # classes
    resources = AssignMeta.resources_class(es_client_key="tenant-data")

    # clients
    resource_config = ResourceConfig(
        host="elasticsearch.dev-schro.steeleye.co",
        port=9200,
    )
    clients = {
        "tenant-data": get_repository_by_cluster_version(
            resource_config=resource_config
        )
    }

    assign_meta_task.clients = clients
    identifier = DotDict({"tenant": "schroders"})
    context.swarm = DotDict()
    context.swarm.identifier = identifier

    # Execute
    result = assign_meta_task.execute(
        source_frame=df, params=params, resources=resources
    )
    result
