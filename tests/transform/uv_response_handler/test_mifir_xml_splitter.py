import json
from pathlib import Path
from xml.etree import cElementTree

from swarm_tasks.transform.steeleye.tr.unavista.response.response_handler import (
    map_elem_to_report,
)
from swarm_tasks.transform.steeleye.tr.unavista.response.response_handler import (
    map_elem_to_tr,
)
from swarm_tasks.transform.steeleye.tr.unavista.response.response_handler import (
    UNAVISTA_ARM_NS,
)
from swarm_tasks.transform.steeleye.tr.unavista.response.response_handler import (
    UNAVISTA_NCA_NS,
)


def test_read_xml():
    print("-------------XXX-------------")
    process_file(Path(__file__).parent.joinpath("data", "response-arm-simple.xml"))
    print("-------------XXX-------------")
    process_file(Path(__file__).parent.joinpath("data", "response-nca-no-details.xml"))
    print("-------------XXX-------------")
    process_file(Path(__file__).parent.joinpath("data", "response-nca-multiple.xml"))


def process_file(file_path, chunk_size: int = 100):
    count: int = 0
    resp_type = "nca" if ("NCA" in file_path.name.upper()) else "arm"
    xmlns = UNAVISTA_NCA_NS if ("NCA" in file_path.name.upper()) else UNAVISTA_ARM_NS
    for event, elem in cElementTree.iterparse(file_path, events=("end",)):
        if elem.tag == (xmlns + "MessageInformation"):
            print(json.dumps(map_elem_to_report(elem, resp_type, xmlns, "dummy/url")))
            elem.clear()

        elif elem.tag == (xmlns + "ErrorTransactionInformation") or elem.tag == (
            xmlns + "TransactionInformation"
        ):
            count += 1
            print(json.dumps(map_elem_to_tr(elem, "test-report", resp_type, xmlns)))

            if count % chunk_size == 0:
                print("-------------")
            elem.clear()

    print(f"processed {count} items")
