import pandas as pd
import pytest

from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import Params


@pytest.fixture()
def source_dataframe() -> pd.DataFrame:
    """Represents a source dataframe parsed from an input csv file."""
    df = pd.DataFrame(
        {"foo": ["foo_0", "foo_1", pd.NA], "bar": ["bar_0", "bar_1", pd.NA]}
    )
    return df


class TestConcatAttributes:
    def test_default_params(self, source_dataframe):
        params = dict(
            source_attributes=["foo", "bar"], target_attribute="target_column"
        )

        expected_data = {"target_column": ["foo_0bar_0", "foo_1bar_1", pd.NA]}

        self._run_test(df=source_dataframe, params=params, expected_data=expected_data)

    def test_delimiter_to_empty(self):
        params = Params(
            source_attributes=["foo", "bar"],
            target_attribute="target_column",
        )
        assert params.delimiter == ""

    def test_param_delimiter_set(self, source_dataframe):
        params = dict(
            source_attributes=["foo", "bar"],
            target_attribute="target_column",
            delimiter=",",
        )

        expected_data = {"target_column": ["foo_0,bar_0", "foo_1,bar_1", pd.NA]}

        self._run_test(df=source_dataframe, params=params, expected_data=expected_data)

    def test_param_mask(self, source_dataframe):
        params = dict(
            source_attributes=["foo", "bar"],
            target_attribute="target_column",
            mask="`foo`.str.startswith('foo_0').fillna(False)",
        )

        expected_data = {"target_column": ["foo_0bar_0", pd.NA, pd.NA]}

        self._run_test(df=source_dataframe, params=params, expected_data=expected_data)

    def test_param_prefix(self, source_dataframe):
        params = dict(
            source_attributes=["foo", "bar"],
            target_attribute="target_column",
            prefix="attribute name",
        )

        expected_data = {
            "target_column": ["foo: foo_0bar: bar_0", "foo: foo_1bar: bar_1", pd.NA]
        }

        self._run_test(df=source_dataframe, params=params, expected_data=expected_data)

    def test_param_sort_attributes(self, source_dataframe):
        # Ascending
        params = dict(
            source_attributes=["foo", "bar"],
            target_attribute="target_column",
            sort_attributes="ascending",
        )

        expected_data = {"target_column": ["bar_0foo_0", "bar_1foo_1", pd.NA]}

        self._run_test(df=source_dataframe, params=params, expected_data=expected_data)

        # Descending
        params = dict(
            source_attributes=["foo", "bar"],
            target_attribute="target_column",
            sort_attributes="descending",
        )

        expected_data = {"target_column": ["foo_0bar_0", "foo_1bar_1", pd.NA]}

        self._run_test(df=source_dataframe, params=params, expected_data=expected_data)

    def test_param_max_length(self):
        source_dataframe = pd.DataFrame(
            {
                "first_name": ["Sachin", "Rahul", "Sourav", pd.NA, pd.NA],
                "last_name": ["Tendulkar", "Dravid", "Ganguly", "Kohli", pd.NA],
            }
        )
        params = dict(
            source_attributes=["first_name", "last_name"],
            max_length=9,
            target_attribute="target_column",
        )

        expected_data = {
            "target_column": ["SachinTen", "RahulDrav", "SouravGan", "Kohli", pd.NA]
        }

        self._run_test(df=source_dataframe, params=params, expected_data=expected_data)

    @staticmethod
    def _run_test(df: pd.DataFrame, params: dict, expected_data: dict):
        params = Params(**params)
        task = ConcatAttributes(name="test-map-attribute", params=params)
        outcome_df = task.execute(source_frame=df, params=params)
        expected_df = pd.DataFrame(expected_data)
        assert outcome_df.equals(expected_df)
