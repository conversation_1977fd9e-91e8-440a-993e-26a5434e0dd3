import pandas as pd
import pytest
from pydantic import ValidationError

from swarm_tasks.transform.calculations.basic_operation import BasicOperation
from swarm_tasks.transform.calculations.basic_operation import Params


@pytest.fixture()
def empty_frame() -> pd.DataFrame:
    return pd.DataFrame({})


@pytest.fixture()
def source_frame_with_str_col_containing_floats() -> pd.DataFrame:
    """Source frame which contains 1 string column and 1 float column.
    The string column should be converted to float in the task"""
    return pd.DataFrame({"a": [1.0, 2.0], "b": ["2.2", "3.3"]})


@pytest.fixture()
def source_frame_with_invalid_str_column() -> pd.DataFrame:
    """Source frame which contains 1 string column and 1 float column.
    The string column should be converted to float in the task"""
    return pd.DataFrame({"a": [1.0, 2.0], "b": ["haha", "jaja"]})


@pytest.fixture()
def params_fixture() -> Params:
    return Params(
        **{
            "source_attributes": ["a", "b"],
            "target_attribute": "c",
            "operator": "MULTIPLY",
        }
    )


@pytest.fixture()
def source_frame_division() -> pd.DataFrame:
    """Source frame which contains 1 string column and 1 float column.
    The string column should be converted to float in the task"""
    return pd.DataFrame({"a": [5.0, 6.0], "b": [2.0, 3.0]})


@pytest.fixture()
def division_params_fixture() -> Params:
    return Params(
        **{
            "source_attributes": ["a", "b"],
            "target_attribute": "c",
            "operator": "DIVIDE",
            "division_elements": {"dividend": "a", "divisor": "b"},
        }
    )


class TestBasicOperation:
    def test_empty_frame(self, empty_frame: pd.DataFrame, params_fixture: Params):
        """Test for empty frame"""
        result = self._run_task(source_frame=empty_frame, params=params_fixture)
        assert result.empty

    def test_multiply_and_successful_type_cast_to_float(
        self,
        source_frame_with_str_col_containing_floats: pd.DataFrame,
        params_fixture: Params,
    ):
        """Tests that the string column containing floats is type cast correctly, and
        the result contains the product of the 2 columns"""
        result = self._run_task(
            source_frame=source_frame_with_str_col_containing_floats,
            params=params_fixture,
        )
        expected_result = pd.DataFrame({"c": [2.20000, 6.60000]})
        assert not pd.testing.assert_frame_equal(result, expected_result)

    def test_invalid_string_column_source_attribute(
        self, params_fixture: Params, source_frame_with_invalid_str_column: pd.DataFrame
    ):
        """Test for the case where one of the attributes is a string column containing
        non-float values. This will result in a df containing pd.NA"""
        result = self._run_task(
            source_frame=source_frame_with_invalid_str_column, params=params_fixture
        )
        expected_result = pd.DataFrame({"c": [pd.NA, pd.NA]})
        assert not pd.testing.assert_frame_equal(result, expected_result)

    def test_division(
        self, division_params_fixture: Params, source_frame_division: pd.DataFrame
    ):
        """
        Test case for division part of the task
        """
        result = self._run_task(
            source_frame=source_frame_division, params=division_params_fixture
        )
        expected_result = pd.DataFrame({"c": [2.5, 2.0]})
        pd.testing.assert_frame_equal(result, expected_result)

    def test_division_element_params(self):
        """
        Tests the validators for the params for divisions
        """

        with pytest.raises(ValidationError) as e:
            Params(
                **{
                    "source_attributes": ["a", "b"],
                    "target_attribute": "c",
                    "operator": "DIVIDE",
                }
            )
        assert e.match(
            "'division_elements' param is needed when 'operator' == 'DIVIDE'."
        )

        with pytest.raises(ValidationError) as e:
            Params(
                **{
                    "source_attributes": ["a", "b"],
                    "target_attribute": "c",
                    "operator": "DIVIDE",
                    "division_elements": {"dividend": "a"},
                }
            )
        assert e.match(
            "'division_elements' param needs to contain the 'dividend' and 'divisor' columns."
        )

        with pytest.raises(ValidationError) as e:
            Params(
                **{
                    "source_attributes": ["a"],
                    "target_attribute": "c",
                    "operator": "DIVIDE",
                    "division_elements": {"dividend": "a", "divisor": "b"},
                }
            )
        assert e.match(
            "'dividend' and 'divisor' columns need to be listed in 'source_attributes'. Missing 'b'"
        )

    @staticmethod
    def _run_task(params: Params, source_frame: pd.DataFrame):
        task = BasicOperation(name="BasicOperation", params=params)
        return task.execute(source_frame=source_frame, params=params)
