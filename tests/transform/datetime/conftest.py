import pandas as pd
import pytest


@pytest.fixture()
def time_data():
    """
    Note on test data:
    epoch time    -> readable time
    1653040800000 -> "2022-05-20T10:00:00",
    1653046140000 -> "2022-05-20T11:29:00",
    1653049740000 -> "2022-05-20T12:29:00",
    """
    return pd.Series([1653040800000, 1653046140000, 1653049740000, pd.NA])


@pytest.fixture()
def time_delta():
    # 60000 is 1 min in milli-seconds
    return pd.Series([60000, 60000, 60000, pd.NA])


@pytest.fixture()
def expected_result_add_delta_to_epoch():
    return pd.Series(
        [
            "2022-05-20T10:01:00.000000Z",
            "2022-05-20T11:30:00.000000Z",
            "2022-05-20T12:30:00.000000Z",
        ]
    )


@pytest.fixture()
def time_delta_series():
    return pd.Series(
        [
            pd.to_timedelta(60000, unit="ms"),
            pd.to_timedelta(120000, unit="ms"),
            pd.to_timedelta(180000, unit="ms"),
        ]
    )


@pytest.fixture()
def expected_result_format_call_duration():
    return pd.Series(["00:01:00", "00:02:00", "00:03:00"])
