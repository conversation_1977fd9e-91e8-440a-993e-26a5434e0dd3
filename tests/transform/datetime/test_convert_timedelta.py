import pandas as pd

from swarm_tasks.transform.datetime.convert_timedelta import ConvertT<PERSON><PERSON>el<PERSON>
from swarm_tasks.transform.datetime.convert_timedelta import Params


class TestConvertTimeDelta:
    def test_task(self):
        source_frame = pd.DataFrame(
            {"duration": [3600000, 18000000, 91389000, 3302000, 6211000]}
        )
        expected = pd.Series(
            ["01:00:00", "05:00:00", "25:23:09", "00:55:02", "01:43:31"]
        )
        params = Params(
            source_attribute="duration", target_attribute="actual", source_unit="ms"
        )
        task = ConvertTimeDelta(name="ConvertTimeDelta", params=params)
        result = task.execute(source_frame, params=params)

        assert result["actual"].equals(expected)
