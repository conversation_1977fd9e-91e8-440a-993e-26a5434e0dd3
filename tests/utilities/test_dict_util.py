import pytest

from swarm_tasks.utilities.dict_util import strtobool


@pytest.mark.parametrize("val", ["Y", "y", "True", "On", "true", "1"])
def test_strtobool_true_vals(val):
    bool_val = strtobool(val)
    assert bool_val == 1


@pytest.mark.parametrize("val", ["N", "n", "False", "off", "false", "0"])
def test_strtobool_false_vals(val):
    bool_val = strtobool(val)
    assert bool_val == 0


def test_strtobool_invalid_value():
    with pytest.raises(ValueError):
        strtobool("Tralse")
