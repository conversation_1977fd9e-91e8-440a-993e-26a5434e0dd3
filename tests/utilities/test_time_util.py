import datetime
from unittest.mock import patch
from unittest.mock import PropertyMock

import swarm.utilities.time_util
from swarm.utilities.time_util import add_leading_zero
from swarm.utilities.time_util import get_utc_time_diff
from swarm.utilities.time_util import pretty_time_delta


class TestTimeUtil:
    @patch.object(
        swarm.utilities.time_util, "get_date_time_utcnow", new_callable=PropertyMock
    )
    def test_can_get_utc_time_diff(self, mocker):
        mocker.side_effect = [datetime.datetime(2023, 2, 6, 17, 55, 26)]

        assert get_utc_time_diff(datetime.datetime(2023, 2, 6, 17, 54, 1)) == "01m25s"

    def test_can_pretty_time_delta(self):
        assert pretty_time_delta(7) == "7s"
        assert pretty_time_delta(30) == "30s"
        assert pretty_time_delta(65) == "01m05s"
        assert pretty_time_delta(90) == "01m30s"
        assert pretty_time_delta(1500) == "25m00s"
        assert pretty_time_delta(3690) == "01h01m30s"

    def test_it_add_leading_zero(self):
        assert add_leading_zero(7) == "07"
        assert add_leading_zero(10) == "10"
