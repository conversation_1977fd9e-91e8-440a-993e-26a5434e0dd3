import pytest
from se_boltons.decorators import retry

MAX_FUNCTION_RUNS = 3
function_run_counter = 0


@retry(exceptions=(AttributeError,), max_retry=MAX_FUNCTION_RUNS)
def this_raises_zero_div_error_which_isnt_retried() -> None:
    """Raises a ZeroDivisionError which is NOT handled by decorator"""
    global function_run_counter
    function_run_counter += 1
    _ = 90 / 0


@retry(exceptions=(ZeroDivisionError,), max_retry=MAX_FUNCTION_RUNS)
def this_raises_zero_div_error_which_is_retried() -> None:
    """Raises a ZeroDivisionError which is handled by decorator"""
    global function_run_counter
    function_run_counter += 1
    _ = 90 / 0


# noinspection PyUnresolvedReferences
@retry(max_retry=MAX_FUNCTION_RUNS)
def this_raises_multiple_errors_which_is_retried() -> None:
    """Raises a different exception on each run, all exceptions are handled by decorator"""
    global function_run_counter
    function_run_counter += 1
    if function_run_counter % 2 == 0:
        _ = 90 / 0
    else:
        a = {}
        _ = a.len


class TestRetryDecorator:
    def test_only_specified_exceptions_are_retried(self) -> None:
        """
        Calls function which raises a error which should not be retried.
        and checks function is called only once.
        """
        global function_run_counter
        function_run_counter = 0
        with pytest.raises(ZeroDivisionError):
            this_raises_zero_div_error_which_isnt_retried()

        assert function_run_counter == 1

    def test_all_specified_exceptions_are_retried(self) -> None:
        """
        Calls function which raises a error which should be retried,
        and checks function call is retried required times.
        """
        global function_run_counter
        function_run_counter = 0
        with pytest.raises(ZeroDivisionError):
            this_raises_zero_div_error_which_is_retried()

        assert function_run_counter == MAX_FUNCTION_RUNS

    def test_retries_count(self) -> None:
        """
        Calls function which raises different errors,
        and confirms function call is retried required times.
        """
        global function_run_counter
        function_run_counter = 0
        with pytest.raises(Exception):
            this_raises_multiple_errors_which_is_retried()

        assert function_run_counter == MAX_FUNCTION_RUNS
