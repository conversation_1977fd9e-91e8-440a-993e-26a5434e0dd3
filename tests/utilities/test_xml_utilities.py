import pickle
import xml.etree.ElementTree as ET
from pathlib import Path

import pytest

from swarm_tasks.utilities.xml_utilities import dictify
from swarm_tasks.utilities.xml_utilities import parse_nested_xml

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")


@pytest.fixture
def xml_content_dict():
    with open(f"{SCRIPT_PATH}/data/test_xml_content.pkl", "rb") as f:
        content = pickle.load(f)
    return content


@pytest.fixture
def expected_result():
    with open(f"{SCRIPT_PATH}/data/expected_parsed_xml.pkl", "rb") as f:
        content = pickle.load(f)
    return content


class TestXMLUtilities:
    def test_parse_nested_xml(self, xml_content_dict, expected_result):
        data_path = "Call.Data"
        result = parse_nested_xml(data_path, xml_content_dict)
        assert result == expected_result

    def test_dictify(self):
        et = ET.fromstring(
            """<?xml version="1.0"?>
        <data>
            <country name="Liechtenstein">
                <rank>1</rank>
                <year>2008</year>
                <gdppc>141100</gdppc>
                <neighbor name="Austria" direction="E"/>
                <neighbor name="Switzerland" direction="W"/>
            </country>
            <country name="Singapore">
                <rank>4</rank>
                <year>2011</year>
                <gdppc>59900</gdppc>
                <neighbor name="Malaysia" direction="N"/>
            </country>
            <country name="Panama">
                <rank>68</rank>
                <year>2011</year>
                <gdppc>13600</gdppc>
                <neighbor name="Costa Rica" direction="W"/>
                <neighbor name="Colombia" direction="E"/>
            </country>
        </data>"""
        )
        expected_result = {
            "data": {
                "_text": "\n                ",
                "country": [
                    {
                        "name": "Liechtenstein",
                        "_text": "\n            ",
                        "rank": [{"_text": "1"}],
                        "year": [{"_text": "2008"}],
                        "gdppc": [{"_text": "141100"}],
                        "neighbor": [
                            {"name": "Austria", "direction": "E"},
                            {"name": "Switzerland", "direction": "W"},
                        ],
                    },
                    {
                        "name": "Singapore",
                        "_text": "\n            ",
                        "rank": [{"_text": "4"}],
                        "year": [{"_text": "2011"}],
                        "gdppc": [{"_text": "59900"}],
                        "neighbor": [{"name": "Malaysia", "direction": "N"}],
                    },
                    {
                        "name": "Panama",
                        "_text": "\n            ",
                        "rank": [{"_text": "68"}],
                        "year": [{"_text": "2011"}],
                        "gdppc": [{"_text": "13600"}],
                        "neighbor": [
                            {"name": "Costa Rica", "direction": "W"},
                            {"name": "Colombia", "direction": "E"},
                        ],
                    },
                ],
            }
        }
        expected_result["data"]["_text"] = "\n"
        expected_result["data"]["country"][0]["_text"] = "\n"
        expected_result["data"]["country"][1]["_text"] = "\n"
        expected_result["data"]["country"][2]["_text"] = "\n"

        result = dictify(et)
        result["data"]["_text"] = "\n"
        result["data"]["country"][0]["_text"] = "\n"
        result["data"]["country"][1]["_text"] = "\n"
        result["data"]["country"][2]["_text"] = "\n"

        assert result == expected_result
