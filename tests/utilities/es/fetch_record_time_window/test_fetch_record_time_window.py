import pandas as pd
import pytest
from addict import addict
from dateutil.tz import UTC
from swarm.conf import SettingsCls

from swarm_tasks.utilities.es.fetch_record_time_window.fetch_record_time_window import (
    FetchRecordTimeWindow,
)
from swarm_tasks.utilities.es.fetch_record_time_window.fetch_record_time_window import (
    Params,
)
from swarm_tasks.utilities.es.fetch_record_time_window.fetch_record_time_window import (
    Resources,
)
from swarm_tasks.utilities.es.fetch_record_time_window.flow_args_model import (
    FlowArgsModel,
)


@pytest.fixture
def params():
    return Params(
        **{
            "body": "{'sort': [{'&timestamp': {'order': 'asc'}}], 'query': {'bool': {'must': [{'wildcard': {'dummy_field': 'fc*'}}, {'terms': {'&model': ['dummy_models']}}], 'filter': {'range': {'&timestamp': {'gte': '%gte%', 'lte': '%lte%', 'format': 'epoch_millis'}}}}}}",
            "index": ".dummy_index",
        }
    )


@pytest.fixture
def flow_args_time_difference():
    return FlowArgsModel(**{"time_difference": "24 hours"})


@pytest.fixture
def flow_args_start_and_end_time():
    return FlowArgsModel(
        **{"es_start_time": "2022-01-10 00:00:00", "es_end_time": "2022-02-26"}
    )


@pytest.fixture()
def expected_query_time_difference():
    return {
        "sort": [{"&timestamp": {"order": "asc"}}],
        "query": {
            "bool": {
                "must": [
                    {"wildcard": {"dummy_field": "fc*"}},
                    {"terms": {"&model": ["dummy_models"]}},
                ],
                "filter": {
                    "range": {
                        "&timestamp": {
                            "gte": "1649346311000",
                            "lte": "1649432711000",
                            "format": "epoch_millis",
                        }
                    }
                },
            }
        },
    }


@pytest.fixture()
def expected_query_start_end_time():
    return {
        "sort": [{"&timestamp": {"order": "asc"}}],
        "query": {
            "bool": {
                "must": [
                    {"wildcard": {"dummy_field": "fc*"}},
                    {"terms": {"&model": ["dummy_models"]}},
                ],
                "filter": {
                    "range": {
                        "&timestamp": {
                            "gte": "1641772800000",
                            "lte": "1645833600000",
                            "format": "epoch_millis",
                        }
                    }
                },
            }
        },
    }


@pytest.fixture()
def input_df():
    """
    dummy dataframe, doesn't really matter its contents
    as the `es.scroll` function gets mocked and its result is returned
    """
    return pd.DataFrame.from_dict({"first": [1, 2], "second": ["A", "B"]})


@pytest.fixture()
def expected_df():
    """
    dummy dataframe, but with added `__swarm_raw_index__`
    """
    return pd.DataFrame.from_dict(
        {"__swarm_raw_index__": [0, 1], "first": [1, 2], "second": ["A", "B"]}
    )


class TestFetchRecordTimeWindow:
    def test_build_query_time_difference(
        self,
        mocker,
        params,
        flow_args_time_difference,
        expected_query_time_difference,
        input_df,
    ):
        task = self._init_task(mocker, params, input_df)
        mocker.patch.object(
            pd.Timestamp,
            "utcnow",
            return_value=pd.Timestamp(ts_input="2022-04-08 15:45:11", tzinfo=UTC),
        )
        query = task.build_query(params=params, flow_args=flow_args_time_difference)

        assert query == expected_query_time_difference

    def test_build_query_start_end_time(
        self,
        mocker,
        params,
        flow_args_start_and_end_time,
        expected_query_start_end_time,
        input_df,
    ):
        task = self._init_task(mocker, params, input_df)
        query = task.build_query(params=params, flow_args=flow_args_start_and_end_time)

        assert query == expected_query_start_end_time

    def test_time_difference(
        self, mocker, params, flow_args_time_difference, input_df, expected_df
    ):
        task = self._init_task(mocker, params, input_df)

        result = task.execute(
            params=params, resources=Resources(), flow_args=flow_args_time_difference
        )

        assert not pd.testing.assert_frame_equal(
            pd.read_csv(result[0].path), expected_df
        )

    def test_es_start_and_end_time(
        self, mocker, params, flow_args_start_and_end_time, input_df, expected_df
    ):
        task = self._init_task(mocker, params, input_df)

        result = task.execute(
            params=params, resources=Resources(), flow_args=flow_args_start_and_end_time
        )

        assert not pd.testing.assert_frame_equal(
            pd.read_csv(result[0].path), expected_df
        )

    @staticmethod
    def _init_task(mocker, params, input_df):
        mock_connections = mocker.patch.object(
            SettingsCls, "connections", new_callable=mocker.PropertyMock
        )
        mock_connections.return_value = addict.Dict(
            {"reference-data": {"dummy": "field"}}
        )
        mocker.patch.object(
            SettingsCls, "tenant", new_callable=mocker.PropertyMock, return_value="test"
        )
        mocker.patch.object(FetchRecordTimeWindow, "run_query", return_value=input_df)
        task = FetchRecordTimeWindow(
            name="FetchRecordTimeWindow", params=params, resources=Resources()
        )

        return task
