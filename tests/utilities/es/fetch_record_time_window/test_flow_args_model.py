import pytest

from swarm_tasks.utilities.es.fetch_record_time_window.flow_args_model import (
    FlowArgsModel,
)


class TestFetchRecordTimeWindowFlowArgs:
    def test_time_fields_validations_only_start_time(self):
        with pytest.raises(AttributeError):
            FlowArgsModel(**{"es_start_time": "2022-01-10 00:00:00"})

    def test_time_fields_validations_all_fields(self):
        with pytest.raises(AttributeError):
            FlowArgsModel(
                **{
                    "time_difference": "24 hours",
                    "es_start_time": "2022-01-10 00:00:00",
                    "es_end_time": "2022-02-26",
                }
            )

    def test_time_fields_validations_no_fields(self):
        with pytest.raises(AttributeError):
            FlowArgsModel(**{})

    def test_time_fields_validations_wrong_timedelta(self):
        with pytest.raises(ValueError):
            FlowArgsModel(**{"time_difference": "not a timedelta"})
