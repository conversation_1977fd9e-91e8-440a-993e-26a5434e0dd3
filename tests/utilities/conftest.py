import pandas as pd
import pytest


@pytest.fixture()
def source_instrument_df_sort_venue() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "instrumentIdCode": [
                "EZ2L186SZC20",
                "EZCX77JLVQ70",
                "EZ8PT3NQDCH0",
                "EZQ5J7ZP96K0",
                "EZ7RDWYT8D10",
                "EZZ4TLBS06W7",
            ],
            "cfiAttribute4": [
                "Physical",
                "Cash",
                "Physical",
                "Cash",
                "Cash",
                "Physical",
            ],
            "cfiAttribute3": [
                "Vanilla",
                "Vanilla",
                "Vanilla",
                "Vanilla",
                "Vanilla",
                "Vanilla",
            ],
            "cfiAttribute2": [
                "Random",
                "Random",
                "Random",
                "Random",
                "European Put",
                "European Chooser",
            ],
            "cfiAttribute1": [
                "Spot-Forward swap",
                "Spot-Forward swap",
                "Spot-Forward swap",
                "Spot-Forward swap",
                "Spot-Forward swap",
                "Spot-Forward swap",
            ],
            "venue.tradingVenue": ["XLON", "XXXX", "FXRQ", "XDUB", "360T", pd.NA],
        }
    )
    return df
