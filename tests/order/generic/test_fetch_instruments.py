import addict
import pandas as pd
from swarm.conf import SettingsCls

from swarm_tasks.order.generic import fetch_instruments
from swarm_tasks.order.generic import utils
from swarm_tasks.order.generic.fetch_instruments import FetchInstruments
from swarm_tasks.order.generic.fetch_instruments import Params


class TestFetchInstruments:
    """Test suite for fetch instruments"""

    def test_empty_source_frame_fetch_instruments(self):
        params = Params(
            security_id_col="__security_id__", trade_date="date", exchange="CME"
        )
        task = FetchInstruments(name="FetchInstruments", params=params)
        result = task.process(source_frame=pd.DataFrame(), params=params)
        assert result.equals(pd.DataFrame())

    def test_end_to_end_fetch_instruments(
        self,
        source_frame_fetch_instrument,
        expected_fetch_instruments_df,
        expected_target_df_fetch_instruments,
        mocker,
    ):
        mock_connections = mocker.patch.object(
            SettingsCls, "connections", new_callable=mocker.PropertyMock
        )
        mock_connections.return_value = addict.Dict(
            {
                "tenant-data": {
                    "meta": {
                        "id": "&id",
                        "model": "&model",
                        "hash": "&hash",
                        "prefix": "&",
                        "key": "&key",
                    },
                    "MAX_TERMS_SIZE": 1024,
                }
            }
        )
        instruments = mocker.patch.object(
            fetch_instruments, "get_venue_direct_instrument"
        )
        instruments.return_value = expected_fetch_instruments_df
        params = Params(
            security_id_col="__security_id__",
            trade_date="date",
            get_multileg_indicator=True,
            multileg_indicator="__is_multileg_trade__",
            exchange="CME",
        )
        task = FetchInstruments(name="FetchInstruments", params=params)
        result = task.process(source_frame=source_frame_fetch_instrument, params=params)
        assert result.fillna(pd.NA).equals(expected_target_df_fetch_instruments)

    def test_end_to_end_fetch_instruments_with_negative_time_delta(
        self,
        source_frame_fetch_instrument,
        expected_fetch_instruments_df,
        expected_target_df_fetch_instruments,
        mocker,
    ):
        mock_connections = mocker.patch.object(
            SettingsCls, "connections", new_callable=mocker.PropertyMock
        )
        mock_connections.return_value = addict.Dict(
            {
                "tenant-data": {
                    "meta": {
                        "id": "&id",
                        "model": "&model",
                        "hash": "&hash",
                        "prefix": "&",
                        "key": "&key",
                    },
                    "MAX_TERMS_SIZE": 1024,
                }
            }
        )
        instruments = mocker.patch.object(
            fetch_instruments, "get_venue_direct_instrument"
        )
        instruments.return_value = expected_fetch_instruments_df
        params = Params(
            security_id_col="__security_id__",
            trade_date="date",
            get_multileg_indicator=True,
            multileg_indicator="__is_multileg_trade__",
            exchange="CME",
        )
        # Setting the trade date greater than instrument &timestamp
        source_frame_fetch_instrument["date"][0] = "2022-05-06"
        task = FetchInstruments(name="FetchInstruments", params=params)
        result = task.process(source_frame=source_frame_fetch_instrument, params=params)
        assert result.fillna(pd.NA).equals(expected_target_df_fetch_instruments)

    def test_end_to_end_fetch_instruments_with_exchange(
        self,
        source_frame_fetch_instrument,
        expected_fetch_instruments_df_only_cme,
        expected_target_df_fetch_instruments_only_cme,
        mocker,
    ):
        mock_connections = mocker.patch.object(
            SettingsCls, "connections", new_callable=mocker.PropertyMock
        )
        mock_connections.return_value = addict.Dict(
            {
                "tenant-data": {
                    "meta": {
                        "id": "&id",
                        "model": "&model",
                        "hash": "&hash",
                        "prefix": "&",
                        "key": "&key",
                    },
                    "MAX_TERMS_SIZE": 1024,
                }
            }
        )
        instruments = mocker.patch.object(
            fetch_instruments, "get_venue_direct_instrument"
        )
        instruments.return_value = expected_fetch_instruments_df_only_cme
        params = Params(
            security_id_col="__security_id__",
            trade_date="date",
            get_multileg_indicator=True,
            multileg_indicator="__is_multileg_trade__",
            exchange="CME",
        )
        task = FetchInstruments(name="FetchInstruments", params=params)
        result = task.process(source_frame=source_frame_fetch_instrument, params=params)
        assert result.fillna(pd.NA).equals(
            expected_target_df_fetch_instruments_only_cme
        )

    def test_end_to_end_fetch_instruments_with_exchange_venue_col(
        self,
        source_frame_with_exchange_venue_col,
        expected_fetch_instruments_cme_exchange_venue_col,
        expected_fetch_instruments_eurex_exchange_venue_col,
        expected_output_with_exchange_venue_col,
        mocker,
    ):
        mock_connections = mocker.patch.object(
            SettingsCls, "connections", new_callable=mocker.PropertyMock
        )
        mock_connections.return_value = addict.Dict(
            {
                "reference-data": {
                    "meta": {
                        "id": "&id",
                        "model": "&model",
                        "hash": "&hash",
                        "prefix": "&",
                        "key": "&key",
                    },
                    "MAX_TERMS_SIZE": 1024,
                }
            }
        )
        instruments = mocker.patch.object(utils, "es_scroll")

        instruments.side_effect = [
            expected_fetch_instruments_cme_exchange_venue_col,
            expected_fetch_instruments_eurex_exchange_venue_col,
        ]
        params = Params(
            security_id_col="__exchange_symbol_local__",
            trade_date="date",
            get_multileg_indicator=True,
            multileg_indicator="__is_multileg_trade__",
            exchange_venue_col="__exchange_venue__",
        )
        task = FetchInstruments(name="FetchInstruments", params=params)
        result = task.process(
            source_frame=source_frame_with_exchange_venue_col, params=params
        )
        pd.testing.assert_frame_equal(
            left=result.fillna(pd.NA), right=expected_output_with_exchange_venue_col
        )
