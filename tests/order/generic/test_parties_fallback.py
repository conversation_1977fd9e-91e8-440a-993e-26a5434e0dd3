from dataclasses import dataclass
from typing import Dict
from typing import List
from typing import Union

import pandas as pd
import pytest

from swarm_tasks.order.generic.parties_fallback import MARKET_COUNTERPARTY_META_KEY
from swarm_tasks.order.generic.parties_fallback import MARKET_PERSON_META_KEY
from swarm_tasks.order.generic.parties_fallback import Params
from swarm_tasks.order.generic.parties_fallback import PartiesFallback
from swarm_tasks.order.generic.parties_fallback import <PERSON>Fields
from swarm_tasks.order.generic.parties_fallback import <PERSON>Field
from swarm_tasks.order.generic.parties_fallback import PartyType
from swarm_tasks.order.generic.parties_fallback import Resources


@pytest.fixture()
def input_params() -> Params:
    input_params = Params(
        buyer_attribute="__buyer__",
        seller_attribute="__seller__",
        executing_entity_attribute="__exec_entity__",
    )
    return input_params


@pytest.fixture
def resource() -> Resources:
    resource = Resources(es_client_key="tenant-data")
    return resource


@pytest.fixture
def task_with_mocked_es_client(mocker, resource):
    es_mock = mocker.Mock()
    es_mock.meta.key = "&key"
    es_mock.meta.id = "&id"
    task = PartiesFallback(name="PartiesFallback", resources=resource)
    task.clients = {"tenant-data": es_mock}
    return task


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """
    creates empty dataframe
    """
    df = pd.DataFrame()
    return df


@pytest.fixture()
def all_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            PartiesFields.PARTIES_BUYER.value: [
                [{"id": "linked_buyer1"}],
                [{"id": "linked_buyer2"}],
                pd.NA,
            ],
            PartiesFields.PARTIES_SELLER.value: [
                [{"id": "linked_seller1"}],
                [{"id": "linked_seller2"}],
                pd.NA,
            ],
            PartiesFields.PARTIES_EXECUTING_ENTITY.value: [
                {"id": "linked_entity1"},
                {"id": "linked_entity2"},
                pd.NA,
            ],
            "__buyer__": ["buyer_1", "buyer_2", "buyer_3"],
            "__seller__": ["seller_1", "seller_2", "seller_3"],
            "__exec_entity__": ["exec_entity_1", "exec_entity_2", "exec_entity_3"],
            "__buy_sell": ["BUYI", "BUYI", "BUYI"],
        }
    )
    return df


@pytest.fixture()
def expected_result_all_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            PartiesFields.PARTIES_BUYER.value: [
                [{"id": "linked_buyer1"}],
                [{"id": "linked_buyer2"}],
                [{"name": "buyer_3", "&id": "buyer_3", "&key": MARKET_PERSON_META_KEY}],
            ],
            PartiesFields.PARTIES_BUYER_DECISION_MAKER.value: pd.NA,
            PartiesFields.PARTIES_CLIENT.value: pd.NA,
            PartiesFields.PARTIES_COUNTERPARTY.value: pd.NA,
            PartiesFields.PARTIES_EXECUTING_ENTITY.value: [
                {"id": "linked_entity1"},
                {"id": "linked_entity2"},
                {
                    "name": "exec_entity_3",
                    "&id": "exec_entity_3",
                    "&key": MARKET_COUNTERPARTY_META_KEY,
                },
            ],
            PartiesFields.PARTIES_EXECUTION_WITHIN_FIRM.value: pd.NA,
            PartiesFields.PARTIES_INVEST_DEC_WITHIN_FIRM.value: pd.NA,
            PartiesFields.PARTIES_SELLER.value: [
                [{"id": "linked_seller1"}],
                [{"id": "linked_seller2"}],
                [
                    {
                        "name": "seller_3",
                        "&id": "seller_3",
                        "&key": MARKET_PERSON_META_KEY,
                    }
                ],
            ],
            PartiesFields.PARTIES_SELLER_DECISION_MAKER.value: pd.NA,
            PartiesFields.PARTIES_TRADER.value: pd.NA,
        }
    )
    return df


@dataclass
class FormatSellPartyCase:
    account_ids: Union[str, List[str]]
    party_type: PartyType
    expect_result: Union[Dict, List]
    as_list: bool


class TestPartiesFallback(object):
    """
    Test cases for the "TestPartiesFallback" class
    """

    def test_empty_input_df_without_source_columns(
        self,
        empty_source_df: pd.DataFrame,
    ):
        task = PartiesFallback(name="test-parties-fallback", params=None)
        result = task.execute(empty_source_df, params=None)
        assert result.empty

    def test_all_col_in_source_df(
        self,
        all_col_in_source_df: pd.DataFrame,
        input_params: Params,
        expected_result_all_col_in_source_df,
        task_with_mocked_es_client,
        resource,
        mocker,
    ):
        es_mock = mocker.Mock()
        es_mock.meta.key = "&key"
        es_mock.meta.id = "&id"
        result = task_with_mocked_es_client.execute(
            all_col_in_source_df, params=input_params, resources=resource
        )
        assert result.equals(expected_result_all_col_in_source_df)

    def test_it_can__format_sell_parties(self):
        source_frame: pd.DataFrame = pd.DataFrame(
            {
                PartiesFields.PARTIES_CLIENT: [
                    ["CGF_HEAL"],
                    ["HAKA", "VANCOUVER", "CGF_WATE"],
                    "HAKA",
                    pd.NA,
                ]
            }
        )

        party_type: str = PartyType.FIRM

        result: pd.Series = PartiesFallback._format_parties(
            source=source_frame,
            name=PartiesFields.PARTIES_CLIENT,
            meta_key="&key",
            meta_id="&id",
            party_type=party_type,
            mask=source_frame[PartiesFields.PARTIES_CLIENT].notnull(),
            as_list=False,
        )

        pd.testing.assert_series_equal(
            left=result,
            right=pd.Series(
                data=[
                    [
                        {
                            "&id": "CGF_HEAL",
                            "&key": "MarketCounterparty:",
                            "name": "CGF_HEAL",
                        }
                    ],
                    [
                        {"&id": "HAKA", "&key": "MarketCounterparty:", "name": "HAKA"},
                        {
                            "&id": "VANCOUVER",
                            "&key": "MarketCounterparty:",
                            "name": "VANCOUVER",
                        },
                        {
                            "&id": "CGF_WATE",
                            "&key": "MarketCounterparty:",
                            "name": "CGF_WATE",
                        },
                    ],
                    # The following ensure the old behabiour is preserved
                    {"&id": "HAKA", "&key": "MarketCounterparty:", "name": "HAKA"},
                ]
            ),
        )

    @pytest.mark.parametrize(
        "case",
        [
            FormatSellPartyCase(
                # Test `PartyField.NAME` is a list a list is returned
                # https://steeleye.atlassian.net/browse/ON-3469
                account_ids=["HAKA", "VANCOUVER", "CGF_WATE"],
                party_type=PartyType.FIRM,
                expect_result=[
                    {"&id": "HAKA", "&key": "MarketCounterparty:", "name": "HAKA"},
                    {
                        "&id": "VANCOUVER",
                        "&key": "MarketCounterparty:",
                        "name": "VANCOUVER",
                    },
                    {
                        "&id": "CGF_WATE",
                        "&key": "MarketCounterparty:",
                        "name": "CGF_WATE",
                    },
                ],
                as_list=False,  # this is irrelevant for this test case
            ),
            FormatSellPartyCase(
                # Test if `party_type` is not `PartyType.PERSON` a dict is returned
                account_ids="HAKA",
                party_type=PartyType.FIRM,
                expect_result={
                    "&id": "HAKA",
                    "&key": "MarketCounterparty:",
                    "name": "HAKA",
                },
                as_list=False,
            ),
            FormatSellPartyCase(
                # Test if `party_type` is `PartyType.PERSON` a list is returned
                account_ids="HAKA",
                party_type=PartyType.PERSON,
                expect_result=[
                    {"&id": "HAKA", "&key": "MarketCounterparty:", "name": "HAKA"}
                ],
                as_list=True,
            ),
        ],
    )
    def test_it_can__format_sell_party(self, case: FormatSellPartyCase):
        meta_key: str = "&key"
        meta_id: str = "&id"

        series: pd.Series = pd.Series(
            {
                PartyField.NAME: case.account_ids,
                meta_id: case.account_ids,
                meta_key: MARKET_COUNTERPARTY_META_KEY,
            }
        )

        result: Dict = PartiesFallback._format_single_party(
            party=series,
            as_list=case.as_list,
            meta_key=meta_key,
            meta_id=meta_id,
        )

        assert result == case.expect_result
