��	      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK7��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo�� executionDetails.tradingCapacity��executionDetails.validityPeriod��	hierarchy��	_order.id��_orderState.id��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers��_order.__meta_model__��_orderState.__meta_model__��
orderClass��"orderIdentifiers.aggregatedOrderId��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��.orderIdentifiers.tradingVenueTransactionIdCode��!orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��"priceFormingData.remainingQuantity��+_orderState.priceFormingData.tradedQuantity��reportDetails.transactionRefNo��	sourceKey��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��timestamps.tradingDateTime��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��!transactionDetails.positionEffect��transactionDetails.quantity��$_order.transactionDetails.recordType��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime��	__price__��__last_px__��__stop_px__��__newo_in_file__��instrumentDetails.instrument�et�b�name�Nu��R�h
�pandas.core.indexes.range��
RangeIndex���}�(h]N�start�K �stop�K�step�Ku��R�e]�hhK ��h��R�(KK7K��h!�]�(�SELL�ho�ICE POF Exchange��
2023-05-08�ho�NEWO��PARF��Limit��0|1749|KBLJUWA��AOTC�]��DAVY�a�
Standalone��183491413559|5792127�hz]�(}�(�labelId��id:kyte��path��buyer��type��se_schema.static.market��IdentifierType����ARRAY���R�u}�(h}�id:kyte�h�reportDetails.executingEntity�h�h��OBJECT���R�u}�(h}�id:ice�h�seller�h�h�u}�(h}�id:ice�h�counterparty�h�h�u}�(h}�id:1001�h�1tradersAlgosWaiversIndicators.executionWithinFirm�h�h�u}�(h}�id:2119�h�clientIdentifiers.client�h�h�u}�(h}�id:kbltt-fx1�h�trader�h�h�ue�id:kyte��id:kyte��id:ice��id:ice��pandas._libs.missing��NA���h�h��id:1001��id:2119��id:kbltt-fx1�]�(h|h�h�h�h�h�h�e�Order��
OrderState��
Regular Trade�h��2764138�hzhz�2764139�G@I      G@H�     G?�      h���s3://shrenik.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/kyte_test/0020a396ca42a542f1487d9e9545dc7f3776a2de433e6a0c4ad68d509d09d47b_9406.fix��2023-05-08T06:29:57.254312Z��2023-05-08T06:29:57.254312Z��2023-05-08T06:29:57.254312Z�h�hoh��Open�G?�      �Market Side�hvh�G@X;33333G@X;33333h��}�(�	sourceKey��*iceInstruments.IFLL.FCXXXX.20230426.221540��&instrumentClassificationEMIRAssetClass��CO��&id��IFLL|IFMM0024!|20240617|EUR��cfiGroup��Commodities futures��cfiCategory��Futures��instrumentFullName��IFLL - I - Jun-24 - Future��&key��<VenueDirectInstrument:IFLL|IFMM0024!|20240617|EUR:1682550716��notionalCurrency1��EUR��ext.exchangeSymbolLocal��5792127��ext.instrumentUniqueIdentifier�h��venue.tradingVenue��IFLL��derivative.expiryDate��
2024-06-17��derivative.priceDisplayFactor�K� derivative.underlyingInstruments�]�}��underlyingInstrumentCode��GB00F71K4T03�sa�derivative.isUserDefinedSpread��uet�ba]�h
h}�(hhhK ��h��R�(KK7��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3h4h5h6h7h8h9h:h;h<h=h>h?h@hAhBhChDhEhFhGhHhIhJhKhLhMhNhOhPhQhRhShThUhVhWhXhYhZh[et�bh]Nu��R�a}��0.14.1�}�(�axes�h
�blocks�]�}�(�values�hl�mgr_locs��builtins��slice���K K7K��R�uaust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.