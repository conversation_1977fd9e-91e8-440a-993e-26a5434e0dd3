���.      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK7��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo�� executionDetails.tradingCapacity��executionDetails.validityPeriod��	hierarchy��	_order.id��_orderState.id��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers��_order.__meta_model__��_orderState.__meta_model__��
orderClass��"orderIdentifiers.aggregatedOrderId��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��.orderIdentifiers.tradingVenueTransactionIdCode��!orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��"priceFormingData.remainingQuantity��+_orderState.priceFormingData.tradedQuantity��reportDetails.transactionRefNo��	sourceKey��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��timestamps.tradingDateTime��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��!transactionDetails.positionEffect��transactionDetails.quantity��$_order.transactionDetails.recordType��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime��	__price__��__last_px__��__stop_px__��__newo_in_file__��instrumentDetails.instrument�et�b�name�Nu��R�h
�pandas.core.indexes.range��
RangeIndex���}�(h]N�start�K �stop�K�step�Ku��R�e]�(hhK ��h��R�(KK1K��h!�]�(�SELL�hoho�BUYI�hphp�BUYI��SELL�hrhrhqhohohohphphphqhrhrhrhq�ICE POF Exchange�hshshshshshshshshshs�
2023-03-14�hththththt�
2023-03-14��
2023-03-14��
2023-03-14��
2023-03-14��
2023-03-14�hohohohphphphqhrhrhrhq�NEWO�hzhzhzhzhzhzhzhzhzhz�pandas._libs.missing��NA���h}h}h}h}h}�FILL�h~h~h~h~�Limit�hhhhhhhhhh�805|200SG761�h�h�h�h�h��0|805|200SG761��0|805|200SG761��0|805|200SG761��0|805|200SG761��0|805|200SG761��AOTC�h�h�h�h�h�h�h�h�h�h�]��GTCV�ah�h�h�h�h�h�h�h�h�h��
Standalone�h�h�h�h�h�h�h�h�h�h��303350403|5652854��303350403|5652856��303350403|5652858��303350403|5652860��303350403|5652862��303350403|5652864��303350403|5652862��303350403|5652856��303350403|5652858��303350403|5652854��303350403|5652860�h�h�h�h�h�h�h�h�h�h�h�]�(}�(�labelId��id:socar��path��buyer��type��se_schema.static.market��IdentifierType����ARRAY���R�u}�(h��id:socar�h��reportDetails.executingEntity�h�h��OBJECT���R�u}�(h��id:ice�h��seller�h�h�u}�(h��id:ice�h��counterparty�h�h�u}�(h��id:3�h��1tradersAlgosWaiversIndicators.executionWithinFirm�h�h�u}�(h��id:7073�h��clientIdentifiers.client�h�h�u}�(h��
id:ecoutya�h��trader�h�h�ueh�h�h�h�h�]�(}�(h��id:socar�h�h�h�h�u}�(h��id:socar�h�h�h�h�u}�(h��id:ice�h�h�h�h�u}�(h��id:ice�h�h�h�h�u}�(h��id:3�h�h�h�h�u}�(h��id:7073�h�h�h�h�u}�(h��
id:ecoutya�h�h�h�h�ue]�(}�(h��id:socar�h�h�h�h�u}�(h��id:socar�h�h�h�h�u}�(h��id:ice�h�h�h�h�u}�(h��id:ice�h�h�h�h�u}�(h��id:3�h�h�h�h�u}�(h��id:7073�h�h�h�h�u}�(h��
id:ecoutya�h�h�h�h�ue]�(}�(h��id:socar�h�h�h�h�u}�(h��id:socar�h�h�h�h�u}�(h��id:ice�h�h�h�h�u}�(h��id:ice�h�h�h�h�u}�(h��id:3�h�h�h�h�u}�(h��id:7073�h�h�h�h�u}�(h��
id:ecoutya�h�h�h�h�ue]�(}�(h��id:socar�h�h�h�h�u}�(h��id:socar�h�h�h�h�u}�(h��id:ice�h�h�h�h�u}�(h��id:ice�h�h�h�h�u}�(h��id:3�h�h�h�h�u}�(h��id:7073�h�h�h�h�u}�(h��
id:ecoutya�h�h�h�h�ue]�(}�(h��id:socar�h�h�h�h�u}�(h��id:socar�h�h�h�h�u}�(h��id:ice�h�h�h�h�u}�(h��id:ice�h�h�h�h�u}�(h��id:3�h�h�h�h�u}�(h��id:7073�h�h�h�h�u}�(h��
id:ecoutya�h�h�h�h�ue�id:socar�j  j  j  j  j  �id:socar��id:socar��id:socar��id:socar��id:socar��id:socar�j  j  j  j  j  �id:socar��id:socar��id:socar��id:socar��id:socar��id:ice�j  j  j  j  j  �id:ice��id:ice��id:ice��id:ice��id:ice��id:ice�j  j  j  j  j  �id:ice��id:ice��id:ice��id:ice��id:ice�h}h}h}h}h}h}h}h}h}h}h}h}h}h}h}h}h}h}h}h}h}h}h}h}h}h}h}h}h}h}h}h}h}�id:3�j  j  j  j  j  �id:3��id:3��id:3��id:3��id:3��id:7073�j   j   j   j   j   �id:7073��id:7073��id:7073��id:7073��id:7073��
id:ecoutya�j&  j&  j&  j&  j&  �
id:ecoutya��
id:ecoutya��
id:ecoutya��
id:ecoutya��
id:ecoutya�]�(h�h�h�h�h�h�h�ej,  j,  j,  j,  j,  ]�(h�h�h�h�h�h�h�e]�(h�h�h�h�h�h�h�e]�(h�h�h�h�h�h�h�e]�(h�h�h�h�h�h�h�e]�(h�h�h�h�h�h�j   e�Order�j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  �
OrderState�j3  j3  j3  j3  j3  j3  j3  j3  j3  j3  h}h}h}h}h}h}�
Regular Trade�j4  j4  j4  j4  h}h}h}h}h}h}�6021515��6021515��6021515��6021515��6021515��5562119�j:  j:  j:  j:  j:  �5562119��5562119��5562119��5562119��5562119�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��303350403:1:Ack�j@  j@  j@  j@  j@  �6021522��6021518��6021519��6021517��6021521�j@  j@  j@  j@  j@  j@  jA  jB  jC  jD  jE  ��s3://integration.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/Mleg_on_mleg/2f7d095672f3b312d0a62fabc3da40aa13baa5823c11d97d68f4a03445942564_155459.fix�jF  jF  jF  jF  jF  ��s3://integration.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/Mleg_on_mleg/6e155637cf506aff50ff7dfa5cf7e2870d006b8fb4d0c8d84b6e85f73f7964d2_156615.fix���s3://integration.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/Mleg_on_mleg/7eaf29ba590828c41bd2259501c0572b71b22df70dee203698cb404503e15066_156611.fix���s3://integration.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/Mleg_on_mleg/87cf1550c34ae7829d8c6365a3bd7b309cb637107bfca6465b343da15ff5ba7a_156612.fix���s3://integration.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/Mleg_on_mleg/c67e1316b84f7b544cccbefedf074487e19473a51bbef4b9927d6aee707eb5c4_156610.fix���s3://integration.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/Mleg_on_mleg/dcfa75f4b8cf9ec02f618a3bd0f20774aaa665382dfac639326d304eb0c4e3fc_156614.fix��2023-03-14T17:16:24.248318Z�jL  jL  jL  jL  jL  �2023-03-14T18:10:40.139200Z��2023-03-14T18:10:40.139200Z��2023-03-14T18:10:40.139200Z��2023-03-14T18:10:40.139200Z��2023-03-14T18:10:40.139200Z��2023-03-14T17:16:24.248318Z�jR  jR  jR  jR  jR  �2023-03-14T18:10:40.139200Z��2023-03-14T18:10:40.139200Z��2023-03-14T18:10:40.139200Z��2023-03-14T18:10:40.139200Z��2023-03-14T18:10:40.139200Z��2023-03-14T17:16:24.248318Z�jX  jX  jX  jX  jX  �2023-03-14T18:10:40.139200Z��2023-03-14T18:10:40.139200Z��2023-03-14T18:10:40.139200Z��2023-03-14T18:10:40.139200Z��2023-03-14T18:10:40.139200Z�jL  jL  jL  jL  jL  jL  jM  jN  jO  jP  jQ  hohohohphphphqhrhrhrhqj:  j:  j:  j:  j:  j:  j;  j<  j=  j>  j?  h}h}h}h}h}h}h}h}h}h}h}�Market Side�j^  j^  j^  j^  j^  j^  j^  j^  j^  j^  h�h�h�h�h�h�h�h�h�h�h�jL  jL  jL  jL  jL  jL  jM  jN  jO  jP  jQ  h}h}h}h}h}h}h}h}h}h}h}�����������}�(�	sourceKey��*iceInstruments.IFEU.FCXXXX.20230424.034508��&id��IFEU|ULDSMJ0023!|20230428|USD��instrumentFullName��IFEU - ULD - Apr-23 - Future��&key��>VenueDirectInstrument:IFEU|ULDSMJ0023!|20230428|USD:1682308667��notionalCurrency1��USD��ext.exchangeSymbolLocal��5652854��ext.instrumentUniqueIdentifier�jc  �venue.tradingVenue��IFEU��derivative.priceDisplayFactor�K� derivative.underlyingInstruments�]�}��underlyingInstrumentCode��GB00H208M169�sa�derivative.isUserDefinedSpread��u}�(j`  ja  jb  �IFEU|ULDSMK0023!|20230531|USD�jd  �IFEU - ULD - May-23 - Future�jf  �>VenueDirectInstrument:IFEU|ULDSMK0023!|20230531|USD:1682308667�jh  ji  jj  �5652856�jl  jw  jm  jn  jo  Kjp  ]�}�js  �GB00H208M276�saju  �u}�(j`  ja  jb  �IFEU|ULDSMM0023!|20230630|USD�jd  �IFEU - ULD - Jun-23 - Future�jf  �>VenueDirectInstrument:IFEU|ULDSMM0023!|20230630|USD:1682308667�jh  ji  jj  �5652858�jl  j  jm  jn  jo  Kjp  ]�}�js  �GB00H208M383�saju  �u}�(j`  ja  jb  �IFEU|ULDSMN0023!|20230731|USD�jd  �IFEU - ULD - Jul-23 - Future�jf  �>VenueDirectInstrument:IFEU|ULDSMN0023!|20230731|USD:1682308667�jh  ji  jj  �5652860�jl  j�  jm  jn  jo  Kjp  ]�}�js  �GB00H208M490�saju  �u}�(j`  ja  jb  �IFEU|ULDSMQ0023!|20230831|USD�jd  �IFEU - ULD - Aug-23 - Future�jf  �>VenueDirectInstrument:IFEU|ULDSMQ0023!|20230831|USD:1682308667�jh  ji  jj  �5652862�jl  j�  jm  jn  jo  Kjp  ]�}�js  �GB00H208M508�saju  �u}�(j`  ja  jb  �IFEU|ULDSMU0023!|20230929|USD�jd  �IFEU - ULD - Sep-23 - Future�jf  �>VenueDirectInstrument:IFEU|ULDSMU0023!|20230929|USD:1682308667�jh  ji  jj  �5652864�jl  j�  jm  jn  jo  Kjp  ]�}�js  �GB00H208M615�saju  �u}�(j`  �*iceInstruments.IFEU.FCXXXX.20230424.034508��&id��IFEU|ULDSMQ0023!|20230831|USD�jd  �IFEU - ULD - Aug-23 - Future��&key��>VenueDirectInstrument:IFEU|ULDSMQ0023!|20230831|USD:1682308667�jh  ji  �ext.exchangeSymbolLocal�j�  �ext.instrumentUniqueIdentifier�j�  �venue.tradingVenue�jn  �derivative.isUserDefinedSpread���derivative.legs�h}� derivative.underlyingInstruments�]�}�js  j�  sau}�(j`  j�  j�  �IFEU|ULDSMK0023!|20230531|USD�jd  �IFEU - ULD - May-23 - Future�j�  �>VenueDirectInstrument:IFEU|ULDSMK0023!|20230531|USD:1682308667�jh  ji  j�  jz  j�  j�  j�  jn  j�  �j�  h}j�  ]�}�js  j}  sau}�(j`  j�  j�  �IFEU|ULDSMM0023!|20230630|USD�jd  �IFEU - ULD - Jun-23 - Future�j�  �>VenueDirectInstrument:IFEU|ULDSMM0023!|20230630|USD:1682308667�jh  ji  j�  j�  j�  j�  j�  jn  j�  �j�  h}j�  ]�}�js  j�  sau}�(j`  j�  j�  �IFEU|ULDSMJ0023!|20230428|USD�jd  �IFEU - ULD - Apr-23 - Future�j�  �>VenueDirectInstrument:IFEU|ULDSMJ0023!|20230428|USD:1682308667�jh  ji  j�  jk  j�  j�  j�  jn  j�  �j�  h}j�  ]�}�js  jt  sau}�(j`  j�  j�  �IFEU|ULDSMN0023!|20230731|USD�jd  �IFEU - ULD - Jul-23 - Future�j�  �>VenueDirectInstrument:IFEU|ULDSMN0023!|20230731|USD:1682308667�jh  ji  j�  j�  j�  j�  j�  jn  j�  �j�  h}j�  ]�}�js  j�  sauet�bhhK ��h��R�(KKK��h�f8�����R�(K�<�NNNJ����J����K t�b�B        �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?                                                                                              �?      �?      �?      �?      �?                                                      �?      �?      �?      �?      �?ffffff�?ffffff�?ffffff�?ffffff�?ffffff�?ffffff�?ffffff�?ffffff�?ffffff�?ffffff�?ffffff�?                                                ������7@     @9@     @9@     @9@������7@�t�be]�(h
h}�(hhhK ��h��R�(KK1��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3h4h5h6h7h8h9h:h;h<h=h>h?h@hAhBhChDhEhFhJhKhLhMhNhOhPhQhRhThUhVhYhZh[et�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hGhHhIhShWhXet�bh]Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hl�mgr_locs�hhK ��h��R�(KK1��h�i8�����R�(Kj�  NNNJ����J����K t�b�B�                                                                  	       
                     
                                                                                                                                             !       %       &       '       (       )       *       +       ,       -       /       0       1       4       5       6       �t�bu}�(j�  j�  j�  hhK ��h��R�(KK��j�  �C0"       #       $       .       2       3       �t�bueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.