���)      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK;��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�Activity��Status (Tag 39)��Type��Receive Date Time��As Of Date Time (Tag 60)��
Side (Tag 54)��Quantity (Tag 53)��Fill Quantity (Tag 32)��Ticker (Tag 55)��Order Type (Tag 40)��TIF (Tag59)��Execution Price (Tag 31)��Average Price (Tag 6)��Limit Price (Tag 44)��Broker��Order ID��Route ID��Fill ID��Account (Tag 1)��Trader UUID��Instruction (Tag 58)��Product��Liquidity (Tag 851)��Last Capacity (Tag 29)��Last Market (Tag 30)��
Strategy Name��Transaction Reporting MIC��Price Currency��Trader Name��Order #��FIGI��Contract Expiration��ISIN��Local Exch Symbol��Last Market��Parsekey��
Security Name��__order_status__��__external_order_received__��__internal_order_submitted__��__order_submitted__��__order_received__��__trading_date_time__��__order_status_updated__��Buyside LEI��Full Exch Symbol��GTD Date (Tag 126)��
OCC_Symbol��Stop Price (Tag 99)��PM Name��Tran Account��Originator LEI��__quantity_newo__��__last_fill_filled_quantity__��__last_fill_fill_id__��__last_fill_price__��__last_fill_quantity__��__last_fill_receive_date_time__��__last_fill_limit_price__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C(                                    �t�bha�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�	New-Route��
Exec-Trade��	Exec-Done�h�h�et�b�_dtype�hw�StringDtype���)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�Sent��Part-filled��Cancel�h�h�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�R�h�h�h�h�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�10/10/2023 02:45:30.603000��10/10/2023 03:00:16.708000��10/10/2023 11:36:22.438000��10/10/2023 02:45:30.618000��10/10/2023 03:05:15.634000�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�10/10/2023 02:45:30.568000��10/10/2023 03:00:16.541000��10/10/2023 11:36:22.316000��10/10/2023 02:45:30.609000��10/10/2023 03:05:15.484000�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�SELL�h�hǌBUY/C�h�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�AIXA GR Equity�h�hҌ
ASM NA Equity�h�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�MKT�h�h�h�h�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�DAY�h�h�h�h�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�MKT�h�h�h�h�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�DNCEU�h�h�h�h�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�O.EMT.795.1468573.20231009�j  j  �O.EMT.795.1468575.20231009�j  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�R.EMT.795.1696920330--179044260�j  j  �R.EMT.795.1696920330--179044259�j  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�pandas._libs.missing��NA����!F.EMT.795.1696920330--179044260.3�j  j  �!F.EMT.795.1696920330--179044259.3�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(j  j  j  j  j  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�
19290170.0�j2  j2  j2  j2  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(j  j  j  j  j  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�Equity�jE  jE  jE  jE  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(j  �Auction�j  j  �Taker�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(j  �A�j  j  jZ  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(j  �XETA�j  j  �AQEU�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(j  j  j  j  j  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(j  j  j  j  j  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�EUR�j�  j�  j�  j�  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�STU MCQUAID�j�  j�  j�  j�  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�1534618��1534618��1534618��1534620��1534620�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�BBG000BFT9L9�j�  j�  �BBG000F5L454�j�  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�00/00/00�j�  j�  j�  j�  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�DE000A0WMPJ6�j�  j�  �NL0000334118�j�  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(j  j  j  �ASM�j�  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�XETA�j�  j�  �AQEU�j�  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�AIXA GR Equity�j�  j�  �
ASM NA Equity�j�  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�
AIXTRON SE�j�  j�  �ASM INTL NV�j�  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�NEWO��PARF��CAME�j�  j�  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�10/10/2023 02:45:35.308000�j  j  �10/10/2023 02:45:37.138000�j  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�10/10/2023 02:45:30.603000�j  j  �10/10/2023 02:45:30.618000�j  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�10/10/2023 02:45:35.308000�j  �10/10/2023 11:36:22.438000��10/10/2023 02:45:37.138000�j  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�10/10/2023 02:45:30.662000�j   �10/10/2023 11:36:22.438000��10/10/2023 02:45:30.670000�j"  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(�10/10/2023 02:45:30.603000��10/10/2023 03:00:16.708000��10/10/2023 11:36:22.438000��10/10/2023 02:45:30.618000��10/10/2023 03:05:15.634000�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(j  �10/10/2023 02:45:35.308000�j  j  �10/10/2023 02:45:37.138000�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(j  j  j  j  j  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(j  j  j  j  j  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(j  j  j  j  j  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h��]�(j  j  j  j  j  et�bh�h�)��ubhhK ��h��R�(KKK��h�f8�����R�(KhoNNNJ����J����K t�b�CP    ��A    ��A    ��@    ���@    ���@      �      �      �      �      ��t�bhhK ��h��R�(KK
K��j�  �]�(j  G@�    j  j  G@��     j  G@?�
:�Azj  j  G@x�z0��Hj  G@@@     G@?�
:�A|j  G@x������j  �Who��What��When��Where�j  j  j  j  j  �635400HAZINIQWXBII49�j  ju  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h(at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h)at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h9at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h:at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h<at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h=at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h?at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h@at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hBat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hCat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hDat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hEat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hFat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hGat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hHat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hIat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hJat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hKat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hLat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hMat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hNat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hOat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hPat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hQat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hRat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hSat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hTat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h+hUet�bhaNu��R�h
h}�(hhhK ��h��R�(KK
��h!�]�(h,h0h1hVhWhXhYhZh[h\h]h^h_et�bhaNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hz�mgr_locs��builtins��slice���K KK��R�u}�(j  h�j  j!  KKK��R�u}�(j  h�j  j!  KKK��R�u}�(j  h�j  j!  KKK��R�u}�(j  h�j  j!  KKK��R�u}�(j  h�j  j!  KKK��R�u}�(j  h�j  j!  KK	K��R�u}�(j  h�j  j!  K	K
K��R�u}�(j  h�j  j!  K
KK��R�u}�(j  h�j  j!  K
KK��R�u}�(j  h�j  j!  KKK��R�u}�(j  h�j  j!  KKK��R�u}�(j  j	  j  j!  KKK��R�u}�(j  j  j  j!  KKK��R�u}�(j  j"  j  j!  KKK��R�u}�(j  j+  j  j!  KKK��R�u}�(j  j5  j  j!  KKK��R�u}�(j  j>  j  j!  KKK��R�u}�(j  jH  j  j!  KKK��R�u}�(j  jS  j  j!  KKK��R�u}�(j  j]  j  j!  KKK��R�u}�(j  jh  j  j!  KKK��R�u}�(j  jq  j  j!  KKK��R�u}�(j  jz  j  j!  KKK��R�u}�(j  j�  j  j!  KKK��R�u}�(j  j�  j  j!  KKK��R�u}�(j  j�  j  j!  KKK��R�u}�(j  j�  j  j!  KK K��R�u}�(j  j�  j  j!  K K!K��R�u}�(j  j�  j  j!  K!K"K��R�u}�(j  j�  j  j!  K"K#K��R�u}�(j  j�  j  j!  K#K$K��R�u}�(j  j�  j  j!  K$K%K��R�u}�(j  j�  j  j!  K%K&K��R�u}�(j  j�  j  j!  K&K'K��R�u}�(j  j  j  j!  K'K(K��R�u}�(j  j
  j  j!  K(K)K��R�u}�(j  j  j  j!  K)K*K��R�u}�(j  j%  j  j!  K*K+K��R�u}�(j  j3  j  j!  K+K,K��R�u}�(j  j>  j  j!  K,K-K��R�u}�(j  jG  j  j!  K-K.K��R�u}�(j  jP  j  j!  K.K/K��R�u}�(j  jY  j  j!  K/K0K��R�u}�(j  jd  j  j!  KKZK*��R�u}�(j  jn  j  hhK ��h��R�(KK
��h�i8�����R�(KhoNNNJ����J����K t�b�Ch                     1       2       3       4       5       6       7       8       9       :       �t�bueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.