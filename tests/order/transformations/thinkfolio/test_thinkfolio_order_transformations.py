import os
from pathlib import Path

import pandas as pd
import pytest
from prefect import context
from swarm.task.auditor import Auditor

from swarm_tasks.order.feed.thinkfolio.static import TempColumns
from swarm_tasks.order.transformations.thinkfolio.thinkfolio_order_transformations import (
    ThinkfolioOrderTransformations,
)
from swarm_tasks.utilities.static import SWARM_FILE_URL

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")

SOURCE_EQUITIES_PICKLE_FILE = TEST_FILES_DIR.joinpath("TEST_FILLS_1_20230616.pkl")
EXPECTED_RESULT_EQUITIES_PICKLE_PATH = TEST_FILES_DIR.joinpath(
    "thinkfolio_equities_expected_df.pkl"
)

SOURCE_FIXED_INCOME_PICKLE_FILE = TEST_FILES_DIR.joinpath(
    "TEST_MARKITFI_TRD_1_20230616.pkl"
)
EXPECTED_RESULT_FIXED_INCOME_PICKLE_PATH = TEST_FILES_DIR.joinpath(
    "thinkfolio_fixed_income_expected_df.pkl"
)
SOURCE_FX_PICKLE_FILE = TEST_FILES_DIR.joinpath("TEST_FILLS_1_FX_20230901.pkl")
EXPECTED_RESULT_FX_PICKLE_PATH = TEST_FILES_DIR.joinpath(
    "thinkfolio_fx_expected_df.pkl"
)


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


class TestThinkfolioOrderTransformations:
    """
    Test for the PrimaryTransformations for Thinkfolio feed
    """

    @pytest.mark.parametrize(
        "source_path, expected_path",
        [
            (SOURCE_EQUITIES_PICKLE_FILE, EXPECTED_RESULT_EQUITIES_PICKLE_PATH),
            (SOURCE_FIXED_INCOME_PICKLE_FILE, EXPECTED_RESULT_FIXED_INCOME_PICKLE_PATH),
            (SOURCE_FX_PICKLE_FILE, EXPECTED_RESULT_FX_PICKLE_PATH),
        ],
    )
    def test_end_to_end(self, mocker, source_path, expected_path, auditor):
        source_frame = pd.read_pickle(source_path)
        os.environ[SWARM_FILE_URL] = Path("dummy/path/", source_path.name).as_posix()

        mock_executing_entity = mocker.patch.object(
            ThinkfolioOrderTransformations, "_get_executing_entity"
        )
        mock_executing_entity.return_value = pd.DataFrame(
            data="lei:test_executing_entity",
            index=source_frame.index,
            columns=[TempColumns.EXECUTING_ENTITY_WITH_LEI],
        )

        task = ThinkfolioOrderTransformations(
            source_frame=source_frame,
            logger=context.get("logger"),
            auditor=auditor,
        )
        result = task.process()

        expected = pd.read_pickle(expected_path)

        pd.testing.assert_frame_equal(left=result, right=expected)
