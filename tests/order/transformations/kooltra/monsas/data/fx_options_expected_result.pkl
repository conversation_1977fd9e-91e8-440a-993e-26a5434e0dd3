���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK8��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo��+executionDetails.passiveAggressiveIndicator�� executionDetails.tradingCapacity��	hierarchy��	_order.id��_orderState.id��_order.__meta_model__��_orderState.__meta_model__��orderIdentifiers.orderIdCode��!orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��"_orderState.priceFormingData.price��+_orderState.priceFormingData.tradedQuantity��reportDetails.transactionRefNo��sourceIndex��	sourceKey��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��timestamps.tradingDateTime��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��$_orderState.transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��'_orderState.transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��transactionDetails.recordType��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers��__expiry_date__��__option_strike_price__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C               �t�bh^�__swarm_raw_index__�u��R�e]�(hhK ��h��R�(KKK��h�f8�����R�(KhlNNNJ����J����K t�b�C`    �SA    �SA    @�@    �%�@    �SA    �SA    @�@    �%�@    �SA    �SAH�z�G�?H�z�G�?�t�bhhK ��h��R�(KKK��hk�C               �t�bhhK ��h��R�(KK1K��h!�]�(�1��2�h�h��Kooltra�h��
2021-12-13��
2021-12-13��BUYI��SELL��NEWO�h��FILL�h��Market�h��kPremium Date: 15/12/2021, Type: Vanilla, Strategy: Call EUR, Put AUD, Expiration Convention: 10:00 New York��kPremium Date: 15/12/2021, Type: Vanilla, Strategy: Call EUR, Put AUD, Expiration Convention: 10:00 New York��PASV�h��AOTC�h��
Standalone�h��98877.V0��98879.V0�h�h��Order�h��
OrderState�h�h�h�h�h�h�h���/Users/<USER>/Desktop/steeleye/se-prefect-dir/swarm-tasks/tests/order/transformations/kooltra/monsas/data/Integration test FX Options-with-normalised-cols.csv�h��2021-12-13T08:55:20.000000Z��2021-12-13T08:56:36.000000Z�h�h�h�h�h�h�h�h��pandas._libs.missing��NA���h��EUR�h��MONE�h��EUR��EUR�h�h��Market Side�h�h�h�h�h��XOFF�h�h�h�]�(}�(�labelId��XXXXAUDEUROC2022-01-14��path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�u}�(h�� XXXXAUDEUROC2022-01-141.58000000�h�h�h�h�ue]�(}�(h��XXXXAUDEUROC2022-01-14�h�h�h�h�u}�(h�� XXXXAUDEUROC2022-01-141.58000000�h�h�h�h�ue]�(}�(h��lei:213800whtudjhvl2hg18�h��buyer�h�h��ARRAY���R�u}�(h��id:lp�h��buyerDecisionMaker�h�h�u}�(h��lei:213800whtudjhvl2hg18�h��reportDetails.executingEntity�h�h�u}�(h��lei:dummylei�h��seller�h�h�u}�(h��lei:dummylei�h��counterparty�h�h�u}�(h��id:lp�h��1tradersAlgosWaiversIndicators.executionWithinFirm�h�h�u}�(h��id:lp�h��trader�h�h�ue]�(}�(h��lei:dummylei�h�h�h�h�u}�(h��lei:213800whtudjhvl2hg18�h�h�h�h�u}�(h��lei:213800whtudjhvl2hg18�h�h�h�h�u}�(h��id:lp�h��sellerDecisionMaker�h�h�u}�(h��lei:dummylei�h�h�h�h�u}�(h��id:lp�h�h�h�h�u}�(h��id:lp�h�h�h�h�ue�lei:213800whtudjhvl2hg18��lei:213800whtudjhvl2hg18��lei:213800whtudjhvl2hg18��lei:dummylei��lei:dummylei��lei:213800whtudjhvl2hg18��lei:dummylei��lei:dummylei��id:lp�h�h��id:lp�h�h��id:lp��id:lp�h�h��id:lp��id:lp�]�(h�h�h�h�h�h�h�h�h�e]�(h�h�h�h�h�h�h�h�h�e�
2022-01-14��
2022-01-14�et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h7h8h9hChFh\et�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK1��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3h4h5h6h:h<h=h>h?h@hAhBhDhEhGhHhIhJhKhLhMhNhOhPhQhRhShThUhVhWhXhYhZh[et�bh^Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hv�mgr_locs�hhK ��h��R�(KK��hk�C0                            !       7       �t�bu}�(j  h�j  �builtins��slice���KKK��R�u}�(j  h�j  hhK ��h��R�(KK1��hk�B�                                                                  	       
                     
                                                                                                          "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       3       4       5       6       �t�bueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.