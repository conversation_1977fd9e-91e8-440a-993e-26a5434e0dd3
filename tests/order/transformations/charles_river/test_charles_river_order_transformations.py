import os
from pathlib import Path
from typing import Dict
from typing import List
from unittest.mock import patch

import pandas as pd
import pytest
from mock.mock import MagicMock
from prefect import context
from swarm.conf import SettingsCls
from swarm.task.auditor import Auditor

from swarm_tasks.order.feed.charles_river.static import FileTypes
from swarm_tasks.order.feed.charles_river.static import SourceColumns
from swarm_tasks.order.feed.charles_river.static import TempColumns
from swarm_tasks.order.transformations.charles_river import (
    charles_river_order_transformations,
)
from swarm_tasks.order.transformations.charles_river.charles_river_order_transformations import (
    CharlesRiverOrderTransformations,
)
from swarm_tasks.order.transformations.order_transform_maps import (
    charles_river_transform_map,
)
from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
TEST_FILE_PATH = TEST_FILES_DIR.joinpath(r"test_file.pkl")
ALLOCATIONS_EXPECTED_FILE_PATH = TEST_FILES_DIR.joinpath(r"expected_allocations.pkl")
ORDERS_EXPECTED_FILE_PATH = TEST_FILES_DIR.joinpath(r"expected_orders.pkl")
FIRSTSENTIER_ALLOCATIONS_EXPECTED_FILE_PATH = TEST_FILES_DIR.joinpath(
    r"expected_allocations_firstsentier.pkl"
)
FIRSTSENTIER_ORDERS_EXPECTED_FILE_PATH = TEST_FILES_DIR.joinpath(
    r"expected_orders_firstsentier.pkl"
)
ROBECO_ALLOCATIONS_EXPECTED_FILE_PATH = TEST_FILES_DIR.joinpath(
    r"expected_allocations_robeco.pkl"
)
ROBECO_ORDERS_EXPECTED_FILE_PATH = TEST_FILES_DIR.joinpath(
    r"expected_orders_robeco.pkl"
)


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


class TestCharlesRiverOrderTransformations:
    """
    Test Charles River Order Transformations
    """

    @staticmethod
    def mock_get_tenant_lei(mocker, test_df):
        mock_get_tenant_lei = mocker.patch.object(GetTenantLEI, "process")
        tenant_lei_df = pd.DataFrame(index=test_df.index)
        tenant_lei_df = tenant_lei_df.assign(
            **{TempColumns.EXECUTING_ENTITY: "lei:sample_lei"}
        )
        mock_get_tenant_lei.return_value = tenant_lei_df
        return mock_get_tenant_lei

    @pytest.mark.parametrize(
        "realm, test_file_path, expected_file_path",
        [
            ("default.dev.steeleye.co", TEST_FILE_PATH, ALLOCATIONS_EXPECTED_FILE_PATH),
            ("default.dev.steeleye.co", TEST_FILE_PATH, ORDERS_EXPECTED_FILE_PATH),
            (
                "firstsentier.dev.steeleye.co",
                TEST_FILE_PATH,
                FIRSTSENTIER_ALLOCATIONS_EXPECTED_FILE_PATH,
            ),
            (
                "firstsentier.dev.steeleye.co",
                TEST_FILE_PATH,
                FIRSTSENTIER_ORDERS_EXPECTED_FILE_PATH,
            ),
            (
                "robeco.dev.steeleye.co",
                TEST_FILE_PATH,
                ROBECO_ALLOCATIONS_EXPECTED_FILE_PATH,
            ),
            (
                "robeco.dev.steeleye.co",
                TEST_FILE_PATH,
                ROBECO_ORDERS_EXPECTED_FILE_PATH,
            ),
        ],
    )
    def test_end_to_end_transformations(
        self,
        mocker: MagicMock,
        realm: str,
        test_file_path: str,
        expected_file_path: str,
        auditor: Auditor,
    ):
        os.environ["SWARM_FILE_URL"] = str(test_file_path)

        # mock realm to chose default transformation
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = realm

        source_frame = pd.read_pickle(test_file_path)
        expected = pd.read_pickle(expected_file_path)

        if "allocation" in str(expected_file_path):
            file_type = FileTypes.ALLOCATIONS.value
        else:
            file_type = FileTypes.ORDER.value

        source_frame = source_frame.assign(**{SourceColumns.FILE_TYPE: file_type})

        self.mock_get_tenant_lei(mocker, source_frame)

        task = charles_river_transform_map.transformation(tenant=realm.split(".")[0])(
            source_frame=source_frame, logger=context.get("logger"), auditor=auditor
        )
        result = task.process()

        pd.testing.assert_frame_equal(
            result.drop(["sourceKey", TempColumns.FILE_TYPE], axis=1, errors="ignore"),
            expected.drop(
                ["sourceKey", TempColumns.FILE_TYPE], axis=1, errors="ignore"
            ),
        )

    @pytest.mark.parametrize(
        "lst, expected_result",
        [
            (
                ["Z, A", "A", "id:VANCOUVER", "lei:HAKA", "id:VANCOUVER"],
                ["Z, A", "A", "VANCOUVER", "HAKA"],
            ),
            (pd.NA, []),
        ],
    )
    def test_it_can__remove_id_lei_from_list(
        self, lst: List[str], expected_result: List[str]
    ):
        """
        1. Tests it can remove `lei:` and `id:` from a list of values.
        2. Tests the order is preserved.
        3. Tests it can handle missing values (pd.NA).
        """

        result: List[str] = CharlesRiverOrderTransformations._remove_id_lei_from_list(
            lst=lst,
        )

        assert result == expected_result

    @pytest.mark.parametrize(
        "s3_file_present",
        [True, False],
    )
    @patch.object(charles_river_order_transformations, "read_csv_from_s3_download")
    def test_ultimate_venue_logic_with_and_without_s3_file(
        self,
        mock_s3_df,
        mocker,
        ultimate_venue_source_data: Dict[str, pd.DataFrame],
        broker_code_mic_code_lookup_table: pd.DataFrame,
        expected_result_ultimate_venue_with_s3_file: pd.DataFrame,
        expected_result_ultimate_venue_without_s3_file: pd.DataFrame,
        s3_file_present: bool,
    ):
        if s3_file_present:
            mock_s3_df.return_value = broker_code_mic_code_lookup_table
            expected_result = expected_result_ultimate_venue_with_s3_file
        else:
            mock_s3_df.return_value = pd.DataFrame()
            expected_result = expected_result_ultimate_venue_without_s3_file
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "test.dev.steeleye.co"

        obj = CharlesRiverOrderTransformations(
            source_frame=ultimate_venue_source_data.get("source_frame"), auditor=None
        )
        obj.pre_process_df = ultimate_venue_source_data.get("pre_process_df")
        result = obj._temp_ultimate_venue()
        pd.testing.assert_frame_equal(left=result, right=expected_result)
