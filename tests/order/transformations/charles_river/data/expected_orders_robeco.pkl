���;      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK]��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date�� executionDetails.aggregatedOrder��!executionDetails.buySellIndicator��executionDetails.limitPrice��(_orderState.executionDetails.orderStatus��#_order.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo��+executionDetails.passiveAggressiveIndicator�� executionDetails.routingStrategy��executionDetails.stopPrice�� executionDetails.tradingCapacity��executionDetails.validityPeriod��	hierarchy��	_order.id��_orderState.id��_order.__meta_model__��_orderState.__meta_model__��"orderIdentifiers.aggregatedOrderId��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��orderIdentifiers.parentOrderId��.orderIdentifiers.tradingVenueTransactionIdCode��!orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��priceFormingData.price��priceFormingData.tradedQuantity��reportDetails.transactionRefNo��sourceIndex��	sourceKey��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��timestamps.tradingDateTime��3tradersAlgosWaiversIndicators.shortSellingIndicator��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��transactionDetails.recordType��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��asset_class_attribute��underlying_isin_attribute��option_type_attribute��option_strike_price_attribute��underlying_symbol_attribute��currency_attribute��expiry_date_attribute��notional_currency_2_attribute��venue_attribute��isin_attribute��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers��SECURITYNAME��__option_type_mapped__��__instr_quantity_notation__��__newo_in_file__��__is_created_through_fallback__��$__inst_fb_best_ex_asset_class_main__��#__inst_fb_best_ex_asset_class_sub__��__inst_id_code__��__inst_alt_id_code__��__parties_fb_exec_entiy__��__parties_fb_trader__��__parties_fb_exec_within_firm__��__parties_fb_inv_dec_maker__��__parties_fb_client__��__parties_fb_counterparty__��__parties_fb_buyer__��__parties_fb_seller__��__fill_amount__��__order_net_amount__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(h�numpy.core.numeric��_frombuffer���(�(                                           �h�i8�����R�(K�<�NNNJ����J����K t�bK���C�t�R�h��__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.integer��IntegerArray���)��}�(�_data�h�(�(       [�]    [�]    N�]    N�]    B!�]    �h�i8�����R�(Kh�NNNJ����J����K t�bK��h�t�R��_mask�h�(�            �h�b1�����R�(Kh"NNNJ����J����K t�bK��h�t�R��_cache�}��dtype�h��
Int64Dtype���)��}�h�}�(�numpy_dtype�h��kind��i�usbsubh�)��}�(h�h�(�(                                          �h�K��h�t�R�h�h�(�       �h�K��h�t�R�h�}�h�h�subh�)��}�(h�h�(�(       [�]    [�]    N�]    N�]    B!�]    �h�K��h�t�R�h�h�(�            �h�K��h�t�R�h�}�h�h�subh�)��}�(h�h�(�(       Oj�]    Oj�]    ���]    ���]    ��]    �h�K��h�t�R�h�h�(�            �h�K��h�t�R�h�}�h�h�subh�)��}�(h�h�(�(                                          �h�K��h�t�R�h�h�(�       �h�K��h�t�R�h�}�h�h�sub�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h!�]�(�
1570246491�h�
1570246990�h��
1570251074�et�b�_dtype�h�StringDtype���)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�DE0001102333�j  �DE0006231004�j  �US1266501006�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�id:sogepafi��id:sogepafi��
id:jpmalgl��
id:jpmalgl��
id:jpmalgl�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�
id:rca2003��
id:rca2003��
id:rob6593��
id:rob6593��
id:ibc3084�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�
id:rca5003��
id:rca5003��id:gen_trd_auto��id:gen_trd_auto��id:gen_trd_auto�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�
id:rca5003��
id:rca5003��id:gen_trd_auto��id:gen_trd_auto��id:gen_trd_auto�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�*1.750 BUNDESREPUB. DEUTSCHLAND 15-FEB-2024�jb  �Infineon Technologies AG�jc  �CVS Health Corp�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�RCA5003�jw  �GEN_TRD_AUTO�jx  jx  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�RCA5003��RCA5003��GEN_TRD_AUTO��GEN_TRD_AUTO��GEN_TRD_AUTO�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�RCA2003��RCA2003��ROB6593��ROB6593��IBC3084�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�SOGEPAFI��SOGEPAFI��JPMALGL��JPMALGL��JPMALGL�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�SOGEPAFI��SOGEPAFI��JPMALGL��JPMALGL��JPMALGL�et�bh�h�)��ubh�(�       �h�KK��h�t�R�h�(�(           �CA    �CA    �j�@    �j�@     ��@�h�f8�����R�(Kh�NNNJ����J����K t�bKK��h�t�R�h�(�x                                           d       d       d       d       d       �       �       �       �       �       �h�i8�����R�(Kh�NNNJ����J����K t�bKK��h�t�R�hhK ��h��R�(KKCK��h!�]�(�1�j�  j�  j�  �2�j�  j�  j�  j�  j�  �
Charles River�j�  j�  j�  j�  �
2023-03-06��
2023-03-06��
2023-03-07��
2023-03-07��
2023-03-06�������BUYI�j�  j�  j�  �SELL��pandas._libs.missing��NA���j�  j�  j�  j�  j�  �FILL�j�  j�  j�  �NEWO�j�  j�  j�  j�  �Market�j�  j�  j�  j�  �}Order Reason: F_2_ADJPFO, Region: Belgium, CRD Security Id: 59268321, Order Status: ACCT, Source file : ROBECO_CRIMS_20230307��}Order Reason: F_2_ADJPFO, Region: Belgium, CRD Security Id: 59268321, Order Status: ACCT, Source file : ROBECO_CRIMS_20230307��|Order Reason: EQ_TARGET, Region: Germany, CRD Security Id: 10797420, Order Status: ACCT, Source file : ROBECO_CRIMS_20230307��|Order Reason: EQ_TARGET, Region: Germany, CRD Security Id: 10797420, Order Status: ACCT, Source file : ROBECO_CRIMS_20230307���Order Reason: EQ_MDL, Region: United States Eastern Time, CRD Security Id: 10794964, Order Status: ACCT, Source file : ROBECO_CRIMS_20230307�j�  �AGRE�j�  �PASV�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �AOTC�j�  j�  j�  j�  j�  j�  j�  j�  j�  �
Standalone�j�  j�  j�  j�  �Order�j�  j�  j�  j�  �
OrderState�j�  j�  j�  j�  �F|1570246491|0��F|1570246491|1��F|1570246990|2��F|1570246990|3��F|1570251074|4�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �h/Users/<USER>/Desktop/steeleye/swarm-tasks/tests/order/transformations/charles_river/data/test_file.pkl�j�  j�  j�  j�  �2023-03-06T15:27:44.313000Z��2023-03-06T15:27:44.313000Z��2023-03-07T10:24:27.503000Z��2023-03-07T10:24:27.503000Z��2023-03-06T09:52:48.397000Z�j�  j�  j�  j�  j�  �2023-03-06T15:45:51.383000Z��2023-03-06T15:45:51.383000Z��2023-03-07T10:25:03.280000Z��2023-03-07T10:25:03.280000Z��2023-03-06T11:07:27.423000Z�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �EUR�j�  j�  j�  �USD��PERC�j�  �MONE�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �UNIT�j�  j�  j�  j�  �"se_elastic_schema.static.reference��OrderRecordType����Market Side���R�j  j  j  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �IEXD�j  �XETR�j  �XNYS�j  j  j  j  j  ]�(}�(�labelId�j  �path��instrumentDetails.instrument��type��se_elastic_schema.static.market��IdentifierType����OBJECT���R�u}�(j  �DE0001102333EURIEXD�j  j	  j
  j  ue]�(}�(j  j  j  j	  j
  j  u}�(j  �DE0001102333EURIEXD�j  j	  j
  j  ue]�(}�(j  j  j  j	  j
  j  u}�(j  �DE0006231004EURXETR�j  j	  j
  j  ue]�(}�(j  j  j  j	  j
  j  u}�(j  �DE0006231004EURXETR�j  j	  j
  j  ue]�(}�(j  j  j  j	  j
  j  u}�(j  �US1266501006USDXNYS�j  j	  j
  j  uej�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j  j  j  j  j  ]�(}�(j  �lei:sample_lei�j  �buyer�j
  j
  �ARRAY���R�u}�(j  �lei:sample_lei�j  �reportDetails.executingEntity�j
  j  u}�(j  �id:sogepafi�j  �seller�j
  j)  u}�(j  �id:sogepafi�j  �counterparty�j
  j  u}�(j  �
id:rca2003�j  �:tradersAlgosWaiversIndicators.investmentDecisionWithinFirm�j
  j  u}�(j  �
id:rca5003�j  �1tradersAlgosWaiversIndicators.executionWithinFirm�j
  j  u}�(j  �alabama�j  �clientIdentifiers.client�j
  j)  u}�(j  �
id:alabama�j  j;  j
  j)  u}�(j  �
id:rca5003�j  �trader�j
  j)  ue]�(}�(j  �lei:sample_lei�j  j&  j
  j)  u}�(j  �lei:sample_lei�j  j,  j
  j  u}�(j  �id:sogepafi�j  j/  j
  j)  u}�(j  �id:sogepafi�j  j2  j
  j  u}�(j  �
id:rca2003�j  j5  j
  j  u}�(j  �
id:rca5003�j  j8  j
  j  u}�(j  �alabama�j  j;  j
  j)  u}�(j  �
id:alabama�j  j;  j
  j)  u}�(j  �
id:rca5003�j  j@  j
  j)  ue]�(}�(j  �lei:sample_lei�j  j&  j
  j)  u}�(j  �lei:sample_lei�j  j,  j
  j  u}�(j  �
id:jpmalgl�j  j/  j
  j)  u}�(j  �
id:jpmalgl�j  j2  j
  j  u}�(j  �
id:rob6593�j  j5  j
  j  u}�(j  �id:gen_trd_auto�j  j8  j
  j  u}�(j  �cgf_cee�j  j;  j
  j)  u}�(j  �
id:cgf_cee�j  j;  j
  j)  u}�(j  �id:gen_trd_auto�j  j@  j
  j)  ue]�(}�(j  �lei:sample_lei�j  j&  j
  j)  u}�(j  �lei:sample_lei�j  j,  j
  j  u}�(j  �
id:jpmalgl�j  j/  j
  j)  u}�(j  �
id:jpmalgl�j  j2  j
  j  u}�(j  �
id:rob6593�j  j5  j
  j  u}�(j  �id:gen_trd_auto�j  j8  j
  j  u}�(j  �cgf_cee�j  j;  j
  j)  u}�(j  �
id:cgf_cee�j  j;  j
  j)  u}�(j  �id:gen_trd_auto�j  j@  j
  j)  ue]�(}�(j  �
id:jpmalgl�j  j&  j
  j)  u}�(j  �lei:sample_lei�j  j,  j
  j  u}�(j  �lei:sample_lei�j  j/  j
  j)  u}�(j  �
id:jpmalgl�j  j2  j
  j  u}�(j  �
id:ibc3084�j  j5  j
  j  u}�(j  �id:gen_trd_auto�j  j8  j
  j  u}�(j  �
iuf_condor�j  j;  j
  j)  u}�(j  �
id:iuf_condor�j  j;  j
  j)  u}�(j  �id:gen_trd_auto�j  j@  j
  j)  ue�lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��lei:sample_lei��
id:jpmalgl��id:sogepafi��id:sogepafi��
id:jpmalgl��
id:jpmalgl��lei:sample_lei�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �alabama��alabama��cgf_cee��cgf_cee��
iuf_condor�]�(j  j  j$  j*  j-  j0  j3  j6  j9  j<  j>  e]�(j  j  jB  jD  jF  jH  jJ  jL  jN  jP  jR  e]�(j  j  jU  jW  jY  j[  j]  j_  ja  jc  je  e]�(j  j  jh  jj  jl  jn  jp  jr  jt  jv  jx  e]�(j   j!  j{  j}  j  j�  j�  j�  j�  j�  j�  ej�  j�  j�  j�  j�  j�  j�  j�  j�  j�  ������Debt Instruments�j�  �Equity�j�  j�  �Bonds�j�  � �j�  j�  �crims_DBR_FIXB_IEXD��crims_DBR_FIXB_IEXD��crims_IFX_COM_XETR��crims_IFX_COM_XETR��crims_CVS_COM_XNYS��
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei�]��ALABAMA�a]�j�  a]��CGF_CEE�a]�j�  a]��
IUF_CONDOR�a�
sample_lei��
sample_lei��
sample_lei��
sample_lei��
sample_lei�et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h:at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h=at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hLat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h<at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hZat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hbat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hgat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hjat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hkat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hmat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hoat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hvat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hyat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hzat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h{at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h}at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hsat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h@at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hDh�h�et�bh�Nu��R�h
h}�(hhhK ��h��R�(KKC��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3h4h5h8h9h?hAhBhChEhFhGhHhIhJhKhMhNhOhPhQhRhShThUhVhWhXhYh[h\h]h^h_h`hahchdhehfhhhihlhnhphqhrhthuhwhxh|h~et�bh�Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs��builtins��slice���KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  K'K(K��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j  j�  j�  K5K6K��R�u}�(j�  j  j�  j�  K=K>K��R�u}�(j�  j#  j�  j�  KBKCK��R�u}�(j�  j1  j�  j�  KEKFK��R�u}�(j�  j?  j�  j�  KFKGK��R�u}�(j�  jM  j�  j�  KHKIK��R�u}�(j�  j[  j�  j�  KJKKK��R�u}�(j�  jg  j�  j�  KQKRK��R�u}�(j�  jp  j�  j�  KTKUK��R�u}�(j�  j{  j�  j�  KUKVK��R�u}�(j�  j�  j�  j�  KVKWK��R�u}�(j�  j�  j�  j�  KXKYK��R�u}�(j�  j�  j�  j�  KZK[K��R�u}�(j�  j�  j�  j�  KNKOK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  h�(�              [       \       �j�  K��h�t�R�u}�(j�  j�  j�  h�(�                                                                      	       
                     
                                                                              !       "       #       $       %       &       (       )       *       +       ,       -       .       /       0       1       2       3       4       6       7       8       9       :       ;       <       >       ?       @       A       C       D       G       I       K       L       M       O       P       R       S       W       Y       �h�KC��h�t�R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.