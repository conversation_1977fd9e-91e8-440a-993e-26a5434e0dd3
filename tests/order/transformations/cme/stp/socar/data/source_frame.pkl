�cpandas.core.frame
DataFrame
q )�q}q(X   _mgrqcpandas.core.internals.managers
BlockManager
q)�q(]q(cpandas.core.indexes.base
_new_Index
qcpandas.core.indexes.base
Index
q}q	(X   dataq
cnumpy.core.multiarray
_reconstruct
qcnumpy
ndarray
qK �q
Cbq�qRq(KKX�qcnumpy
dtype
qX   O8q���qRq(KX   |qNNNJ����J����K?tqb�]q(X	   S3FileURLqX   BeginStringqX
   BodyLengthqX   MsgTypeqX	   MsgSeqNumqX   SenderCompIDqX   SenderSubIDqX   SendingTimeq X   TargetCompIDq!X   ExecIDq"X   SecurityIDSourceq#X   LastPxq$X   LastQtyq%X
   SecurityIDq&X   Symbolq'X   TransactTimeq(X	   TradeDateq)X   SecurityDescq*X   SecurityTypeq+X   MaturityMonthYearq,X   SecurityExchangeq-X	   PriceTypeq.X   MultiLegReportingTypeq/X   CFICodeq0X   TradeReportTransTypeq1X   MaturityDateq2X   ff_552q3X   Sideq4X   OrderIDq5X   ClOrdIDq6X   ff_453q7X   PartyIDq8X
   PartyIDSourceq9X	   PartyRoleq:X   ff_802q;X
   PartySubIDq<X   PartySubIDTypeq=X   TradeInputSourceq>X   CustOrderCapacityq?X   CustOrderHandlingInstq@X   ff_10034qAX   ff_10027qBX   ff_10029qCX   ff_10030qDX   ff_1016qEX   SideTrdRegTimestampqFX   SideTrdRegTimestampTypeqGX   AggressorIndicatorqHX   TradeRequestIDqIX
   TradeReportIDqJX   ClearingBusinessDateqKX   LastUpdateTimeqLX   TrdTypeqMX   TradeReportTypeqNX
   TrdMatchIDqOX   TrdRptStatusqPX
   UnitOfMeasureqQX   TradeIDqRX   SecondaryTradeIDqSX   UnitOfMeasureQtyqTX   SettlMethodqUX	   VenueTypeqVX   ClearedIndicatorqWX   ff_6070qXX   ff_10026qYX   ff_37711qZX   CheckSumq[X   SecondaryExecIDq\X   StrategyLinkIDq]X   SecuritySubTypeq^X   TimeUnitq_X   ff_10037q`X   TargetSubIDqaX	   LeavesQtyqbX   OrdTypeqcX   SecondaryOrderIdqdX   TimeInForceqeX   OrderQtyqfX   PositionEffectqgX   PriceqhX   AccountqiX   ff_1031qjX   ff_5979qkX   StopPxqlX   ff_1028qmX   ExecTypeqnX   TextqoX   OrigClOrdIDqpetqqbX   nameqrNu�qsRqthcpandas.core.indexes.range
RangeIndex
qu}qv(hrNX   startqwK X   stopqxKX   stepqyKu�qzRq{e]q|(hhK �q}h�q~Rq(KKK�q�hX   f8q����q�Rq�(KX   <q�NNNJ����J����K tq�b�C     Ђ@       @     @�@q�tq�bhhK �q�h�q�Rq�(KK
K�q�hX   i8q����q�Rq�(Kh�NNNJ����J����K tq�b�CP�      �                                         e                      q�tq�bhhK �q�h�q�Rq�(KKKK�q�h�]q�(X�   s3://diogo.dev.steeleye.co/ingress/raw/order-feed-cme-stp-fix/14f768b77c3e5efb3e28ecc1626f650317742f15f1ed03a2031de7c44a826072_1168.fixq�X   FIX.4.4q�X   AEq�X
   CMESTPFIX4q�X   STPq�X   ********-09:40:03.692q�X   socar_trading_saq�X   19016141q�X   Hq�X   9Nq�X   A9NG3q�X   ********-09:40:03.000000000q�X   ********q�X$   Argus Propane (Saudi Aramco) Futuresq�X   FUTq�X   202302q�X   NYMEXq�X   2q�X   FCECSOq�X   20230202q�X   1q�h�cpandas._libs.missing
NA
q�X	   C19016141q�X   10q�]q�(X   104q�X   socar_trading_saq�X   CMEq�X   NYMEXq�X   ST511q�X   A-639363q�X   spectron_services_ltdq�X
   myamamoto3q�X   BYAPq�X   104q�e]q�(X   Cq�h�h�h�h�X   Dq�e]q�(KKKKKKKK$K>M�e]q�(h�h�h�h�h�e]q�(X   Socar Trading SAq�h�X$   Marex Spectron International Limitedq�X   Muneshige Yamamotoq�X   BENJAMIN YAPq�e]q�(KKKK	K	eX   CPCq�h�h�X)   SNZ2OJLFK8MNNCLQOF39CPC001019016141BN0001q�h�X   0q�h�X   ********-09:34:47.000000000Zq�X   Yq�X$   99f77222-8fb0-11ed-b1fe-0242c0a80007q�X   18592925D160101D2A1D32034003507q�X   ********q�X   ********-09:40:03.508000000q�X   18592925D160101D2A1CFq�X   tq�X   103292q�X   18592925D160101D2A1D3q�h�X   Xq�X   1000q�X   USDq�h�X   202q�X	   D47889559q�X   18592925D160101D2A1D1q�X   GNq�X   Moq�X   2000q�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�etq�be]q�(hh}q�(h
hhK �q�h�q�Rq�(KK�q�h�]q�(h$h%hTetq�bhrNu�q�Rq�hh}q�(h
hhK �q�h�q�Rq�(KK
�q�h�]q�(hhh.h1h?hGhMhNhPhWetq�bhrNu�q�Rq�hh}q�(h
hhK �q�h�q�Rq�(KKK�q�h�]q�(hhhhhh h!h"h#h&h'h(h)h*h+h,h-h/h0h2h3h4h5h6h7h8h9h:h;h<h=h>h@hAhBhChDhEhFhHhIhJhKhLhOhQhRhShUhVhXhYhZh[h\h]h^h_h`hahbhchdhehfhghhhihjhkhlhmhnhohpetq�bhrNu�q�Rq�e}q�X   0.14.1q�}q�(X   axesq�hX   blocksq�]q�(}q�(X   valuesq�hX   mgr_locsr   hhK �r  h�r  Rr  (KK�r  h��C              ;       r  tr  bu}r  (h�h�j   hhK �r  h�r	  Rr
  (KK
�r  h��CP                            &       .       4       5       7       >       r  tr
  bu}r  (h�h�j   hhK �r  h�r  Rr  (KKK�r  h��BX                                                    	       
       
                                                                                                                               !       "       #       $       %       '       (       )       *       +       ,       -       /       0       1       2       3       6       8       9       :       <       =       ?       @       A       B       C       D       E       F       G       H       I       J       K       L       M       N       O       P       Q       R       S       T       U       V       W       r  tr  bueustr  bX   _typr  X	   dataframer  X	   _metadatar  ]r  X   attrsr  }r  ub.