import os
from pathlib import Path

import pandas as pd
import pytest
from mock.mock import <PERSON><PERSON><PERSON>
from prefect import context
from swarm.conf import SettingsCls
from swarm.task.auditor import Auditor

from swarm_tasks.order.feed.shell.static import TempColumns
from swarm_tasks.order.transformations.order_transform_maps import (
    shell_orders_transform_map,
)
from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
TEST_FILE_PATH = TEST_FILES_DIR.joinpath(r"test_file.pkl")
EXPECTED_FILE_PATH = TEST_FILES_DIR.joinpath(r"expected_file.pkl")


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


class TestShellOrderTransformations:
    """
    Test Shell Order Transformations
    """

    @staticmethod
    def mock_get_tenant_lei(mocker, test_df):
        mock_get_tenant_lei = mocker.patch.object(GetTenantLEI, "process")
        tenant_lei_df = pd.DataFrame(index=test_df.index)
        tenant_lei_df = tenant_lei_df.assign(
            **{TempColumns.EXECUTING_ENTITY: "lei:sample_lei"}
        )
        mock_get_tenant_lei.return_value = tenant_lei_df
        return mock_get_tenant_lei

    def test_end_to_end_transformations(
        self,
        mocker: MagicMock,
        auditor: Auditor,
    ):
        os.environ["SWARM_FILE_URL"] = "mock_path"

        # Mock realm to chose default transformation
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "default.dev.steeleye.co"

        source_frame = pd.read_pickle(TEST_FILE_PATH)
        expected = pd.read_pickle(EXPECTED_FILE_PATH)

        self.mock_get_tenant_lei(mocker, source_frame)

        task = shell_orders_transform_map.transformation(tenant="foo")(
            source_frame=source_frame, logger=context.get("logger"), auditor=auditor
        )
        result = task.process()

        pd.testing.assert_frame_equal(result, expected)
