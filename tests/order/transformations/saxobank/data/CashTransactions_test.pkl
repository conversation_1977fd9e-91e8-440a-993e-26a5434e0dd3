���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK+��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�Account��AccountCurrency��TransactionNumber��OrderNumber��FrontOfficeLinkID��Date��	ValueDate��GrossAmountCashCurrency��CashCurrency��FeeCashCurrency��CashToAccountRate��GrossAmountAccountCurrency��FeeAccountCurrency��NetAmountAccountCurrency��PartnerAccountKey��Comment��
CounterpartID��!ExecutingEntityIdentificationCode��TradeExecutionTime��InstrumentType��
DirtyPrice��Price��	TradeTime��InstrumentCode��TermOfUnderlyingIndex��BuySell��UnderlyingInstrumentISINCode��	NetAmount��TradeNumber��ISOMic��
UserIdOnTrade��FXType��
ExpiryDate��ISINCode��Venue��UnderlyingInstrumentCode��InstrumentISINCode��	OrderType��ISIN��Strike��TradedAmount��CallPut��InstrumentCurrency�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK	��h�i8�����R�(K�<�NNNJ����J����K t�b�CH                                                                �t�bhQ�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK	��h!�]�(�pandas._libs.missing��NA���hthththththththtet�b�_dtype�hg�StringDtype���)��ubhi)��}�(hlhhK ��h��R�(KK	��h!�]�(hththththththththtet�bhvhx)��ubhi)��}�(hlhhK ��h��R�(KK	��h!�]�(hththththththththtet�bhvhx)��ubhi)��}�(hlhhK ��h��R�(KK	��h!�]�(hththththththththtet�bhvhx)��ubhi)��}�(hlhhK ��h��R�(KK	��h!�]�(hththththththththtet�bhvhx)��ubhi)��}�(hlhhK ��h��R�(KK	��h!�]�(hththththththththtet�bhvhx)��ubhi)��}�(hlhhK ��h��R�(KK	��h!�]�(hththththththththtet�bhvhx)��ubhi)��}�(hlhhK ��h��R�(KK	��h!�]�(hththththththththtet�bhvhx)��ubhi)��}�(hlhhK ��h��R�(KK	��h!�]�(hththththththththtet�bhvhx)��ubhi)��}�(hlhhK ��h��R�(KK	��h!�]�(hththththththththtet�bhvhx)��ubhi)��}�(hlhhK ��h��R�(KK	��h!�]�(hththththththththtet�bhvhx)��ubhi)��}�(hlhhK ��h��R�(KK	��h!�]�(hththththththththtet�bhvhx)��ubhi)��}�(hlhhK ��h��R�(KK	��h!�]�(hththththththththtet�bhvhx)��ubhi)��}�(hlhhK ��h��R�(KK	��h!�]�(hththththththththtet�bhvhx)��ubhi)��}�(hlhhK ��h��R�(KK	��h!�]�(hththththththththtet�bhvhx)��ubhi)��}�(hlhhK ��h��R�(KK	��h!�]�(hththththththththtet�bhvhx)��ubhi)��}�(hlhhK ��h��R�(KK	��h!�]�(hththththththththtet�bhvhx)��ubhi)��}�(hlhhK ��h��R�(KK	��h!�]�(hththththththththtet�bhvhx)��ubhi)��}�(hlhhK ��h��R�(KK	��h!�]�(hththththththththtet�bhvhx)��ubhi)��}�(hlhhK ��h��R�(KK	��h!�]�(hththththththththtet�bhvhx)��ubhi)��}�(hlhhK ��h��R�(KK	��h!�]�(hththththththththtet�bhvhx)��ubhhK ��h��R�(KKK	��h�f8�����R�(Kh_NNNJ����J����K t�b�Bh        �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      ��t�bhhK ��h��R�(KKK	��h!�]�(�1234567INET�j=  j=  j=  j=  j=  j=  j=  j=  �
2000000000�j>  j>  j>  j>  j>  j>  j>  j>  �
4000000000�j?  j?  j?  j?  j?  j?  j?  j?  �20211012�j@  j@  j@  j@  j@  j@  j@  j@  �20211012�jA  jA  jA  jA  jA  jA  jA  jA  �-8000��-15000��-5715��-5000��-6600��-1400��-500��-2000�jE  �EUR�jJ  jJ  jJ  jJ  jJ  jJ  jJ  jJ  �0�jK  jK  jK  jK  jK  jK  jK  jK  �1�jL  jL  jL  jL  jL  jL  jL  jL  �-8000��-15000��-5715��-5000��-6600��-1400��-500��-2000�jP  jK  jK  jK  jK  jK  jK  jK  jK  jK  �-8000��-15000��-5715��-5000��-6600��-1400��-500��-2000�jX  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      et�bhi)��}�(hlhhK ��h��R�(KK	��h!�]�(�EUR�je  je  je  je  je  je  je  je  et�bhvhx)��ubhi)��}�(hlhhK ��h��R�(KK	��h!�]�(�
3000000000�jo  jo  jo  jo  jo  jo  jo  jo  et�bhvhx)��ubhi)��}�(hlhhK ��h��R�(KK	��h!�]�(�	123654789�jy  jy  jy  jy  jy  jy  jy  jy  et�bhvhx)��ube]�(h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bhQNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bhQNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bhQNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bhQNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h<at�bhQNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h=at�bhQNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bhQNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h?at�bhQNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bhQNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hBat�bhQNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hCat�bhQNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hDat�bhQNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hEat�bhQNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hFat�bhQNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hGat�bhQNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hHat�bhQNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hIat�bhQNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hJat�bhQNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hKat�bhQNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hNat�bhQNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hOat�bhQNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h9h:h@hLhMet�bhQNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h%h'h)h*h+h,h-h.h/h0h1h2h3h4et�bhQNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bhQNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h(at�bhQNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhQNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hj�mgr_locs��builtins��slice���KKK��R�u}�(jn  hzjo  jr  KKK��R�u}�(jn  h�jo  jr  KKK��R�u}�(jn  h�jo  jr  KKK��R�u}�(jn  h�jo  jr  KKK��R�u}�(jn  h�jo  jr  KKK��R�u}�(jn  h�jo  jr  KKK��R�u}�(jn  h�jo  jr  KKK��R�u}�(jn  h�jo  jr  KKK��R�u}�(jn  h�jo  jr  KKK��R�u}�(jn  h�jo  jr  KKK��R�u}�(jn  h�jo  jr  KK K��R�u}�(jn  h�jo  jr  K K!K��R�u}�(jn  h�jo  jr  K!K"K��R�u}�(jn  h�jo  jr  K"K#K��R�u}�(jn  h�jo  jr  K#K$K��R�u}�(jn  j  jo  jr  K$K%K��R�u}�(jn  j
  jo  jr  K%K&K��R�u}�(jn  j  jo  jr  K&K'K��R�u}�(jn  j  jo  jr  K)K*K��R�u}�(jn  j%  jo  jr  K*K+K��R�u}�(jn  j0  jo  hhK ��h��R�(KK��h^�C(                     '       (       �t�bu}�(jn  j:  jo  hhK ��h��R�(KK��h^�Cp                                                  	       
                     
                     �t�bu}�(jn  j^  jo  jr  KKK��R�u}�(jn  jh  jo  jr  KKK��R�u}�(jn  jr  jo  jr  KKK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.