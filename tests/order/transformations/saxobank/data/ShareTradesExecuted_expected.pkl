���!      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK2��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType�� executionDetails.tradingCapacity��	hierarchy��	_order.id��_orderState.id��_order.__meta_model__��_orderState.__meta_model__��orderIdentifiers.orderIdCode��!orderIdentifiers.transactionRefNo��priceFormingData.price��reportDetails.transactionRefNo��sourceIndex��	sourceKey��timestamps.tradingDateTime��timestamps.orderReceived��timestamps.orderSubmitted��timestamps.orderStatusUpdated��#transactionDetails.buySellIndicator��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��#transactionDetails.quantityCurrency��priceFormingData.tradedQuantity�� priceFormingData.initialQuantity��transactionDetails.quantity��#transactionDetails.quantityNotation��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK	��h�i8�����R�(K�<�NNNJ����J����K t�b�CH                                                                �t�bhX�__swarm_raw_index__�u��R�e]�(hhK ��h��R�(KKK	��h!�]�(�1��2�hshshththshththshthshshththshtht�Saxobank�huhuhuhuhuhuhuhu�
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��BUYI��SELL�hhh�h�hh�h��NEWO�h�h�h�h�h�h�h�h��FILL�h�h�h�h�h�h�h�h��MARKET�h�h�h�h�h�h�h�h��AOTC�h�h�h�h�h�h�h�h��
Standalone�h�h�h�h�h�h�h�h��
**********��
**********��
**********��
**********��
**********��
**********��
**********��
**********��
**********�h�h�h�h�h�h�h�h�h��Order�h�h�h�h�h�h�h�h��
OrderState�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�b�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK	��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�
**********��
**********��
5017466447��
5017492447��
5017192522��
5016788141��
5016891257��
5016891256��
5017812785�et�b�_dtype�h��StringDtype���)��ubhhK ��h��R�(KKK	��h�f8�����R�(KhfNNNJ����J����K t�b�CH)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?�t�bh�)��}�(h�hhK ��h��R�(KK	��h��]�(h�h�h�h�h�h�h�h�h�et�bh�h�)��ubhhK ��h��R�(KKK	��he�CH                                                                �t�bhhK ��h��R�(KKK	��h!�]�(�w/Users/<USER>/Github/se-prefect-dir/swarm-tasks/tests/order/transformations/saxobank/data/ShareTradesExecuted_test.pkl�h�h�h�h�h�h�h�h͌2021-10-12T10:39:51.000000Z��2021-10-12T10:39:51.000000Z��2021-10-12T16:19:33.107000Z��2021-10-12T15:42:11.470000Z��2021-10-12T13:30:09.990000Z��2021-10-12T09:41:04.270000Z��2021-10-12T10:39:51.000000Z��2021-10-12T10:39:51.000000Z��2021-10-12T22:03:07.430000Z�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�hh�hhh�h�hh�h�et�bhhK ��h��R�(KKK	��h��CH)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?�t�bhhK ��h��R�(KKK	��h!�]�(�EUR�h�CAD�h�USD�h�h�h�h�MONE�h�h�h�h�h�h�h�h�pandas._libs.missing��NA���h�h�h�h�h�h�h�h�et�bhhK ��h��R�(KKK	��h��C�      n@      n@     ��@     @�@      I@     ��@    �v�@    �v�@     @@      n@      n@     ��@     @�@      I@     ��@    �v�@    �v�@     @@      n@      n@     ��@     @�@      I@     ��@    �v�@    �v�@     @@�t�bhhK ��h��R�(KKK	��h!�]�(�UNIT�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h֌XETR�h��XTNX��XTSE��XNAS��XMAD�h�h��PINX��XOFF�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�]�(}�(�labelId��id:040300zz0406ic1zzz00��path��buyer��type��se_schema.static.market��IdentifierType����ARRAY���R�u}�(j   �id:040300zz0406ic1zzz00�j  �reportDetails.executingEntity�j  j  �OBJECT���R�u}�(j   �
id:1111111�j  �seller�j  j
  u}�(j   �
id:1111111�j  �counterparty�j  j  ue]�(}�(j   �
id:1111111�j  j  j  j
  u}�(j   �id:040300zz0406ic1zzz00�j  j
  j  j  u}�(j   �id:040300zz0406ic1zzz00�j  j  j  j
  u}�(j   �
id:1111111�j  j  j  j  ue]�(}�(j   �id:040300zz0406ic1zzz00�j  j  j  j
  u}�(j   �id:040300zz0406ic1zzz00�j  j
  j  j  u}�(j   �
id:1111111�j  j  j  j
  u}�(j   �
id:1111111�j  j  j  j  u}�(j   �
id:1111111�j  �trader�j  j
  ue]�(}�(j   �id:040300zz0406ic1zzz00�j  j  j  j
  u}�(j   �id:040300zz0406ic1zzz00�j  j
  j  j  u}�(j   �
id:1111111�j  j  j  j
  u}�(j   �
id:1111111�j  j  j  j  u}�(j   �
id:1111111�j  j+  j  j
  ue]�(}�(j   �
id:1111111�j  j  j  j
  u}�(j   �id:040300zz0406ic1zzz00�j  j
  j  j  u}�(j   �id:040300zz0406ic1zzz00�j  j  j  j
  u}�(j   �
id:1111111�j  j  j  j  u}�(j   �
id:1111111�j  j+  j  j
  ue]�(}�(j   �
id:1111111�j  j  j  j
  u}�(j   �id:040300zz0406ic1zzz00�j  j
  j  j  u}�(j   �id:040300zz0406ic1zzz00�j  j  j  j
  u}�(j   �
id:1111111�j  j  j  j  u}�(j   �
id:1111111�j  j+  j  j
  ue]�(}�(j   �id:040300zz0406ic1zzz00�j  j  j  j
  u}�(j   �id:040300zz0406ic1zzz00�j  j
  j  j  u}�(j   �
id:1111111�j  j  j  j
  u}�(j   �
id:1111111�j  j  j  j  ue]�(}�(j   �
id:1111111�j  j  j  j
  u}�(j   �id:040300zz0406ic1zzz00�j  j
  j  j  u}�(j   �id:040300zz0406ic1zzz00�j  j  j  j
  u}�(j   �
id:1111111�j  j  j  j  ue]�(}�(j   �
id:1111111�j  j  j  j
  u}�(j   �id:040300zz0406ic1zzz00�j  j
  j  j  u}�(j   �id:040300zz0406ic1zzz00�j  j  j  j
  u}�(j   �
id:1111111�j  j  j  j  ue�id:040300zz0406ic1zzz00��id:040300zz0406ic1zzz00��id:040300zz0406ic1zzz00��id:040300zz0406ic1zzz00��id:040300zz0406ic1zzz00��id:040300zz0406ic1zzz00��id:040300zz0406ic1zzz00��id:040300zz0406ic1zzz00��id:040300zz0406ic1zzz00��id:040300zz0406ic1zzz00��
id:1111111��id:040300zz0406ic1zzz00��id:040300zz0406ic1zzz00��
id:1111111��
id:1111111��id:040300zz0406ic1zzz00��
id:1111111��
id:1111111��
id:1111111��id:040300zz0406ic1zzz00��
id:1111111��
id:1111111��id:040300zz0406ic1zzz00��id:040300zz0406ic1zzz00��
id:1111111��id:040300zz0406ic1zzz00��id:040300zz0406ic1zzz00��
id:1111111��
id:1111111��
id:1111111��
id:1111111��
id:1111111��
id:1111111��
id:1111111��
id:1111111��
id:1111111�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�
id:1111111��
id:1111111��
id:1111111��
id:1111111�h�h�h�et�bhhK ��h��R�(KKK	��h!�]�(]�(h�j  j  j  e]�(j  j  j  j  e]�(j!  j#  j%  j'  j)  e]�(j-  j/  j1  j3  j5  e]�(j8  j:  j<  j>  j@  e]�(jC  jE  jG  jI  jK  e]�(jN  jP  jR  jT  e]�(jW  jY  j[  j]  e]�(j`  jb  jd  jf  eet�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3et�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h8h9h:h;h<h=et�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h?h@hAet�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hBhChDet�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hEhFhGhHhIhJhKhLhMhNhOhPhQhRhShThUet�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hVat�bhXNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hp�mgr_locs��builtins��slice���K KK��R�u}�(j  h�j  j  KKK��R�u}�(j  h�j  j  KKK��R�u}�(j  h�j  j  KKK��R�u}�(j  h�j  j  KKK��R�u}�(j  h�j  j  KKK��R�u}�(j  h�j  j  KKK��R�u}�(j  h�j  j  KKK��R�u}�(j  h�j  j  KK K��R�u}�(j  h�j  j  K K1K��R�u}�(j  j�  j  j  K1K2K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.