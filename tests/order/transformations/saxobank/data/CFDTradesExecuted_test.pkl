��`+      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKJ��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�
ReportingDate��InstrumentType��
CounterpartID��CounterpartName��
AccountNumber��PartnerAccountKey��AccountCurrency��InstrumentCode��InstrumentDescription��InstrumentCurrency��ISINCode��ExchangeDescription��TradedAmount��
FigureSize��TradeNumber��OrderNumber��	TradeTime��	TradeDate��	ValueDate��BuySell��Price��QuotedValue��CommissionInstrumentCurrency��TransferFee��TaxInstrumentCurrency��RelatedTradeID��	TradeType��CAEventTypeName��	CAEventID��TradeAllocation��RootTradeID��ExternalOrderID��OriginalTradeID��
CorrectionLeg��
CAEventTypeID��EODRate��ExchangeISOCode��ISOMic��
InstrumentUIC��ActualTradeDate��CountryOfIssue��!ExecutingEntityIdentificationCode��InstrumentClassification��PriceMultiplier��TermOfTheUnderlyingIndex��SettlementStyle��
UserIdOnTrade��
EmployeeId��DecisionMakerUserId��Venue��TradeExecutionTime��OTCPostTradeIndicator��CommodityDerivativeIndicator��'SecuritiesFinancingTransactionIndicator��SaxoPortfolioManagerTriggered��AorrAlAtionKAy��ParentInstrumentUIC��ParentInstrumentName��+AdditionalTransactionCostInstrumentCurrency��OriginatingTool��	NetAmount��UnderlyingInstrumentCode��Strike��TransmissionOfOrderIndicator��
DirtyPrice��CallPut��ISIN��InstrumentISINCode��UnderlyingInstrumentISINCode��TermOfUnderlyingIndex��ShortSellingIndicator��ComplexTradeComponentId��FXType��
ExpiryDate�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK
��h�i8�����R�(K�<�NNNJ����J����K t�b�CP                                                                	       �t�bhp�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK
��h!�]�(�pandas._libs.missing��NA���h�h�h�h�h�h�h�h�h�et�b�_dtype�h��StringDtype���)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(�CFDs�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(�12345678�j  j  j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(�EU50.I�j  �US500.I��GER40.I�j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(�EUR�j  �USD�j  j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(�EU0009658145�j%  �US78378X1072��DE0008469008�j'  j&  j&  j&  j&  j&  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(�	100000001��	100000002��	100000003��	100000004��	100000005��	100000006��	100000007��	100000008��	100000009��	100000010�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(�20211012 07:03:49��20211012 07:11:13��20211012 07:47:59��20211012 05:24:45��20211012 12:49:32��20211012 08:45:20��20211012 11:24:09��20211012 17:05:08��20211012 17:26:14��20211012 19:05:28�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(�Buy��Sell�jX  jX  jX  jW  jW  jW  jW  jW  et�bh�h�)��ubhhK ��h��R�(KKK
��h�f8�����R�(Kh~NNNJ����J����K t�b�B�        �?      �      �      �      �      �?      �?      �?      �?      �?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      ��t�bhhK ��h��R�(KK/K
��h!�]�(�20211012�jj  jj  jj  jj  jj  jj  jj  jj  jj  �Name�jk  jk  jk  jk  jk  jk  jk  jk  jk  �
123456INET�jl  jl  jl  jl  jl  jl  jl  jl  jl  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �EUR�jm  jm  jm  jm  jm  jm  jm  jm  jm  �EU Stocks 50�jn  �	US SPX500��
Germany 40�jp  jo  jo  jo  jo  jo  �Euronext Paris�jq  �New York Stock Exchange��Deutsche B?se (XETRA)�js  jr  jr  jr  jr  jr  �1�jt  jt  jt  jt  jt  jt  jt  jt  jt  �
2000000001��
2000000002��
2000000003��
2000000004��
2000000005��
2000000006��
2000000007��
2000000008��
2000000009��
2000000010��20211012�j  j  j  j  j  j  j  j  j  �20211012�j�  j�  j�  j�  j�  j�  j�  j�  j�  �-1.01��1.01�j�  j�  j�  j�  j�  j�  j�  j�  �0�j�  j�  j�  j�  j�  j�  j�  j�  j�  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �
5100000000�j�  j�  j�  j�  j�  j�  j�  j�  j�  �Open Order Monitor�j�  j�  j�  j�  j�  j�  j�  j�  j�  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �No�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �10.01�j�  j�  j�  j�  j�  j�  j�  j�  j�  �XPAR�j�  �XNYS��XETR�j�  j�  j�  j�  j�  j�  �16752�j�  �1375��1373�j�  j�  j�  j�  j�  j�  �20211012�j�  j�  j�  j�  j�  j�  j�  j�  j�  �CH�j�  �US��DE�j�  j�  j�  j�  j�  j�  �JESXCC�j�  j�  j�  j�  j�  j�  j�  j�  j�  jt  jt  jt  jt  jt  jt  jt  jt  jt  jt  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �Cash settled�j�  j�  j�  j�  j�  j�  j�  j�  j�  �1111111�j�  j�  j�  j�  j�  j�  j�  j�  j�  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �PAR�j�  �NYSE��FSE�j�  j�  j�  j�  j�  j�  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �FALSE�j�  j�  j�  j�  j�  j�  j�  j�  j�  �FALSE�j�  j�  j�  j�  j�  j�  j�  j�  j�  �FALSE�j�  j�  j�  j�  j�  j�  j�  j�  j�  �$AA1A1A1A-AA11-111A-1A11-A1AA10110AAA��$AA1A1A0A-AA11-111A-1A11-A1AA10110AAA��$1A1A1111-A111-11AA-1AAA-110A11AAA111��$A110A111-1111-1A01-1AAA-1A1A111A1A11��$A1111AAA-111A-1110-A110-AAAA1AAAAAA1��$A1AA0111-A111-1111-A1A1-AA11AA11111A��$AAAAA111-00AA-1AA1-AAAA-A1A1111AA111��$01A111A1-1A11-11AA-11A1-11AAA11A11A1��$11A101AA-1110-1A11-11AA-11AAA01111AA��$1AAA1A11-11AA-111A-A1AA-1A11AAA11A11�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �SaxoTrader Pro�j�  �SaxoTraderGO�j�  j�  j�  j�  j�  j�  j�  et�bh�)��}�(h�hhK ��h��R�(KK
��h!�]�(�XPAR�j�  �XNYS��XETR�j�  j�  j�  j�  j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(�666666TL1111IC1AAD00��666666TL1111IC1AAD01��666666TL1111IC1AAD02��666666TL1111IC1AAD03��666666TL1111IC1AAD04��666666TL1111IC1AAD05��666666TL1111IC1AAD06��666666TL1111IC1AAD07��666666TL1111IC1AAD08��666666TL1111IC1AAD09�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK
��h!�]�(�20211012 07:03:49.833��20211012 07:11:13.510��20211012 07:47:59.140��20211012 05:24:45.017��20211012 12:49:32.217��20211012 08:45:20.827��20211012 11:24:09.270��20211012 17:05:08.597��20211012 17:26:14.570��20211012 19:05:28.013�et�bh�h�)��ube]�(h
h}�(hhhK ��h��R�(KK��h!�]�hbat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hdat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hfat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hgat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hhat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hiat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hjat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hkat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hlat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hmat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hnat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h,at�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h1h9hahcheet�bhpNu��R�h
h}�(hhhK ��h��R�(KK/��h!�]�(h%h(h)h*h+h-h0h2h4h6h7h:h;h<h=h>h?h@hAhBhChDhEhFhGhHhJhKhLhMhOhPhQhRhShThUhVhXhYhZh[h\h]h^h_h`et�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hIat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hNat�bhpNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hWat�bhpNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs��builtins��slice���K=K>K��R�u}�(j�  h�j�  j�  K?K@K��R�u}�(j�  h�j�  j�  KAKBK��R�u}�(j�  h�j�  j�  KBKCK��R�u}�(j�  h�j�  j�  KCKDK��R�u}�(j�  h�j�  j�  KDKEK��R�u}�(j�  h�j�  j�  KEKFK��R�u}�(j�  h�j�  j�  KFKGK��R�u}�(j�  h�j�  j�  KGKHK��R�u}�(j�  h�j�  j�  KHKIK��R�u}�(j�  h�j�  j�  KIKJK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j  j�  j�  K	K
K��R�u}�(j�  j  j�  j�  K
KK��R�u}�(j�  j*  j�  j�  KKK��R�u}�(j�  j=  j�  j�  KKK��R�u}�(j�  jP  j�  j�  KKK��R�u}�(j�  j]  j�  hhK ��h��R�(KK��h}�C(              <       >       @       �t�bu}�(j�  jg  j�  hhK ��h��R�(KK/��h}�Bx                                                    
                                                                                                                 !       "       #       %       &       '       (       *       +       ,       -       .       /       0       1       3       4       5       6       7       8       9       :       ;       �t�bu}�(j�  j�  j�  j�  K$K%K��R�u}�(j�  j�  j�  j�  K)K*K��R�u}�(j�  j�  j�  j�  K2K3K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.