��y)      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK2��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType�� executionDetails.tradingCapacity��	hierarchy��	_order.id��_orderState.id��_order.__meta_model__��_orderState.__meta_model__��orderIdentifiers.orderIdCode��!orderIdentifiers.transactionRefNo��priceFormingData.price��reportDetails.transactionRefNo��sourceIndex��	sourceKey��timestamps.tradingDateTime��timestamps.orderReceived��timestamps.orderSubmitted��timestamps.orderStatusUpdated��#transactionDetails.buySellIndicator��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��#transactionDetails.quantityCurrency��priceFormingData.tradedQuantity�� priceFormingData.initialQuantity��transactionDetails.quantity��#transactionDetails.quantityNotation��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�CX                                                                	       
       �t�bhX�__swarm_raw_index__�u��R�e]�(hhK ��h��R�(KKK��h!�]�(�1�hs�2�hthshthththshshshshshththshthththshshs�Saxobank�huhuhuhuhuhuhuhuhuhu�
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��BUYI�h��SELL�h�h�h�h�h�h�h�h��NEWO�h�h�h�h�h�h�h�h�h�h��FILL�h�h�h�h�h�h�h�h�h�h��MARKET�h�h�h�h�h�h�h�h�h�h��AOTC�h�h�h�h�h�h�h�h�h�h��
Standalone�h�h�h�h�h�h�h�h�h�h��
**********��
**********��
**********��
**********��
**********��
**********��
**********��
**********��
**********��
**********��
**********�h�h�h�h�h�h�h�h�h�h�h��Order�h�h�h�h�h�h�h�h�h�h��
OrderState�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�b�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�
8000000000��
8000000001��
8000000002��
8000000003��
8000000004��
8000000005��
8000000006��
8000000007��
8000000008��
8000000009��
8000000010�et�b�_dtype�h��StringDtype���)��ubhhK ��h��R�(KKK��h�f8�����R�(KhfNNNJ����J����K t�b�CX�HP|�?.�!��u�?('�UHy�?���?�y�?\���(�T@�"��?&ǝ��z�?�d��?{�/L�
�?��{��?c���&��?�t�bh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubhhK ��h��R�(KKK��he�CX                                                                	       
       �t�bhhK ��h��R�(KKK��h!�]�(�t/Users/<USER>/Github/se-prefect-dir/swarm-tasks/tests/order/transformations/saxobank/data/FXTradesExecuted_test.pkl�h�h�h�h�h�h�h�h�h�hӌ2021-10-12T12:24:11.603000Z��2021-10-12T15:43:03.617000Z��2021-10-12T14:57:34.103000Z��2021-10-12T15:01:42.063000Z��2021-10-12T11:29:19.020000Z��2021-10-12T11:25:50.917000Z��2021-10-12T11:27:01.403000Z��2021-10-12T11:24:24.893000Z��2021-10-12T11:52:01.477000Z��2021-10-12T05:53:56.547000Z��2021-10-12T15:13:26.957000Z�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bhhK ��h��R�(KKK��h��CX�HP|�?.�!��u�?('�UHy�?���?�y�?\���(�T@�"��?&ǝ��z�?�d��?{�/L�
�?��{��?c���&��?�t�bhhK ��h��R�(KKK��h!�]�(�USD�h�h�h�JPY��CAD�h�AUD�h�h�h�MONE�h�h�h�h�h�h�h�h�h�h�EUR�h�h�h�h�h�h�h�h�h�h�et�bhhK ��h��R�(KKK��h��B       p�@     p�@     p�@     p�@     j�@     L�@     ��@     j�@     @�@     L�@     ��@     p�@     p�@     p�@     p�@     j�@     L�@     ��@     j�@     @�@     L�@     ��@     p�@     p�@     p�@     p�@     j�@     L�@     ��@     j�@     @�@     L�@     ��@�t�bhhK ��h��R�(KKK��h!�]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�hތpandas._libs.missing��NA���h�h�h�h�h�h�h�h�h�h��XOFF�j   j   j   j   j   j   j   j   j   j   ]�}�(�labelId��XXXXUSDEURFXSPOT��path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�ua]�}�(j  �XXXXUSDEURFXSPOT�j  j  j  j
  ua]�}�(j  �XXXXUSDEURFXSPOT�j  j  j  j
  ua]�}�(j  �XXXXUSDEURFXSPOT�j  j  j  j
  ua]�}�(j  �XXXXJPYEURFXSPOT�j  j  j  j
  ua]�}�(j  �XXXXCADEURFXSPOT�j  j  j  j
  ua]�}�(j  �XXXXUSDEURFXSPOT�j  j  j  j
  ua]�}�(j  �XXXXAUDEURFXSPOT�j  j  j  j
  ua]�}�(j  �XXXXCADEURFXSPOT�j  j  j  j
  uah�h�]�(}�(j  �lei:549300tl5406ic1xkd09�j  �buyer�j  j
  �ARRAY���R�u}�(j  �lei:549300tl5406ic1xkd09�j  �reportDetails.executingEntity�j  j
  u}�(j  �
id:1000001�j  �seller�j  j,  u}�(j  �
id:1000001�j  �counterparty�j  j
  u}�(j  �
id:1000001�j  �trader�j  j,  ue]�(}�(j  �lei:549300tl5406ic1xkd09�j  j)  j  j,  u}�(j  �lei:549300tl5406ic1xkd09�j  j/  j  j
  u}�(j  �
id:1000001�j  j2  j  j,  u}�(j  �
id:1000001�j  j5  j  j
  u}�(j  �
id:1000001�j  j8  j  j,  ue]�(}�(j  �
id:1000001�j  j)  j  j,  u}�(j  �lei:549300tl5406ic1xkd09�j  j/  j  j
  u}�(j  �lei:549300tl5406ic1xkd09�j  j2  j  j,  u}�(j  �
id:1000001�j  j5  j  j
  u}�(j  �
id:1000001�j  j8  j  j,  ue]�(}�(j  �
id:1000001�j  j)  j  j,  u}�(j  �lei:549300tl5406ic1xkd09�j  j/  j  j
  u}�(j  �lei:549300tl5406ic1xkd09�j  j2  j  j,  u}�(j  �
id:1000001�j  j5  j  j
  u}�(j  �
id:1000001�j  j8  j  j,  ue]�(}�(j  �lei:549300tl5406ic1xkd09�j  j)  j  j,  u}�(j  �lei:549300tl5406ic1xkd09�j  j/  j  j
  u}�(j  �
id:1000001�j  j2  j  j,  u}�(j  �
id:1000001�j  j5  j  j
  u}�(j  �
id:1000001�j  j8  j  j,  ue]�(}�(j  �
id:1000001�j  j)  j  j,  u}�(j  �lei:549300tl5406ic1xkd09�j  j/  j  j
  u}�(j  �lei:549300tl5406ic1xkd09�j  j2  j  j,  u}�(j  �
id:1000001�j  j5  j  j
  u}�(j  �
id:1000001�j  j8  j  j,  ue]�(}�(j  �
id:1000001�j  j)  j  j,  u}�(j  �lei:549300tl5406ic1xkd09�j  j/  j  j
  u}�(j  �lei:549300tl5406ic1xkd09�j  j2  j  j,  u}�(j  �
id:1000001�j  j5  j  j
  u}�(j  �
id:1000001�j  j8  j  j,  ue]�(}�(j  �
id:1000001�j  j)  j  j,  u}�(j  �lei:549300tl5406ic1xkd09�j  j/  j  j
  u}�(j  �lei:549300tl5406ic1xkd09�j  j2  j  j,  u}�(j  �
id:1000001�j  j5  j  j
  u}�(j  �
id:1000001�j  j8  j  j,  ue]�(}�(j  �lei:549300tl5406ic1xkd09�j  j)  j  j,  u}�(j  �lei:549300tl5406ic1xkd09�j  j/  j  j
  u}�(j  �
id:1000001�j  j2  j  j,  u}�(j  �
id:1000001�j  j5  j  j
  u}�(j  �
id:1000001�j  j8  j  j,  ue]�(}�(j  �lei:549300tl5406ic1xkd09�j  j)  j  j,  u}�(j  �lei:549300tl5406ic1xkd09�j  j/  j  j
  u}�(j  �
id:1000001�j  j2  j  j,  u}�(j  �
id:1000001�j  j5  j  j
  u}�(j  �
id:1000001�j  j8  j  j,  ue]�(}�(j  �lei:549300tl5406ic1xkd09�j  j)  j  j,  u}�(j  �lei:549300tl5406ic1xkd09�j  j/  j  j
  u}�(j  �
id:1000001�j  j2  j  j,  u}�(j  �
id:1000001�j  j5  j  j
  u}�(j  �
id:1000001�j  j8  j  j,  ue�lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��
id:1000001��
id:1000001��lei:549300tl5406ic1xkd09��
id:1000001��
id:1000001��
id:1000001��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��
id:1000001��
id:1000001��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��
id:1000001��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��
id:1000001��
id:1000001��
id:1000001��
id:1000001��
id:1000001��
id:1000001��
id:1000001��
id:1000001��
id:1000001��
id:1000001��
id:1000001��
id:1000001��
id:1000001��
id:1000001�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��
id:1000001��
id:1000001��
id:1000001��
id:1000001��
id:1000001��
id:1000001��
id:1000001��
id:1000001��
id:1000001��
id:1000001��
id:1000001�et�bhhK ��h��R�(KKK��h!�]�(]�(j  j'  j-  j0  j3  j6  e]�(j  j:  j<  j>  j@  jB  e]�(j  jE  jG  jI  jK  jM  e]�(j  jP  jR  jT  jV  jX  e]�(j  j[  j]  j_  ja  jc  e]�(j  jf  jh  jj  jl  jn  e]�(j  jq  js  ju  jw  jy  e]�(j!  j|  j~  j�  j�  j�  e]�(j$  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  eet�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3et�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h8h9h:h;h<h=et�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h?h@hAet�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hBhChDet�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hEhFhGhHhIhJhKhLhMhNhOhPhQhRhShThUet�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hVat�bhXNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hp�mgr_locs��builtins��slice���K KK��R�u}�(j[  h�j\  j_  KKK��R�u}�(j[  h�j\  j_  KKK��R�u}�(j[  h�j\  j_  KKK��R�u}�(j[  h�j\  j_  KKK��R�u}�(j[  h�j\  j_  KKK��R�u}�(j[  h�j\  j_  KKK��R�u}�(j[  h�j\  j_  KKK��R�u}�(j[  h�j\  j_  KK K��R�u}�(j[  h�j\  j_  K K1K��R�u}�(j[  j�  j\  j_  K1K2K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.