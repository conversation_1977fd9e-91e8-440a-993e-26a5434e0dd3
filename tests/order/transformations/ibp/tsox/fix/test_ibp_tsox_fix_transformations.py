from pathlib import Path

import pandas as pd
import pytest
from prefect import context
from swarm.task.auditor import Auditor

from swarm_tasks.order.transformations.ibp.tsox.fix.ibp_tsox_fix_order_transformations import (
    IbpTsoxFixOrderTransformations,
)

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")

# The source file is a pickle of the df which should be used as  the source dataframe
SOURCE_FRAME_PKL = TEST_FILES_DIR.joinpath("input_source_frame.pkl")
EXPECTED_TRANSFORMED_DF = TEST_FILES_DIR.joinpath("expected_target_frame.pkl")


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


@pytest.fixture()
def source_frame() -> pd.DataFrame:
    return pd.read_pickle(SOURCE_FRAME_PKL)


@pytest.fixture()
def expected_transformed_df() -> pd.DataFrame:
    return pd.read_pickle(EXPECTED_TRANSFORMED_DF)


class TestIbpTsoxFixOrderTransformations:
    """Tests IBP TSOX FIX transformations""" ""

    def test_end_to_end_ice_pof_fix_order_transformations(
        self,
        source_frame: pd.DataFrame,
        expected_transformed_df: pd.DataFrame,
        auditor: Auditor,
    ):
        task = IbpTsoxFixOrderTransformations(
            source_frame=source_frame,
            logger=context.get("logger"),
            auditor=auditor,
        )
        result = task.process()
        assert result.equals(expected_transformed_df)
