���u      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK`��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��executionDetails.limitPrice��(_orderState.executionDetails.orderStatus��#_order.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo��&executionDetails.shortSellingIndicator��executionDetails.stopPrice�� executionDetails.tradingCapacity��executionDetails.validityPeriod��	hierarchy��_orderState.id��	_order.id��_order.__meta_model__��_orderState.__meta_model__��orderIdentifiers.orderIdCode��orderIdentifiers.parentOrderId��!orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��priceFormingData.price��"priceFormingData.remainingQuantity��+_orderState.priceFormingData.tradedQuantity��reportDetails.transactionRefNo��sourceIndex��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��timestamps.tradingDateTime��3tradersAlgosWaiversIndicators.shortSellingIndicator��transactionDetails.basketId��#transactionDetails.buySellIndicator��#transactionDetails.commissionAmount��+transactionDetails.commissionAmountCurrency��'transactionDetails.commissionAmountType��*transactionDetails.complexTradeComponentId��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��'_orderState.transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��transactionDetails.recordType��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��notional_currency_2_attribute��asset_class_attribute��bbg_figi_id_attribute��isin_attribute��option_type_attribute��option_strike_price_attribute��underlying_isin_attribute��venue_attribute��'underlying_symbol_expiry_code_attribute��underlying_symbol_attribute��currency_attribute��expiry_date_attribute��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��__fallback_buyer__��__fallback_seller__��__fallback_counterparty__��__fallback_client__��__fallback_buyer_dec_maker__��__fallback_seller_dec_maker__��__fallback_inv_dec_in_firm__��__fallback_trader__��__fallback_exec_within_firm__��__fallback_executing_entity__��marketIdentifiers��__newo_in_file__��__newo_cancel_indicator__��__asset_class__��__isntr_ids_currency__��	__venue__�� __instrument_unique_identifier__��__instrument_full_name__��__price_reference_ric__��__instrument_classification__��__created_through_fallback__��$orderIdentifiers.internalOrderIdCode�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(h�numpy.core.numeric��_frombuffer���(��                                                  	       
                                                                      #       (       )                     "       *       �h�i8�����R�(K�<�NNNJ����J����K t�bK���C�t�R�h�Nu��R�e]�(hhK ��h��R�(KKJK��h!�]�(�2��1�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��Enfusion�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��
2024-08-30��
2024-08-30��
2024-09-03��
2024-09-03��
2024-09-03��
2024-09-03��
2024-09-03��
2024-09-04��
2024-09-03��
2024-09-03��
2024-08-30��
2024-08-30��
2024-09-03��
2024-09-03��
2024-09-03��
2024-09-03��
2024-09-03��
2024-09-04��
2024-09-03��
2024-09-03��
2024-09-03��
2024-09-04��
2024-09-03��
2024-09-04��SELL��BUYI�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��pandas._libs.missing��NA���h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��FILL��FILL��FILL��FILL��FILL��FILL��FILL��FILL��FILL��FILL�h�h��FILL��FILL��NEWO�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�hΌMarket�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��SESH�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��
Standalone�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��Order�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��
OrderState�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��66095843920240830��66095847020240830��66247162120240903��66247161020240903��66245009720240903��66245006120240903��66245232020240903��66251588020240904��66236756020240903��66236755620240903��66095843920240830��66095847020240830��66247162120240903��66247161020240903��66245009720240903��66245006120240903��66245232020240903��66251588020240904��66236756020240903��66236755620240903��66231441920240903��66246888020240904��66231441920240903��66246888020240904�GAb�QB��GAc)0��GA?O��g8GA?b�	5TvG@�d     G@�     GA��    G@$      G@`�     G@4      GAb�QB��GAc)0��GA?O��g8GA?b�	5TvG@�d     G@�     GA��    G@$      G@`�     G@4      G@.      G@       G@.      G@       G@b�/�V�G@a��	�G@0y�+�G@��NrTG@����|��G@���0�
�G@���M:�G@��     G        G        G@b�/�V�G@a��	�G@0y�+�G@��NrTG@����|��G@���0�
�G@���M:�G@��     G        G        G@���    G@�W�    G@���    G@�W�    h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�2024-08-30T15:24:57.000000Z��2024-08-30T15:20:58.000000Z��2024-09-03T16:12:52.000000Z��2024-09-03T16:12:54.000000Z��2024-09-03T00:00:00.000000Z��2024-09-03T00:21:59.000000Z��2024-09-03T00:00:00.000000Z��2024-09-04T00:00:00.000000Z��2024-09-03T00:00:01.000000Z��2024-09-03T00:00:00.000000Z��2024-08-30T15:24:57.000000Z��2024-08-30T15:20:58.000000Z��2024-09-03T16:12:52.000000Z��2024-09-03T16:12:54.000000Z��2024-09-03T00:00:00.000000Z��2024-09-03T00:21:59.000000Z��2024-09-03T00:00:00.000000Z��2024-09-04T00:00:00.000000Z��2024-09-03T00:00:01.000000Z��2024-09-03T00:00:00.000000Z��2024-09-03T00:00:00.000000Z��2024-09-04T00:00:00.000000Z��2024-09-03T00:00:00.000000Z��2024-09-04T00:00:00.000000Z�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�j   j  j  j  h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�j   j  j  j  h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�j   j  j  j  h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�G@(����G?�
qu�"G?�!�.H�G@o@     h�h�h�h�h�h�G@(����G?�
qu�"G?�!�.H�G@o@     h�h�h�h�h�h��JPY�j  �HKD�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  h�h�h�h�h�h�h�h��Basis points�j  j  h�h�h�h�h�h�h�j  j  j  h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �MONE�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �JPY�j  �HKD�j	  h�h�h�h�h�h�j  j  j	  j	  h�h�h�h�h�h�h�h�h�h��MONE�j
  j
  j
  �UNIT�j  j  j  j  j  j
  j
  j
  j
  j  j  j  j  j  j  j  j  j  j  �
Allocation�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�j   j  j  j  h�h�h�h��XTKS�j
  j
  �XSIM��XOSE�j  h�h�h�h�j
  j
  j
  j  j  j  �XOFF�j  j  j  �XXXX�j  j  j  �XOFF�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  ]�(}�(�labelId��XXXXUSDJPYFXFWD2024-09-17��path��instrumentDetails.instrument��type��se_elastic_schema.static.market��IdentifierType����OBJECT���R�u}�(j  �XXXXJPYUSDFXFWD2024-09-17�j  j  j  j  ue]�(}�(j  �XXXXUSDJPYFXFWD2025-01-06�j  j  j  j  u}�(j  �XXXXJPYUSDFXFWD2025-01-06�j  j  j  j  ue]�}�(j  �XXXXUSDHKDFXSPOT�j  j  j  j  ua]�(}�(j  �XXXXUSDHKDFXFWD2024-11-05�j  j  j  j  u}�(j  �XXXXHKDUSDFXFWD2024-11-05�j  j  j  j  ue]�(}�(j  �JP3637300009�j  j  j  j  u}�(j  �JP3637300009JPYXTKS�j  j  j  j  u}�(j  �4704�j  j  j  j  ue]�(}�(j  �JP3407200009�j  j  j  j  u}�(j  �JP3407200009JPYXTKS�j  j  j  j  u}�(j  �5726�j  j  j  j  ue]�(}�(j  �JP3869970008�j  j  j  j  u}�(j  �JP3869970008JPYXTKS�j  j  j  j  u}�(j  �8698�j  j  j  j  ue]�(}�(j  �XSIMNKU24FF 00:00:00�j  j  j  j  u}�(j  �NKU24�j  j  j  j  ueh�h�]�(}�(j  �XXXXUSDJPYFXFWD2024-09-17�j  j  j  j  u}�(j  �XXXXJPYUSDFXFWD2024-09-17�j  j  j  j  ue]�(}�(j  �XXXXUSDJPYFXFWD2025-01-06�j  j  j  j  u}�(j  �XXXXJPYUSDFXFWD2025-01-06�j  j  j  j  ue]�}�(j  �XXXXUSDHKDFXSPOT�j  j  j  j  ua]�(}�(j  �XXXXUSDHKDFXFWD2024-11-05�j  j  j  j  u}�(j  �XXXXHKDUSDFXFWD2024-11-05�j  j  j  j  ue]�(}�(j  j1  j  j  j  j  u}�(j  �JP3637300009JPYXTKS�j  j  j  j  u}�(j  j5  j  j  j  j  ue]�(}�(j  j8  j  j  j  j  u}�(j  �JP3407200009JPYXTKS�j  j  j  j  u}�(j  j<  j  j  j  j  uee(]�(}�(j  j?  j  j  j  j  u}�(j  �JP3869970008JPYXTKS�j  j  j  j  u}�(j  jC  j  j  j  j  ue]�(}�(j  �XSIMNKU24FF 00:00:00�j  j  j  j  u}�(j  jH  j  j  j  j  ueh�h�]�(}�(j  �XSIMNKU24FF 00:00:00�j  j  j  j  u}�(j  jH  j  j  j  j  ue]�}�(j  �96-30-11-0-102-18221-0�j  j  j  j  ua]�(}�(j  �XSIMNKU24FF 00:00:00�j  j  j  j  u}�(j  jH  j  j  j  j  ue]�}�(j  jt  j  j  j  j  ua�JPY�j{  �HKD�j|  h�h�h�j{  h�h�j{  j{  j|  j|  h�h�h�j{  h�h�j{  j{  j{  j{  �
fx forward�j}  �fx spot�j}  h�h�h��future��option�j�  j}  j}  j~  j}  h�h�h�j  j�  j�  j  j  j  j  h�h�h�h�h�h�h�h�G@�̀    G@�I�    h�h�h�h�h�h�h�h�G@�̀    G@�I�    h�h�h�h�h�h�h�h�h�h�h�jH  h�h�h�h�h�h�h�h�h�jH  h�h�jH  h�jH  h�h�h�h�h�h�h�h��NI�h�h�h�h�h�h�h�h�h�j�  h�h�j�  �TP�j�  j�  �USD�j�  j�  j�  j  j  j  j  j  j  j�  j�  j�  j�  j  j  j  j  j  j  j  j  j  j  �
2024-09-17��
2025-01-06�h��
2024-11-05�h�h�h�h�h�h��
2024-09-17��
2025-01-06�h��
2024-11-05�h�h�h�h�h�h�h�h�h�h�]�(}�(j  �id:tora baml ht�j  �buyer�j  j  �ARRAY���R�u}�(j  �id:example name�j  �reportDetails.executingEntity�j  j  u}�(j  �id:example name�j  �seller�j  j�  u}�(j  �id:tora baml ht�j  �counterparty�j  j  u}�(j  �	clnt:nore�j  �1tradersAlgosWaiversIndicators.executionWithinFirm�j  j  u}�(j  �id:example name�j  �clientIdentifiers.client�j  j�  ue]�(}�(j  �id:example name�j  j�  j  j�  u}�(j  �id:example name�j  j�  j  j  u}�(j  �id:tora baml ht�j  j�  j  j�  u}�(j  �id:tora baml ht�j  j�  j  j  u}�(j  �	clnt:nore�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  ue]�(}�(j  �id:tora ubs ht�j  j�  j  j�  u}�(j  �id:example name�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  u}�(j  �id:tora ubs ht�j  j�  j  j  u}�(j  �	clnt:nore�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  ue]�(}�(j  �id:example name�j  j�  j  j�  u}�(j  �id:example name�j  j�  j  j  u}�(j  �id:tora ubs ht�j  j�  j  j�  u}�(j  �id:tora ubs ht�j  j�  j  j  u}�(j  �	clnt:nore�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  ue]�(}�(j  �id:tora ubs lt�j  j�  j  j�  u}�(j  �id:example name�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  u}�(j  �id:tora ubs lt�j  j�  j  j  u}�(j  �	clnt:nore�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  ue]�(}�(j  �id:tora ubs lt�j  j�  j  j�  u}�(j  �id:example name�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  u}�(j  �id:tora ubs lt�j  j�  j  j  u}�(j  �	clnt:nore�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  ue]�(}�(j  �id:tora ubs lt�j  j�  j  j�  u}�(j  �id:example name�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  u}�(j  �id:tora ubs lt�j  j�  j  j  u}�(j  �	clnt:nore�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  ue]�(}�(j  �id:tora ubs lt�j  j�  j  j�  u}�(j  �id:example name�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  u}�(j  �id:tora ubs lt�j  j�  j  j  u}�(j  �	clnt:nore�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  ue]�(}�(j  �id:zz-intrltrf�j  j�  j  j�  u}�(j  �id:example name�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  u}�(j  �id:zz-intrltrf�j  j�  j  j  u}�(j  �	clnt:nore�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  ue]�(}�(j  �id:zz-intrltrf�j  j�  j  j�  u}�(j  �id:example name�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  u}�(j  �id:zz-intrltrf�j  j�  j  j  u}�(j  �	clnt:nore�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  ue]�(}�(j  �id:tora baml ht�j  j�  j  j�  u}�(j  �id:example name�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  u}�(j  �id:tora baml ht�j  j�  j  j  u}�(j  �	clnt:nore�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  ue]�(}�(j  �id:example name�j  j�  j  j�  u}�(j  �id:example name�j  j�  j  j  u}�(j  �id:tora baml ht�j  j�  j  j�  u}�(j  �id:tora baml ht�j  j�  j  j  u}�(j  �	clnt:nore�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  ue]�(}�(j  �id:tora ubs ht�j  j�  j  j�  u}�(j  �id:example name�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  u}�(j  �id:tora ubs ht�j  j�  j  j  u}�(j  �	clnt:nore�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  ue]�(}�(j  �id:example name�j  j�  j  j�  u}�(j  �id:example name�j  j�  j  j  u}�(j  �id:tora ubs ht�j  j�  j  j�  u}�(j  �id:tora ubs ht�j  j�  j  j  u}�(j  �	clnt:nore�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  ue]�(}�(j  �id:tora ubs lt�j  j�  j  j�  u}�(j  �id:example name�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  u}�(j  �id:tora ubs lt�j  j�  j  j  u}�(j  �	clnt:nore�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  ue]�(}�(j  �id:tora ubs lt�j  j�  j  j�  u}�(j  �id:example name�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  u}�(j  �id:tora ubs lt�j  j�  j  j  u}�(j  �	clnt:nore�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  ue]�(}�(j  �id:tora ubs lt�j  j�  j  j�  u}�(j  �id:example name�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  u}�(j  �id:tora ubs lt�j  j�  j  j  u}�(j  �	clnt:nore�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  ue]�(}�(j  �id:tora ubs lt�j  j�  j  j�  u}�(j  �id:example name�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  u}�(j  �id:tora ubs lt�j  j�  j  j  u}�(j  �	clnt:nore�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  ue]�(}�(j  �id:zz-intrltrf�j  j�  j  j�  u}�(j  �id:example name�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  u}�(j  �id:zz-intrltrf�j  j�  j  j  u}�(j  �	clnt:nore�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  ue]�(}�(j  �id:zz-intrltrf�j  j�  j  j�  u}�(j  �id:example name�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  u}�(j  �id:zz-intrltrf�j  j�  j  j  u}�(j  �	clnt:nore�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  ue]�(}�(j  �id:example name�j  j�  j  j�  u}�(j  �id:example name�j  j�  j  j  u}�(j  �id:tora ubs lt�j  j�  j  j�  u}�(j  �multi�j  j�  j  j  u}�(j  �	clnt:nore�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  ue]�(}�(j  �id:example name�j  j�  j  j�  u}�(j  �id:example name�j  j�  j  j  u}�(j  �id:tora ubs lt�j  j�  j  j�  u}�(j  �multi�j  j�  j  j  u}�(j  �	clnt:nore�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  ue]�(}�(j  �id:example name�j  j�  j  j�  u}�(j  �id:example name�j  j�  j  j  u}�(j  �id:tora ubs lt�j  j�  j  j�  u}�(j  �multi�j  j�  j  j  u}�(j  �	clnt:nore�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  ue]�(}�(j  �id:example name�j  j�  j  j�  u}�(j  �id:example name�j  j�  j  j  u}�(j  �id:tora ubs lt�j  j�  j  j�  u}�(j  �multi�j  j�  j  j  u}�(j  �	clnt:nore�j  j�  j  j  u}�(j  �id:example name�j  j�  j  j�  ue�id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:tora baml ht��id:example name��id:tora ubs ht��id:example name��id:tora ubs lt��id:tora ubs lt��id:tora ubs lt��id:tora ubs lt��id:zz-intrltrf��id:zz-intrltrf��id:tora baml ht��id:example name��id:tora ubs ht��id:example name��id:tora ubs lt��id:tora ubs lt��id:tora ubs lt��id:tora ubs lt��id:zz-intrltrf��id:zz-intrltrf��id:example name��id:example name��id:example name��id:example name��id:example name��id:tora baml ht��id:example name��id:tora ubs ht��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:tora baml ht��id:example name��id:tora ubs ht��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:tora ubs lt��id:tora ubs lt��id:tora ubs lt��id:tora ubs lt��id:tora baml ht��id:tora baml ht��id:tora ubs ht��id:tora ubs ht��id:tora ubs lt��id:tora ubs lt��id:tora ubs lt��id:tora ubs lt��id:zz-intrltrf��id:zz-intrltrf��id:tora baml ht��id:tora baml ht��id:tora ubs ht��id:tora ubs ht��id:tora ubs lt��id:tora ubs lt��id:tora ubs lt��id:tora ubs lt��id:zz-intrltrf��id:zz-intrltrf��multi��multi��multi��multi�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name��id:example name�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��tora baml ht��example name��tora ubs ht��example name��tora ubs lt��tora ubs lt��tora ubs lt��tora ubs lt��zz-intrltrf��zz-intrltrf��tora baml ht��example name��tora ubs ht��example name��tora ubs lt��tora ubs lt��tora ubs lt��tora ubs lt��zz-intrltrf��zz-intrltrf��example name��example name��example name��example name��example name��tora baml ht��example name��tora ubs ht��example name��example name��example name��example name��example name��example name��example name��tora baml ht��example name��tora ubs ht��example name��example name��example name��example name��example name��example name��tora ubs lt��tora ubs lt��tora ubs lt��tora ubs lt��tora baml ht��tora baml ht��tora ubs ht��tora ubs ht��tora ubs lt��tora ubs lt��tora ubs lt��tora ubs lt��zz-intrltrf��zz-intrltrf��tora baml ht��tora baml ht��tora ubs ht��tora ubs ht��tora ubs lt��tora ubs lt��tora ubs lt��tora ubs lt��zz-intrltrf��zz-intrltrf�j'  j(  j)  j*  �example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name��example name�]�(j  j   j�  j�  j�  j�  j�  j�  e]�(j#  j%  j�  j�  j�  j�  j�  j�  e]�(j(  j�  j�  j�  j�  j�  j�  e]�(j+  j-  j�  j�  j�  j�  j�  j�  e]�(j0  j2  j4  j�  j�  j�  j�  j�  j�  e]�(j7  j9  j;  j�  j�  j�  j�  j�  j�  e]�(j>  j@  jB  j�  j�  j�  j�  j�  j�  e]�(jE  jG  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j   j  j  j  e]�(j	  j  j
  j  j  j  e]�(jJ  jL  j  j  j  j  j  j   e]�(jO  jQ  j#  j%  j'  j)  j+  j-  e]�(jT  j0  j2  j4  j6  j8  j:  e]�(jW  jY  j=  j?  jA  jC  jE  jG  e]�(j\  j]  j_  jJ  jL  jN  jP  jR  jT  e]�(ja  jb  jd  jW  jY  j[  j]  j_  ja  e]�(jf  jg  ji  jd  jf  jh  jj  jl  jn  e]�(jk  jm  jq  js  ju  jw  jy  j{  e]�(j~  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  e]�(jo  jq  j�  j�  j�  j�  j�  j�  e]�(js  j�  j�  j�  j�  j�  j�  e]�(jv  jx  j�  j�  j�  j�  j�  j�  e]�(jz  j�  j�  j�  j�  j�  j�  e������������������������j}  j}  j~  j}  h�h�h�j  j�  j�  j}  j}  j~  j}  h�h�h�j  j�  j�  j  j  j  j  j�  j�  j�  j�  j  j  j  j  j  j  j�  j�  j�  j�  j  j  j  j  j  j  j  j  j  j  j  j$  j)  j,  j1  j8  j?  jF  h�h�jK  jP  jU  jX  j1  j8  j?  jl  h�h�jp  jt  jw  jt  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      h�h�h�h�et�b�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h!�]�(�AOTC�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�b�_dtype�j�  �StringDtype���)��ubj�  )��}�(j�  hhK ��h��R�(KK��h!�]�(�	660958439��	660958470��	662471621��	662471610��	662450097��	662450061��	662452320��	662515880��	662367560��	662367556�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �	662314419��	662468880�j�  j�  et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��h!�]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��h!�]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bj�  j�  )��ubh�(�@      ��BQ�bA��0)cA8gԍO?AvT5	�b?A     d�@     �@    ��A      $@     �`@      4@��BQ�bA��0)cA8gԍO?AvT5	�b?A     d�@     �@    ��A      $@     �`@      4@      .@       @      .@       @�V�/�b@�	��a@�+��y0@TrN�@��|Ћ��@�
�0���@�:M���@     ��@                �V�/�b@�	��a@�+��y0@TrN�@��|Ћ��@�
�0���@�:M���@     ��@                    ���@    �W�@    ���@    �W�@��BQ�bA��0)cA8gԍO?AvT5	�b?A     d�@     �@    ��A      $@     �`@      4@��BQ�bA��0)cA8gԍO?AvT5	�b?A     d�@     �@    ��A      $@     �`@      4@      .@       @      .@       @�h�f8�����R�(Kh�NNNJ����J����K t�bKK��h�t�R�h�(��                                                  	       
                                                                      #       (       )                     "       *       �h�KK��h�t�R�j�  )��}�(j�  hhK ��h��R�(KK��h!�]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��h!�]�(h�h�h�h�j5  j<  jC  jH  h�h�h�h�h�h�j5  j<  jC  jH  h�h�jH  jt  jH  jt  et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��h!�]�(h�h�h�h�j1  j8  j?  h�h�h�h�h�h�h�j1  j8  j?  h�h�h�h�h�h�h�et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�h�h��CALL�j6  h�h�h�h�h�h�h�h�j6  j6  h�h�h�h�et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�h��JP9010C00002�j@  j@  h�h�h�h�h�h�h�j@  j@  j@  j@  �JP9010100007�j@  jA  et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��h!�]�(h�h�h�h�j
  j
  j
  j  j  j  h�h�h�h�j
  j
  j
  j  j  j  j  j  j  j  et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��h!�]�(�	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore��	clnt:nore�et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��h!�]�(jT  jU  jV  jW  jX  jY  jZ  j[  j\  j]  j^  j_  j`  ja  jb  jc  jd  je  jf  jg  jh  ji  jj  jk  et�bj�  j�  )��ubh�(�0                               �h�b1�����R�(Kh"NNNJ����J����K t�bKK��h�t�R�j�  )��}�(j�  hhK ��h��R�(KK��h!�]�(h�h�h�h�j
  j
  j
  j  j  j  h�h�h�h�j
  j
  j
  j  j  j  j  j  j  j  et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��h!�]�(�FX Forward USD/JPY 2024-09-17��FX Forward USD/JPY 2025-01-06��FX Spot USD/HKD 2024-09-05��FX Forward USD/HKD 2024-11-05�j1  j8  j?  �NIKKEI 225 SEP24��NKY 09/13/24 C38500 Index��NKY 09/13/24 C39500 Index�j�  j�  j�  j�  j1  j8  j?  j�  j�  j�  j�  �
TPU4 Index�j�  j�  et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��h!�]�(h�h�h�h��4704.T��5726.T��8698.T��SSIU4��JNI385I4.OS��JNI395I4.OS�h�h�h�h�j�  j�  j�  j�  j�  j�  j�  �JTIU4�j�  j�  et�bj�  j�  )��ubj�  )��}�(j�  hhK ��h��R�(KK��h!�]�(�JFTXFP�j�  h�j�  h�h�h��FFICSX��OCEICS�j�  j�  j�  h�j�  h�h�h�j�  j�  j�  j�  j�  j�  j�  et�bj�  j�  )��ube]�(h
h}�(hhhK ��h��R�(KKJ��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h2h3h6h7h9h:h;h<h=h?hAhBhChDhEhFhGhHhIhJhKhMhNhPhQhRhThUhVhWhXhYh]h`hahbhchdhehfhghhhihjhkhmhnhohphqhrhshthuhvhxhyh{h|h}hh�et�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h1at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h>hLhOet�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h@at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hSat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hZat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h[at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h\at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h^at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h_at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hlat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hwat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hzh�et�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h~at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs�h�(�P                                                                      	       
              
                                                                                                          !       "       #       $       %       &       (       )       +       ,       -       /       0       1       2       3       4       8       ;       <       =       >       ?       @       A       B       C       D       E       F       H       I       J       K       L       M       N       O       P       Q       S       T       V       W       X       Z       _       �h�KJ��h�t�R�u}�(jq  j�  jr  �builtins��slice���KK
K��R�u}�(jq  j�  jr  jz  KKK��R�u}�(jq  j�  jr  jz  KKK��R�u}�(jq  j�  jr  jz  KKK��R�u}�(jq  j  jr  h�(�              '       *       �h�K��h�t�R�u}�(jq  j  jr  jz  KKK��R�u}�(jq  j  jr  jz  K.K/K��R�u}�(jq  j  jr  jz  K5K6K��R�u}�(jq  j&  jr  jz  K6K7K��R�u}�(jq  j/  jr  jz  K7K8K��R�u}�(jq  j9  jr  jz  K9K:K��R�u}�(jq  jD  jr  jz  K:K;K��R�u}�(jq  jM  jr  jz  KGKHK��R�u}�(jq  jn  jr  jz  KRKSK��R�u}�(jq  j~  jr  jz  KUKgK	��R�u}�(jq  j  jr  jz  KYKZK��R�u}�(jq  j�  jr  jz  K[K\K��R�u}�(jq  j�  jr  jz  K\K]K��R�u}�(jq  j�  jr  jz  K]K^K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.