���U      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK���h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�OR<PERSON>RID��
PARENTORDERID��	ORDERDAT<PERSON>��ORDERTIMESTAMP��	EVENTTY<PERSON><PERSON><PERSON><PERSON><PERSON>ENTI<PERSON>NTIFICATIONCODE��DESCRIPTION��	ORDERSIDE��ORDERTOTALQUANTITY��QUANTITYTYPE��EXECUTIONORDERTYPE��ORDERLIMITPRICE��CURRENCY��ORDERTIMEINFORCE��ORDERSTATUSTEXT��ORDERREFERENCE��!EXECUTIONENTITYIDENTIFICATIONCODE��TRADER��BUYERIDENTIFICATIONCODE��SELLERIDENTIFICATIONCODE��ORDEREXECUTIONDESTINATION��LASTQTY��LASTPX��
EXECUTIONDATE��
EXECUTIONTIME��EXECID��ISIN��SED<PERSON>��CUSIP��<PERSON><PERSON><PERSON><PERSON>OWKEY��TICKER��RIC��EXCHANGESHORTNAME��CFI��REPORTSTATUS��
OPTIONTYPE��COUNTRYOFTHEBRANCHFORTHEB<PERSON><PERSON><PERSON>��<PERSON><PERSON><PERSON><PERSON><PERSON>RRE<PERSON><PERSON>INGQUANTITY��TERMOFUNDERLYINGINDEX-VA<PERSON>UE��<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>CATIONCODETYPE��@COUNTRYOFTHEBRANCHSUPERVISINGTHEPERSONRESPONSIBLEFORTHEEXECUTION��OTCPOST-TRADEINDICATOR��SELLER-SURNAME(S)��NOTIONALCURRENCY1��LASTFILLTIME��BUYER-FIRSTNAME(S)��<PERSON><PERSON><PERSON><PERSON>CISIONMAKER-SURNAME(S)��TRANSMISSIONOFORDERINDICATOR��FUTUREBLOOMBERGROOT��COMMISSIONTYPE��WAIVEINDICATOR��COMMISSIONS��SENDTOSTEELEYE��
EXPIRYDATE�� SELLERDECISIONMAKER-FIRSTNAME(S)��COUNTERPARTY��BUYERNPCODE��LENAME��COUNTRYOFTHEBRANCHFORTHESELLER��COMMODITYDERIVATIVEINDICATOR��INVESTMENTDECISIONWITHFIRM��SELLER-DATEOFBIRTH��'SECURITIESFINANCINGTRANSACTIONINDICATOR��TRANSACTIONREFERENCENUMBER��BUYER-DATEOFBIRTH��COUNTRYOFBRANCHMEMBERSHIP��SELLERIDENTIFICATIONCODETYPE��PRICE��LIFECYCLEEVENT��STRIKEPRICECURRENCY��SELLERDECISIONMAKERCODETYPE��OPTIONEXERCISESTYLE��-TRANSMITTINGFIRMIDENTIFICATIONCODEFORTHEBUYER��FILLER/NETAMOUNT��BUYERDECISIONMAKER-SURNAME(S)��UP-FRONTPAYMENT��EXECUTIONWITHINFIRM��FUTUREEXPIRATIONDATE��	NETAMOUNT��BUYERDECISIONMAKERNPCODE��UNDERLYINGINDEXNAME��EXCHANGEMICCODE��BUYERDECISIONMAKER-DATEOFBIRTH��SHORTSELLINGINDICATOR��TRADINGDATETIME��TRADINGCAPACITY��INVESTMENTDECISIONWITHFIRM-TYPE��BUYERDECISIONMAKERCODETYPE��"INVESTMENTDECISIONWITHINFIRMNPCODE��DCOUNTRYOFTHEBRANCHRESPONSIBLEFORTHEPERSONMAKINGTHEINVESTMENTDECISION��BUYER-SURNAME(S)��STRIKEPRICETYPE��PORTFOLIOMANAGER��UNDERLYINGINSTRUMENTCODE��)TRADINGVENUETRANSACTIONIDENTIFICATIONCODE��BRANCHLOCATION��QUANTITY��OPTIONCONTRACTBLOOMBERGROOTCODE��VENUE��SENDTOSTEELEYEELIGIBLE��#DERIVATIVENOTIONALINCREASE/DECREASE��ORDERSTATUS��EXECUTIONWITHINFIRM-TYPE��QUANTITYCURRENCY��SELLERDECISIONMAKERCODE��COMPLEXTRADECOMPONENTID��NOTIONALCURRENCY2��SELLERDECISIONMAKER-DATEOFBIRTH��TRANSACTIONTYPE��EXECUTIONWITHINFIRMNPCODE��
PRICECURRENCY��	CURRENCY2��	ORDERTYPE��ORDERCUMULATIVEQUANTITY��OPTIONSTRIKE��INSTRUCTIONS��MATURITYDATE��OPTIONEXPIRATIONDATE��
TRADECANCELED��SELLER-FIRSTNAME(S)��UP-FRONTPAYMENTCURRENCY��BUYERDECISIONMAKERCODE��DELIVERYTYPE��PRICEMULTIPLIER��STRIKEPRICE��"INVESTMENTFIRMCONVEREDBY2014/65/EU��SELLERDECISIONMAKERNPCODE��
PRICE-TYPE��INSTRUMENTFULLNAME��BUYERDECISIONMAKER-FIRSTNAME(S)��SELLERNPCODE��.TRANSMITTINGFIRMIDENTIFICATIONCODEFORTHESELLER��ORDERREMAININGQUANTITY��INSTRUMENTCLASSIFICATION��ORDERSTOPPRICE��OSI��	EXECREFID��	PROGRAMID�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(h�numpy.core.numeric��_frombuffer���(�                             �h�i8�����R�(K�<�NNNJ����J����K t�bK���C�t�R�h��__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�pandas._libs.missing��NA���h�h�et�b�_dtype�hȌStringDtype���)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�17615542�j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�20220527�j#  j#  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�02:39:48.000�j-  j-  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�New�j7  j7  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�CA53680Q2071�jA  jA  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�LITHIUM AMERS ORD�jK  jK  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�sell�jU  jU  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�UNIT�j_  j_  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�Market�ji  �Limit�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�USD�jt  jt  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�GTC�j~  j~  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�3SOLD(LAC) 182 at 28.14(USD) for ORDER ID # 18161574�j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�2022052715394750517-616960�j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�2138002EE8DT72X2QN40�j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�
Tyson Appadoo�j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�	JEFF_BIDS�j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�INTC�j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�EMSX�j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�2022-05-27T13:40:49.000��2022-05-27T13:40:52.000��2022-05-27T13:40:53.000�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�13:40:49.000 +0000��13:40:52.000 +0000��13:40:53.000 +0000�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�	527313231��	527313397��	527313417�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�CA53680Q2071�j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�BF4X269�j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�	53680Q207�j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�
LAC US Equity�j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�kal��NKM24��LAC�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�LAC�j&  j&  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��hԉ]�(�NYSE (Q)�j0  j0  et�bh�h�)��ubh�(�8          x�&A    x�&A    x�&A                      @      Y@      Y@     �r@�G�z.<@�(\��5<@333333<@      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      ��h�f8�����R�(Kh�NNNJ����J����K t�bK
K��h�t�R�hhK ��h��R�(KKK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�17591573�jD  G�      �ESVUFR�jE  jE  h�h�h�h�h�hٌ!PROG-202310051659191672474-665764�h�jF  et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�hGat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hHat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hIat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hJat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hKat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hLat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hMat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hNat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hOat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hPat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hQat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hRat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hSat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hTat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hVat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hWat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hYat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hZat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h[at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h\at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h]at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h^at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h_at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h`at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�haat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hbat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hcat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hdat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�heat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hfat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hgat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hiat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hjat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hkat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hlat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hmat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hoat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hpat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hqat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hrat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hsat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�htat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�huat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hvat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hwat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hxat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hyat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hzat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h{at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h|at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h}at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h~at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h(at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h)at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h+at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h,at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h1at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h9at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h<at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h=at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h?at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h@at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hBat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hCat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hDat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hEat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK
��h!�]�(h-h0h:h;hXhhhnh�h�h�h�h�h�et�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h%hFhUh�h�et�bh�Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hˌmgr_locs��builtins��slice���K"K#K��R�u}�(j�	  h�j�	  j�	  K#K$K��R�u}�(j�	  h�j�	  j�	  K$K%K��R�u}�(j�	  h�j�	  j�	  K%K&K��R�u}�(j�	  h�j�	  j�	  K&K'K��R�u}�(j�	  j  j�	  j�	  K'K(K��R�u}�(j�	  j  j�	  j�	  K(K)K��R�u}�(j�	  j  j�	  j�	  K)K*K��R�u}�(j�	  j  j�	  j�	  K*K+K��R�u}�(j�	  j'  j�	  j�	  K+K,K��R�u}�(j�	  j0  j�	  j�	  K,K-K��R�u}�(j�	  j9  j�	  j�	  K-K.K��R�u}�(j�	  jB  j�	  j�	  K.K/K��R�u}�(j�	  jK  j�	  j�	  K/K0K��R�u}�(j�	  jT  j�	  j�	  K1K2K��R�u}�(j�	  j]  j�	  j�	  K2K3K��R�u}�(j�	  jf  j�	  j�	  K4K5K��R�u}�(j�	  jo  j�	  j�	  K5K6K��R�u}�(j�	  jx  j�	  j�	  K6K7K��R�u}�(j�	  j�  j�	  j�	  K7K8K��R�u}�(j�	  j�  j�	  j�	  K8K9K��R�u}�(j�	  j�  j�	  j�	  K9K:K��R�u}�(j�	  j�  j�	  j�	  K:K;K��R�u}�(j�	  j�  j�	  j�	  K;K<K��R�u}�(j�	  j�  j�	  j�	  K<K=K��R�u}�(j�	  j�  j�	  j�	  K=K>K��R�u}�(j�	  j�  j�	  j�	  K>K?K��R�u}�(j�	  j�  j�	  j�	  K?K@K��R�u}�(j�	  j�  j�	  j�	  K@KAK��R�u}�(j�	  j�  j�	  j�	  KAKBK��R�u}�(j�	  j�  j�	  j�	  KBKCK��R�u}�(j�	  j�  j�	  j�	  KDKEK��R�u}�(j�	  j�  j�	  j�	  KEKFK��R�u}�(j�	  j�  j�	  j�	  KFKGK��R�u}�(j�	  j  j�	  j�	  KGKHK��R�u}�(j�	  j  j�	  j�	  KHKIK��R�u}�(j�	  j  j�	  j�	  KJKKK��R�u}�(j�	  j#  j�	  j�	  KKKLK��R�u}�(j�	  j,  j�	  j�	  KLKMK��R�u}�(j�	  j5  j�	  j�	  KMKNK��R�u}�(j�	  j>  j�	  j�	  KNKOK��R�u}�(j�	  jG  j�	  j�	  KOKPK��R�u}�(j�	  jP  j�	  j�	  KPKQK��R�u}�(j�	  jY  j�	  j�	  KQKRK��R�u}�(j�	  jb  j�	  j�	  KRKSK��R�u}�(j�	  jk  j�	  j�	  KSKTK��R�u}�(j�	  jt  j�	  j�	  KTKUK��R�u}�(j�	  j}  j�	  j�	  KUKVK��R�u}�(j�	  j�  j�	  j�	  KVKWK��R�u}�(j�	  j�  j�	  j�	  KWKXK��R�u}�(j�	  j�  j�	  j�	  KXKYK��R�u}�(j�	  j�  j�	  j�	  KYKZK��R�u}�(j�	  j�  j�	  j�	  KZK[K��R�u}�(j�	  j�  j�	  j�	  K[K\K��R�u}�(j�	  j�  j�	  j�	  K\K]K��R�u}�(j�	  j�  j�	  j�	  K]K^K��R�u}�(j�	  j�  j�	  j�	  K^K_K��R�u}�(j�	  j�  j�	  j�	  K_K`K��R�u}�(j�	  j�  j�	  j�	  KaKbK��R�u}�(j�	  j�  j�	  j�	  KbKcK��R�u}�(j�	  j�  j�	  j�	  KcKdK��R�u}�(j�	  j�  j�	  j�	  KdKeK��R�u}�(j�	  j  j�	  j�	  KeKfK��R�u}�(j�	  j
  j�	  j�	  KfKgK��R�u}�(j�	  j  j�	  j�	  KgKhK��R�u}�(j�	  j  j�	  j�	  KhKiK��R�u}�(j�	  j(  j�	  j�	  KiKjK��R�u}�(j�	  j1  j�	  j�	  KjKkK��R�u}�(j�	  j:  j�	  j�	  KkKlK��R�u}�(j�	  jC  j�	  j�	  KlKmK��R�u}�(j�	  jL  j�	  j�	  KmKnK��R�u}�(j�	  jU  j�	  j�	  KnKoK��R�u}�(j�	  j^  j�	  j�	  KoKpK��R�u}�(j�	  jg  j�	  j�	  KpKqK��R�u}�(j�	  jp  j�	  j�	  KqKrK��R�u}�(j�	  jy  j�	  j�	  KsKtK��R�u}�(j�	  j�  j�	  j�	  KtKuK��R�u}�(j�	  j�  j�	  j�	  KuKvK��R�u}�(j�	  j�  j�	  j�	  KvKwK��R�u}�(j�	  j�  j�	  j�	  KwKxK��R�u}�(j�	  j�  j�	  j�	  KxKyK��R�u}�(j�	  j�  j�	  j�	  KyKzK��R�u}�(j�	  j�  j�	  j�	  KzK{K��R�u}�(j�	  j�  j�	  j�	  K}K~K��R�u}�(j�	  j�  j�	  j�	  K~KK��R�u}�(j�	  j�  j�	  j�	  KK�K��R�u}�(j�	  j�  j�	  j�	  K�K�K��R�u}�(j�	  j�  j�	  j�	  K�K�K��R�u}�(j�	  j�  j�	  j�	  K�K�K��R�u}�(j�	  j�  j�	  j�	  K�K�K��R�u}�(j�	  j   j�	  j�	  K�K�K��R�u}�(j�	  j	  j�	  j�	  K�K�K��R�u}�(j�	  j  j�	  j�	  KKK��R�u}�(j�	  j  j�	  j�	  KKK��R�u}�(j�	  j&  j�	  j�	  KKK��R�u}�(j�	  j0  j�	  j�	  KKK��R�u}�(j�	  j:  j�	  j�	  KKK��R�u}�(j�	  jD  j�	  j�	  KKK��R�u}�(j�	  jN  j�	  j�	  KKK��R�u}�(j�	  jX  j�	  j�	  K	K
K��R�u}�(j�	  jb  j�	  j�	  K
KK��R�u}�(j�	  jm  j�	  j�	  KK
K��R�u}�(j�	  jw  j�	  j�	  K
KK��R�u}�(j�	  j�  j�	  j�	  KKK��R�u}�(j�	  j�  j�	  j�	  KKK��R�u}�(j�	  j�  j�	  j�	  KKK��R�u}�(j�	  j�  j�	  j�	  KKK��R�u}�(j�	  j�  j�	  j�	  KKK��R�u}�(j�	  j�  j�	  j�	  KKK��R�u}�(j�	  j�  j�	  j�	  KKK��R�u}�(j�	  j�  j�	  j�	  KKK��R�u}�(j�	  j�  j�	  j�	  KKK��R�u}�(j�	  j�  j�	  j�	  KKK��R�u}�(j�	  j�  j�	  j�	  KKK��R�u}�(j�	  j�  j�	  j�	  KKK��R�u}�(j�	  j�  j�	  j�	  KKK��R�u}�(j�	  j	  j�	  j�	  KKK��R�u}�(j�	  j  j�	  j�	  KKK��R�u}�(j�	  j  j�	  j�	  KK K��R�u}�(j�	  j)  j�	  j�	  K K!K��R�u}�(j�	  j:  j�	  h�(�h                                   3       C       I       `       r       {       |       �       �       �h�K
��h�t�R�u}�(j�	  j=  j�	  h�(�(               !       0       �       �       �h�i8�����R�(Kh�NNNJ����J����K t�bK��h�t�R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.