��Z"      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKZ��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�buySell��__synth_parent.buySell��dataSourceName��date��!executionDetails.buySellIndicator��executionDetails.limitPrice��executionDetails.orderStatus��+__synth_parent.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo��&executionDetails.shortSellingIndicator��executionDetails.stopPrice�� executionDetails.tradingCapacity��executionDetails.validityPeriod��	hierarchy��id��__synth_parent.id��__synth_parent.__meta_model__��__meta_model__��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��orderIdentifiers.parentOrderId��!orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��priceFormingData.price��priceFormingData.tradedQuantity��reportDetails.transactionRefNo��sourceIndex��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��timestamps.tradingDateTime��3tradersAlgosWaiversIndicators.shortSellingIndicator��transactionDetails.basketId��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��transactionDetails.recordType��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��notional_currency_2_attribute��asset_class_attribute��bbg_figi_id_attribute��isin_attribute��option_type_attribute��option_strike_price_attribute��underlying_isin_attribute��venue_attribute��'underlying_symbol_expiry_code_attribute��underlying_symbol_attribute��currency_attribute��expiry_date_attribute��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��__fallback_buyer__��__fallback_seller__��__fallback_counterparty__��__fallback_client__��__fallback_buyer_dec_maker__��__fallback_seller_dec_maker__��__fallback_inv_dec_in_firm__��__fallback_trader__��__fallback_exec_within_firm__��__fallback_executing_entity__��marketIdentifiers��__asset_class__��__isntr_ids_currency__��	__venue__�� __instrument_unique_identifier__��__instrument_full_name__��__price_reference_ric__��__instrument_classification__��__created_through_fallback__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(h�numpy.core.numeric��_frombuffer���(�                                     �h�i8�����R�(K�<�NNNJ����J����K t�bK���C�t�R�h�Nu��R�e]�(hhK ��h��R�(KKQK��h!�]�(�2��1�h�h�h�h�h�h��Enfusion�h�h�h��
2022-09-23��
2022-08-29��
2022-08-29��
2022-08-29��SELL��BUYI�h�h��PARF�h��REME�h��NEWO�h�h�h��Limit�h�h�h���Order Status Text: SOLD(GCPL) 1 at 921.75(INR) for ORDER ID # 4691895, RIC: GOCP.NS, SEDOL: B1BDGY0, Description: GODREJ CONSUMER PRODUCTS ORD, __bbyellow_code__: GCPL IN Equity���Order Status Text: BOT(INMART) 2779 at 4399.65(INR) for ORDER ID # 4670637, RIC: INMR.NS, SEDOL: BKDX4P8, Description: INDIAMART INTERMESH ORD, __bbyellow_code__: INMART IN Equity���Order Status Text: BOT(INMART) 2779 at 4399.65(INR) for ORDER ID # 4670637, RIC: INMR.NS, SEDOL: BKDX4P8, Description: INDIAMART INTERMESH ORD, __bbyellow_code__: INMART IN Equity���Order Status Text: BOT(INMART) 2779 at 4399.65(INR) for ORDER ID # 4670637, RIC: INMR.NS, SEDOL: BKDX4P8, Description: INDIAMART INTERMESH ORD, __bbyellow_code__: INMART IN Equity��pandas._libs.missing��NA���h�h�h�h�h�h�h��AOTC�h�h�h�]��GTCV�a]�h�a]�h�a]�h�a�
Standalone�h�h�h��Order�h�h�h��
OrderState�h�h�h��202209201816526099-650225��202208261802563073-650225�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��2022-09-23T10:16:53.000000Z��2022-08-29T10:02:56.000000Z��2022-08-29T10:02:56.000000Z��2022-08-29T10:02:56.000000Z�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��INR�h�h�h��MONE�h�h�h�h�h�h�h�h�h�h�h��UNIT�h�h�h��Market Side�h�h�h�h�h�h�h�h�h�h�h��XNSE�h�h�h�h�h�h�h�]�(}�(�labelId��INE102D01028��path��instrumentDetails.instrument��type��se_elastic_schema.static.market��IdentifierType����OBJECT���R�u}�(hƌINE102D01028INRXNSE�h�h�h�h�u}�(hƌGODREJCP�h�h�h�h�ue]�(}�(hƌINE933S01016�h�h�h�h�u}�(hƌINE933S01016INRXNSE�h�h�h�h�u}�(hƌ	INDIAMART�h�h�h�h�ue]�(}�(h�h�h�h�h�h�u}�(hƌINE933S01016INRXNSE�h�h�h�h�u}�(h�h�h�h�h�h�ue]�(}�(h�h�h�h�h�h�u}�(hƌINE933S01016INRXNSE�h�h�h�h�u}�(h�h�h�h�h�h�ueh�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�]�(}�(hƌid:emsx�hȌbuyer�h�h͌ARRAY���R�u}�(hƌlei:2549002kvu12t4k1kl65�hȌreportDetails.executingEntity�h�h�u}�(hƌlei:2549002kvu12t4k1kl65�hȌseller�h�h�u}�(hƌid:emsx�hȌcounterparty�h�h�u}�(hƌid:emsx�hȌclientIdentifiers.client�h�h�u}�(hƌid:goh chaimei�hȌtrader�h�h�ue]�(}�(hƌlei:2549002kvu12t4k1kl65�h�h�h�h�u}�(hƌlei:2549002kvu12t4k1kl65�h�h�h�h�u}�(hƌid:emsx�h�h�h�h�u}�(hƌid:emsx�h�h�h�h�u}�(hƌid:emsx�h�h�h�h�u}�(hƌid:goh chaimei�h�h�h�h�ue]�(}�(hƌlei:2549002kvu12t4k1kl65�h�h�h�h�u}�(hƌlei:2549002kvu12t4k1kl65�h�h�h�h�u}�(hƌid:emsx�h�h�h�h�u}�(hƌid:emsx�h�h�h�h�u}�(hƌid:emsx�h�h�h�h�u}�(hƌid:goh chaimei�h�h�h�h�ue]�(}�(hƌlei:2549002kvu12t4k1kl65�h�h�h�h�u}�(hƌlei:2549002kvu12t4k1kl65�h�h�h�h�u}�(hƌid:emsx�h�h�h�h�u}�(hƌid:emsx�h�h�h�h�u}�(hƌid:emsx�h�h�h�h�u}�(hƌid:goh chaimei�h�h�h�h�ue�lei:2549002kvu12t4k1kl65��lei:2549002kvu12t4k1kl65��lei:2549002kvu12t4k1kl65��lei:2549002kvu12t4k1kl65��id:emsx��lei:2549002kvu12t4k1kl65��lei:2549002kvu12t4k1kl65��lei:2549002kvu12t4k1kl65��lei:2549002kvu12t4k1kl65��id:emsx��id:emsx��id:emsx��id:emsx��id:emsx��id:emsx��id:emsx�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��id:emsx��id:emsx��id:emsx��id:emsx��id:goh chaimei��id:goh chaimei��id:goh chaimei��id:goh chaimei��emsx��2549002kvu12t4k1kl65��2549002kvu12t4k1kl65��2549002kvu12t4k1kl65��2549002kvu12t4k1kl65��emsx��emsx��emsx��emsx��emsx��emsx��emsx��emsx��emsx��emsx��emsx�h�h�h�h�h�h�h�h�h�h�h�h��goh chaimei��goh chaimei��goh chaimei��goh chaimei�h�h�h�h��2549002kvu12t4k1kl65��2549002kvu12t4k1kl65��2549002kvu12t4k1kl65��2549002kvu12t4k1kl65�]�(h�h�h�h�h�h�h�h�h�e]�(h�h�h�h�h�j  j  j  j  e]�(h�h�h�j
  j  j  j  j  j  e]�(h�h�h�j  j  j  j  j  j!  eh�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h׌GOCP.NS��INMR.NS�jX  jX  et�bh�(��            ��@     0�@     0�@     0�@    `
 A    ��@    ��@    ��@      �      �      �      �      �      �      �      ��h�f8�����R�(Kh�NNNJ����J����K t�bKK��h�t�R�h�(��       ͂1    ��'    ��'    ��'    ͂1    ��'    ��'    ��'    ͂1    ��'    ��'    ��'                                 �h�KK��h�t�R�h�(�       �h�b1�����R�(Kh"NNNJ����J����K t�bKK��h�t�R�e]�(h
h}�(hhhK ��h��R�(KKQ��h!�]�(h%h&h'h(h)h+h,h-h.h/h0h1h2h3h6h7h8h:h;h=h>h?hAhBhChDhEhFhGhHhIhJhKhLhMhNhOhPhQhRhShThUhVhWhXhYh[h\h]h^h_h`hahbhchdhehfhghhhihjhkhlhmhnhohphqhrhshthuhvhwhxhyhzh{h|et�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h*h<hZh}et�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h4h5h9h@et�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h~at�bh�Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs�h�(��                                                               	       
                     
                                                                                                          !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       3       4       6       7       8       9       :       ;       <       =       >       ?       @       A       B       C       D       E       F       G       H       I       J       K       L       M       N       O       P       Q       R       S       T       U       V       W       �h�KQ��h�t�R�u}�(j�  ja  j�  h�(�                      5       X       �h�K��h�t�R�u}�(j�  je  j�  h�(�                                    �h�K��h�t�R�u}�(j�  jm  j�  �builtins��slice���KYKZK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.