��#      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK_��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��executionDetails.limitPrice��(_orderState.executionDetails.orderStatus��#_order.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo��&executionDetails.shortSellingIndicator��executionDetails.stopPrice�� executionDetails.tradingCapacity��executionDetails.validityPeriod��	hierarchy��_orderState.id��	_order.id��_order.__meta_model__��_orderState.__meta_model__��orderIdentifiers.orderIdCode��orderIdentifiers.parentOrderId��!orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��priceFormingData.price��"priceFormingData.remainingQuantity��+_orderState.priceFormingData.tradedQuantity��reportDetails.transactionRefNo��sourceIndex��	sourceKey��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��timestamps.tradingDateTime��3tradersAlgosWaiversIndicators.shortSellingIndicator��#transactionDetails.buySellIndicator��#transactionDetails.commissionAmount��+transactionDetails.commissionAmountCurrency��'transactionDetails.commissionAmountType��*transactionDetails.complexTradeComponentId��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��'_orderState.transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��transactionDetails.recordType��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��notional_currency_2_attribute��asset_class_attribute��bbg_figi_id_attribute��isin_attribute��option_type_attribute��option_strike_price_attribute��underlying_isin_attribute��venue_attribute��'underlying_symbol_expiry_code_attribute��underlying_symbol_attribute��currency_attribute��expiry_date_attribute��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��__fallback_buyer__��__fallback_seller__��__fallback_counterparty__��__fallback_client__��__fallback_buyer_dec_maker__��__fallback_seller_dec_maker__��__fallback_inv_dec_in_firm__��__fallback_trader__��__fallback_exec_within_firm__��__fallback_executing_entity__��marketIdentifiers��__newo_in_file__��__newo_cancel_indicator__��__asset_class__��__isntr_ids_currency__��	__venue__�� __instrument_unique_identifier__��__instrument_full_name__��__price_reference_ric__��__instrument_classification__��__created_through_fallback__�et�b�name�Nu��R�h
�pandas.core.indexes.range��
RangeIndex���}�(h�N�start�K �stop�K�step�Ku��R�e]�(�numpy.core.numeric��_frombuffer���(�          �h�b1�����R�(Kh"NNNJ����J����K t�bKK���C�t�R�h�(�0       ����Mbp?      �?����Mbp?      �      �      ��h�f8�����R�(K�<�NNNJ����J����K t�bKK��h�t�R�h�(�                             �h�i8�����R�(Kh�NNNJ����J����K t�bKK��h�t�R�hhK ��h��R�(KKZK��h!�]�(�2��1�h�h�h�h��Enfusion�h�h��
2022-05-27��pandas._libs.missing��NA����
2022-05-27��SELL��BUYI�h�h�h�h��PARF�h�h��NEWO�h�h��Market��Market�h��Order Type: Electronic�h��Order Type: Electronic�h�h�h�h�h�h��AOTC�h�h�]��GTCV�ah�]�h�a�
Standalone�h�hȌALLO|17591573|249581833��17591573��ALLO|17591573|249581833�h�h�h�h��Order�h��
OrderState�h�h�h�h�hˌ17591572�h�hΌ	249581833��	249618392�h�G@�2     G�      G@�2     G@ �	k��h�G@ �	k��G        h�G        G@�2     h�G@�2     h�h�hόz/Users/<USER>/PycharmProjects/swarm/swarm-tasks/tests/order/transformations/enfusion/v2/data/test_allocations.pkl�h�hь2022-05-27T13:41:25.000000Z�h��2022-05-27T13:41:25.000000Z��2022-05-27T13:41:25.000000Z�h��2022-05-27T13:41:25.000000Z�h�h�h�h�h�h�h�h�h�h�h�h��USD�h�h֌Amount per unit�h�h�h�h�h�G@ �	k��h�G@ �	k��h�h�h֌MONE�h�h�G@�2     h�G@�2     h�h�h��UNIT�h�hٌ
Allocation��Market Side�h�h�h�h�h�h�hՌXNYS��XCME��XOSE��XOFF�h�h�]�(}�(�labelId��US00835Q1031��path��instrumentDetails.instrument��type��se_elastic_schema.static.market��IdentifierType����OBJECT���R�u}�(h�US00835Q1031USDXNYS�h�h�h�h�u}�(h�NKM24�h�h�h�h�ue]�(}�(h�%XCMEUS78378X1072FF2022-06-17 00:00:00�h�h�h�h�u}�(h�"XCMEUS78378X1072FF2022-06 00:00:00�h�h�h�h�u}�(h�1224�h�h�h�h�ue]�(}�(h�h�h�h�h�h�u}�(h�US00835Q1031USDXOSE�h�h�h�h�u}�(h�h�h�h�h�h�ueh��USD�h�h��future�h�h�h�h�h�h�h�h�h�h�h��US78378X1072�h�h�h�h�h�h�h�h�h�h�h�h�h�h��
2022-06-17�h�]�(}�(h�id:emsx ubs lt�h�buyer�h�h�ARRAY���R�u}�(h�id:lei:sample_lei�h�reportDetails.executingEntity�h�h�u}�(h�lei:2138002ee8dt72x2qn40�h�seller�h�j  u}�(h�lei:sample_lei�h�sellerDecisionMaker�h�j  u}�(h�id:emsx ubs lt�h�counterparty�h�h�u}�(h�id:tyson appadoo�h�:tradersAlgosWaiversIndicators.investmentDecisionWithinFirm�h�h�u}�(h�	clnt:nore�h�1tradersAlgosWaiversIndicators.executionWithinFirm�h�h�u}�(h�id:fifthdelta master fund ltd�h�clientIdentifiers.client�h�j  u}�(h�id:tyson appadoo�h�trader�h�j  ue]�(}�(h�lei:2138002ee8dt72x2qn40�h�j  h�j  u}�(h�lei:sample_lei�h�buyerDecisionMaker�h�j  u}�(h�id:lei:sample_lei�h�j
  h�h�u}�(h�id:emsx ubs fut algo�h�j
  h�j  u}�(h�id:emsx ubs fut algo�h�j  h�h�u}�(h�	clnt:nore�h�j  h�h�u}�(h�id:fifthdelta master fund ltd�h�j  h�j  ue]�(}�(h�id:emsx ubs lt�h�j  h�j  u}�(h�id:lei:sample_lei�h�j
  h�h�u}�(h�lei:2138002ee8dt72x2qn40�h�j
  h�j  u}�(h�lei:sample_lei�h�j  h�j  u}�(h�id:emsx ubs lt�h�j  h�h�u}�(h�id:tyson appadoo�h�j  h�h�u}�(h�	clnt:nore�h�j  h�h�u}�(h�id:fifthdelta master fund ltd�h�j  h�j  u}�(h�id:tyson appadoo�h�j  h�j  ue�id:lei:sample_lei��id:lei:sample_lei��id:lei:sample_lei��id:emsx ubs lt��lei:2138002ee8dt72x2qn40��id:emsx ubs lt��lei:2138002ee8dt72x2qn40��id:emsx ubs fut algo��lei:2138002ee8dt72x2qn40��id:emsx ubs lt��id:emsx ubs fut algo��id:emsx ubs lt�h��lei:sample_lei�h��lei:sample_lei�h��lei:sample_lei��id:tyson appadoo�h��id:tyson appadoo��	clnt:nore��	clnt:nore��	clnt:nore��id:fifthdelta master fund ltd��id:fifthdelta master fund ltd��id:fifthdelta master fund ltd��id:tyson appadoo�h��id:tyson appadoo��emsx ubs lt��2138002ee8dt72x2qn40��emsx ubs lt��2138002ee8dt72x2qn40��emsx ubs fut algo��2138002ee8dt72x2qn40��emsx ubs lt��emsx ubs fut algo��emsx ubs lt��fifthdelta master fund ltd��fifthdelta master fund ltd��fifthdelta master fund ltd�h��
sample_lei�h��
sample_lei�h��
sample_lei��
tyson appadoo�h��
tyson appadoo��
tyson appadoo�h��
tyson appadoo�jT  jU  jV  �lei:sample_lei��lei:sample_lei��lei:sample_lei�]�(h�h�h�j  j  j  j  j  j  j  j  j  e]�(h�h�h�j!  j#  j&  j(  j*  j,  j.  e]�(h�h�h�j1  j3  j5  j7  j9  j;  j=  j?  jA  e���h�h�h�h�h�h�h�h�h�h�h�h�h�EMINI S&P JUN2��NKY 04/11/25 P36500 Index�h�h�h�G�      �FFICSX�G�      et�bh�(�       �h�KK��h�t�R�e]�(h
h}�(hhhK ��h��R�(KK��h!�]�hzat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hHh]et�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h@at�bh�Nu��R�h
h}�(hhhK ��h��R�(KKZ��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3h4h5h6h7h8h9h:h;h<h=h>h?hAhBhChDhEhFhGhIhJhKhLhMhNhOhPhQhRhShThUhVhWhXhYhZh[h\h^h_h`hahbhchdhehfhghhhihjhkhlhmhnhohphqhrhshthuhvhwhxhyh{h|h}h~hh�h�h�et�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs��builtins��slice���KUKVK��R�u}�(j�  h�j�  j�  K#KMK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  h�(��                                                                      	       
                     
                                                                                                                                      !       "       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       3       4       5       6       7       9       :       ;       <       =       >       ?       @       A       B       C       D       E       F       G       H       I       J       K       L       M       N       O       P       Q       R       S       T       V       W       X       Y       Z       [       \       ]       �h�KZ��h�t�R�u}�(j�  j|  j�  j�  K^K_K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.