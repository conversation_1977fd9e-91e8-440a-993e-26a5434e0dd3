��M+      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKZ��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�buySell��__synth_parent.buySell��dataSourceName��date��!executionDetails.buySellIndicator��executionDetails.limitPrice��executionDetails.orderStatus��+__synth_parent.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo��&executionDetails.shortSellingIndicator��executionDetails.stopPrice�� executionDetails.tradingCapacity��executionDetails.validityPeriod��	hierarchy��id��__synth_parent.id��__synth_parent.__meta_model__��__meta_model__��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��orderIdentifiers.parentOrderId��!orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��priceFormingData.price��priceFormingData.tradedQuantity��reportDetails.transactionRefNo��sourceIndex��	sourceKey��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��timestamps.tradingDateTime��3tradersAlgosWaiversIndicators.shortSellingIndicator��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��transactionDetails.recordType��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��notional_currency_2_attribute��asset_class_attribute��bbg_figi_id_attribute��isin_attribute��option_type_attribute��option_strike_price_attribute��underlying_isin_attribute��venue_attribute��'underlying_symbol_expiry_code_attribute��underlying_symbol_attribute��currency_attribute��expiry_date_attribute��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��__fallback_buyer__��__fallback_seller__��__fallback_counterparty__��__fallback_client__��__fallback_buyer_dec_maker__��__fallback_seller_dec_maker__��__fallback_inv_dec_in_firm__��__fallback_trader__��__fallback_exec_within_firm__��__fallback_executing_entity__��marketIdentifiers��__asset_class__��__isntr_ids_currency__��	__venue__�� __instrument_unique_identifier__��__instrument_full_name__��__price_reference_ric__��__instrument_classification__��__created_through_fallback__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(h�numpy.core.numeric��_frombuffer���(�                             �h�i8�����R�(K�<�NNNJ����J����K t�bK���C�t�R�h��__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h!�]�(�Market�h��Limit�et�b�_dtype�h��StringDtype���)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�2022052715394750517-616960�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�17615542�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�	527313231��	527313397��	527313417�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�pandas._libs.missing��NA���h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�kal��NKM24��LAC�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�CA53680Q2071�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�id:tyson appadoo��id:tyson appadoo��id:tyson appadoo�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�id:tyson appadoo��id:tyson appadoo��id:tyson appadoo�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�
tyson appadoo��
tyson appadoo��
tyson appadoo�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�
tyson appadoo��
tyson appadoo��
tyson appadoo�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(�LAC�jB  jB  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(h�h�h�et�bh�h�)��ubh�(��           x�&A    x�&A    x�&A�G�z.<@�(\��5<@333333<@      Y@      Y@     �r@�G�z.<@�(\��5<@333333<@      Y@      Y@     �r@      �      �      ��h�f8�����R�(Kh�NNNJ����J����K t�bKK��h�t�R�h�(�                             �h�KK��h�t�R�hhK ��h��R�(KKBK��h!�]�(�2�j_  j_  j_  j_  j_  �Enfusion�j`  j`  �
2022-05-27��
2022-05-27��
2022-05-27��SELL�jd  jd  h�h�G@      �PARF�je  je  �NEWO�jf  jf  ��Order Status Text: SOLD(LAC) 182 at 28.14(USD) for ORDER ID # 18161574, CUSIP: 53680Q207, RIC: LAC, SEDOL: BF4X269, Description: LITHIUM AMERS ORD, __bbyellow_code__: LAC US Equity���Order Status Text: SOLD(LAC) 182 at 28.14(USD) for ORDER ID # 18161574, CUSIP: 53680Q207, RIC: LAC, SEDOL: BF4X269, Description: LITHIUM AMERS ORD, __bbyellow_code__: LAC US Equity���Order Status Text: SOLD(LAC) 182 at 28.14(USD) for ORDER ID # 18161574, CUSIP: 53680Q207, RIC: LAC, SEDOL: BF4X269, Description: LITHIUM AMERS ORD, __bbyellow_code__: LAC US Equity�h�h�h�h�h�h�AOTC�jj  jj  ]��GTCV�a]�jl  a]�jl  a�
Standalone�jo  jo  �17591573�jp  h�jp  jp  h�Order�jq  jq  �
OrderState�jr  jr  jp  jp  h�l/Users/<USER>/Desktop/steeleye/swarm-tasks/tests/order/transformations/enfusion/v2/data/test_executions.pkl�js  js  �2022-05-27T02:39:48.000000Z��2022-05-27T02:39:48.000000Z��2022-05-27T02:39:48.000000Z��2022-05-27T13:40:49.000000Z��2022-05-27T13:40:52.000000Z��2022-05-27T13:40:53.000000Z�jt  ju  jv  jw  jx  jy  h�h�h�jd  jd  jd  �USD�jz  jz  �MONE�j{  j{  h�h�h�UNIT�j|  j|  �Market Side�j}  j}  jj  jj  jj  jw  jx  jy  �XOFF�j~  j~  ]�(}�(�labelId�h��path��instrumentDetails.instrument��type��se_elastic_schema.static.market��IdentifierType����OBJECT���R�u}�(j�  h�j�  j�  j�  j�  ue]�(}�(j�  h�j�  j�  j�  j�  u}�(j�  h�j�  j�  j�  j�  ue]�(}�(j�  h�j�  j�  j�  j�  u}�(j�  h�j�  j�  j�  j�  ueh�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�jz  jz  jz  h�h�h�]�(}�(j�  �id:emsx�j�  �buyer�j�  j�  �ARRAY���R�u}�(j�  �id:lei:sample_lei�j�  �reportDetails.executingEntity�j�  j�  u}�(j�  �lei:2138002ee8dt72x2qn40�j�  �seller�j�  j�  u}�(j�  �lei:sample_lei�j�  �sellerDecisionMaker�j�  j�  u}�(j�  �id:emsx�j�  �counterparty�j�  j�  u}�(j�  �id:tyson appadoo�j�  �:tradersAlgosWaiversIndicators.investmentDecisionWithinFirm�j�  j�  u}�(j�  �id:emsx�j�  �clientIdentifiers.client�j�  j�  u}�(j�  �id:tyson appadoo�j�  �trader�j�  j�  ue]�(}�(j�  �id:emsx�j�  j�  j�  j�  u}�(j�  �id:lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:2138002ee8dt72x2qn40�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:emsx�j�  j�  j�  j�  u}�(j�  �id:tyson appadoo�j�  j�  j�  j�  u}�(j�  �id:emsx�j�  j�  j�  j�  u}�(j�  �id:tyson appadoo�j�  j�  j�  j�  ue]�(}�(j�  �id:emsx�j�  j�  j�  j�  u}�(j�  �id:lei:sample_lei�j�  j�  j�  j�  u}�(j�  �lei:2138002ee8dt72x2qn40�j�  j�  j�  j�  u}�(j�  �lei:sample_lei�j�  j�  j�  j�  u}�(j�  �id:emsx�j�  j�  j�  j�  u}�(j�  �id:tyson appadoo�j�  j�  j�  j�  u}�(j�  �id:emsx�j�  j�  j�  j�  u}�(j�  �id:tyson appadoo�j�  j�  j�  j�  ue�id:lei:sample_lei��id:lei:sample_lei��id:lei:sample_lei��id:emsx��id:emsx��id:emsx��lei:2138002ee8dt72x2qn40��lei:2138002ee8dt72x2qn40��lei:2138002ee8dt72x2qn40��id:emsx��id:emsx��id:emsx�h�h�h�lei:sample_lei��lei:sample_lei��lei:sample_lei�h�h�h�id:emsx��id:emsx��id:emsx��emsx��emsx��emsx��2138002ee8dt72x2qn40��2138002ee8dt72x2qn40��2138002ee8dt72x2qn40��emsx��emsx��emsx��emsx��emsx��emsx�h�h�h�
sample_lei��
sample_lei��
sample_lei�h�h�h�lei:sample_lei��lei:sample_lei��lei:sample_lei�]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  eh�h�h�jz  jz  jz  h�h�h�h�h�h�et�bh�(�       �h�b1�����R�(Kh"NNNJ����J����K t�bKK��h�t�R�e]�(h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h:at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h?at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hHat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hRat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hWat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hXat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hhat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hkat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hrat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hsat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h{at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h|at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h}at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h<h=h>hIhLhZet�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h@at�bh�Nu��R�h
h}�(hhhK ��h��R�(KKB��h!�]�(h%h&h'h(h)h*h+h,h.h/h0h1h2h3h4h5h6h7h9hAhBhChDhEhFhGhJhKhMhNhOhPhQhShThUhVhYh[h\h]h^h_h`hahbhchdhehfhghihjhlhmhnhohphqhthuhvhwhxhyhzet�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h~at�bh�Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs��builtins��slice���KK	K��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  K#K$K��R�u}�(j�  h�j�  j�  K-K.K��R�u}�(j�  h�j�  j�  K2K3K��R�u}�(j�  h�j�  j�  K3K4K��R�u}�(j�  j  j�  j�  KCKDK��R�u}�(j�  j  j�  j�  KFKGK��R�u}�(j�  j  j�  j�  KMKNK��R�u}�(j�  j&  j�  j�  KNKOK��R�u}�(j�  j2  j�  j�  KVKWK��R�u}�(j�  j;  j�  j�  KWKXK��R�u}�(j�  jE  j�  j�  KXKYK��R�u}�(j�  jU  j�  h�(�0                            $       '       5       �h�i8�����R�(Kh�NNNJ����J����K t�bK��h�t�R�u}�(j�  jY  j�  j�  KKK��R�u}�(j�  j\  j�  h�(�                                                               	       
                     
                                                                                     !       "       %       &       (       )       *       +       ,       .       /       0       1       4       6       7       8       9       :       ;       <       =       >       ?       @       A       B       D       E       G       H       I       J       K       L       O       P       Q       R       S       T       U       �j�  KB��h�t�R�u}�(j�  j�  j�  j�  KYKZK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.