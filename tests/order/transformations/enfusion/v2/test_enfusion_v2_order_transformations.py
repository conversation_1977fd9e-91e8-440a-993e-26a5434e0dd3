import os
from pathlib import Path

import pandas as pd
import pytest
from prefect import context
from swarm.conf import SettingsCls
from swarm.task.auditor import Auditor

from swarm_tasks.order.feed.enfusion.v2.static import AlloMapKeys
from swarm_tasks.order.feed.enfusion.v2.static import ExecMapKeys
from swarm_tasks.order.transformations.order_transform_maps import (
    enfusion_v2_transform_map,
)

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
ALLOCATIONS_TEST_FILE_PATH = TEST_FILES_DIR.joinpath(r"test_allocations.pkl")
EXECUTIONS_TEST_FILE_PATH = TEST_FILES_DIR.joinpath(r"test_executions.pkl")
TEST_FILL_BY_FILL_FILE_PATH = TEST_FILES_DIR.joinpath(
    r"test_executions_fill_by_fill.pkl"
)
TEST_AGGREGATION_ALLOC_FILE_PATH = TEST_FILES_DIR.joinpath(
    r"test_aggregated_allocations.pkl"
)
ALLOCATIONS_EXPECTED_FILE_PATH = TEST_FILES_DIR.joinpath(r"expected_allocations.pkl")
EXECUTIONS_EXPECTED_FILE_PATH = TEST_FILES_DIR.joinpath(r"expected_executions.pkl")
EXPECTED_AGGREGATED_ALLOC_PATH = TEST_FILES_DIR.joinpath(
    r"expected_aggregated_alloc.pkl"
)
EXPECTED_FILL_BY_FILL_FILE_PATH = TEST_FILES_DIR.joinpath(r"expected_fill_by_fill.pkl")


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


@pytest.fixture()
def fill_by_fill_map() -> dict:
    return {
        ExecMapKeys.EARLIER_EXECUTION_DATE: {
            "20021965": "2022-09-21T09:57:52.000",
            "19368894": "2022-09-21T05:57:08.000",
        },
        ExecMapKeys.MEAN_PRICE: {
            "20021965": 4391.214132675666,
            "19368894": 4391.214132675666,
        },
        ExecMapKeys.TOTAL_QUANTITY: {"20021965": 2889.0, "19368894": 2889.0},
        ExecMapKeys.FIRST_EXEC_ID: {"20021965": "921125557", "19368894": "92134181"},
    }


@pytest.fixture()
def allocation_aggregation_map():
    return {
        AlloMapKeys.BUY_SELL: {
            "JP36574000022024-09-03": "1",
            "PHS SEP4 17900C2024-09-03": "1",
            "NIKKEI 225 SEP242024-09-03": "1",
            "TOPIX SEP42024-09-04": "1",
        },
        AlloMapKeys.EARLIER_TRADING_DATE_TIME: {
            "JP36574000022024-09-03": "2024-09-03T00:00:00.000000Z",
            "PHS SEP4 17900C2024-09-03": "2024-09-03T00:00:00.000000Z",
            "NIKKEI 225 SEP242024-09-03": "2024-09-03T00:00:00.000000Z",
            "TOPIX SEP42024-09-04": "2024-09-04T00:00:00.000000Z",
        },
        AlloMapKeys.FIRST_TRANSACTION_REF_NUMBER: {
            "JP36574000022024-09-03": "662324954",
            "PHS SEP4 17900C2024-09-03": "662367554",
            "NIKKEI 225 SEP242024-09-03": "662314419",
            "TOPIX SEP42024-09-04": "662468880",
        },
        AlloMapKeys.MEAN_PRICE: {
            "JP36574000022024-09-03": 1537.2313772893774,
            "PHS SEP4 17900C2024-09-03": 0.0,
            "NIKKEI 225 SEP242024-09-03": 38782.0,
            "TOPIX SEP42024-09-04": 2731.875,
        },
        AlloMapKeys.NET_QUANTITY: {
            "JP36574000022024-09-03": 0.0,
            "PHS SEP4 17900C2024-09-03": 0.0,
            "NIKKEI 225 SEP242024-09-03": 15.0,
            "TOPIX SEP42024-09-04": 8.0,
        },
    }


class TestEnfusionV2OrderTransformations:
    """
    Test EnfusionV2OrderTransformations
    """

    @pytest.mark.parametrize(
        "test_file_path, expected_file_path",
        [
            (ALLOCATIONS_TEST_FILE_PATH, ALLOCATIONS_EXPECTED_FILE_PATH),
            (EXECUTIONS_TEST_FILE_PATH, EXECUTIONS_EXPECTED_FILE_PATH),
        ],
    )
    def test_end_to_end_transformations(
        self, mocker, test_file_path, expected_file_path, auditor
    ):
        os.environ["SWARM_FILE_URL"] = str(test_file_path)

        # mock realm to chose default transformation
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "default.dev.steeleye.co"

        source_frame = pd.read_pickle(test_file_path)
        task = enfusion_v2_transform_map.transformation(tenant="foo")(
            source_frame=source_frame, logger=context.get("logger"), auditor=auditor
        )
        result = task.process()
        expected = pd.read_pickle(expected_file_path)

        pd.testing.assert_frame_equal(
            result.drop(["sourceKey"], axis=1),
            expected,
        )

    def test_end_to_end_transformations_with_fill_by_fill_false(
        self, mocker, auditor, fill_by_fill_map
    ):
        os.environ["SWARM_FILE_URL"] = str(TEST_FILL_BY_FILL_FILE_PATH)

        # mock realm to chose default transformation
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "default.dev.steeleye.co"

        source_frame = pd.read_pickle(TEST_FILL_BY_FILL_FILE_PATH)
        task = enfusion_v2_transform_map.transformation(tenant="foo")(
            source_frame=source_frame,
            fill_by_fill_map=fill_by_fill_map,
            fill_by_fill_flag=False,
            logger=context.get("logger"),
            auditor=auditor,
        )
        result = task.process()
        expected = pd.read_pickle(EXPECTED_FILL_BY_FILL_FILE_PATH)

        pd.testing.assert_frame_equal(
            result.drop(["sourceKey"], axis=1),
            expected,
        )

    def test_end_to_end_transformations_with_alloc_aggregation(
        self,
        mocker,
        auditor,
        allocation_aggregation_map,
    ):
        os.environ["SWARM_FILE_URL"] = str(TEST_AGGREGATION_ALLOC_FILE_PATH)

        # mock realm to chose default transformation
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "default.dev.steeleye.co"

        source_frame = pd.read_pickle(TEST_AGGREGATION_ALLOC_FILE_PATH)
        task = enfusion_v2_transform_map.transformation(tenant="foo")(
            source_frame=source_frame,
            allocation_aggregation_map=allocation_aggregation_map,
            logger=context.get("logger"),
            auditor=auditor,
        )
        result = task.process()
        expected = pd.read_pickle(EXPECTED_AGGREGATED_ALLOC_PATH)

        pd.testing.assert_frame_equal(result.drop(["sourceKey"], axis=1), expected)
