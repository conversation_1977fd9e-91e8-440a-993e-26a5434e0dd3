���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK5��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo�� executionDetails.routingStrategy�� executionDetails.tradingCapacity��executionDetails.validityPeriod��	hierarchy��	_order.id��_orderState.id��_order.isSynthetic��_order.__meta_model__��_orderState.__meta_model__��
orderClass��"orderIdentifiers.aggregatedOrderId��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��.orderIdentifiers.tradingVenueTransactionIdCode��!orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��"priceFormingData.remainingQuantity��+_orderState.priceFormingData.tradedQuantity��reportDetails.transactionRefNo��sourceIndex��	sourceKey��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��timestamps.tradingDateTime��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��!transactionDetails.positionEffect��transactionDetails.quantity��$_order.transactionDetails.recordType��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime��multiLegReportingType��
__symbol__��	__price__��__last_px__��__stop_px__��__newo_in_file__��	__buyer__��
__seller__��__executing_entity__��__execution_within_firm__��__investment_decision__��
__trader__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(h�numpy.core.numeric��_frombuffer���(�@                                                         	       �h�i8�����R�(K�<�NNNJ����J����K t�bK���C�t�R�h[Nu��R�e]�(hhK ��h��R�(KKK��h!�]�(�SELL�hwhwhw�BUYI�hxhwhwhwhwhwhwhxhxhwhw�ICE POF Exchange�hyhyhyhyhyhyhy�
2024-07-04��
2024-07-04��
2024-07-04��
2024-07-04��
2024-07-04��
2024-07-04��
2024-07-04��
2024-07-04�hwhwhwhwhxhxhwhw�NEWO�h�h�h�h�h�h�h��pandas._libs.missing��NA����REME�h��CAME��FILL�h�h�h��Limit�h�h�h�h�h�h�h��13885|18922-ERA��13885|18922-ERA��13885|18922-ERA��userKilled|13885|18922-ERA��K|13885|18922-ERA��0|13885|18922-ERA��K|13885|18922-ERA��13885|18922-ERA��<NA>�h�h�h��+Marex Spectron International Limited-Broker�h��-TP ICAP E&C Limited (Tullett Prebon) - Broker�h��AOTC�h�h�h�h�h�h�h�]��DAVY�ah�h�h�]��IOCV�ah�h�h��
Standalone�h�h�h�h�h�h�h��251645560|7461877��442533104|7461852��141912810|7163932��934363014|7460844��1194696|6914856��298079505|6581325��1165085|6947967��173025060|5941680�h�h�h�h�h�h�h�h�et�bhd(�         �h�b1�����R�(Kh"NNNJ����J����K t�bKK��hlt�R�hhK ��h��R�(KKK��h!�]�(�Order�h�h�h�h�h�h�h��
OrderState�h�h�h�h�h�h�h�h�h�h�h��Block Trade��
Regular Trade�h�h�h�h�h�h��1194697�h��1165086�h��99402692��99402621��79634379��99402563��1194696��1124606��1165085��1219857�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��251645560:1:Ack��442533104:2:Unfill��141912810:1:Ack��934363014:10:Unfill��1194698��1124610��1165088��173025060:2:Ack�et�bhd(�@             @      @      �?      I@      $@       @      .@       @�h�f8�����R�(KhiNNNJ����J����K t�bKK��hlt�R�hhK ��h��R�(KKK��h!�]�(G@      G@      G?�      G        G        G        G        G@       G        G        G        G        G@$      G?�      G@.      G        h�h�h�h�h�h�h�h�et�bhd(�@                                                         	       �hhKK��hlt�R�hhK ��h��R�(KKK��h!�]�(��s3://eracommodities.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/ash_party_test/0014175e3603b7cfc461c652b1a2cd866a697352a46a5d24fb76233eee7713cb_53741.fix���s3://eracommodities.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/ash_party_test/01e3e781b76567f0aafa8c15fde0fc566098b6bfacabcab4f41edeba87287f51_53821.fix���s3://eracommodities.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/ash_party_test/032e1f8e46b9b7a9fdcc106669d8755edcb23db71cbd13e597a348265aa76a50_53948.fix���s3://eracommodities.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/ash_party_test/037e15b7ffb3fedafee44d0871d077314dcd8a3d99a066498bce7e82178efef3_53854.fix���s3://eracommodities.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/ash_party_test/03acad4f34156117b7dd414b63a2139e58f8eb907f3131e44536ce85d59fb070_53653.fix���s3://eracommodities.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/ash_party_test/04b49a02bdd63f67a3be9294b78537949440874ee686b5518d62f3f7a392f0ca_53491.fix���s3://eracommodities.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/ash_party_test/05959e3801c1396f94062d415dc62de13cf2437a04d09ead74d52d65b33f363e_53580.fix���s3://eracommodities.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/ash_party_test/06acc6d48900ed4b4b18c332c395a9abe414229894833a0827e6f43d4070ce80_53758.fix��2024-07-04T08:19:57.397000Z��2024-07-04T08:30:00.010000Z��2024-07-04T08:58:41.828389Z��2024-07-04T08:33:40.967000Z��2024-07-04T07:49:07.475000Z��2024-07-04T07:06:33.134469Z��2024-07-04T07:30:30.954000Z��2024-07-04T08:24:19.954317Z��2024-07-04T08:19:57.397000Z��2024-07-04T08:30:00.010000Z��2024-07-04T08:58:41.828389Z��2024-07-04T08:33:40.967000Z��2024-07-04T07:49:07.475000Z��2024-07-04T07:06:33.134469Z��2024-07-04T07:30:30.954000Z��2024-07-04T08:24:19.954317Z��2024-07-04T08:19:57.397000Z��2024-07-04T08:30:00.010000Z��2024-07-04T08:58:41.828389Z��2024-07-04T08:33:40.967000Z��2024-07-04T07:49:07.475000Z��2024-07-04T07:06:33.134469Z��2024-07-04T07:30:30.954000Z��2024-07-04T08:24:19.954317Z�h�h�h�h�h�h�h�h�hwhwhwhwhxhxhwhwh�h�h�h�h�h�h�h��Open�h�h�h�h�h�h�h�G        G        G        G        G@$      G?�      G@.      G        �Market Side�j   j   j   j   j   j   j   h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�se_elastic_schema.static.mifid2��MultiLegReportingType����Outright���R�j  j  j  j  �Single Leg of Multi-leg���R�j  j	  j  �7461877��7461852��7163932��7460844��6914856��6581325��6947967��5941680�et�bhd(�@            �@     @�@
ףp=�3@������Y@      @      �      @333333"��h�KK��hlt�R�hhK ��h��R�(KKK��h!�]�(G        G        G        G        G@��     G�      G@�`     G        h�h�h�h�h�h�h�h���������et�bhhK ��h��R�(KKK��h!�]�(�lei:2138008NEORY2DQV4R24�j"  j"  j"  j"  j"  j"  j"  �id:ICE�j#  j#  j#  j#  j#  j#  j#  �id:1��id:1��id:1��id:1��id:1��id:1��id:1��id:1��id:cnelson15��id:tsherif2��id:jbeddoes1��id:phorrocks��id:tsherif2��id:tsherif2��id:tsherif2��id:tsherif2�h�h�h�h�h�h�h�h�j,  j-  j.  j/  j0  j1  j2  j3  et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3et�bh[Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bh[Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h5h6h7h8h9h:h;h<et�bh[Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h=at�bh[Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h>h?h@et�bh[Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bh[Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hBhChDhEhFhGhHhIhJhKhLhMhNhOet�bh[Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hPat�bh[Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hQhRhSet�bh[Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hThUhVhWhXhYet�bh[Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�ht�mgr_locs��builtins��slice���K KK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KK+K��R�u}�(j�  j  j�  j�  K+K,K��R�u}�(j�  j  j�  j�  K,K/K��R�u}�(j�  j  j�  j�  K/K5K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.