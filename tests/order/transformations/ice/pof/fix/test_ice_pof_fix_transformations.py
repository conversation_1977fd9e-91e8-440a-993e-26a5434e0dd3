from pathlib import Path

import pandas as pd
import pytest
from prefect import context
from se_elastic_schema.static.mifid2 import MultiLegReportingType
from se_trades_tasks.order.static import OrderColumns
from swarm.task.auditor import Auditor

from swarm_tasks.order.transformations.ice.pof.fix import (
    eracommodities_ice_pof_fix_order_transformations,
)
from swarm_tasks.order.transformations.ice.pof.fix.arrow_ice_pof_fix_order_transformations import (
    ArrowIcePofFixOrderTransformations,
)
from swarm_tasks.order.transformations.ice.pof.fix.eracommodities_ice_pof_fix_order_transformations import (
    EraCommoditiesIcePofFixOrderTransformations,
)
from swarm_tasks.order.transformations.ice.pof.fix.ice_pof_fix_order_transformations import (
    IcePofFixOrderTransformations,
)
from swarm_tasks.order.transformations.ice.pof.fix.static import SourceColumns
from swarm_tasks.order.transformations.ice.pof.fix.static import TempColumns

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")

# The source file is a pickle of the df which should be used as  the source dataframe
SOURCE_FRAME_PKL = TEST_FILES_DIR.joinpath("source_frame.pkl")
SOURCE_FRAME_ERACOMMODITIES_PKL = TEST_FILES_DIR.joinpath(
    "source_frame_eracommodities.pkl"
)
SOURCE_FRAME_ARROW_PKL = TEST_FILES_DIR.joinpath("source_frame_arrow.pkl")
EXPECTED_TRANSFORMED_DF = TEST_FILES_DIR.joinpath("expected_transformed_df.pkl")
EXPECTED_ERA_TRANSFORMED_DF = TEST_FILES_DIR.joinpath(
    "expected_eracommodities_transformed_df.pkl"
)
EXPECTED_ARROW_TRANSFORMED_DF = TEST_FILES_DIR.joinpath(
    "expected_arrow_transformed_df.pkl"
)


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


@pytest.fixture()
def source_frame() -> pd.DataFrame:
    return pd.read_pickle(SOURCE_FRAME_PKL)


@pytest.fixture()
def source_frame_eracommodities() -> pd.DataFrame:
    return pd.read_pickle(SOURCE_FRAME_ERACOMMODITIES_PKL)


@pytest.fixture()
def source_frame_arrow() -> pd.DataFrame:
    return pd.read_pickle(SOURCE_FRAME_ARROW_PKL)


@pytest.fixture()
def expected_transformed_df() -> pd.DataFrame:
    return pd.read_pickle(EXPECTED_TRANSFORMED_DF)


@pytest.fixture()
def expected_eracommodities_transformed_df() -> pd.DataFrame:
    return pd.read_pickle(EXPECTED_ERA_TRANSFORMED_DF)


@pytest.fixture()
def expected_arrow_transformed_df() -> pd.DataFrame:
    return pd.read_pickle(EXPECTED_ARROW_TRANSFORMED_DF)


class TestIcePofFixOrderTransformations:
    """Tests transformations in ICE POF FIX transformations""" ""

    def test_end_to_end_ice_pof_fix_order_transformations(
        self,
        source_frame: pd.DataFrame,
        expected_transformed_df: pd.DataFrame,
        auditor: Auditor,
    ):
        task = IcePofFixOrderTransformations(
            source_frame=source_frame,
            logger=context.get("logger"),
            auditor=auditor,
            es_client=None,
            tenant="test",
        )
        result = task.process()
        pd.testing.assert_frame_equal(left=result, right=expected_transformed_df)

    def test_end_to_end_eracommodities_ice_pof_fix_order_transformations(
        self,
        mocker,
        source_frame_eracommodities: pd.DataFrame,
        expected_eracommodities_transformed_df: pd.DataFrame,
        auditor: Auditor,
    ):
        task = EraCommoditiesIcePofFixOrderTransformations(
            source_frame=source_frame_eracommodities,
            logger=context.get("logger"),
            auditor=auditor,
            es_client=None,
            tenant="test",
        )
        self.mock_get_tenant_lei(mocker, source_frame_eracommodities)
        result = task.process()
        pd.testing.assert_frame_equal(
            left=result, right=expected_eracommodities_transformed_df
        )

    def test_end_to_end_arrow_ice_pof_fix_order_transformations(
        self,
        mocker,
        source_frame_arrow: pd.DataFrame,
        expected_arrow_transformed_df: pd.DataFrame,
        auditor: Auditor,
    ):
        task = ArrowIcePofFixOrderTransformations(
            source_frame=source_frame_arrow,
            logger=context.get("logger"),
            auditor=auditor,
            es_client=None,
            tenant="test",
        )
        self.mock_get_tenant_lei(mocker, source_frame_arrow)
        result = task.process()
        pd.testing.assert_frame_equal(left=result, right=expected_arrow_transformed_df)

    @staticmethod
    def mock_get_tenant_lei(mocker, test_df: pd.DataFrame):
        mock_get_tenant_lei = mocker.patch.object(
            eracommodities_ice_pof_fix_order_transformations, "run_get_tenant_lei"
        )
        tenant_lei_df = pd.DataFrame(index=test_df.index)
        tenant_lei_df = tenant_lei_df.assign(
            **{TempColumns.BUYER: "lei:2138008NEORY2DQV4R24"}
        )
        mock_get_tenant_lei.return_value = tenant_lei_df
        return mock_get_tenant_lei

    def test_multi_leg_reporting_type(
        self,
        auditor: Auditor,
    ):
        source_frame = pd.DataFrame(
            {
                SourceColumns.MULTI_LEG_REPORTING_TYPE: [
                    "1",
                    "2",
                    "3",
                    "foo",
                    pd.NA,
                    pd.NA,
                ],
                SourceColumns.FF_9092: [pd.NA, pd.NA, pd.NA, pd.NA, pd.NA, "something"],
            }
        )

        expected_result = pd.DataFrame(
            {
                OrderColumns.MULTI_LEG_REPORTING_TYPE: [
                    MultiLegReportingType.OUTRIGHT,
                    MultiLegReportingType.SINGLE_LEG_OF_MULTI_LEG,
                    MultiLegReportingType.MULTI_LEG_SECURITY,
                    pd.NA,
                    MultiLegReportingType.OUTRIGHT,
                    MultiLegReportingType.MULTI_LEG_SECURITY,
                ]
            }
        )

        task = IcePofFixOrderTransformations(
            source_frame=source_frame,
            logger=context.get("logger"),
            auditor=auditor,
            es_client=None,
            tenant="test",
        )
        result = task._multi_leg_reporting_type()
        pd.testing.assert_frame_equal(left=result, right=expected_result)
