��&0      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK6��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�	S3FileURL��BeginString��
BodyLength��MsgType��	MsgSeqNum��SenderCompID��SenderSubID��SendingTime��TargetCompID��TargetSubID��OnBehalfOfSubID��AvgPx��ClOrdID��
Commission��CommType��CumQty��Currency��ExecID��
ExecTransType��	HandlInst��SecurityIDSource��LastCapacity��LastPx��LastQty��OrderID��OrderQty��	OrdStatus��OrdType��OrigClOrdID��Price��
SecurityID��Side��Symbol��TimeInForce��TransactTime��	SettlType��	SettlDate��	TradeDate��ff_76��StopPx��ExecType��	LeavesQty��SecurityExchange��ff_453��PartyID��
PartyIDSource��	PartyRole��CheckSum��LastMkt��Text��
SettlCurrency��TradeReportingIndicator��Account��
ExecBroker�et�b�name�Nu��R�h
�pandas.core.indexes.range��
RangeIndex���}�(h\N�start�K �stop�K�step�Ku��R�e]�(hhK ��h��R�(KKK��h!�]�(��s3://jose.dev.steeleye.co/ingress/raw/order-feed-emsx-fix/********/03b7fbc0ad2934600ec94330fd3e4ec945c51b172ca82a7655213fa3f678e452_1212.fix���s3://jose.dev.steeleye.co/ingress/raw/order-feed-emsx-fix/********/0f0b4a366aafc177b8139ac3dcc6bdb19d4d56d9c870b9cda2e16301ddc22b7a_1158.fix���s3://jose.dev.steeleye.co/ingress/raw/order-feed-emsx-fix/********/512a134571535b753b1da79f4c3977ec100ff2e20bc0abc925fed847e509bb58_594.fix���s3://jose.dev.steeleye.co/ingress/raw/order-feed-emsx-fix/********/8336b06d22fff6b2ef98cc8c695d27b5eb1ea7e7d9677060c25618d1189d7556_1151.fix���s3://jose.dev.steeleye.co/ingress/raw/order-feed-emsx-fix/********/8878811b227f11055ddf557890951036e3e5fa9b9a90b42ca67be1da3c232c5f_597.fix���s3://jose.dev.steeleye.co/ingress/raw/order-feed-emsx-fix/********/8ceb8a8e7bb28132f3ed5ce46e7b1d4bfce81653d54e08f6e4a7295d41b6017c_599.fix���s3://jose.dev.steeleye.co/ingress/raw/order-feed-emsx-fix/********/9d960159ba0d0ee7a6a49958bac0bd1258294512fb2cc9894969cda4af096216_1402.fix���s3://jose.dev.steeleye.co/ingress/raw/order-feed-emsx-fix/********/9dfc24047fe6b97ed85f286519f23b67753b94c6b51f088f5ce4d940babe353c_1210.fix���s3://jose.dev.steeleye.co/ingress/raw/order-feed-emsx-fix/********/a26170d8e2be93f49c6bda7f6d1960512480eb10e949a4b27da860d0e050cb70_601.fix���s3://jose.dev.steeleye.co/ingress/raw/order-feed-emsx-fix/********/a9bf178fd60573879723f6aaf6c09200bdebc0f571dd03218978aeb11b4d03d5_606.fix���s3://jose.dev.steeleye.co/ingress/raw/order-feed-emsx-fix/********/d7e12d025b13094b2f79a7db55e54678c5617264634a5684b81f591ead52354d_604.fix���s3://jose.dev.steeleye.co/ingress/raw/order-feed-emsx-fix/********/d89928b3fb756018bbc8aec38e24faa08185771347b6b24af09bace1f3adb434_1403.fix���s3://jose.dev.steeleye.co/ingress/raw/order-feed-emsx-fix/********/eb5c8852d9cc1cc305843569ef47a8897cb310aab1ab4fea3c32335fcb8af0af_598.fix���s3://jose.dev.steeleye.co/ingress/raw/order-feed-emsx-fix/********/ee3d2675449ab80fe24795cd6eb85f1a15593435ae9cd6195050045e16b78ec3_1156.fix��FIX.4.4��FIX.4.4��FIX.4.4��FIX.4.4��FIX.4.4��FIX.4.4��FIX.4.4��FIX.4.4��FIX.4.4��FIX.4.4��FIX.4.4��FIX.4.4��FIX.4.4��FIX.4.4�et�bhhK ��h��R�(KKK��h�i8�����R�(K�<�NNNJ����J����K t�b�Cp�      �      �      �      �      �      �      �      �      �      �      �      �      �      �t�bhhK ��h��R�(KKK��h!�]�(�8�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bhhK ��h��R�(KKK��h��Cp�      �      R            U      W      z      �      Y      ^      \      {      V      �      �t�bhhK ��h��R�(KKK��h!�]�(�BLPDROP��BLPDROP��BLPDROP��BLPDROP��BLPDROP��BLPDROP��BLPDROP��BLPDROP��BLPDROP��BLPDROP��BLPDROP��BLPDROP��BLPDROP��BLPDROP��CTS��CTS��CTS��CTS��CTS��CTS��CTS��CTS��CTS��CTS��CTS��CTS��CTS��CTS��20230120-15:03:58.236��20230120-14:37:04.592��********-09:55:48.191��20230120-14:34:15.511��********-09:57:01.300��********-09:57:52.042��20230120-16:38:38.932��20230120-15:03:04.236��********-09:58:28.511��********-10:00:19.236��********-09:59:34.811��20230120-16:38:39.005��********-09:57:27.671��20230120-14:36:21.450��PARTDROP��PARTDROP��PARTDROP��PARTDROP��PARTDROP��PARTDROP��PARTDROP��PARTDROP��PARTDROP��PARTDROP��PARTDROP��PARTDROP��PARTDROP��PARTDROP��14922427��14922427��14922427��14922427��14922427��14922427��14922427��14922427��14922427��14922427��14922427��14922427��14922427��14922427��MD1��MD1��ND1��MD1��ND1��ND1��MM1��MD1��ND1��ND1��ND1��MM1��ND1��MD1�et�bhhK ��h��R�(KKK��h�f8�����R�(Kh�NNNJ����J����K t�b�Cp     �N@     �N@                fffffF?@o�mF?@             �N@o�mF?@o�mF?@o�mF?@        �)X�lF?@     �N@�t�bhhK ��h��R�(KKK��h!�]�(�NTSL-20230120-00051-00005��NTSL-20230120-00051-00001��NTSL-********-00010-00001��NTSL-20230120-00051-00001��NTSL-********-00010-00001��NTSL-********-00010-00001��NTSL-20220315-00016-00001��NTSL-20230120-00051-00005��NTSL-********-00010-00001��NTSL-********-00010-00007��NTSL-********-00010-00007��NTSL-20220315-00017-00001��NTSL-********-00010-00001��NTSL-20230120-00051-00001�G        G        �pandas._libs.missing��NA���j  G        G        j  G        j  G        G        j  G        G        �1�j  j  j  j  j  j  j  j  j  j  j  j  j  et�bhhK ��h��R�(KKK��j  �Cp    `P�@    `P�@                     ��@     k�@            `P�@     k�@     k�@     k�@            �i�@     ��@�t�bhhK ��h��R�(KKK��h!�]�(�GBP��GBP��USD��GBP��USD��USD��GBP��GBP��USD��USD��USD��GBP��USD��GBP��NTSL-EUN1F89CR8��NTSL-EUN1F89706��NTSL-EUN1M8I04��NTSL-EUN1F896DB��NTSL-EUN1M8I0S��NTSL-EUN1M8I4U��NTSL-EUN1F89R7C��NTSL-EUN1F89CLB��NTSL-EUN1M8I6T��NTSL-EUN1M8IAF��NTSL-EUN1M8I8Z��NTSL-EUN1F89R7D��NTSL-EUN1M8I4J��NTSL-EUN1F896VK��0�jF  jF  jF  jF  jF  jF  jF  jF  jF  jF  jF  jF  jF  j  �2�jG  jG  jG  jG  jG  j  jG  j  j  jG  jG  jG  �4�jH  jH  jH  jH  jH  jH  jH  jH  jH  jH  jH  jH  jH  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bhhK ��h��R�(KKK��j  �C�             �N@                fffffF?@
ףp=J?@                                                H�z�G?@     �N@             ؈@                     ��@      $@                                                     ��@     ��@�t�bhhK ��h��R�(KKK��h!�]�(�$NTSL-BEU20230120-00051-00005-EMSXEU2��$NTSL-BEU20230120-00051-00001-EMSXEU2��$NTSL-BEU********-00010-00001-EMSXEU2��$NTSL-BEU20230120-00051-00001-EMSXEU2��$NTSL-BEU********-00010-00001-EMSXEU2��$NTSL-BEU********-00010-00001-EMSXEU2��$NTSL-BEU20220315-00016-00001-EMSXEU2��$NTSL-BEU20230120-00051-00005-EMSXEU2��$NTSL-BEU********-00010-00001-EMSXEU2��$NTSL-BEU********-00010-00007-EMSXEU2��$NTSL-BEU********-00010-00007-EMSXEU2��$NTSL-BEU20220315-00017-00001-EMSXEU2��$NTSL-BEU********-00010-00001-EMSXEU2��$NTSL-BEU20230120-00051-00001-EMSXEU2�et�bhhK ��h��R�(KKK��j  �Cp    ���@    ���@    ���@    ���@    ���@    ���@    ��A    ���@    ���@    ���@    ���@    ��A    ���@    ���@�t�bhhK ��h��R�(KKK��h!�]�(jH  j  jF  jF  j  j  �3��6�jo  jH  jp  jo  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �NTSL-20230120-00051-00001�j  j  j  j  j  j  �NTSL-20230120-00051-00001�j  �NTSL-********-00010-00001��NTSL-********-00010-00001�j  j  j  et�bhhK ��h��R�(KKK��j  �Cp                                                                                                                �t�bhhK ��h��R�(KK	K��h!�]�(�IE00B4L5Y983��IE00B4L5Y983��IE00BKM4GZ66��IE00B4L5Y983��IE00BKM4GZ66��IE00BKM4GZ66��GB00BJ0LT190��IE00B4L5Y983��IE00BKM4GZ66��IE00BKM4GZ66��IE00BKM4GZ66��GG00BWWYMV85��IE00BKM4GZ66��IE00B4L5Y983�j  j  j  j  j  j  jG  j  j  j  j  jG  j  j  �SWDA��SWDA��EIMI��SWDA��EIMI��EIMI��HGT��SWDA��EIMI��EIMI��EIMI��APAX��EIMI��SWDA�j  j  j  j  j  j  j  j  j  j  j  j  j  j  �20230120-15:03:58.195��20230120-14:37:04.549��********-09:55:48.148��20230120-14:34:15.468��********-09:57:01.256��********-09:57:52.000��20230120-16:38:38.884��20230120-15:03:04.195��********-09:58:28.470��********-10:00:19.130��********-09:59:34.770��20230120-16:38:38.885��********-09:57:27.629��20230120-14:36:21.407�jF  jF  jF  jF  jF  jF  jF  jF  jF  jF  jF  jF  jF  jF  �20230124��20230124��20230125��20230124��20230125��20230125��20230125��20230124��20230125��20230125��20230125��20230125��20230125��20230124��20230120��20230120��********��20230120��********��********��********��20230120��********��********��********��********��********��20230120��NTSL��NTSL��NTSL��NTSL��NTSL��NTSL��NTSL��NTSL��NTSL��NTSL��NTSL��NTSL��NTSL��NTSL�et�bhhK ��h��R�(KKK��j  �Cp                                                                                                                �t�bhhK ��h��R�(KKK��h!�]�(jH  j  jF  jF  j  j  jo  jp  jo  jH  jp  jo  j  j  et�bhhK ��h��R�(KKK��j  �Cp     P�@     P�@    ���@    ���@     ܞ@     ��@    ��A     P�@     ��@     ��@     ��@    ��A     �@     �@�t�bhhK ��h��R�(KKK��h!�]�(�LN��LN��LN��LN��LN��LN��LN��LN��LN��LN��LN��LN��LN��LN�j  j  j  j  j  j  j  j  j  j  j  j  j  j  �549300EF8AZLRG0UC208��549300EF8AZLRG0UC208�j  j  �549300EF8AZLRG0UC208��549300EF8AZLRG0UC208�j  �549300EF8AZLRG0UC208��549300EF8AZLRG0UC208��549300EF8AZLRG0UC208��549300EF8AZLRG0UC208�j  �549300EF8AZLRG0UC208��549300EF8AZLRG0UC208��N�j  j  j  j  j  j  j  j  j  j  j  j  j  KKj  j  KKj  KKKKj  KK�055��222��073��047��089��222��090��043��053��031��091��041��059��092�j  �XLON�j  j  �BMTF��XLON�j  j  j  j  j  j  �XLON��BMTF�j  �EUCTS�j  j  �EUCTS��EUCTS�j  j  j  j  j  j  �EUCTS��EUCTS�j  �GBP��USD��GBP��USD��USD��GBP�j  �USD�j  j  �GBP��USD��GBP�j  Kj  j  KKj  j  j  j  j  j  KKj  j  �20160045�j  �20160045��20160045��2103001�j  �20160045��20160045��20160045��2103001��20160045�j  et�bhhK ��h��R�(KKK��h!�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&et�bh\Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bh\Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h(at�bh\Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h)at�bh\Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h*h+h,h-h.h/et�bh\Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h0at�bh\Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h1h2h3et�bh\Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bh\Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h5h6h7h8h9h:et�bh\Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h;h<et�bh\Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h=at�bh\Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bh\Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h?h@hAet�bh\Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hBat�bh\Nu��R�h
h}�(hhhK ��h��R�(KK	��h!�]�(hChDhEhFhGhHhIhJhKet�bh\Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hLat�bh\Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hMat�bh\Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hNat�bh\Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hOhPhQhRhShThUhVhWhXhYet�bh\Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hZat�bh\Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hk�mgr_locs��builtins��slice���K KK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  j	  j�  j�  KKK��R�u}�(j�  j!  j�  j�  KKK��R�u}�(j�  j'  j�  j�  KKK��R�u}�(j�  jL  j�  j�  KKK��R�u}�(j�  jR  j�  j�  KKK��R�u}�(j�  jf  j�  j�  KKK��R�u}�(j�  jl  j�  j�  KKK��R�u}�(j�  jx  j�  j�  KKK��R�u}�(j�  j~  j�  j�  KK'K��R�u}�(j�  j�  j�  j�  K'K(K��R�u}�(j�  j�  j�  j�  K(K)K��R�u}�(j�  j�  j�  j�  K)K*K��R�u}�(j�  j�  j�  j�  K*K5K��R�u}�(j�  j4  j�  j�  K5K6K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.