��t3      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK=��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��executionDetails.limitPrice��(_orderState.executionDetails.orderStatus��#_order.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo�� executionDetails.tradingCapacity��executionDetails.validityPeriod��_orderState.id��	_order.id��_order.__meta_model__��_orderState.__meta_model__��"orderIdentifiers.aggregatedOrderId��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��!orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��priceFormingData.price��"priceFormingData.remainingQuantity��+_orderState.priceFormingData.tradedQuantity��reportDetails.transactionRefNo��sourceIndex��timestamps.orderReceived��timestamps.orderSubmitted��timestamps.orderStatusUpdated��timestamps.tradingDateTime��#transactionDetails.buySellIndicator��transactionDetails.price��transactionDetails.priceAverage�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��transactionDetails.recordType��+transactionDetails.settlementAmountCurrency��!transactionDetails.settlementDate��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��isin_attribute��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers��!__instrument_created_through_fb__��__newo_in_file__�et�b�name�Nu��R�h
�pandas.core.indexes.range��
RangeIndex���}�(hcN�start�K �stop�K�step�Ku��R�e]�(hhK ��h��R�(KK6K��h!�]�(�1�huhuhuhuhu�2�huhuhuhuhvhuhuhuhuhuhuhuhuhvhuhuhuhuhvhuhu�BBG - EMSX - FIX�hwhwhwhwhwhwhwhwhwhwhwhwhw�
2023-01-20��
2023-01-20��
2023-01-23��
2023-01-20��
2023-01-23��
2023-01-23��
2023-01-20��
2023-01-20��
2023-01-23��
2023-01-23��
2023-01-23��
2023-01-20��
2023-01-23��
2023-01-20��BUYI�h�h�h�h�h��SELL�h�h�h�h�h�h�h��pandas._libs.missing��NA���h�h�h�h�h�h�h�h�h�h�h�h�h��CAME��PARF�h�h�h�h��DNFD��PNDC�h�h�h�h�h�h��NEWO�h�h�h�h�h�h�h�h�h�h�h�h�h��Market�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��20160045�h��20160045��20160045��2103001�h��20160045��20160045��20160045��2103001��20160045�h��AOTC�h�h�h�h�h�h�h�h�h�h�h�h�h�]��GTCV�ah�h�h�h�h�h�h�h�h�h�h�h�h��$NTSL-BEU20230120-00051-00005-EMSXEU2��$NTSL-BEU20230120-00051-00001-EMSXEU2��$NTSL-BEU20230123-00010-00001-EMSXEU2��$NTSL-BEU20230120-00051-00001-EMSXEU2��$NTSL-BEU20230123-00010-00001-EMSXEU2��$NTSL-BEU20230123-00010-00001-EMSXEU2��$NTSL-BEU20220315-00016-00001-EMSXEU2��$NTSL-BEU20230120-00051-00005-EMSXEU2��$NTSL-BEU20230123-00010-00001-EMSXEU2��$NTSL-BEU20230123-00010-00007-EMSXEU2��$NTSL-BEU20230123-00010-00007-EMSXEU2��$NTSL-BEU20220315-00017-00001-EMSXEU2��$NTSL-BEU20230123-00010-00001-EMSXEU2��$NTSL-BEU20230120-00051-00001-EMSXEU2�h�h�h�h�h�h�h�h�h�h�h�h�h�h��Order�h�h�h�h�h�h�h�h�h�h�h�h�h��
OrderState�h�h�h�h�h�h�h�h�h�h�h�h�h��NTSL-20230120-00051-00005��NTSL-20230120-00051-00001��NTSL-20230123-00010-00001��NTSL-20230120-00051-00001��NTSL-20230123-00010-00001��NTSL-20230123-00010-00001��NTSL-20220315-00016-00001��NTSL-20230120-00051-00005��NTSL-20230123-00010-00001��NTSL-20230123-00010-00007��NTSL-20230123-00010-00007��NTSL-20220315-00017-00001��NTSL-20230123-00010-00001��NTSL-20230120-00051-00001��NTSL-EUN1F89CR8��NTSL-EUN1F89706��NTSL-EUN1M8I04��NTSL-EUN1F896DB��NTSL-EUN1M8I0S��NTSL-EUN1M8I4U��NTSL-EUN1F89R7C��NTSL-EUN1F89CLB��NTSL-EUN1M8I6T��NTSL-EUN1M8IAF��NTSL-EUN1M8I8Z��NTSL-EUN1F89R7D��NTSL-EUN1M8I4J��NTSL-EUN1F896VK�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�G@��     h�h�G@��     G@$      h�h�h�h�h�h�G@��     G@��     h�h�h�h�h�h�h�h�h�h�h�h�h�h��2023-01-20T15:03:58.195000Z��2023-01-20T14:37:04.549000Z��2023-01-23T09:55:48.148000Z��2023-01-20T14:34:15.468000Z��2023-01-23T09:57:01.256000Z��2023-01-23T09:57:52.000000Z��2023-01-20T16:38:38.884000Z��2023-01-20T15:03:04.195000Z��2023-01-23T09:58:28.470000Z��2023-01-23T10:00:19.130000Z��2023-01-23T09:59:34.770000Z��2023-01-20T16:38:38.885000Z��2023-01-23T09:57:27.629000Z��2023-01-20T14:36:21.407000Z�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��GBP��GBP��USD��GBP��USD��USD��GBP��GBP��USD��USD��USD��GBP��USD��GBP��MONE�h�h�h�h�h�h�h�h�h�h�h�h�h�h�G@��     h�h�G@��     G@$      h�h�h�h�h�h�G@��     G@��     h�h�h�h�h�h�h�h�h�h�h�h�h�h�UNIT�h�h�h�h�h�h�h�h�h�h�h�h�h�Client Side�h�h�h�h�h�h�h�h�h�h�h�h�h�h��GBP��USD��GBP��USD��USD��GBP�h��USD�h�h��GBP��USD��GBP��
2023-01-24��
2023-01-24��
2023-01-25��
2023-01-24��
2023-01-25��
2023-01-25��
2023-01-25��
2023-01-24��
2023-01-25��
2023-01-25��
2023-01-25��
2023-01-25��
2023-01-25��
2023-01-24�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��XLON�h�h��BMTF��XLON�h�h�h�h�h�h��XLON��BMTF�h�j   h�h�j  j  h�h�h�h�h�h�j  j  ]�}�(�labelId��IE00B4L5Y983��path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�ua]�}�(j  �IE00B4L5Y983�j	  j
  j  j  ua]�}�(j  �IE00BKM4GZ66�j	  j
  j  j  ua]�}�(j  �IE00B4L5Y983�j	  j
  j  j  ua]�}�(j  �IE00BKM4GZ66�j	  j
  j  j  ua]�}�(j  �IE00BKM4GZ66�j	  j
  j  j  ua]�}�(j  �GB00BJ0LT190�j	  j
  j  j  ua]�}�(j  �IE00B4L5Y983�j	  j
  j  j  ua]�}�(j  �IE00BKM4GZ66�j	  j
  j  j  ua]�}�(j  �IE00BKM4GZ66�j	  j
  j  j  ua]�}�(j  �IE00BKM4GZ66�j	  j
  j  j  ua]�}�(j  �GG00BWWYMV85�j	  j
  j  j  ua]�}�(j  �IE00BKM4GZ66�j	  j
  j  j  ua]�}�(j  �IE00B4L5Y983�j	  j
  j  j  uaj  j  j  j  j  j   j#  j&  j)  j,  j/  j2  j5  j8  ]�(}�(j  �
lei:123456789�j	  �buyer�j  j  �ARRAY���R�u}�(j  �
lei:123456789�j	  �reportDetails.executingEntity�j  j  u}�(j  �id:ntsl�j	  �seller�j  j?  u}�(j  �id:ntsl�j	  �counterparty�j  j  u}�(j  �
lei:123456789�j	  �clientIdentifiers.client�j  j?  ue]�(}�(j  �
lei:123456789�j	  j<  j  j?  u}�(j  �
lei:123456789�j	  jB  j  j  u}�(j  �id:ntsl�j	  jE  j  j?  u}�(j  �id:ntsl�j	  jH  j  j  u}�(j  �
lei:123456789�j	  jK  j  j?  ue]�(}�(j  �
lei:123456789�j	  j<  j  j?  u}�(j  �
lei:123456789�j	  jB  j  j  u}�(j  �id:ntsl�j	  jE  j  j?  u}�(j  �id:ntsl�j	  jH  j  j  u}�(j  �
lei:123456789�j	  jK  j  j?  ue]�(}�(j  �
lei:123456789�j	  j<  j  j?  u}�(j  �
lei:123456789�j	  jB  j  j  u}�(j  �id:ntsl�j	  jE  j  j?  u}�(j  �id:ntsl�j	  jH  j  j  u}�(j  �
lei:123456789�j	  jK  j  j?  ue]�(}�(j  �
lei:123456789�j	  j<  j  j?  u}�(j  �
lei:123456789�j	  jB  j  j  u}�(j  �id:ntsl�j	  jE  j  j?  u}�(j  �id:ntsl�j	  jH  j  j  u}�(j  �
lei:123456789�j	  jK  j  j?  ue]�(}�(j  �
lei:123456789�j	  j<  j  j?  u}�(j  �
lei:123456789�j	  jB  j  j  u}�(j  �id:ntsl�j	  jE  j  j?  u}�(j  �id:ntsl�j	  jH  j  j  u}�(j  �
lei:123456789�j	  jK  j  j?  ue]�(}�(j  �
lei:123456789�j	  j<  j  j?  u}�(j  �
lei:123456789�j	  jB  j  j  u}�(j  �id:ntsl�j	  jE  j  j?  u}�(j  �id:ntsl�j	  jH  j  j  u}�(j  �
lei:123456789�j	  jK  j  j?  ue]�(}�(j  �
lei:123456789�j	  j<  j  j?  u}�(j  �
lei:123456789�j	  jB  j  j  u}�(j  �id:ntsl�j	  jE  j  j?  u}�(j  �id:ntsl�j	  jH  j  j  u}�(j  �
lei:123456789�j	  jK  j  j?  ue]�(}�(j  �
lei:123456789�j	  j<  j  j?  u}�(j  �
lei:123456789�j	  jB  j  j  u}�(j  �id:ntsl�j	  jE  j  j?  u}�(j  �id:ntsl�j	  jH  j  j  u}�(j  �
lei:123456789�j	  jK  j  j?  ue]�(}�(j  �
lei:123456789�j	  j<  j  j?  u}�(j  �
lei:123456789�j	  jB  j  j  u}�(j  �id:ntsl�j	  jE  j  j?  u}�(j  �id:ntsl�j	  jH  j  j  u}�(j  �
lei:123456789�j	  jK  j  j?  ue]�(}�(j  �
lei:123456789�j	  j<  j  j?  u}�(j  �
lei:123456789�j	  jB  j  j  u}�(j  �id:ntsl�j	  jE  j  j?  u}�(j  �id:ntsl�j	  jH  j  j  u}�(j  �
lei:123456789�j	  jK  j  j?  ue]�(}�(j  �
lei:123456789�j	  j<  j  j?  u}�(j  �
lei:123456789�j	  jB  j  j  u}�(j  �id:ntsl�j	  jE  j  j?  u}�(j  �id:ntsl�j	  jH  j  j  u}�(j  �
lei:123456789�j	  jK  j  j?  ue]�(}�(j  �
lei:123456789�j	  j<  j  j?  u}�(j  �
lei:123456789�j	  jB  j  j  u}�(j  �id:ntsl�j	  jE  j  j?  u}�(j  �id:ntsl�j	  jH  j  j  u}�(j  �
lei:123456789�j	  jK  j  j?  ue]�(}�(j  �
lei:123456789�j	  j<  j  j?  u}�(j  �
lei:123456789�j	  jB  j  j  u}�(j  �id:ntsl�j	  jE  j  j?  u}�(j  �id:ntsl�j	  jH  j  j  u}�(j  �
lei:123456789�j	  jK  j  j?  ue�
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��id:ntsl��id:ntsl��id:ntsl��id:ntsl��id:ntsl��id:ntsl��id:ntsl��id:ntsl��id:ntsl��id:ntsl��id:ntsl��id:ntsl��id:ntsl��id:ntsl��id:ntsl��id:ntsl��id:ntsl��id:ntsl��id:ntsl��id:ntsl��id:ntsl��id:ntsl��id:ntsl��id:ntsl��id:ntsl��id:ntsl��id:ntsl��id:ntsl�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789��
lei:123456789�h�h�h�h�h�h�h�h�h�h�h�h�h�h�]�(j  j:  j@  jC  jF  jI  e]�(j  jM  jO  jQ  jS  jU  e]�(j  jX  jZ  j\  j^  j`  e]�(j  jc  je  jg  ji  jk  e]�(j  jn  jp  jr  jt  jv  e]�(j  jy  j{  j}  j  j�  e]�(j"  j�  j�  j�  j�  j�  e]�(j%  j�  j�  j�  j�  j�  e]�(j(  j�  j�  j�  j�  j�  e]�(j+  j�  j�  j�  j�  j�  e]�(j.  j�  j�  j�  j�  j�  e]�(j1  j�  j�  j�  j�  j�  e]�(j4  j�  j�  j�  j�  j�  e]�(j7  j�  j�  j�  j�  j�  e��������������et�bhhK ��h��R�(KKK��h�f8�����R�(K�<�NNNJ����J����K t�b�B0      ���@    ���@    ���@    ���@    ���@    ���@    ��A    ���@    ���@    ���@    ���@    ��A    ���@    ���@             �N@                fffffF?@
ףp=J?@                                                H�z�G?@     �N@     P�@     P�@    ���@    ���@     ܞ@     ��@    ��A     P�@     ��@     ��@     ��@    ��A     �@     �@             �N@                fffffF?@
ףp=J?@                                                H�z�G?@     �N@     �N@     �N@                fffffF?@o�mF?@             �N@o�mF?@o�mF?@o�mF?@        �)X�lF?@     �N@�t�bhhK ��h��R�(KKK��h�i8�����R�(Kj7  NNNJ����J����K t�b�Cp                                                                	       
                     
       �t�bhhK ��h��R�(KKK��h�b1�����R�(Kh"NNNJ����J����K t�b�C�t�be]�(h
h}�(hhhK ��h��R�(KK6��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3h4h5h6h7h8h<h=h?h@hAhBhChFhGhHhIhJhKhLhMhNhOhPhQhRhShThUhVhWhXhYhZh[h\h]h^h_haet�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h9h:h;hDhEet�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bhcNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h`at�bhcNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hr�mgr_locs�hhK ��h��R�(KK6��jA  �B�                                                                  	       
                     
                                                                                                  !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       3       4       5       6       7       8       9       :       <       �t�bu}�(j{  j2  j|  hhK ��h��R�(KK��jA  �C(                                    �t�bu}�(j{  j=  j|  �builtins��slice���KKK��R�u}�(j{  jG  j|  j�  K;K<K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.