���f      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK/��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�CONTRACTCLASSIFICATION��TRADESTATUS��	UNNAMED:2��	COMMODITY��	UNNAMED:4��CURRENCY��
COUNTERYPARTY��	TRADEDATE��EXPIRYMONTH��
PROMPTDATE��
PROMPTCODE��B/S��TYPE��
TRADECATEGORY��	PRICETYPE��	PRICECODE��VENUE��SUBACC��CLIENTID��CONTRANOMINATEDCLEARER��
SELECTMATCHNO��MATCHNO��MATCH<PERSON>IPID��	TRADETIME��TIME<PERSON>AC<PERSON>T��M<PERSON>CHINGSTATUS��PUBLICREFERENCE(LONG)��PRIVATEREFERENCE��LASTMODIFIEDTIME��	MATCHTIME��
LATEINDICATOR��LCHMATCHNUMBER��CLEARINGSLIPID��
DATAORIGIN��4-WAYAGREEMENTFLAG��BASELEGFLAG��
VOLATILITY��CLEARINGERRORCODE��ABBREVIATEDPRICE��VOLUME��PRICE��TRADEPREMIUM��STRIKEPRICE��A<PERSON><PERSON><PERSON><PERSON>��
CLIENTCODE��
ALLEGEDB/S��LCHPRIVATEREFERENCE�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KKI��h�i8�����R�(K�<�NNNJ����J����K t�b�BH                                                                  	       
                     
                                                                                                                                             !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       3       4       5       6       7       8       9       :       ;       <       =       >       ?       @       A       B       C       D       E       F       G       H       �t�bhU�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KKI��h!�]�(�pandas._libs.missing��NA���hxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxet�b�_dtype�hk�StringDtype���)��ubhm)��}�(hphhK ��h��R�(KKI��h!�]�(hxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxet�bhzh|)��ubhm)��}�(hphhK ��h��R�(KKI��h!�]�(�OPTIONS��CARRIES��FUTURES�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bhzh|)��ubhm)��}�(hphhK ��h��R�(KKI��h!�]�(�AH�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��NI�h�h�h�h�h�h�h�h�h�h�h��PB�h�h��ZS�h��CA�h�h�h�h�h�et�bhzh|)��ubhm)��}�(hphhK ��h��R�(KKI��h!�]�(�USD�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bhzh|)��ubhm)��}�(hphhK ��h��R�(KKI��h!�]�(�UNA��SFL��GSF��JPM�h�h�h�h�h�h��XXX�h��EMV�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��NHF�h�h�h�h�h�h�h�h�h�h�h��MFL�h�h�h��SNC�h�h�h�h�et�bhzh|)��ubhm)��}�(hphhK ��h��R�(KKI��h!�]�(�
19/05/2022��
20/05/2022�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bhzh|)��ubhm)��}�(hphhK ��h��R�(KKI��h!�]�(�
20/07/2022�h�h�h�h�h�h�h�hό
19/08/2022�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�hЌ
24/05/2022�hь
18/08/2022�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�hь
05/08/2022�h�h�h�et�bhzh|)��ubhm)��}�(hphhK ��h��R�(KKI��h!�]�(�B�h݌S�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bhzh|)��ubhm)��}�(hphhK ��h��R�(KKI��h!�]�(�TC��F�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bhzh|)��ubhm)��}�(hphhK ��h��R�(KKI��h!�]�(�REVERSAL��NORMAL��GIVE-UP EXECUTOR�h�h�h�h�
CORRECTION�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bhzh|)��ubhm)��}�(hphhK ��h��R�(KKI��h!�]�(�CURRENT�j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   et�bhzh|)��ubhm)��}�(hphhK ��h��R�(KKI��h!�]�(�None�j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  �Settlement Price�j
  j  j
  j
  j
  et�bhzh|)��ubhm)��}�(hphhK ��h��R�(KKI��h!�]�(�	Telephone��	Telephone��	Telephone��	Telephone��	Telephone��	Telephone��	Telephone��	Telephone��	Telephone��	Telephone��Select�j  �Ring��	Telephone�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j   �	Telephone��	Telephone�j  j  j  �	Telephone�j  j  j  j  �	Telephone�j  j  j   j  �
Basis Ring��	Telephone��	Telephone��	Telephone�j&  j&  �	Telephone�et�bhzh|)��ubhm)��}�(hphhK ��h��R�(KKI��h!�]�(�UNA��SFL��GSF��JPM�j4  j7  j5  j4  j7  j5  �XXX�j8  �EMV�j5  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j8  j9  j5  �NHF�j8  j8  j8  j:  j8  j8  j8  j8  j5  j8  j8  �MFL�j8  j:  j5  �SNC�j5  j:  j:  j<  et�bhzh|)��ubhm)��}�(hphhK ��h��R�(KKI��h!�]�(hxhxhxhxhxhxhxhxhxhx�TAAH20220520X0005851��TAAH20220520X0005853�hxhx�TAAH20220520X0010186��TAAH20220520X0008539��TAAH20220520X0008541��TAAH20220520X0008540��TAAH20220520X0008542��TAAH20220520X0008543��TAAH20220520X0008530��TAAH20220520X0008535��TAAH20220520X0008534��TAAH20220520X0008536��TAAH20220520X0008537��TAAH20220520X0008538��TAAH20220520X0008523��TAAH20220520X0008525��TAAH20220520X0008526��TAAH20220520X0008527��TAAH20220520X0008528��TAAH20220520X0008529��TAAH20220520X0008514��TAAH20220520X0008515��TAAH20220520X0008519��TAAH20220520X0008520��TAAH20220520X0008521��TAAH20220520X0008522��TAAH20220520X0006048��TAAH20220520X0006063��TAAH20220520X0006065��TAAH20220520X0006067��TAAH20220520X0006069��TAAH20220520X0008513��TAAH20220520X0006041��TAAH20220520X0006042��TAAH20220520X0006043��TAAH20220520X0006044��TAAH20220520X0006045��TAAH20220520X0006046�hxhxhx�TANI20220520X0000579��TANI20220520X0000582��TANI20220520X0000585�hx�TANI20220520X0000827��TANI20220520X0000579��TANI20220520X0000582��TANI20220520X0000585�hx�TAPB20220520X0002381��TAPB20220520X0002382�hx�TAZS20220520X0002353�hxhxhxhxhxhxhxet�bhzh|)��ubhm)��}�(hphhK ��h��R�(KKI��h!�]�(�25847��37276��44452��48227��51890��62611��48901��49047��62915�j�  �33428��33433��33789�j�  �54763��46496��46498��46497��46499��46500��46424��46442��46441��46443��46444��46445��46412��46419��46420��46421��46422��46423��46398��46399��46408��46409��46410��46411��34338��34439��34441��34443��34445��46397��34258��34332��34333��34334��34335��34336��34224��46478��21494��24981��24982��24983�j�  �33374�j�  j�  j�  j�  �31096��31097��33010��31449��34296��52460��31456�j�  �33140��33154��32714�et�bhzh|)��ubhm)��}�(hphhK ��h��R�(KKI��h!�]�(�10278661��10393941��10381652��10517161��10558311��10525442��10512081��10526092��10537802��10393952��10361412��10361452��10358461��10512092��10590262��10499331��10499351��10499341��10499361��10499371��10498731��10498961��10498951��10498971��10498981��10498991��10498611��10498681��10498691��10498701��10498711��10498721��10498491��10498501��10498571��10498581��10498591��10498601��10372601��10374121��10374141��10374161��10374181��10498481��10371711��10372541��10372551��10372561��10372571��10372581��10365522��10496811��10201222��10269241��10269261��10269281��10201231��10360871��10269252��10269272��10269292��10496822��10335031��10335041��10339082��10339042��10368001��10559492��10327131��10559501��10342782��10351301��10327142�et�bhzh|)��ubhm)��}�(hphhK ��h��R�(KKI��h!�]�(�16:43��13:30��13:17��15:10�j  j  �15:05�j  j  j  �13:06�j   �12:59�j  �16:02��15:01�j#  j#  j#  j#  j#  j#  j#  j#  j#  j#  �15:00�j#  j#  j#  j#  j#  j$  j$  j$  j$  j$  j$  �13:13��13:14�j&  j&  j&  j$  j%  j%  j%  j%  j%  j%  �13:04��14:58��09:44��11:14�j*  j*  j)  �13:05�j*  j*  j*  j(  �12:52�j,  �12:49��12:55��12:54��15:39��12:36�j0  �12:34�j2  j2  et�bhzh|)��ubhm)��}�(hphhK ��h��R�(KKI��h!�]�(�MT�j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  et�bhzh|)��ubhm)��}�(hphhK ��h��R�(KKI��h!�]�(�75400�hx�SIG�jF  jF  jF  hxjF  jF  hxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxhxet�bhzh|)��ubhm)��}�(hphhK ��h��R�(KKI��h!�]�(�25847��37276��44452��48227��51890��62611��48901��49047��62915�jR  �33428��33433��33789�jW  �54763��46496��46498��46497��46499��46500��46424��46442��46441��46443��46444��46445��46412��46419��46420��46421��46422��46423��46398��46399��46408��46409��46410��46411��34338��34439��34441��34443��34445��46397��34258��34332��34333��34334��34335��34336��34224��46478��21494��24981��24982��24983�j�  �33374�j�  j�  j�  j�  �31096��31097��33010��31449��34296��52460��31456�j�  �33140��33154��32714�et�bhzh|)��ubhm)��}�(hphhK ��h��R�(KKI��h!�]�(�FIX API�j�  j�  j�  �	SMART GUI�j�  j�  j�  j�  j�  �SELECT�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bhzh|)��ubhm)��}�(hphhK ��h��R�(KKI��h!�]�(�0.00��2,955.00��2,956.40��2,983.03�j�  j�  �2,977.00�j�  j�  �2,955.50��2,950.00�j�  �2,940.00��2,978.25��2,951.00��2,984.00�j�  j�  j�  j�  �2,983.00�j�  j�  �2,984.50�j�  j�  j�  �2,982.00�j�  j�  j�  j�  �2,981.00�j�  j�  j�  j�  j�  j�  �2,956.00�j�  j�  j�  j�  �2,953.50�j�  j�  j�  j�  j�  �	27,950.00��	28,000.00��	27,803.50��	27,791.00�j�  j�  �	27,800.00��	27,820.00��	27,790.00�j�  j�  �	28,023.00��2,139.50�j�  �2,142.00��3,742.00��3,738.85��9,477.00��9,476.00��9,455.00��9,458.85��9,460.35��9,460.50�et�bhzh|)��ubhm)��}�(hphhK ��h��R�(KKI��h!�]�(�
102.000000��0.000000�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bhzh|)��ubhhK ��h��R�(KKKI��h�f8�����R�(KhcNNNJ����J����K t�b�B�       @@      4@      4@      D@      D@      D@      D@      D@      D@      4@      �?      @      $@      D@      @      �?      �?      �?      �?      @       @      �?      �?      �?      �?      @      �?       @      �?      �?      @      �?      �?       @      �?      �?      �?      �?       @       @       @       @      �?       @      @      �?      �?      �?       @       @      �?      �?      2@       @       @      ,@      2@      �?       @       @      ,@      �?      @      �?      @      �?      �?      �?      �?      �?      �?      @      @     p�@                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                �t�bhhK ��h��R�(KKKI��h!�]�(�CT�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �
Cleared Trade�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �Aluminium High Grade�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �Primary Nickel�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �Lead�j�  j�  �Special High Grade Zinc�j�  �Copper A Grade�j�  j�  j�  j�  j�  �JUL22�G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �220706��220720�j�  j�  j�  j�  j�  j�  j�  �220819��3�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �220524�j�  �220818�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �220805�j�  j�  j�  �H�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �1643��1330��1317��1510�j�  j�  �1505�j�  j�  j�  �1306�j�  �R2�j�  �1602��1501�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �1500�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �1313��1314�j  j  j  j�  j   j   j   j   j   j   j�  �1458��0944��1114�j  j  j  �1305�j  j  j  j  �1252�j  j�  �1255��C2��1539��1236�j	  j  j  �1234�G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �10:30:00��12:46:00��13:49:00��14:12:00��14:40:00��15:54:00��14:16:00��14:17:00��15:58:00�j
  �12:06:00�j  �12:09:00�j  �15:02:00��14:02:00�j  j  j  j  �14:01:00�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �12:14:00��12:15:00�j  j  j  j  �12:13:00�j  j  j  j  j  �12:12:00�j  �09:27:00��10:14:00�j  j  j  j  j  j  j  j  �11:52:00�j   �12:03:00��11:56:00�j  �14:46:00��13:16:00�j#  �12:04:00�j%  j$  �10:29:00��12:46:00��13:49:00��14:12:00��14:39:00��15:53:00��14:16:00��14:17:00��15:57:00�j'  �12:06:00�j/  �12:08:00�j,  �15:02:00��14:01:00�j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  �14:00:00�j2  j2  j2  j2  j2  j3  j3  j3  j3  j3  j3  �12:13:00��12:14:00�j5  j5  j5  j3  j4  j4  j4  j4  j4  j4  �12:12:00�j2  �09:27:00��10:14:00�j8  j8  j7  �12:05:00�j8  j8  j8  j2  �11:52:00�j:  �12:03:00��11:55:00�j4  �14:45:00�j<  j=  �12:04:00�j>  �12:01:00�G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �10278661��10393941��10381652��10517161��10558311��10525442��10512081��10526092��10537802��10393952��10361412��10361452��10358461��10512092��10590262��10499331��10499351��10499341��10499361��10499371��10498731��10498961��10498951��10498971��10498981��10498991��10498611��10498681��10498691��10498701��10498711��10498721��10498491��10498501��10498571��10498581��10498591��10498601��10372601��10374121��10374141��10374161��10374181��10498481��10371711��10372541��10372551��10372561��10372571��10372581��10365522��10496811��10201222��10269241��10269261��10269281��10201231��10360871��10269252��10269272��10269292��10496822��10335031��10335041��10339082��10339042��10368001��10559492��10327131��10559501��10342782��10351301��10327142��No�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �Yes�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �0.010000�G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      et�bhm)��}�(hphhK ��h��R�(KKI��h!�]�(hxhxhxhxhxhxhxhxhxhx�TL�j�  hxhxj�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �MS�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  hxhxhxj�  j�  j�  hx�NP�j�  j�  j�  hxj�  j�  hxj�  hxhxhxhxhxhxhxet�bhzh|)��ube]�(h
h}�(hhhK ��h��R�(KK��h!�]�hRat�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hSat�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h(at�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h+at�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h,at�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h0at�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h1at�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h9at�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h:at�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h<at�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h?at�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hDat�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hFat�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hMat�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hNat�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hLhOet�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h&h'h)h-h/h6h7h=h@hAhBhChEhGhHhIhJhKhPet�bhUNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hQat�bhUNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hn�mgr_locs��builtins��slice���K-K.K��R�u}�(j�  h~j�  j�  K.K/K��R�u}�(j�  h�j�  j�  K KK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  K	K
K��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KK
K��R�u}�(j�  h�j�  j�  K
KK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j-  j�  j�  KKK��R�u}�(j�  j?  j�  j�  KKK��R�u}�(j�  jx  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j5  j�  j�  KKK��R�u}�(j�  j?  j�  j�  KKK��R�u}�(j�  jJ  j�  j�  KK K��R�u}�(j�  j�  j�  j�  K!K"K��R�u}�(j�  j�  j�  j�  K(K)K��R�u}�(j�  j�  j�  j�  K)K*K��R�u}�(j�  j�  j�  j�  K'K-K��R�u}�(j�  j�  j�  hhK ��h��R�(KK��hb�C�                            
                                                                "       #       $       %       &       +       �t�bu}�(j�  j�  j�  j�  K,K-K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.