import os
from pathlib import Path
from unittest.mock import patch

import pandas as pd
import pytest
from prefect import context
from swarm.task.auditor import Auditor

from swarm_tasks.order.transformations.lme.eod.lme_eod_transformations import (
    LmeEodTransformations,
)
from swarm_tasks.order.transformations.lme.eod.lme_eod_transformations import (
    TempColumns,
)

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")

# The source file is a pickle of the df which should be used as  the source dataframe
SOURCE_PICKLE_FILE = TEST_FILES_DIR.joinpath("source_frame.pkl")
EXPECTED_RESULT_PICKLE_PATH = TEST_FILES_DIR.joinpath("expected_target_df.pkl")


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


class TestLmeEodTransformations:
    """
    Test suite for Primary Transformations for the LME EOD Feed
    """

    @patch.object(
        LmeEodTransformations,
        "_get_executing_entity",
    )
    def test_end_to_end_lme_eod_transformations(self, mock_executing_entity, auditor):
        os.environ["SWARM_FILE_URL"] = str(SOURCE_PICKLE_FILE)
        source_frame = pd.read_pickle(SOURCE_PICKLE_FILE)
        mock_executing_entity.return_value = pd.DataFrame(
            data="lei:test_lme_executing_entity",
            index=source_frame.index,
            columns=[TempColumns.EXECUTING_ENTITY_WITH_LEI],
        )

        task = LmeEodTransformations(
            source_frame=source_frame,
            logger=context.get("logger"),
            auditor=auditor,
        )
        result = task.process()
        result = result.drop(["sourceKey"], axis=1)

        expected = pd.read_pickle(EXPECTED_RESULT_PICKLE_PATH)
        pd.testing.assert_frame_equal(left=result, right=expected)
