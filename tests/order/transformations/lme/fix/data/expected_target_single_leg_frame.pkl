���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKB��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��executionDetails.limitPrice��executionDetails.orderType��(_orderState.executionDetails.orderStatus��#_order.executionDetails.orderStatus��&executionDetails.outgoingOrderAddlInfo�� executionDetails.routingStrategy��executionDetails.stopPrice�� executionDetails.tradingCapacity��	_order.id��_orderState.id��marketIdentifiers.instrument��underlying_symbol_attribute��expiry_date_attribute��currency_attribute��venue_attribute��asset_class_attribute��_order.__meta_model__��_orderState.__meta_model__��orderIdentifiers.orderIdCode��!orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��"_orderState.priceFormingData.price��._orderState.priceFormingData.remainingQuantity��)_order.priceFormingData.remainingQuantity��+_orderState.priceFormingData.tradedQuantity��reportDetails.transactionRefNo��sourceIndex��	sourceKey��&_orderState.timestamps.tradingDateTime��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��#transactionDetails.buySellIndicator��transactionDetails.quantity��#transactionDetails.quantityCurrency��$_orderState.transactionDetails.price��transactionDetails.priceAverage�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��#transactionDetails.quantityNotation��"transactionDetails.tradingCapacity��._orderState.transactionDetails.tradingDateTime��transactionDetails.venue�� transactionDetails.ultimateVenue��__newo_in_file__��__expiry_date__��__is_created_through_fallback__��__best_ex_asset_class_main__��__asset_class__��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers�et�b�name�Nu��R�h
�pandas.core.indexes.range��
RangeIndex���}�(hhN�start�K �stop�K�step�Ku��R�e]�(hhK ��h��R�(KKK��h�b1�����R�(Kh"NNNJ����J����K t�b�C�t�bhhK ��h��R�(KK	K��h�f8�����R�(K�<�NNNJ����J����K t�b�C�     �@     �@      .@      .@     �@     �@              (@      .@      .@      �?      �?      �?      �?     �@     �@     �@     �@�t�bhhK ��h��R�(KKK��h�i8�����R�(Kh�NNNJ����J����K t�b�C               �t�bhhK ��h��R�(KK7K��h!�]�(�2�h�h�h��LME Select Fix�h��
2023-11-07��
2023-11-07��SELL�h��Limit�h��FILL��PARF��NEWO�h��House�h��Algorithmic�h��pandas._libs.missing��NA���h��MTCH�h��ON-CA-20231107-001068�h�h�h�]�(}�(�labelId��XLMECAUSDFF2024-02-07 00:00:00��path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�u}�(h��XLMECAUSDFF2024-02 00:00:00�h�h�h�h�ue]�(}�(h��XLMECAUSDFF2024-02-07 00:00:00�h�h�h�h�u}�(h��XLMECAUSDFF2024-02 00:00:00�h�h�h�h�ue�CA�h��20240207�h��USD�h��XLME�h��future�h��Order�h
OrderState�h�h�h�h�h��TACA20231107X0000043A��TACA20231107X0000032A���s3://icbc.uat.steeleye.co/fix/order-feed-lme-fix/20231108/037c43f51e90f158a2fcdba3ed46239c413cf8e2f4f9d3cd2f9119c20b52056f_117.fix���s3://icbc.uat.steeleye.co/fix/order-feed-lme-fix/20231108/0849c68059693edd856ae06e4b0cb3637289dcba741f90cfca7e15ec02aaeb0a_112.fix��2023-11-07T01:00:49.560000Z��2023-11-07T01:00:49.471000Z�h�h�h�h�h�h�h�h�h�h�h�h��MONE�hʌUNIT�h�h�h�h�h�h�h�h�h���h�h��Other Instruments�h�h�h�]�(}�(h��id:lme�h��buyer�h�h��ARRAY���R�u}�(h��
id:6443511�h��seller�h�h�u}�(h��id:lme�h��counterparty�h�h�u}�(h��	id:100022�h��:tradersAlgosWaiversIndicators.investmentDecisionWithinFirm�h�h�u}�(h��	id:100022�h��1tradersAlgosWaiversIndicators.executionWithinFirm�h�h�u}�(h��
id:6443511�h��clientIdentifiers.client�h�h�u}�(h��
id:6443511�h��trader�h�h�ue]�(}�(h��id:lme�h�h�h�h�u}�(h��
id:6443511�h�h�h�h�u}�(h��id:lme�h�h�h�h�u}�(h��	id:100022�h�h�h�h�u}�(h��	id:100022�h�h�h�h�u}�(h��
id:6443511�h�h�h�h�u}�(h��
id:6443511�h�h�h�h�ueh�h��id:lme��id:lme��
id:6443511��
id:6443511��id:lme��id:lme�h�h�h�h��	id:100022��	id:100022��	id:100022��	id:100022��
id:6443511��
id:6443511��
id:6443511��
id:6443511�]�(h�h�h�h�h�h�h�h�h�e]�(h�h�h�h�h�h�h�h�h�eet�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�hXat�bhhNu��R�h
h}�(hhhK ��h��R�(KK	��h!�]�(h*h>h?h@hAhBhKhMhNet�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hDat�bhhNu��R�h
h}�(hhhK ��h��R�(KK7��h!�]�(h%h&h'h(h)h+h,h-h.h/h0h1h2h3h4h5h6h7h8h9h:h;h<h=hChEhFhGhHhIhJhLhOhPhQhRhShThUhVhWhYhZh[h\h]h^h_h`hahbhchdhehfet�bhhNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hw�mgr_locs��builtins��slice���K3K4K��R�u}�(j2  h�j3  hhK ��h��R�(KK	��h��CH                                          &       (       )       �t�bu}�(j2  h�j3  j6  KK K��R�u}�(j2  h�j3  hhK ��h��R�(KK7��h��B�                                                           	       
                     
                                                                                                   !       "       #       $       %       '       *       +       ,       -       .       /       0       1       2       4       5       6       7       8       9       :       ;       <       =       >       ?       @       A       �t�bueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.