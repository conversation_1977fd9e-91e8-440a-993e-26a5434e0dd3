import logging
import os
from pathlib import Path
from unittest.mock import patch

import pandas as pd
import pytest
from mock import MagicMock
from prefect.engine.signals import SKIP
from swarm.task.auditor import Auditor

from swarm_tasks.io.read.aws import df_from_s3_csv
from swarm_tasks.order.transformations.metatrader.mt5.mt5_deals_transformations import (
    MT5DealsTransformations,
)
from swarm_tasks.order.transformations.metatrader.mt5.oanda.mt5_oanda_deals_transformations import (
    MT5OandaDealsTransformations,
)
from swarm_tasks.order.transformations.metatrader.mt5.onefinancial.mt5_onefinancial_deals_transformations import (
    MT5OneFinancialDealsTransformations,
)
from swarm_tasks.order.transformations.metatrader.mt5.static import TempPartyIDs
from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data/")


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


@pytest.fixture()
def log():
    logger = logging.getLogger("dummy")
    logger.setLevel(logging.INFO)
    return logger


@pytest.fixture()
def mock_get_tenant_lei(mocker, source_deals_frame):
    mock_get_tenant_lei = mocker.patch.object(GetTenantLEI, "process")
    tenant_lei_df = pd.DataFrame(index=source_deals_frame.index)
    tenant_lei_df = tenant_lei_df.assign(
        **{TempPartyIDs.EXECUTING_ENTITY: "lei:sample_lei"}
    )
    mock_get_tenant_lei.return_value = tenant_lei_df
    return mock_get_tenant_lei


class TestMT5OrderDealsTransformations:
    """
    Test for MT5OrderStateTransformations
    """

    @patch.object(df_from_s3_csv.DfFromS3Csv, "process")
    def test_end_to_end_transformations(
        self,
        mock_df_from_s3: MagicMock,
        mock_get_tenant_lei: MagicMock,
        source_deals_frame: pd.DataFrame,
        source_instrument_frame: pd.DataFrame,
        expected_result_deals: pd.DataFrame,
        auditor: Auditor,
        log: logging.Logger,
    ):
        os.environ["SWARM_FILE_URL"] = str(f"{TEST_FILES_DIR}sample_deals.csv")
        mock_df_from_s3.return_value = source_instrument_frame
        task_deals_mt5 = MT5DealsTransformations(
            source_frame=source_deals_frame, auditor=auditor, logger=log
        )
        result_order_state_mt5 = task_deals_mt5.process()

        pd.testing.assert_frame_equal(
            result_order_state_mt5.drop(["sourceKey"], axis=1).reindex(
                sorted(result_order_state_mt5.columns), axis=1
            ),
            expected_result_deals.drop(["sourceKey"], axis=1).reindex(
                sorted(expected_result_deals.columns), axis=1
            ),
        )

    @patch.object(df_from_s3_csv.DfFromS3Csv, "process")
    def test_end_to_end_transformations_onefinancial(
        self,
        mock_df_from_s3: MagicMock,
        mock_get_tenant_lei: MagicMock,
        source_deals_frame: pd.DataFrame,
        source_instrument_frame: pd.DataFrame,
        expected_result_deals_onefinancial: pd.DataFrame,
        auditor: Auditor,
        log: logging.Logger,
    ):
        os.environ["SWARM_FILE_URL"] = str(f"{TEST_FILES_DIR}sample_deals.csv")
        mock_df_from_s3.return_value = source_instrument_frame
        task_order_mt5_one_financial = MT5OneFinancialDealsTransformations(
            source_frame=source_deals_frame, auditor=auditor, logger=log
        )
        result_order_mt5 = task_order_mt5_one_financial.process()

        pd.testing.assert_frame_equal(
            result_order_mt5.drop(["sourceKey"], axis=1).reindex(
                sorted(result_order_mt5.columns), axis=1
            ),
            expected_result_deals_onefinancial.drop(["sourceKey"], axis=1).reindex(
                sorted(expected_result_deals_onefinancial.columns), axis=1
            ),
        )

    @patch.object(df_from_s3_csv.DfFromS3Csv, "process")
    def test_end_to_end_transformations_oanda(
        self,
        mock_df_from_s3: MagicMock,
        source_deals_frame: pd.DataFrame,
        source_instrument_frame: pd.DataFrame,
        expected_result_deals_oanda: pd.DataFrame,
        auditor: Auditor,
        log: logging.Logger,
    ):
        os.environ["SWARM_FILE_URL"] = str(
            f"{TEST_FILES_DIR}mt5-ogm-deals-2022-02-14.csv"
        )
        mock_df_from_s3.return_value = source_instrument_frame
        task_order_mt5_oanda = MT5OandaDealsTransformations(
            source_frame=source_deals_frame, auditor=auditor, logger=log
        )
        result_order_mt5 = task_order_mt5_oanda.process()

        pd.testing.assert_frame_equal(
            result_order_mt5.drop(["sourceKey"], axis=1).reindex(
                sorted(result_order_mt5.columns), axis=1
            ),
            expected_result_deals_oanda.drop(["sourceKey"], axis=1).reindex(
                sorted(expected_result_deals_oanda.columns), axis=1
            ),
        )

    @patch.object(df_from_s3_csv.DfFromS3Csv, "process")
    def test_skip_execution(
        self,
        mock_df_from_s3: MagicMock,
        source_deals_frame: pd.DataFrame,
        source_instrument_frame: pd.DataFrame,
        expected_result_deals: pd.DataFrame,
        auditor: Auditor,
        log: logging.Logger,
    ):
        os.environ["SWARM_FILE_URL"] = str(f"{TEST_FILES_DIR}sample_deals.csv")
        mock_df_from_s3.return_value = pd.DataFrame(index=source_deals_frame.index)
        task_order_mt5 = MT5DealsTransformations(
            source_frame=source_deals_frame, auditor=auditor, logger=log
        )
        with pytest.raises(SKIP) as e:
            task_order_mt5.process()

        assert e.type == SKIP
