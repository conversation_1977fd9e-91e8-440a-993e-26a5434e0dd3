���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKV��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��(_orderState.executionDetails.orderStatus��#_order.executionDetails.orderStatus��&executionDetails.outgoingOrderAddlInfo�� executionDetails.tradingCapacity��	_order.id��_orderState.id��_order.isSynthetic��_orderState.isSynthetic��_orderState.__meta_model__��_order.__meta_model__��"orderIdentifiers.aggregatedOrderId��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��-_orderState.orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��"_orderState.priceFormingData.price��"priceFormingData.remainingQuantity��+_orderState.priceFormingData.tradedQuantity��reportDetails.transactionRefNo��sourceIndex��	sourceKey��)_orderState.timestamps.orderStatusUpdated��$_order.timestamps.orderStatusUpdated��timestamps.tradingDateTime��#transactionDetails.buySellIndicator��#transactionDetails.commissionAmount��!transactionDetails.positionEffect��$_orderState.transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��transactionDetails.recordType��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers��__EXECUTING_ENTITY__��
__CLIENT__��__INVESTMENT_DEC_WITHIN_FIRM__��TEMP_XXXX_VENUE��__TEMP_BUY_SELL_INDICATOR__��__TEMP_INST_ID_SYMBOL__��=__TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_CONTRACT_TYPE__��<__TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_PRODUCT_TYPE__��;__TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_ASSET_CLASS__��?__TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_BASE_PRODUCT__��F__TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_FURTHER_SUB_PRODUCT__��__TEMP_INST_ID_DELIVERY_TYPE__��!__TEMP_INST_ID_PRICE_MULTIPLIER__��$__TEMP_INST_ID_EXT_EXCHANGE_SYMBOL__��(__TEMP_INST_ID_INSTRUMENT_ID_CODE_TYPE__��__TEMP_INST_ID_PRICE_NOTATION__��*__TEMP_INST_ID_INSTRUMENT_CLASSIFICATION__��%__TEMP_INST_ID_INSTRUMENT_FULL_NAME__��)__TEMP_INST_ID_UNDERLYING_INSTRUMENT_ID__��&__TEMP_INST_ID_UNDERLYING_INDEX_NAME__��__TEMP_INST_ASSET_CLASS__��(__TEMP_INST_UNIQUE_ID_M_ISIN_POPULATED__��TEMP_ASSET_CLASS��(__TEMP_INST_UNIQUE_ID_T_ISIN_POPULATED__��*__TEMP_INST_UNIQUE_ID_ISIN_NOT_POPULATED__��#__TEMP_INST_UNIQUE_ID_CASH_EQUITY__��%__TEMP_INST_ID_INSTRUMENT_UNIQUE_ID__��__TEMP_PARENT_META_MODEL__��$__TEMP_IS_CREATED_THROUGH_FALLBACK__��
CONTRACT_SIZE�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(h�numpy.core.numeric��_frombuffer���(�                      �h�i8�����R�(K�<�NNNJ����J����K t�bK���C�t�R�h|Nu��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h!�]�(�US2000��XAUUSD�et�b�_dtype�h��StringDtype���)��ubh�(�         �h�b1�����R�(Kh"NNNJ����J����K t�bKK��h�t�R�h�(�`             @{�G�z�?�(\�B��@�Q��=�@      @{�G�z�?�(\�B��@�Q��=�@      @{�G�z�?      �?      Y@�h�f8�����R�(Kh�NNNJ����J����K t�bKK��h�t�R�h�(�                                     �h�KK��h�t�R�hhK ��h��R�(KKJK��h!�]�(�1��2�h�h��MetaTrader5 - Deals�h��
2022-02-14��
2022-02-14��BUYI��SELL��FILL�hŌNEWO�hƌ[Gateway - 1, Price Gateway - 2019.404, Login - 4008001, mt5_symbol - US2000, FV_adj - 1.589��VGateway - 1, Price Gateway - 1871.38, Login - 4006934, mt5_symbol - XAUUSD, FV_adj - 0��DEAL�hɌ263524|263524|4008001��261886|263523|4006934�h�hˌ
OrderState�ȟOrder�h͌263524��261886��34676280��34676274�h�hˌ
263524|220646��
263523|220645�K K �
263524|220646��
263523|220645���/Users/<USER>/PycharmProjects/swarm/swarm-tasks/tests/order/transformations/metatrader/mt5/datamt5-ogm-deals-2022-02-14.csv�h֌2022-02-14T21:54:19.268000Z��2022-02-14T21:51:56.624000Z�h�h�h�h�h�hČ0�hٌOpen��Close��USD��USD��MONE�hތpandas._libs.missing��NA���h�UNIT�h�Client Side�h�h�h�h�h،XXXX�h�h�h�]�}�(�labelId��US Russ 2000-indices-cfd��path��instrumentDetails.instrument��type��se_elastic_schema.static.market��IdentifierType����OBJECT���R�ua]�}�(h�Gold-commodities-cfd�h�h�h�h�ua]�(}�(h�
id:4008001�h�buyer�h�h�ARRAY���R�u}�(h�id:ogm�h�reportDetails.executingEntity�h�h�u}�(h�id:ogm�h�seller�h�h�u}�(h�id:ogm�h�sellerDecisionMaker�h�h�u}�(h�id:ogm�h�counterparty�h�h�u}�(h�
id:4008001�h�clientIdentifiers.client�h�h�u}�(h�
id:4008001�h�trader�h�h�ue]�(}�(h�id:ogm�h�h�h�h�u}�(h�id:ogm�h�buyerDecisionMaker�h�h�u}�(h�id:ogm�h�h�h�h�u}�(h�
id:4006934�h�j  h�h�u}�(h�id:ogm�h�j  h�h�u}�(h�
id:4006934�h�j
  h�h�u}�(h�
id:4006934�h�j
  h�h�ue�id:ogm��id:ogm��
id:4008001��id:ogm��id:ogm��
id:4006934��id:ogm��id:ogm�h�id:ogm��id:ogm�h�h�h�h�h�
id:4008001��
id:4006934��
id:4008001��
id:4006934�]�(h�h�h�h�j  j  j  j  e]�(h�j  j  j  j  j  j  j  e�id:ogm�j.  �
id:4008001��
id:4006934�h�h�h�h�h�hČCD�j1  �CFD�j2  �EQ��CO�h�ME�h�PR��CASH�j7  �US Russ 2000��Gold��OTHR�j:  h�hތJEIXCC��JTKXCC�h�h�h�h�US2000�h�j2  j2  �
XXXXTBCUSDCFD��
XXXXUSDCFD�� �j@  �
XXXXUSDCFD��
XXXXUSDCFD��
XXXXUS2000CFD��
XXXXXAUUSDCFD��USDXXXX��USDXXXX�jC  jD  h�h�et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�hbat�bh|Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h0h1hyet�bh|Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h8h9h;hEhHhzet�bh|Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h=hiet�bh|Nu��R�h
h}�(hhhK ��h��R�(KKJ��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h2h3h4h5h6h7h:h<h>h?h@hAhBhChDhFhGhIhJhKhLhMhNhOhPhQhRhShThUhVhWhXhYhZh[h\h]h^h_h`hahchdhehfhghhhjhkhlhmhnhohphqhrhshthuhvhwhxet�bh|Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs��builtins��slice���K=K>K��R�u}�(j}  h�j~  h�(�                     T       �h�K��h�t�R�u}�(j}  h�j~  h�(�0                                    #       U       �h�K��h�t�R�u}�(j}  h�j~  j�  KKpK,��R�u}�(j}  h�j~  h�(�P                                                                      	       
       
                                                                                                         !       "       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       3       4       5       6       7       8       9       :       ;       <       >       ?       @       A       B       C       E       F       G       H       I       J       K       L       M       N       O       P       Q       R       S       �h�KJ��h�t�R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.