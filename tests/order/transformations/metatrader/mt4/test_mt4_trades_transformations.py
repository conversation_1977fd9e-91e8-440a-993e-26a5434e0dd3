import logging
import os
from pathlib import Path
from unittest.mock import patch

import pandas as pd
import pytest
from mock import MagicMock
from prefect.engine.signals import SKIP
from swarm.task.auditor import Auditor

from swarm_tasks.io.read.aws import df_from_s3_csv
from swarm_tasks.order.transformations.metatrader.mt4.oanda.mt4_oanda_trades_transformations import (
    MT4OandaTradesTransformations,
)
from swarm_tasks.order.transformations.metatrader.mt4.oanda.mt4_oanda_trades_transformations import (
    MT4TradesTransformations,
)
from swarm_tasks.order.transformations.metatrader.mt4.onefinancial.mt4_onefinancial_trades_transformations import (
    MT4OneFinancialTradesTransformations,
)


SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data/")


class TestMT4TradesTransformations:
    """
    Test for MT4TradesTransformations
    """

    @patch.object(df_from_s3_csv.DfFromS3Csv, "process")
    def test_end_to_end_transformations(
        self,
        mock_df_from_s3: MagicMock,
        source_trades_frame: pd.DataFrame,
        source_instrument_frame: pd.DataFrame,
        expected_result: pd.DataFrame,
        auditor: Auditor,
        log: logging.Logger,
    ):
        os.environ["SWARM_FILE_URL"] = str(f"{TEST_FILES_DIR}sample_trades.csv")
        mock_df_from_s3.return_value = source_instrument_frame
        task_mt4_trades = MT4TradesTransformations(
            source_frame=source_trades_frame, auditor=auditor, logger=log
        )
        result_mt4_trades = task_mt4_trades.process()

        pd.testing.assert_frame_equal(
            result_mt4_trades.drop(["sourceKey"], axis=1),
            expected_result.drop(["sourceKey"], axis=1),
        )

    @patch.object(df_from_s3_csv.DfFromS3Csv, "process")
    def test_end_to_end_transformations_onefinancial(
        self,
        mock_df_from_s3: MagicMock,
        source_trades_frame: pd.DataFrame,
        source_instrument_frame: pd.DataFrame,
        expected_result_onefinancial: pd.DataFrame,
        auditor: Auditor,
        log: logging.Logger,
    ):
        os.environ["SWARM_FILE_URL"] = str(f"{TEST_FILES_DIR}sample_trades.csv")
        mock_df_from_s3.return_value = source_instrument_frame
        task_order_mt4_one_financial = MT4OneFinancialTradesTransformations(
            source_frame=source_trades_frame, auditor=auditor, logger=log
        )
        result_mt4_trades = task_order_mt4_one_financial.process()

        pd.testing.assert_frame_equal(
            result_mt4_trades.drop(["sourceKey"], axis=1),
            expected_result_onefinancial.drop(["sourceKey"], axis=1),
        )

    @patch.object(df_from_s3_csv.DfFromS3Csv, "process")
    def test_end_to_end_transformations_oanda(
        self,
        mock_df_from_s3: MagicMock,
        source_trades_frame: pd.DataFrame,
        source_instrument_frame: pd.DataFrame,
        expected_result_oanda: pd.DataFrame,
        auditor: Auditor,
        log: logging.Logger,
    ):
        os.environ["SWARM_FILE_URL"] = str(
            f"{TEST_FILES_DIR}mt5-ogm-deals-2022-02-14.csv"
        )
        mock_df_from_s3.return_value = source_instrument_frame
        task_mt4_trades_oanda = MT4OandaTradesTransformations(
            source_frame=source_trades_frame, auditor=auditor, logger=log
        )
        result_mt4_trades_oanda = task_mt4_trades_oanda.process()

        pd.testing.assert_frame_equal(
            result_mt4_trades_oanda.drop(["sourceKey"], axis=1),
            expected_result_oanda.drop(["sourceKey"], axis=1),
        )

    @patch.object(df_from_s3_csv.DfFromS3Csv, "process")
    def test_skip_execution(
        self,
        mock_df_from_s3: MagicMock,
        source_trades_frame: pd.DataFrame,
        source_instrument_frame: pd.DataFrame,
        expected_result: pd.DataFrame,
        auditor: Auditor,
        log: logging.Logger,
    ):
        os.environ["SWARM_FILE_URL"] = str(f"{TEST_FILES_DIR}sample_trades.csv")
        mock_df_from_s3.return_value = pd.DataFrame(index=source_trades_frame.index)
        task_mt4_trades = MT4OandaTradesTransformations(
            source_frame=source_trades_frame, auditor=auditor, logger=log
        )
        with pytest.raises(SKIP) as e:
            task_mt4_trades.process()

        assert e.type == SKIP
