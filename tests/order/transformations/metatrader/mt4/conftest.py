import logging
from pathlib import Path

import pandas as pd
import pytest
from swarm.task.auditor import Auditor

from swarm_tasks.order.transformations.metatrader.mt4.static import SourceTradesColumns

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


@pytest.fixture()
def log():
    logger = logging.getLogger("dummy")
    logger.setLevel(logging.INFO)
    return logger


@pytest.fixture()
def source_trades_frame():
    df = pd.read_csv(f"{TEST_FILES_DIR}/sample_trades.csv", dtype="str")
    for col in [
        SourceTradesColumns.CLOSE_PRICE,
        SourceTradesColumns.OPEN_PRICE,
        SourceTradesColumns.VOLUME,
    ]:
        df.loc[:, col] = df.loc[:, col].astype(float)
    return df


@pytest.fixture()
def source_instrument_frame():
    instrument_data_path = f"{TEST_FILES_DIR}/InstrumentData.csv".replace("mt4", "mt5")
    df = pd.read_csv(instrument_data_path, dtype="str")
    return df


@pytest.fixture()
def expected_result():
    df = pd.read_pickle(f"{TEST_FILES_DIR}/expected_result.pkl")
    return df


@pytest.fixture()
def expected_result_onefinancial():
    df = pd.read_pickle(f"{TEST_FILES_DIR}/expected_result_onefinancial.pkl")
    return df


@pytest.fixture()
def expected_result_oanda() -> pd.DataFrame:
    df = pd.read_pickle(f"{TEST_FILES_DIR}/expected_result_oanda.pkl")
    return df
