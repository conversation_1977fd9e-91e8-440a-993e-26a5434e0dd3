��
F      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK>��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�Allocations��
AssetClass��	AssetType��BloombergID��CUSIP��Country��CreateDateTime_x��ID_x��ISIN��Industry��
IndustryGroup��Issuer��LastModifiedDateTime_x��Limit_x��
LocalCurrency��Note��OriginalOrderDateTime��OriginalOrderID��OriginalQuantity��PriceMultiplier��
Quantity_x��Sector��SecurityName��SettlementDate��Side��Source��Status_x��SubIndustry��Symbol��SystemCurrency��
TotalQuantity��	TradeDate��CreateDateTime_y��DestinationCode��DestinationDisplayName��DestinationLongName��ExecutionPrice��
Executions��ID_y��LastModifiedDateTime_y��Limit_y��
Quantity_y��SettlementCurrency��Status_y��Trader��__source_index__��DestinationLegalName��ExpirationDate��OccCode��LimitPrice_y��UnderlyingSymbol��Routes��UnderlyingISIN��StrikePrice��LimitPrice_x��
LimitPrice��CreateDateTime��ID��LastModifiedDateTime��Limit��Quantity��CounterpartyCode�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(h�numpy.core.numeric��_frombuffer���(�                                     �h�i8�����R�(K�<�NNNJ����J����K t�bK���C�t�R�hd�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(X�  [{'AllocationBookType': 'Custodian', 'AllocationIdentifier': 'Order:273010|Route:233885|Portfolio:238 Plan Associates LLC|Custodian:SSB', 'BaseAccountCurrency': 'USD', 'Custodian': 'SSB', 'ExecBrokerCode': 'IIFL', 'ExecBrokerDisplayName': 'IIFL', 'ExecBrokerLongName': 'IIFL Securities Limited', 'ExecutedQuantity': 7840, 'GrossAmount': 8224118.448, 'Manager': 'SMA', 'NetAmount': 8251751.********, 'Portfolio': '2', 'PortfolioName': '238 Plan Associates LLC', 'Price': 1048.9947, 'Quantity': 7840, 'TotalCommissions': 16448.236896, 'TotalFees': 11184.********}, {'AllocationBookType': 'Custodian', 'AllocationIdentifier': 'Order:273010|Route:233885|Portfolio:Cassini Partners, L.P.|Custodian:MSCO', 'BaseAccountCurrency': 'USD', 'Custodian': 'MSCO', 'ExecBrokerCode': 'IIFL', 'ExecBrokerDisplayName': 'IIFL', 'ExecBrokerLongName': 'IIFL Securities Limited', 'ExecutedQuantity': 32160, 'GrossAmount': ********.552, 'Manager': 'SMA', 'NetAmount': ********.4016947, 'Portfolio': '3', 'PortfolioName': 'Cassini Partners, L.P.', 'Price': 1048.9947, 'Quantity': 32160, 'TotalCommissions': 67471.339104, 'TotalFees': 45880.********}, {'AllocationBookType': 'Strategy', 'AllocationIdentifier': 'Order:273010|Route:233885|Portfolio:238 Plan Associates LLC|Strategy:TRADING-NONE', 'BaseAccountCurrency': 'USD', 'ExecBrokerCode': 'IIFL', 'ExecBrokerDisplayName': 'IIFL', 'ExecBrokerLongName': 'IIFL Securities Limited', 'ExecutedQuantity': 7840, 'GrossAmount': 8224118.448, 'Manager': 'SMA', 'NetAmount': 8251751.********, 'Portfolio': '2', 'PortfolioName': '238 Plan Associates LLC', 'Price': 1048.9947, 'Quantity': 7840, 'Strategy1': 'TRADING', 'TotalCommissions': 16448.236896, 'TotalFees': 11184.********}, {'AllocationBookType': 'Strategy', 'AllocationIdentifier': 'Order:273010|Route:233885|Portfolio:Cassini Partners, L.P.|Strategy:TRADING-NONE', 'BaseAccountCurrency': 'USD', 'ExecBrokerCode': 'IIFL', 'ExecBrokerDisplayName': 'IIFL', 'ExecBrokerLongName': 'IIFL Securities Limited', 'ExecutedQuantity': 32160, 'GrossAmount': ********.552, 'Manager': 'SMA', 'NetAmount': ********.4016947, 'Portfolio': '3', 'PortfolioName': 'Cassini Partners, L.P.', 'Price': 1048.9947, 'Quantity': 32160, 'Strategy1': 'TRADING', 'TotalCommissions': 67471.339104, 'TotalFees': 45880.********}]�X�  [{'AllocationBookType': 'Custodian', 'AllocationIdentifier': 'Order:273012|Route:234481|Portfolio:Habrok India Master|Custodian:Kotak', 'BaseAccountCurrency': 'USD', 'Custodian': 'Kotak', 'ExecBrokerCode': 'IDFC', 'ExecBrokerDisplayName': 'DAM Capital', 'ExecBrokerLegalName': 'DAM Capital Advisors Limited', 'ExecBrokerLongName': 'DAM Capital Advisors Limited', 'ExecutedQuantity': 45000, 'GrossAmount': ********, 'Manager': 'HIN', 'NetAmount': ********.2656, 'Portfolio': '1', 'PortfolioName': 'Habrok India Master', 'Price': 607.862, 'Quantity': 45000, 'TotalCommissions': 54707.58, 'TotalFees': 37201.1544}, {'AllocationBookType': 'Strategy', 'AllocationIdentifier': 'Order:273012|Route:234481|Portfolio:Habrok India Master|Strategy:TRADING-NONE', 'BaseAccountCurrency': 'USD', 'ExecBrokerCode': 'IDFC', 'ExecBrokerDisplayName': 'DAM Capital', 'ExecBrokerLegalName': 'DAM Capital Advisors Limited', 'ExecBrokerLongName': 'DAM Capital Advisors Limited', 'ExecutedQuantity': 45000, 'GrossAmount': ********, 'Manager': 'HIN', 'NetAmount': ********.2656, 'Portfolio': '1', 'PortfolioName': 'Habrok India Master', 'Price': 607.862, 'Quantity': 45000, 'Strategy1': 'TRADING', 'TotalCommissions': 54707.58, 'TotalFees': 37201.1544}]�X6  [{'AllocationBookType': 'Custodian', 'AllocationIdentifier': 'Order:272717|Route:NONE|Portfolio:238 Plan Associates LLC|Custodian:SSB', 'BaseAccountCurrency': 'USD', 'Custodian': 'SSB', 'ExecutedQuantity': 0, 'GrossAmount': 0, 'Manager': 'SMA', 'NetAmount': 0, 'Portfolio': '2', 'PortfolioName': '238 Plan Associates LLC', 'Price': 0, 'Quantity': 3082, 'TotalCommissions': 0, 'TotalFees': 0}, {'AllocationBookType': 'Custodian', 'AllocationIdentifier': 'Order:272717|Route:NONE|Portfolio:Cassini Partners, L.P.|Custodian:MSCO', 'BaseAccountCurrency': 'USD', 'Custodian': 'MSCO', 'ExecutedQuantity': 0, 'GrossAmount': 0, 'Manager': 'SMA', 'NetAmount': 0, 'Portfolio': '3', 'PortfolioName': 'Cassini Partners, L.P.', 'Price': 0, 'Quantity': 14094, 'TotalCommissions': 0, 'TotalFees': 0}, {'AllocationBookType': 'Strategy', 'AllocationIdentifier': 'Order:272717|Route:NONE|Portfolio:238 Plan Associates LLC|Strategy:TRADING-NONE', 'BaseAccountCurrency': 'USD', 'ExecutedQuantity': 0, 'GrossAmount': 0, 'Manager': 'SMA', 'NetAmount': 0, 'Portfolio': '2', 'PortfolioName': '238 Plan Associates LLC', 'Price': 0, 'Quantity': 3082, 'Strategy1': 'TRADING', 'TotalCommissions': 0, 'TotalFees': 0}, {'AllocationBookType': 'Strategy', 'AllocationIdentifier': 'Order:272717|Route:NONE|Portfolio:Cassini Partners, L.P.|Strategy:TRADING-NONE', 'BaseAccountCurrency': 'USD', 'ExecutedQuantity': 0, 'GrossAmount': 0, 'Manager': 'SMA', 'NetAmount': 0, 'Portfolio': '3', 'PortfolioName': 'Cassini Partners, L.P.', 'Price': 0, 'Quantity': 14094, 'Strategy1': 'TRADING', 'TotalCommissions': 0, 'TotalFees': 0}]�X8  [{'AllocationBookType': 'Custodian', 'AllocationIdentifier': 'Order:272718|Route:NONE|Portfolio:238 Plan Associates LLC|Custodian:SSB', 'BaseAccountCurrency': 'USD', 'Custodian': 'SSB', 'ExecutedQuantity': 0, 'GrossAmount': 0, 'Manager': 'SMA', 'NetAmount': 0, 'Portfolio': '2', 'PortfolioName': '238 Plan Associates LLC', 'Price': 0, 'Quantity': 11122, 'TotalCommissions': 0, 'TotalFees': 0}, {'AllocationBookType': 'Custodian', 'AllocationIdentifier': 'Order:272718|Route:NONE|Portfolio:Cassini Partners, L.P.|Custodian:MSCO', 'BaseAccountCurrency': 'USD', 'Custodian': 'MSCO', 'ExecutedQuantity': 0, 'GrossAmount': 0, 'Manager': 'SMA', 'NetAmount': 0, 'Portfolio': '3', 'PortfolioName': 'Cassini Partners, L.P.', 'Price': 0, 'Quantity': 40227, 'TotalCommissions': 0, 'TotalFees': 0}, {'AllocationBookType': 'Strategy', 'AllocationIdentifier': 'Order:272718|Route:NONE|Portfolio:238 Plan Associates LLC|Strategy:TRADING-NONE', 'BaseAccountCurrency': 'USD', 'ExecutedQuantity': 0, 'GrossAmount': 0, 'Manager': 'SMA', 'NetAmount': 0, 'Portfolio': '2', 'PortfolioName': '238 Plan Associates LLC', 'Price': 0, 'Quantity': 11122, 'Strategy1': 'TRADING', 'TotalCommissions': 0, 'TotalFees': 0}, {'AllocationBookType': 'Strategy', 'AllocationIdentifier': 'Order:272718|Route:NONE|Portfolio:Cassini Partners, L.P.|Strategy:TRADING-NONE', 'BaseAccountCurrency': 'USD', 'ExecutedQuantity': 0, 'GrossAmount': 0, 'Manager': 'SMA', 'NetAmount': 0, 'Portfolio': '3', 'PortfolioName': 'Cassini Partners, L.P.', 'Price': 0, 'Quantity': 40227, 'Strategy1': 'TRADING', 'TotalCommissions': 0, 'TotalFees': 0}]�et�b�_dtype�h|�StringDtype���)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�Equity�h�h�h�et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�Common Stock�h�h�h�et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�DCMS IS��IIFL IS��EIM IS�h�et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�	Y2023T132��	Y3914X109��	Y2251M148�h�et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�IND�h�h�h�et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�2023-12-28T07:55:43.427Z��2024-01-02T07:04:12.332Z��2023-12-28T07:55:16.207Z�h�et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�273010��273012��272717��272718�et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�INE499A01024��INE530B01024��INE066A01021�h�et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�	Chemicals��Financial Services��Automobiles�h�et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�	Materials��Financial Services��Automobiles & Components�j  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�DCM SHRIRAM LTD��IIFL FINANCE LTD��
EICHER MOTORS�j  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�False�j  j  j  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�INR�j$  j$  j$  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�#Take EIM to 3.5% and add it to DCMS��)1Jan -= buy DCMS Sell IIFL IN same amount�j.  j.  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�2023-12-28T00:00:00.000Z��2024-01-02T00:00:00.000Z�j9  j9  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�274212��273009��272125�jD  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�165000��145000��45000�jP  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�	Materials��
Financials��Consumer Discretionary�j\  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�DCM SHRIRAM LTD��IIFL FINANCE LTD��
EICHER MOTORS�jh  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�
2024-01-04�jt  �
2024-01-11�ju  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�Buy��Sell�j�  j  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�EzeIMS�j�  j�  j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�Complete�j�  �New�j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�Diversified Chemicals��)Commercial & Residential Mortgage Finance��Motorcycle Manufacturers�j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�DCMSHRIRAM.EQ.NIN��IIFL.EQ.NIN��EICHERMOT.EQ.NIN�j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�USD�j�  j�  j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�165000��190000��45000�j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�
2024-01-03�j�  �
2024-01-10�j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�2024-01-03T09:43:40.717Z��2024-01-03T10:07:57.087Z��pandas._libs.missing��NA���j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�IIFL��IDFC�j�  j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�IIFL��FILM��FILM�j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�IIFL Securities Limited��DAM Capital Advisors Limited�j�  j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(��[{'CounterpartyCode': 'IIFL', 'CounterpartyDisplayName': 'IIFL', 'CounterpartyLongName': 'IIFL Securities Limited', 'ExecDateTime': '2024-01-03T09:43:43.210Z', 'ExecutionID': '232373', 'ExecutionPrice': 1048.9947, 'Quantity': 40000}]�X,  [{'CounterpartyCode': 'IDFC', 'CounterpartyDisplayName': 'DAM Capital', 'CounterpartyLegalName': 'DAM Capital Advisors Limited', 'CounterpartyLongName': 'DAM Capital Advisors Limited', 'ExecDateTime': '2024-01-03T10:08:00.515Z', 'ExecutionID': '231475', 'ExecutionPrice': 607.862, 'Quantity': 45000}]�j�  j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�233885��234481�j�  j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�2024-01-03T22:00:26.655Z�j  j�  j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�False�j(  j�  j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�40000.0��45000.0�j�  j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�INR�j=  j�  j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�Manual�jG  j�  j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�RK�jQ  j�  j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(�0��1��2��3�et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(j�  �DAM Capital Advisors Limited�j�  j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(j�  j�  j�  j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(j�  j�  j�  j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(j�  j�  j�  j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(j�  j�  j�  j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h��]�(j�  j�  j�  j�  et�bh�h�)��ubhm(��             �?      �?      �?      �?     ��@     ��@     ��@    ��@      �      �      �      �      �      �      �      �      �      �      �      ��h�f8�����R�(KhrNNNJ����J����K t�bKK��hut�R�hhK ��h��R�(KK	K��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�2024-01-03T22:00:26.655Z��2024-01-03T22:00:26.655Z��2024-01-09T22:00:13.907Z��2024-01-09T22:00:13.907Z�G@�c���SG@���`A�7j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �MSCO��BAMLX��MSCO�j�  et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h(at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h)at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h+at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h,at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h0at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h:at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h<at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h=at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h?at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h@at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hBat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hCat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hDat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hEat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hFat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hGat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hHat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hJat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hKat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hLat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hMat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hNat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hOat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hPat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hQat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hRat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hSat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hTat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hUat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hWat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hXat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hYat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h8h9hVhZh[et�bhdNu��R�h
h}�(hhhK ��h��R�(KK	��h!�]�(h1hIh\h]h^h_h`hahbet�bhdNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h�mgr_locs��builtins��slice���K KK��R�u}�(j{  h�j|  j  KKK��R�u}�(j{  h�j|  j  KKK��R�u}�(j{  h�j|  j  KKK��R�u}�(j{  h�j|  j  KKK��R�u}�(j{  h�j|  j  KKK��R�u}�(j{  h�j|  j  KKK��R�u}�(j{  h�j|  j  KKK��R�u}�(j{  h�j|  j  KK	K��R�u}�(j{  h�j|  j  K	K
K��R�u}�(j{  h�j|  j  K
KK��R�u}�(j{  j  j|  j  KKK��R�u}�(j{  j  j|  j  K
KK��R�u}�(j{  j  j|  j  KKK��R�u}�(j{  j'  j|  j  KKK��R�u}�(j{  j2  j|  j  KKK��R�u}�(j{  j=  j|  j  KKK��R�u}�(j{  jI  j|  j  KKK��R�u}�(j{  jU  j|  j  KKK��R�u}�(j{  ja  j|  j  KKK��R�u}�(j{  jm  j|  j  KKK��R�u}�(j{  jx  j|  j  KKK��R�u}�(j{  j�  j|  j  KKK��R�u}�(j{  j�  j|  j  KKK��R�u}�(j{  j�  j|  j  KKK��R�u}�(j{  j�  j|  j  KKK��R�u}�(j{  j�  j|  j  KKK��R�u}�(j{  j�  j|  j  KKK��R�u}�(j{  j�  j|  j  KK K��R�u}�(j{  j�  j|  j  K K!K��R�u}�(j{  j�  j|  j  K!K"K��R�u}�(j{  j�  j|  j  K"K#K��R�u}�(j{  j�  j|  j  K#K$K��R�u}�(j{  j  j|  j  K%K&K��R�u}�(j{  j  j|  j  K&K'K��R�u}�(j{  j  j|  j  K'K(K��R�u}�(j{  j!  j|  j  K(K)K��R�u}�(j{  j+  j|  j  K)K*K��R�u}�(j{  j6  j|  j  K*K+K��R�u}�(j{  j@  j|  j  K+K,K��R�u}�(j{  jJ  j|  j  K,K-K��R�u}�(j{  jT  j|  j  K-K.K��R�u}�(j{  ja  j|  j  K.K/K��R�u}�(j{  jk  j|  j  K/K0K��R�u}�(j{  jt  j|  j  K0K1K��R�u}�(j{  j}  j|  j  K2K3K��R�u}�(j{  j�  j|  j  K3K4K��R�u}�(j{  j�  j|  j  K4K5K��R�u}�(j{  j�  j|  hm(�(                     1       5       6       �hqK��hut�R�u}�(j{  j�  j|  hm(�H              $       7       8       9       :       ;       <       =       �h�i8�����R�(KhrNNNJ����J����K t�bK	��hut�R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.