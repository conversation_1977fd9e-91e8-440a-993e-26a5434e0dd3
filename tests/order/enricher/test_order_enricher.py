import pandas as pd
import pytest
from prefect.engine.signals import SKIP
from se_trades_tasks.order.universal.order_eod_stats_enricher import (
    run_order_eod_stats_enricher,
)

from swarm_tasks.order.universal.order_enricher import OrderEODStatsEnricher
from swarm_tasks.order.universal.order_enricher import Params


class TestOrderEODStatsEnricher:
    """Test suite for LookupDataFrame"""

    def test_source_frame_empty(
        self,
        empty_source_df: pd.DataFrame,
    ):
        """Tests the task for the case where the source frame is empty"""
        task = OrderEODStatsEnricher(name="OrderEODStatsEnricher")
        with pytest.raises(SKIP):
            task.execute(
                source_frame=pd.DataFrame(),
                param=Params(),
            )

    def test_order_eod_stats_enricher(self):
        assert OrderEODStatsEnricher
        assert run_order_eod_stats_enricher
