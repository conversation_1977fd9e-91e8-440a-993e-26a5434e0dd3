import logging
import re
from abc import ABC
from pathlib import Path

import pandas as pd
import pytest
from freezegun import freeze_time
from moto import mock_aws
from prefect.engine.signals import SKIP
from se_core_tasks.abstractions.abstract_mock_s3 import AbstractMockS3
from se_core_tasks.core.core_dataclasses import Extract<PERSON>athResult
from swarm.conf import SettingsCls

from swarm_tasks.order.feed.thinkfolio.fixed_income_file_merger import (
    FixedIncomeFileMerger,
)

CURRENT_PATH = Path(__file__).parent
INPUT_TRD_FILE_PATH = CURRENT_PATH.joinpath("data/TEST_MARKITFI_TRD_1_20230601.csv")
INPUT_ADD_FILE_PATH = CURRENT_PATH.joinpath("data/TEST_MARKITFI_ADD_20230601.csv")
MERGED_DF_PATH = CURRENT_PATH.joinpath("data/merged_df.pkl")

logger = logging.getLogger(__name__)


class MockS3TestFileNames:
    FILE_1_TRD = "TEST_MARKITFI_TRD_1_20230601.csv"
    FILE_1_ADD = "TEST_MARKITFI_ADD_20230601.csv"
    FILE_2_TRD = "TEST_MARKITFI_TRD_1_20230602.csv"
    FILE_2_ADD = "TEST_MARKITFI_ADD_20230602.csv"
    FILE_3_TRD = "TEST_MARKITFI_TRD_1_20230603.csv"
    FILE_3_ADD = "TEST_MARKITFI_ADD_20230603.csv"


@pytest.fixture
def merged_df() -> pd.DataFrame:
    return pd.read_pickle(MERGED_DF_PATH)


class TestFixedIncomeFileMerger:
    task_was_initialized = False

    @mock_aws
    def test_missing_pair_file(self, mocker):
        self.load_mock_s3(mocker=mocker)
        task = FixedIncomeFileMerger(name="FixedIncomeFileMerger")

        with pytest.raises(SKIP) as e:
            task.process(
                extractor_result=ExtractPathResult(INPUT_TRD_FILE_PATH),
                file_url=f"s3://test.dev.steeleye.co/flows/order-feed-thinkfolio/{MockS3TestFileNames.FILE_1_TRD}",
                logger=logger,
            )

        assert e.match(
            re.escape(
                f"Pair file not found: /flows/order-feed-thinkfolio/{MockS3TestFileNames.FILE_1_ADD}. Info:An error occurred (404) when calling the HeadObject operation: Not Found"
            )
        )

    @mock_aws
    def test_later_timestamp_than_pair(self, mocker):
        self.load_mock_s3(mocker=mocker)
        task = FixedIncomeFileMerger(name="FixedIncomeFileMerger")

        with pytest.raises(SKIP) as e:
            task.process(
                extractor_result=ExtractPathResult(INPUT_TRD_FILE_PATH),
                file_url=f"s3://test.dev.steeleye.co/flows/order-feed-thinkfolio/{MockS3TestFileNames.FILE_2_TRD}",
                logger=logger,
            )

        assert e.match(
            re.escape(
                "Source file timestamp 2022-06-22 12:00:02+00:00 < than pair file timestamp 2022-06-22 12:00:03+00:00"
            )
        )

    @mock_aws
    def test_expected_end_to_end(self, mocker, merged_df):
        self.load_mock_s3(mocker=mocker)
        task = FixedIncomeFileMerger(name="FixedIncomeFileMerger")

        result = task.process(
            extractor_result=ExtractPathResult(INPUT_ADD_FILE_PATH),
            file_url=f"s3://test.dev.steeleye.co/flows/order-feed-thinkfolio/{MockS3TestFileNames.FILE_2_ADD}",
            logger=logger,
        )

        result_df = pd.read_csv(result.path.as_posix())

        pd.testing.assert_frame_equal(result_df, merged_df)

    @mock_aws
    def test_same_timestamp_add(self, mocker):
        self.load_mock_s3(mocker=mocker)
        task = FixedIncomeFileMerger(name="FixedIncomeFileMerger")

        with pytest.raises(SKIP) as e:
            task.process(
                extractor_result=ExtractPathResult(INPUT_ADD_FILE_PATH),
                file_url=f"s3://test.dev.steeleye.co/flows/order-feed-thinkfolio/{MockS3TestFileNames.FILE_3_ADD}",
                logger=logger,
            )

        assert e.match(
            "Pair file has the same timestamp, skipping processing for ADD file."
        )

    @mock_aws
    def test_same_timestamp_trd(self, mocker):
        self.load_mock_s3(mocker=mocker)
        task = FixedIncomeFileMerger(name="FixedIncomeFileMerger")

        result = task.process(
            extractor_result=ExtractPathResult(INPUT_TRD_FILE_PATH),
            file_url=f"s3://test.dev.steeleye.co/flows/order-feed-thinkfolio/{MockS3TestFileNames.FILE_3_TRD}",
            logger=logger,
        )

        # just testing that it doesn't skip, no need to check content as it's done on a previous test
        assert result

    def load_mock_s3(self, mocker):
        fake_bucket_name = "test.dev.steeleye.co"

        if not self.task_was_initialized:
            mock_s3_instance = FixedIncomeFileMergerMockS3()
            mock_s3_instance.create_mock_bucket(bucket=fake_bucket_name)
            mock_s3_instance.load_data_into_mock_s3()

        self.task_was_initialized = True
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = fake_bucket_name


class FixedIncomeFileMergerMockS3(AbstractMockS3, ABC):
    def load_data_into_mock_s3(self):

        files_to_load_dict = {
            MockS3TestFileNames.FILE_1_TRD: {
                "path": INPUT_TRD_FILE_PATH,
                "time": "2022-06-22 12:00:01",
            },
            MockS3TestFileNames.FILE_2_TRD: {
                "path": INPUT_TRD_FILE_PATH,
                "time": "2022-06-22 12:00:02",
            },
            MockS3TestFileNames.FILE_2_ADD: {
                "path": INPUT_ADD_FILE_PATH,
                "time": "2022-06-22 12:00:03",
            },
            MockS3TestFileNames.FILE_3_TRD: {
                "path": INPUT_TRD_FILE_PATH,
                "time": "2022-06-22 12:00:00",
            },
            MockS3TestFileNames.FILE_3_ADD: {
                "path": INPUT_ADD_FILE_PATH,
                "time": "2022-06-22 12:00:00",
            },
        }

        for file_name, file_info in files_to_load_dict.items():
            with open(file_info.get("path")) as test_file:
                body = test_file.read()

            with freeze_time(file_info.get("time")):
                self.s3.put_object(
                    Bucket=self.bucket,
                    Key=f"flows/order-feed-thinkfolio/{file_name}",
                    Body=body,
                )
