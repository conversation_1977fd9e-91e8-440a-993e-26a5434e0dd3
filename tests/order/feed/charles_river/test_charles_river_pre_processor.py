import asyncio

import pandas as pd
import pytest
from prefect.engine.signals import SKIP
from swarm.task.io.read import FrameProducerResult

from swarm_tasks.io.read.aws import s3_download_multiple_files
from swarm_tasks.io.read.aws.s3_download_multiple_files import Params
from swarm_tasks.io.read.aws.s3_download_multiple_files import S3DownloadMultipleFiles


@pytest.fixture()
def sample_source_frame() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "s3_fsi_crims_order_file_url": ["s3://dummy.dev.steeleye.co/dummy.txt"],
            "s3_fsi_crims_place_file_url": ["s3://dummy.dev.steeleye.co/dummy.txt"],
            "s3_fsi_crims_fills_file_url": ["s3://dummy.dev.steeleye.co/dummy.txt"],
            "s3_fsi_crims_allocation_file_url": [
                "s3://dummy.dev.steeleye.co/dummy.txt"
            ],
        }
    )
    return df


@pytest.fixture()
def expected_pre_process_result() -> dict:
    # data = [
    #     {"orders": pd.DataFrame({"a": [1, 2, 3]})},
    #     {"place": pd.DataFrame({"a": [1, 2, 3]})},
    #     {"fills": pd.DataFrame({"a": [1, 2, 3]})},
    #     {"allocation": pd.DataFrame({"a": [1, 2, 3]})},
    # ]
    data = {
        "order": pd.DataFrame({"a": [1, 2, 3]}),
        "place": pd.DataFrame({"a": [1, 2, 3]}),
        "fills": pd.DataFrame({"a": [1, 2, 3]}),
        "allocation": pd.DataFrame({"a": [1, 2, 3]}),
    }
    return data


@pytest.fixture()
def expected_join_results() -> pd.DataFrame:
    df = pd.DataFrame(
        [
            {
                "a": 1,
                "flag_order": 1,
                "flag_place": 1,
                "flag_fills": 1,
                "flag_allocation": 1,
            },
            {
                "a": 2,
                "flag_order": 1,
                "flag_place": 1,
                "flag_fills": 1,
                "flag_allocation": 1,
            },
            {
                "a": 3,
                "flag_order": 1,
                "flag_place": 1,
                "flag_fills": 1,
                "flag_allocation": 1,
            },
        ]
    )
    return df


async def async_return_s3_data(*args, **kwargs):
    coroutine = asyncio.Future()
    data = [
        {"order": pd.DataFrame({"a": [1, 2, 3]})},
        {"place": pd.DataFrame({"a": [1, 2, 3]})},
        {"fills": pd.DataFrame({"a": [1, 2, 3]})},
        {"allocation": pd.DataFrame({"a": [1, 2, 3]})},
    ]
    coroutine.set_result(data)
    return await coroutine


def async_return_s3_data_with_some_missing(*args, **kwargs):
    coroutine = asyncio.Future()
    data = [
        {"order": pd.DataFrame({"a": [1, 2, 3]})},
        {"place": pd.DataFrame({"a": [1, 2, 3]})},
        {"fills": pd.DataFrame({"a": [1, 2, 3]})},
    ]
    coroutine.set_result(data)
    return coroutine


class TestCharlesRiverPreProcessor:
    """
    Test Charles River File Pre Processor Logic
    """

    def test_skip_condition_when_s3_data_could_not_be_read(
        self, mocker, sample_source_frame, expected_pre_process_result
    ):
        params = Params(
            file_columns=[
                "s3_fsi_crims_order_file_url",
                "s3_fsi_crims_place_file_url",
                "s3_fsi_crims_fills_file_url",
                "s3_fsi_crims_allocation_file_url",
            ],
            suffix_identifier_regex=r"s3_fsi_crims_(.*)_file_url",
        )
        mock_async_func_call = mocker.patch.object(
            s3_download_multiple_files, "async_read_csv_from_s3"
        )
        mock_async_func_call.side_effect = async_return_s3_data_with_some_missing

        with pytest.raises(SKIP):
            task = S3DownloadMultipleFiles(name="test_task", params=params)
            task.execute(
                producer_result=FrameProducerResult(frame=sample_source_frame),
                params=params,
            )

    def test_for_download_data_from_s3_links(
        self, mocker, sample_source_frame, expected_pre_process_result
    ):
        params = Params(
            file_columns=[
                "s3_fsi_crims_order_file_url",
                "s3_fsi_crims_place_file_url",
                "s3_fsi_crims_fills_file_url",
                "s3_fsi_crims_allocation_file_url",
            ],
            suffix_identifier_regex=r"s3_fsi_crims_(.*)_file_url",
        )
        mock_async_func_call = mocker.patch.object(
            s3_download_multiple_files, "async_read_csv_from_s3"
        )
        mock_async_func_call.side_effect = async_return_s3_data

        task = S3DownloadMultipleFiles(name="test_task", params=params)
        result = task.execute(
            producer_result=FrameProducerResult(frame=sample_source_frame),
            params=params,
        )
        assert len(result) == len(expected_pre_process_result)
        for key in result:
            assert result[key].equals(expected_pre_process_result[key])

    def test_for_download_data_from_s3_links_without_file_columns(
        self, mocker, sample_source_frame, expected_pre_process_result
    ):
        params = Params(
            suffix_identifier_regex=r"s3_fsi_crims_(.*)_file_url",
        )
        mock_async_func_call = mocker.patch.object(
            s3_download_multiple_files, "async_read_csv_from_s3"
        )
        mock_async_func_call.side_effect = async_return_s3_data

        task = S3DownloadMultipleFiles(name="test_task", params=params)
        result = task.execute(
            producer_result=FrameProducerResult(frame=sample_source_frame),
            params=params,
        )
        assert len(result) == len(expected_pre_process_result)
        for key in result:
            assert result[key].equals(expected_pre_process_result[key])

    def test_for_download_data_from_s3_links_and_join(
        self, mocker, sample_source_frame, expected_join_results
    ):
        params = Params(
            file_columns=[
                "s3_fsi_crims_order_file_url",
                "s3_fsi_crims_place_file_url",
                "s3_fsi_crims_fills_file_url",
                "s3_fsi_crims_allocation_file_url",
            ],
            suffix_identifier_regex=r"s3_fsi_crims_(.*)_file_url",
            join_columns=["a"],
        )
        mock_async_func_call = mocker.patch.object(
            s3_download_multiple_files, "async_read_csv_from_s3"
        )
        mock_async_func_call.side_effect = async_return_s3_data

        task = S3DownloadMultipleFiles(name="test_task", params=params)
        result = task.execute(
            producer_result=FrameProducerResult(frame=sample_source_frame),
            params=params,
        )
        result_df = pd.read_csv(result.path)
        assert not pd.testing.assert_frame_equal(expected_join_results, result_df)
