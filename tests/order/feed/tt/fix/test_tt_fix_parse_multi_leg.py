import pandas as pd
from swarm.task.auditor import Auditor

from swarm_tasks.order.feed.tt.fix.tt_fix_parse_multi_leg import TTFixParseMultiLeg

# source and expected data exists in 'conftest.py'


class TestTTFixParseMultiLeg:
    """
    Test cases for "TestTTFixParseMultiLeg" class
    """

    def test_empty_input_df_without_source_columns(
        self,
        empty_source_df: pd.DataFrame,
    ):
        task = TTFixParseMultiLeg(name="test-tt-fix=parse-multi-leg")
        result = task.execute(empty_source_df)
        assert result.empty

    def test_all_cols_in_source_df(
        self,
        mocker,
        all_cols_in_source_df: pd.DataFrame,
        expected_result_all_cols_in_source_df: pd.DataFrame,
    ):
        task = TTFixParseMultiLeg(name="test-tt-fix=parse-multi-leg")
        mock_auditor = mocker.patch.object(TTFixParseMultiLeg, "auditor")
        mock_auditor.return_value = Auditor(task_name="TTFixParseMultiLeg")
        result = task.execute(all_cols_in_source_df)

        pd.testing.assert_frame_equal(
            left=result.sort_index(axis=1),
            right=expected_result_all_cols_in_source_df.sort_index(axis=1),
        )

    def test_missing_some_cols_in_source_df(
        self,
        missing_some_cols_in_source_df: pd.DataFrame,
        expected_result_missing_some_cols_in_source_df: pd.DataFrame,
        mocker,
    ):
        task = TTFixParseMultiLeg(name="test-tt-fix=parse-multi-leg")
        mock_auditor = mocker.patch.object(TTFixParseMultiLeg, "auditor")
        mock_auditor.return_value = Auditor(task_name="TTFixParseMultiLeg")
        result = task.execute(source_frame=missing_some_cols_in_source_df)

        pd.testing.assert_frame_equal(
            left=result.sort_index(axis=1),
            right=expected_result_missing_some_cols_in_source_df.sort_index(axis=1),
        )
