import pandas as pd
import pytest
from se_trades_tasks.order_and_tr.fix.static import FixTagNames

from swarm_tasks.order.feed.tt.fix.tt_fix_parse_multi_leg import DerivedFields


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """
    creates empty dataframe
    """
    return pd.DataFrame()


@pytest.fixture()
def all_cols_in_source_df() -> pd.DataFrame:
    data = {
        "S3FileURL": ["file_url_1", "file_url_2"],
        "BeginString": ["FIX.4.4", "FIX.4.4"],
        FixTagNames.ORDER_ID: ["order_id_1", "order_id_2"],
        FixTagNames.ORDER_QTY: [500.0, 595],
        FixTagNames.LEG_SIDE: [["1", "2"], ["2", "1"]],
        FixTagNames.LEG_RATIO_QTY: [[1.0, 1.0], [0.1, 1.0]],
        FixTagNames.LEG_SECURITY_ALT_ID_SOURCE: [
            ["8", "4", "8", "4"],
            ["8", "4", "8", "4"],
        ],
        FixTagNames.LEG_SECURITY_ALT_ID: [
            ["alt_sec_id_1", "isin_1", "alt_sec_id_2", "isin_2"],
            ["alt_sec_id_3", "isin_3", "alt_sec_id_4", "isin_4"],
        ],
        FixTagNames.NO_LEGS: ["2", "2"],
        FixTagNames.MULTI_LEG_REPORTING_TYPE: ["3", "3"],
        FixTagNames.SIDE: ["1", "2"],
        FixTagNames.SECURITY_TYPE: [
            ["SEC_TYP_1", "SEC_TYP_2"],
            ["SEC_TYP_3", "SEC_TYP_4"],
        ],
        FixTagNames.LEG_SECURITY_TYPE: [
            ["LEG_SEC_TYP_1", "LEG_SEC_TYP_2"],
            ["LEG_SEC_TYP_3", "LEG_SEC_TYP_4"],
        ],
        FixTagNames.CURRENCY: [
            ["CURRENCY_1", "CURRENCY_2"],
            ["CURRENCY_3", "CURRENCY_4"],
        ],
        FixTagNames.LEG_CURRENCY: [
            ["LEG_CURRENCY_1", "LEG_CURRENCY_2"],
            ["LEG_CURRENCY_3", "LEG_CURRENCY_4"],
        ],
        FixTagNames.CFI_CODE: [
            ["CFI_CODE_1", "CFI_CODE_2"],
            ["CFI_CODE_3", "CFI_CODE_4"],
        ],
        FixTagNames.LEG_CFI_CODE: [
            ["LEG_CFI_CODE_1", "LEG_CFI_CODE_2"],
            ["LEG_CFI_CODE_3", "LEG_CFI_CODE_4"],
        ],
        FixTagNames.STRIKE_PRICE: [
            ["STRIKE_PRICE_1", "STRIKE_PRICE_2"],
            ["STRIKE_PRICE_3", "STRIKE_PRICE_4"],
        ],
        FixTagNames.LEG_STRIKE_PRICE: [
            ["LEG_STRIKE_PRICE_1", "LEG_STRIKE_PRICE_2"],
            ["LEG_STRIKE_PRICE_3", "LEG_STRIKE_PRICE_4"],
        ],
        FixTagNames.MATURITY_DATE: [
            ["MATURITY_DATE_1", "MATURITY_DATE_2"],
            ["MATURITY_DATE_3", "MATURITY_DATE_4"],
        ],
        FixTagNames.LEG_MATURITY_DATE: [
            ["LEG_MATURITY_DATE_1", "LEG_MATURITY_DATE_2"],
            ["LEG_MATURITY_DATE_3", "LEG_MATURITY_DATE_4"],
        ],
        FixTagNames.SYMBOL: [["SYMBOL_1", "SYMBOL_2"], ["SYMBOL_3", "SYMBOL_4"]],
        FixTagNames.LEG_SYMBOL: [
            ["LEG_SYMBOL_1", "LEG_SYMBOL_2"],
            ["LEG_SYMBOL_3", "LEG_SYMBOL_4"],
        ],
        FixTagNames.LEG_EX_DESTINATION: [
            ["LEG_EX_DESTINATION_1", "LEG_EX_DESTINATION_2"],
            ["LEG_EX_DESTINATION_3", "LEG_EX_DESTINATION_4"],
        ],
    }
    return pd.DataFrame(data)


@pytest.fixture()
def missing_some_cols_in_source_df() -> pd.DataFrame:
    data = {
        "S3FileURL": ["file_url_1", "file_url_2"],
        "BeginString": ["FIX.4.4", "FIX.4.4"],
        FixTagNames.ORDER_ID: ["order_id_1", "order_id_2"],
        FixTagNames.ORDER_QTY: [pd.NA, 595],
        FixTagNames.LEG_SIDE: [["1", "2"], ["2", "1"]],
        FixTagNames.LEG_RATIO_QTY: [[1.0, 1.0], [2.0, 1.0]],
        FixTagNames.NO_LEGS: ["2", "2"],
        FixTagNames.MULTI_LEG_REPORTING_TYPE: ["3", "3"],
        FixTagNames.SIDE: ["1", "2"],
        FixTagNames.SECURITY_TYPE: [
            ["SEC_TYP_1", "SEC_TYP_2"],
            ["SEC_TYP_3", "SEC_TYP_4"],
        ],
        FixTagNames.LEG_SECURITY_TYPE: [
            ["LEG_SEC_TYP_1", "LEG_SEC_TYP_2"],
            ["LEG_SEC_TYP_3", "LEG_SEC_TYP_4"],
        ],
        FixTagNames.CURRENCY: [
            ["CURRENCY_1", "CURRENCY_2"],
            ["CURRENCY_3", "CURRENCY_4"],
        ],
        FixTagNames.LEG_CURRENCY: [
            ["LEG_CURRENCY_1", "LEG_CURRENCY_2"],
            ["LEG_CURRENCY_3", "LEG_CURRENCY_4"],
        ],
        FixTagNames.CFI_CODE: [
            ["CFI_CODE_1", "CFI_CODE_2"],
            ["CFI_CODE_3", "CFI_CODE_4"],
        ],
        FixTagNames.LEG_CFI_CODE: [
            ["LEG_CFI_CODE_1", "LEG_CFI_CODE_2"],
            ["LEG_CFI_CODE_3", "LEG_CFI_CODE_4"],
        ],
    }
    return pd.DataFrame(data)


@pytest.fixture()
def expected_result_all_cols_in_source_df() -> pd.DataFrame:
    data = {
        "S3FileURL": ["file_url_1", "file_url_1", "file_url_2", "file_url_2"],
        "BeginString": ["FIX.4.4", "FIX.4.4", "FIX.4.4", "FIX.4.4"],
        FixTagNames.ORDER_ID: ["order_id_1", "order_id_1", "order_id_2", "order_id_2"],
        FixTagNames.ORDER_QTY: [500.0, 500.0, 60, 595.0],
        FixTagNames.LEG_SIDE: [["1", "2"], ["1", "2"], ["2", "1"], ["2", "1"]],
        FixTagNames.LEG_RATIO_QTY: [[1.0, 1.0], [1.0, 1.0], [0.1, 1.0], [0.1, 1.0]],
        FixTagNames.LEG_SECURITY_ALT_ID_SOURCE: [
            ["8", "4", "8", "4"],
            ["8", "4", "8", "4"],
            ["8", "4", "8", "4"],
            ["8", "4", "8", "4"],
        ],
        FixTagNames.LEG_SECURITY_ALT_ID: [
            ["alt_sec_id_1", "isin_1", "alt_sec_id_2", "isin_2"],
            ["alt_sec_id_1", "isin_1", "alt_sec_id_2", "isin_2"],
            ["alt_sec_id_3", "isin_3", "alt_sec_id_4", "isin_4"],
            ["alt_sec_id_3", "isin_3", "alt_sec_id_4", "isin_4"],
        ],
        FixTagNames.NO_LEGS: ["2", "2", "2", "2"],
        FixTagNames.MULTI_LEG_REPORTING_TYPE: ["3", "3", "3", "3"],
        FixTagNames.SIDE: ["1", "1", "2", "2"],
        FixTagNames.SECURITY_TYPE: [
            "LEG_SEC_TYP_1",
            "LEG_SEC_TYP_2",
            "LEG_SEC_TYP_3",
            "LEG_SEC_TYP_4",
        ],
        FixTagNames.LEG_SECURITY_TYPE: [
            ["LEG_SEC_TYP_1", "LEG_SEC_TYP_2"],
            ["LEG_SEC_TYP_1", "LEG_SEC_TYP_2"],
            ["LEG_SEC_TYP_3", "LEG_SEC_TYP_4"],
            ["LEG_SEC_TYP_3", "LEG_SEC_TYP_4"],
        ],
        FixTagNames.CURRENCY: [
            "LEG_CURRENCY_1",
            "LEG_CURRENCY_2",
            "LEG_CURRENCY_3",
            "LEG_CURRENCY_4",
        ],
        FixTagNames.LEG_CURRENCY: [
            ["LEG_CURRENCY_1", "LEG_CURRENCY_2"],
            ["LEG_CURRENCY_1", "LEG_CURRENCY_2"],
            ["LEG_CURRENCY_3", "LEG_CURRENCY_4"],
            ["LEG_CURRENCY_3", "LEG_CURRENCY_4"],
        ],
        FixTagNames.CFI_CODE: [
            "LEG_CFI_CODE_1",
            "LEG_CFI_CODE_2",
            "LEG_CFI_CODE_3",
            "LEG_CFI_CODE_4",
        ],
        FixTagNames.LEG_CFI_CODE: [
            ["LEG_CFI_CODE_1", "LEG_CFI_CODE_2"],
            ["LEG_CFI_CODE_1", "LEG_CFI_CODE_2"],
            ["LEG_CFI_CODE_3", "LEG_CFI_CODE_4"],
            ["LEG_CFI_CODE_3", "LEG_CFI_CODE_4"],
        ],
        FixTagNames.STRIKE_PRICE: [
            "LEG_STRIKE_PRICE_1",
            "LEG_STRIKE_PRICE_2",
            "LEG_STRIKE_PRICE_3",
            "LEG_STRIKE_PRICE_4",
        ],
        FixTagNames.LEG_STRIKE_PRICE: [
            ["LEG_STRIKE_PRICE_1", "LEG_STRIKE_PRICE_2"],
            ["LEG_STRIKE_PRICE_1", "LEG_STRIKE_PRICE_2"],
            ["LEG_STRIKE_PRICE_3", "LEG_STRIKE_PRICE_4"],
            ["LEG_STRIKE_PRICE_3", "LEG_STRIKE_PRICE_4"],
        ],
        FixTagNames.MATURITY_DATE: [
            "LEG_MATURITY_DATE_1",
            "LEG_MATURITY_DATE_2",
            "LEG_MATURITY_DATE_3",
            "LEG_MATURITY_DATE_4",
        ],
        FixTagNames.LEG_MATURITY_DATE: [
            ["LEG_MATURITY_DATE_1", "LEG_MATURITY_DATE_2"],
            ["LEG_MATURITY_DATE_1", "LEG_MATURITY_DATE_2"],
            ["LEG_MATURITY_DATE_3", "LEG_MATURITY_DATE_4"],
            ["LEG_MATURITY_DATE_3", "LEG_MATURITY_DATE_4"],
        ],
        FixTagNames.SYMBOL: [
            "LEG_SYMBOL_1",
            "LEG_SYMBOL_2",
            "LEG_SYMBOL_3",
            "LEG_SYMBOL_4",
        ],
        FixTagNames.LEG_SYMBOL: [
            ["LEG_SYMBOL_1", "LEG_SYMBOL_2"],
            ["LEG_SYMBOL_1", "LEG_SYMBOL_2"],
            ["LEG_SYMBOL_3", "LEG_SYMBOL_4"],
            ["LEG_SYMBOL_3", "LEG_SYMBOL_4"],
        ],
        FixTagNames.LEG_EX_DESTINATION: [
            ["LEG_EX_DESTINATION_1", "LEG_EX_DESTINATION_2"],
            ["LEG_EX_DESTINATION_1", "LEG_EX_DESTINATION_2"],
            ["LEG_EX_DESTINATION_3", "LEG_EX_DESTINATION_4"],
            ["LEG_EX_DESTINATION_3", "LEG_EX_DESTINATION_4"],
        ],
        DerivedFields.DERIVED_SIDE: ["1", "2", "2", "1"],
        DerivedFields.DERIVED_ISIN_MULTILEG: ["isin_1", "isin_2", "isin_3", "isin_4"],
        DerivedFields.DERIVED_ALT_SEC_ID_MULTILEG: [
            "alt_sec_id_1",
            "alt_sec_id_2",
            "alt_sec_id_3",
            "alt_sec_id_4",
        ],
        FixTagNames.SECURITY_EXCHANGE: [
            "LEG_EX_DESTINATION_1",
            "LEG_EX_DESTINATION_2",
            "LEG_EX_DESTINATION_3",
            "LEG_EX_DESTINATION_4",
        ],
    }
    return pd.DataFrame(data)


@pytest.fixture()
def expected_result_missing_some_cols_in_source_df() -> pd.DataFrame:
    data = {
        "S3FileURL": ["file_url_1", "file_url_1", "file_url_2", "file_url_2"],
        "BeginString": ["FIX.4.4", "FIX.4.4", "FIX.4.4", "FIX.4.4"],
        FixTagNames.ORDER_ID: ["order_id_1", "order_id_1", "order_id_2", "order_id_2"],
        FixTagNames.LEG_SIDE: [["1", "2"], ["1", "2"], ["2", "1"], ["2", "1"]],
        FixTagNames.LEG_RATIO_QTY: [[1.0, 1.0], [1.0, 1.0], [2.0, 1.0], [2.0, 1.0]],
        FixTagNames.NO_LEGS: ["2", "2", "2", "2"],
        FixTagNames.MULTI_LEG_REPORTING_TYPE: ["3", "3", "3", "3"],
        FixTagNames.SIDE: ["1", "1", "2", "2"],
        FixTagNames.SECURITY_TYPE: [
            "LEG_SEC_TYP_1",
            "LEG_SEC_TYP_2",
            "LEG_SEC_TYP_3",
            "LEG_SEC_TYP_4",
        ],
        FixTagNames.LEG_SECURITY_TYPE: [
            ["LEG_SEC_TYP_1", "LEG_SEC_TYP_2"],
            ["LEG_SEC_TYP_1", "LEG_SEC_TYP_2"],
            ["LEG_SEC_TYP_3", "LEG_SEC_TYP_4"],
            ["LEG_SEC_TYP_3", "LEG_SEC_TYP_4"],
        ],
        FixTagNames.CURRENCY: [
            "LEG_CURRENCY_1",
            "LEG_CURRENCY_2",
            "LEG_CURRENCY_3",
            "LEG_CURRENCY_4",
        ],
        FixTagNames.LEG_CURRENCY: [
            ["LEG_CURRENCY_1", "LEG_CURRENCY_2"],
            ["LEG_CURRENCY_1", "LEG_CURRENCY_2"],
            ["LEG_CURRENCY_3", "LEG_CURRENCY_4"],
            ["LEG_CURRENCY_3", "LEG_CURRENCY_4"],
        ],
        FixTagNames.CFI_CODE: [
            "LEG_CFI_CODE_1",
            "LEG_CFI_CODE_2",
            "LEG_CFI_CODE_3",
            "LEG_CFI_CODE_4",
        ],
        FixTagNames.LEG_CFI_CODE: [
            ["LEG_CFI_CODE_1", "LEG_CFI_CODE_2"],
            ["LEG_CFI_CODE_1", "LEG_CFI_CODE_2"],
            ["LEG_CFI_CODE_3", "LEG_CFI_CODE_4"],
            ["LEG_CFI_CODE_3", "LEG_CFI_CODE_4"],
        ],
        FixTagNames.ORDER_QTY: [0.0, 0.0, 1190.0, 595.0],
        DerivedFields.DERIVED_SIDE: ["1", "2", "2", "1"],
        DerivedFields.DERIVED_ISIN_MULTILEG: pd.NA,
        DerivedFields.DERIVED_ALT_SEC_ID_MULTILEG: pd.NA,
        FixTagNames.STRIKE_PRICE: pd.NA,
        FixTagNames.MATURITY_DATE: pd.NA,
        FixTagNames.SYMBOL: pd.NA,
        FixTagNames.SECURITY_EXCHANGE: pd.NA,
    }
    return pd.DataFrame(data)
