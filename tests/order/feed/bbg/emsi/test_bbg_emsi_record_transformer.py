import contextlib
import shutil
from pathlib import Path

import pandas as pd
import pytest
from prefect.engine.signals import SKIP
from swarm.conf import SettingsCls
from swarm.task.auditor import Auditor

from swarm_tasks.order.feed.bbg.emsi.record_transformer import BbgEmsiFrameTransformer
from swarm_tasks.order.feed.bbg.emsi.record_transformer import Params
from swarm_tasks.order.feed.bbg.emsi.static import SourceColumns

SCRIPT_PATH = Path(__file__).parent
TEST_FILE_PATH = SCRIPT_PATH.joinpath("test_file.csv")
TEST_INSTRUMENT_FILE_PATH = SCRIPT_PATH.joinpath("instrument_file.csv")
TEST_FILE_PATH_ORDER_NUMBER_MISSING = SCRIPT_PATH.joinpath(
    "EMSI_ORDERS_G10_31052466_20240704.csv"
)
TEST_INSTRUMENT_FILE_PATH_ORDER_NUMBER_MISSING = SCRIPT_PATH.joinpath(
    "EMSI_INSTRUMENT_G10_31052466_20240704.csv"
)
EXPECTED_FILE_PATH = SCRIPT_PATH.joinpath("expected_result.csv")
TEST_FILE_PATH_WITHOUT_ORDER_ID = SCRIPT_PATH.joinpath("test_file_without_orderid.csv")
EXPECTED_FILE_PATH_WITHOUT_ORDER_ID = SCRIPT_PATH.joinpath(
    "expected_result_without_orderid.csv"
)
EXPECTED_FILE_PATH_INSTRUMENT_STORE = SCRIPT_PATH.joinpath(
    "expected_result_instrument_store.csv"
)
EXPECTED_FILE_PATH_WITHOUT_FILL_BY_FILL = SCRIPT_PATH.joinpath(
    "expected_result_without_fill_by_fill.csv"
)
EXPECTED_FILE_PATH_WITH_REMOVE_PARF_FILL_BEFORE_ZERO_CANCEL = SCRIPT_PATH.joinpath(
    "expected_result_with_remove_parf_fill_before_zero_cancel.csv"
)
EXPECTED_FILE_PATH_WITHOUT_ORDER_NUMBER = SCRIPT_PATH.joinpath(
    "expected_result_missing_order_number.csv"
)


class TestBbgEmsiFrameTransformer:
    """
    Test BBG EMSI Order Record Creation Logic
    """

    def test_all_df_dict_empty_scenario(self):
        with pytest.raises(SKIP):
            task = BbgEmsiFrameTransformer(name="test_task")
            task.execute(
                pre_process_result=dict(), params=Params(fill_by_fill_flag=True)
            )

    def test_some_df_dict_empty_scenario(self):
        with pytest.raises(SKIP):
            task = BbgEmsiFrameTransformer(name="test_task")
            task.execute(
                pre_process_result=dict(fills=pd.DataFrame({"a": [1]})),
                params=Params(fill_by_fill_flag=True),
            )

        with pytest.raises(SKIP):
            task = BbgEmsiFrameTransformer(name="test_task")
            task.execute(
                pre_process_result=dict(cax=pd.DataFrame({"a": [1]})),
                params=Params(fill_by_fill_flag=True),
            )

    def test_record_transformation_without_instrument_store_lookup(self, mocker):
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "dummy1.dev.steeleye.co"

        task = BbgEmsiFrameTransformer(
            name="test_task",
        )
        extractor_result = dict(
            orders=pd.read_csv(TEST_FILE_PATH),
            instrument=pd.read_csv(TEST_INSTRUMENT_FILE_PATH),
        )
        result = task.execute(
            result=extractor_result,
            file_paths=pd.DataFrame({"s3_orders_file_url": ["a.csv"]}),
            params=Params(fill_by_fill_flag=True),
        )
        result_df = pd.read_csv(result.path).fillna(pd.NA)
        expected_df = pd.read_csv(EXPECTED_FILE_PATH).fillna(pd.NA)

        # 1 NEWO, 1 CAME and 103 PARF should be created
        pd.testing.assert_frame_equal(result_df, expected_df)

    def test_record_transformation_without_order_id(self, mocker):
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "dummy1.dev.steeleye.co"

        task = BbgEmsiFrameTransformer(
            name="test_task",
        )
        extractor_result = dict(
            orders=pd.read_csv(TEST_FILE_PATH_WITHOUT_ORDER_ID),
            instrument=pd.read_csv(TEST_INSTRUMENT_FILE_PATH),
        )
        result = task.execute(
            result=extractor_result,
            file_paths=pd.DataFrame({"s3_orders_file_url": ["a.csv"]}),
            params=Params(fill_by_fill_flag=True),
        )
        result_df = pd.read_csv(result.path).fillna(pd.NA)
        expected_df = pd.read_csv(EXPECTED_FILE_PATH_WITHOUT_ORDER_ID).fillna(pd.NA)

        # 1 NEWO, 1 CAME and 103 PARF should be created
        pd.testing.assert_frame_equal(result_df, expected_df)

    def test_record_transformation_with_instrument_store_lookup(self, mocker):
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "dummy1.dev.steeleye.co"

        mock_bundle = mocker.patch.object(
            SettingsCls, "bundle", new_callable=mocker.PropertyMock
        )
        mock_bundle.return_value = "order-bbg-emsi-controller"

        order_data = pd.read_csv(TEST_FILE_PATH)
        instrument_data = pd.read_csv(TEST_INSTRUMENT_FILE_PATH)

        order_data.loc[
            order_data[SourceColumns.ACTIVITY] == "New-route", SourceColumns.TICKER
        ] = "ABC"
        order_data.loc[
            order_data[SourceColumns.ACTIVITY] == "Exec-Cancel", SourceColumns.TICKER
        ] = "DEF"
        extractor_result = dict(
            orders=order_data,
            instrument=instrument_data,
        )

        fetch_data_from_instrument_store_return_value = {
            "ABC": pd.DataFrame(
                {
                    SourceColumns.PARSE_KEY: ["ABC"],
                    SourceColumns.ISIN_INSTRUMENT: ["ISIN1"],
                    SourceColumns.LOCAL_EXCH_SYMBOL: ["LCH1"],
                    SourceColumns.LAST_MARKET_INSTRUMENT: ["LMI1"],
                    SourceColumns.SECURITY_NAME: ["IIO"],
                },
            ),
            "DEF": pd.DataFrame(
                {
                    SourceColumns.PARSE_KEY: ["DEF"],
                    SourceColumns.ISIN_INSTRUMENT: ["ISIN2"],
                    SourceColumns.LOCAL_EXCH_SYMBOL: ["LCH2"],
                    SourceColumns.LAST_MARKET_INSTRUMENT: ["LMI2"],
                    SourceColumns.SECURITY_NAME: ["OOI"],
                },
            ),
        }

        mocker.patch(
            "swarm_tasks.order.feed.bbg.emsi.record_transformer.fetch_data_from_instrument_store",
            return_value=fetch_data_from_instrument_store_return_value,
        )

        task = BbgEmsiFrameTransformer(name="test_task")
        result = task.execute(
            result=extractor_result,
            file_paths=pd.DataFrame({"s3_orders_file_url": ["a.csv"]}),
            params=Params(fill_by_fill_flag=True),
        )
        result_df = pd.read_csv(result.path).fillna(pd.NA)
        expected_df = pd.read_csv(EXPECTED_FILE_PATH_INSTRUMENT_STORE).fillna(pd.NA)

        # 1 NEWO, 1 CAME and 103 PARF should be created
        pd.testing.assert_frame_equal(result_df, expected_df)

    def test_record_transformation_without_fill_by_fill(self, mocker):
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "dummy1.dev.steeleye.co"

        mock_bundle = mocker.patch.object(
            SettingsCls, "bundle", new_callable=mocker.PropertyMock
        )
        mock_bundle.return_value = "order-bbg-emsi-controller"

        order_data = pd.read_csv(TEST_FILE_PATH)
        instrument_data = pd.read_csv(TEST_INSTRUMENT_FILE_PATH)

        order_data.loc[
            order_data[SourceColumns.ACTIVITY] == "New-route", SourceColumns.TICKER
        ] = "ABC"
        order_data.loc[
            order_data[SourceColumns.ACTIVITY] == "Exec-Cancel", SourceColumns.TICKER
        ] = "DEF"
        extractor_result = dict(
            orders=order_data,
            instrument=instrument_data,
        )

        fetch_data_from_instrument_store_return_value = {
            "ABC": pd.DataFrame(
                {
                    SourceColumns.PARSE_KEY: ["ABC"],
                    SourceColumns.ISIN_INSTRUMENT: ["ISIN1"],
                    SourceColumns.LOCAL_EXCH_SYMBOL: ["LCH1"],
                    SourceColumns.LAST_MARKET_INSTRUMENT: ["LMI1"],
                    SourceColumns.SECURITY_NAME: ["IIO"],
                },
            ),
            "DEF": pd.DataFrame(
                {
                    SourceColumns.PARSE_KEY: ["DEF"],
                    SourceColumns.ISIN_INSTRUMENT: ["ISIN2"],
                    SourceColumns.LOCAL_EXCH_SYMBOL: ["LCH2"],
                    SourceColumns.LAST_MARKET_INSTRUMENT: ["LMI2"],
                    SourceColumns.SECURITY_NAME: ["OOI"],
                },
            ),
        }

        mocker.patch(
            "swarm_tasks.order.feed.bbg.emsi.record_transformer.fetch_data_from_instrument_store",
            return_value=fetch_data_from_instrument_store_return_value,
        )

        task = BbgEmsiFrameTransformer(name="test_task")
        result = task.execute(
            result=extractor_result,
            file_paths=pd.DataFrame({"s3_orders_file_url": ["a.csv"]}),
            params=Params(fill_by_fill_flag=False),
        )
        result_df = pd.read_csv(result.path).fillna(pd.NA)
        expected_df = pd.read_csv(EXPECTED_FILE_PATH_WITHOUT_FILL_BY_FILL).fillna(pd.NA)

        # 1 NEWO, 1 CAME and 1 PARF should be created
        pd.testing.assert_frame_equal(result_df, expected_df)

    def test_record_transformation_with_remove_parf_fill_before_zero_cancel(
        self, mocker
    ):
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "dummy1.dev.steeleye.co"

        mock_bundle = mocker.patch.object(
            SettingsCls, "bundle", new_callable=mocker.PropertyMock
        )
        mock_bundle.return_value = "order-bbg-emsi-controller"

        order_data = pd.read_csv(TEST_FILE_PATH)
        instrument_data = pd.read_csv(TEST_INSTRUMENT_FILE_PATH)

        order_data.loc[
            order_data[SourceColumns.ACTIVITY] == "New-route", SourceColumns.TICKER
        ] = "ABC"
        order_data.loc[
            order_data[SourceColumns.ACTIVITY] == "Exec-Cancel", SourceColumns.TICKER
        ] = "DEF"
        extractor_result = dict(
            orders=order_data,
            instrument=instrument_data,
        )

        fetch_data_from_instrument_store_return_value = {
            "ABC": pd.DataFrame(
                {
                    SourceColumns.PARSE_KEY: ["ABC"],
                    SourceColumns.ISIN_INSTRUMENT: ["ISIN1"],
                    SourceColumns.LOCAL_EXCH_SYMBOL: ["LCH1"],
                    SourceColumns.LAST_MARKET_INSTRUMENT: ["LMI1"],
                    SourceColumns.SECURITY_NAME: ["IIO"],
                },
            ),
            "DEF": pd.DataFrame(
                {
                    SourceColumns.PARSE_KEY: ["DEF"],
                    SourceColumns.ISIN_INSTRUMENT: ["ISIN2"],
                    SourceColumns.LOCAL_EXCH_SYMBOL: ["LCH2"],
                    SourceColumns.LAST_MARKET_INSTRUMENT: ["LMI2"],
                    SourceColumns.SECURITY_NAME: ["OOI"],
                },
            ),
        }

        mocker.patch(
            "swarm_tasks.order.feed.bbg.emsi.record_transformer.fetch_data_from_instrument_store",
            return_value=fetch_data_from_instrument_store_return_value,
        )

        # making sure Cancel has zero quantity and latest time
        extractor_result["orders"].loc[108, SourceColumns.FILLED_QUANTITY] = 0
        extractor_result["orders"].loc[
            108, SourceColumns.RECEIVE_DATE_TIME
        ] = "04/04/2024 10:55:00.000000"

        task = BbgEmsiFrameTransformer(name="test_task")
        result = task.execute(
            result=extractor_result,
            file_paths=pd.DataFrame({"s3_orders_file_url": ["a.csv"]}),
            params=Params(
                fill_by_fill_flag=True, remove_parf_fill_before_zero_cancel=True
            ),
        )
        result_df = pd.read_csv(result.path).fillna(pd.NA)
        expected_df = pd.read_csv(
            EXPECTED_FILE_PATH_WITH_REMOVE_PARF_FILL_BEFORE_ZERO_CANCEL
        ).fillna(pd.NA)

        # 1 NEWO, 1 CAME
        pd.testing.assert_frame_equal(result_df, expected_df)

    def test_record_transformation_without_parse_key(self, mocker):
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "dummy1.dev.steeleye.co"

        mock_bundle = mocker.patch.object(
            SettingsCls, "bundle", new_callable=mocker.PropertyMock
        )
        mock_bundle.return_value = "order-bbg-emsi-controller"

        mock_auditor = mocker.patch.object(BbgEmsiFrameTransformer, "auditor")
        mock_auditor.return_value = Auditor(task_name="order-bbg-emsi-controller")
        mock_auditor.add.return_value = None

        task = BbgEmsiFrameTransformer(
            name="test_task",
        )
        extractor_result = dict(
            orders=pd.read_csv(TEST_FILE_PATH_ORDER_NUMBER_MISSING),
            instrument=pd.read_csv(TEST_INSTRUMENT_FILE_PATH_ORDER_NUMBER_MISSING),
        )
        result = task.execute(
            result=extractor_result,
            file_paths=pd.DataFrame({"s3_orders_file_url": ["a.csv"]}),
            params=Params(fill_by_fill_flag=True),
        )
        result_df = pd.read_csv(result.path).fillna(pd.NA)
        expected_df = pd.read_csv(EXPECTED_FILE_PATH_WITHOUT_ORDER_NUMBER).fillna(pd.NA)

        pd.testing.assert_frame_equal(result_df, expected_df)
        expected_audit_messages = [
            "1 instrument record(s) with no parseKey and no order number were skipped.",
            "1 instrument record(s) with no parseKey were skipped.",
        ]

        actual_audit_messages = [
            call_args[0][0] for call_args in mock_auditor.add.call_args_list
        ]

        assert actual_audit_messages == expected_audit_messages

    @staticmethod
    def pytest_sessionfinish():
        with contextlib.suppress(FileNotFoundError):
            shutil.rmtree(Path(SCRIPT_PATH).joinpath("tmp_dir"))
