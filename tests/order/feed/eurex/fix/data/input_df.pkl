��i      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKI��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�	S3FileURL��BeginString��
BodyLength��MsgType��	MsgSeqNum��SenderCompID��SendingTime��TargetCompID��Account��CumQty��ExecID��ExecInst��SecurityIDSource��OrderID��OrderQty��	OrdStatus��OrdType��Price��
SecurityID��Side��Symbol��TimeInForce��PositionEffect��ExecType��	LeavesQty��ff_453��PartyID��
PartyIDSource��	PartyRole��ff_802��
PartySubID��PartySubIDType��CustOrderHandlingInst��ProductComplex��TradingCapacity��ff_25008��ff_30060��CheckSum��LastMkt��LastPx��LastQty��	TradeDate��MultiLegReportingType��ff_552��SideTradeID��PartyRoleQualifier��ff_25027��
OrderCategory��SideLiquidityInd��
TradeReportID��	MatchType��TrdType��TransferReason��TradeReportType��
TrdMatchID��TradeID��MessageEventSource��ff_1116��RootPartyID��RootPartyIDSource��
RootPartyRole��ff_1907��RegulatoryTradeID��RegulatoryTradeIDType��SecondaryExecID��LastLiquidityInd��	Triggered��ExecRestatementReason��Text��SecondaryOrderID��
ExDestination��PossDupFlag��StopPx�et�b�name�Nu��R�h
�pandas.core.indexes.range��
RangeIndex���}�(hoN�start�K �stop�K�step�Ku��R�e]�hhK ��h��R�(KKIK��h!�]�(��s3://jose.dev.steeleye.co/ingress/raw/order-feed-eurex-fix/ON-3393/4d4d53a64189e29e61d4e44ec15a349b46ff4cd905e128777c4146f28be02b00_54167.fix���s3://jose.dev.steeleye.co/ingress/raw/order-feed-eurex-fix/ON-3393/76dfe0d624951a75551374851c762f1240cb6ed17ebe40d8f6280bee845ad3d9_54169.fix���s3://jose.dev.steeleye.co/ingress/raw/order-feed-eurex-fix/ON-3393/91ca25bdffdd864b25b485f98b8025270adc1a4b877e692b9690ad1d225b4943_54168.fix���s3://jose.dev.steeleye.co/ingress/raw/order-feed-eurex-fix/ON-3393/9c87218aacbb539f139f816fe0d5323c57d98a76722015f2b0ace7b165ba66d9_54170.fix���s3://jose.dev.steeleye.co/ingress/raw/order-feed-eurex-fix/ON-3393/d9f2c9e89c2d6868aa41b3a19355b0a5dfeb0559b0aa1969456917ff9d2cf39e_54171.fix�h�h�h��FIX.4.4��FIX.4.4��FIX.4.4��FIX.4.4��FIX.4.4�h�h�h�M�M�MM�M�M�M�M��8��AE�h�h��AE�h�h�h�M��M��M��M��M��M��M��M�ӌXEUR��XEUR��XEUR��XEUR��XEUR�h�h�h��20230505-08:23:31.512��20230505-08:23:31.512��20230505-08:23:31.512��20230505-08:23:31.516��20230505-08:23:31.516�h�h�h��1309011-XEUR-FIX_LF-B-PROD-001��1309011-XEUR-FIX_LF-B-PROD-001��1309011-XEUR-FIX_LF-B-PROD-001��1309011-XEUR-FIX_LF-B-PROD-001��1309011-XEUR-FIX_LF-B-PROD-001�h�h�h��A1��A1��A1��A1��A1�h�h�h�G        G@f�     G@f�     G@l�     G@l�     G@l�     G@l�     G@l�     �12_1683235670317537075_1_A��pandas._libs.missing��NA����12_1683235670317537075_1_F_0��12_1683235670317537168_2_F_0�h�h�h�h��H�h�h�h�h�h�h�h��M�h�h�h�h�h�h�h��1683244800385194977��1683244800385194977��1683244800385194977��1683244800385194977��1683244800385194977�h�h�h�G@l�     h�G@l�     G@l�     h�h�h�h��0�h��1��2�h�h�h�h�h�h�h�h�h�h�h�h�G@Z
=p��G@Z
=p��G@Z
=p��G@Z
=p��G@Z
=p��G@Z
=p��G@Z
=p��G@Z
=p�׌8347917��8347917��8347917��8347917��8347917�h�h�h�h�h�h�h�h�h�h�h��690��690��690��690��690�h�h�h�h�h�h�h�h�h�h�h��O�h�h�h�h�h�h�h�h�h��F�h�h�h�h�h�G@l�     G@G�     G@G�     G        G        G        G        G        �5�h�h��3�h�h�h�h�]�(�65282�h��16755��65282��107704�e]�(�MSFFR��65282��16755��107704��PIEFR��ECAG�h�h�e]�(�65282�h��16755��65282��107704�e]�(�16755��65282��107704�e]�(�MSFFR��65282��16755��107704��PIEFR��ECAG�h�h�eh�h�h�]�(�D�h�h�h�h�e]�(h�h�h�h�h�h݌P�h�e]�(h�h�h�h�h�e]�(h�h�h�e]�(h�h�h�h�h�h�h�h�eh�h�h�]�(K$KK;KK7e]�(KKK;K7KKKKe]�(K$KK;KK7e]�(K;KK7e]�(KKK;K7KKKKeh�h�h�h�h�h�h�h�h�h�h��PAT001��PAT001��PAT001�h��PAT001�h�h�h�KKKh�KKKK�W�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�KKKKKKKK�KBLPAMO��KBLPAMO��KBLPAMO��KBLPAMO��KBLPAMO�h�h�h�1683275011509604885��1683275011509604885��1683275011509604885��1683275011511790452��1683275011511790452�h�h�h��132��011��218��241��129�h�h�h�h��XEUR�h�h��XEUR�h�h�h�h�G@Z
=p��G@Z
=p��G@Z
=p��G@Z
=p��G@Z
=p��G@Z
=p��G@Z
=p��h�G@f�     G@f�     G@G�     G@G�     G@G�     G@G�     G@G�     h��20230505�h�h��20230505�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��1710800�h�h��1710900�j  j  j  h�Kh�h�KKKKh��355�h�h��355�j  j  j  h�h�h�h�h�h�h�h�h�Kh�h�KKKKh��12_1_690_12�h�h��12_1_690_13�j  j  j  h��4�h�h�j  j  j  j  h�K h�h�K K K K h�h�h�h�h�h�h�h�h�K h�h�K K K K h��4794��4794��4795��4795�j
  h�h�h��4778�h�h��4779�j  j  j  h��200�h�h��200�j  j  j  h�h�h�h�h�h�h�h�h��XEUR�h�h��XEUR�j  j  j  h��G�h�h�j  j  j  j  h�KIh�h�KIKIKIKIh�h�h�h�h�h�h�h�h��41000000000000083479170168327501150960488500000004794�h�h��41000000000000083479170168327501151179045200000004795�j  j  j  h�Kh�h�KKKKh�h��1710800��1710900�h�h�h�h�h�h�KKh�h�h�h�h�h�K K h�h�h�h�h�h�h�Klh�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�ba]�h
h}�(hhhK ��h��R�(KKI��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3h4h5h6h7h8h9h:h;h<h=h>h?h@hAhBhChDhEhFhGhHhIhJhKhLhMhNhOhPhQhRhShThUhVhWhXhYhZh[h\h]h^h_h`hahbhchdhehfhghhhihjhkhlhmet�bhoNu��R�a}��0.14.1�}�(�axes�h
�blocks�]�}�(�values�h~�mgr_locs��builtins��slice���K KIK��R�uaust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.