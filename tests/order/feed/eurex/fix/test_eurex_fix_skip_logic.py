from pathlib import Path

import pandas as pd
import pytest

from swarm_tasks.order.feed.eurex.fix.eurex_fix_skip_logic import EurexFixSkipLogic

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data/")
INPUT_DF_PATH = TEST_FILES_DIR.joinpath("input_df.pkl").as_posix()
EXPECTED_DF_PATH = TEST_FILES_DIR.joinpath("expected_df.pkl").as_posix()


@pytest.fixture
def input_df() -> pd.DataFrame:
    return pd.read_pickle(INPUT_DF_PATH)


@pytest.fixture
def expected_df() -> pd.DataFrame:
    return pd.read_pickle(EXPECTED_DF_PATH)


class TestEurexFixSkipLogic:
    def test_end_to_end(self, input_df, expected_df):

        task = EurexFixSkipLogic(name="EurexFixSkipLogic")

        result = task.process(source_frame=input_df)

        pd.testing.assert_frame_equal(result, expected_df)
