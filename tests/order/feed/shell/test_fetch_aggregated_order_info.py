from pathlib import Path

import pandas as pd
import pytest
from addict import addict
from se_trades_tasks.order.transformations.bbg.audt.static import SourceColumns
from swarm.conf import Settings
from swarm.conf import SettingsCls

from swarm_tasks.order.feed.shell import fetch_aggregated_order_info
from swarm_tasks.order.feed.shell.fetch_aggregated_order_info import (
    FetchAggregatedOrderInfo,
)
from swarm_tasks.order.feed.shell.fetch_aggregated_order_info import Resources
from swarm_tasks.order.feed.shell.static import (
    ES_COLUMNS_FOR_AGGREGATE_BACKFILL_WITH_PREFIX,
)


INPUT_PATH = Path(__file__).parent.joinpath("data")
INPUT_HITS = INPUT_PATH.joinpath("input_hits_aggregated_order_info.pkl")
EXPECTED_RESULT = INPUT_PATH.joinpath("expected_aggregated_order_info.pkl")


@pytest.fixture
def input_source_frame():
    return pd.DataFrame(
        {SourceColumns.AGGR_FROM: ["1249764", "1244447", "1245728", pd.NA]}
    )


@pytest.fixture
def normal_es_mock_hits():
    return pd.read_pickle(INPUT_HITS)


@pytest.fixture
def empty_es_mock_hits():
    return pd.DataFrame()


@pytest.fixture
def normal_expected_outcome():
    return pd.read_pickle(EXPECTED_RESULT)


@pytest.fixture
def empty_expected_outcome():
    return pd.DataFrame(
        data=pd.NA,
        index=list(range(4)),
        columns=ES_COLUMNS_FOR_AGGREGATE_BACKFILL_WITH_PREFIX,
    )


class TestFetchAggregatedOrderInfo:
    @pytest.mark.parametrize(
        "es_mock_hits,expected_outcome",
        [
            ("empty_es_mock_hits", "empty_expected_outcome"),
            ("normal_es_mock_hits", "normal_expected_outcome"),
        ],
    )
    def test_end_to_end(
        self, mocker, request, input_source_frame, es_mock_hits, expected_outcome
    ):
        Settings.STACK = "dev-blue"
        Settings.FLOW_ID = "dummy.dev.steeleye.co:order-feed-samco-bbg-audt-processor"
        mock_connections = mocker.patch.object(
            SettingsCls, "connections", new_callable=mocker.PropertyMock
        )
        mock_connections.return_value = addict.Dict(
            {
                "tenant-data": {
                    "meta": {
                        "id": "&id",
                        "model": "&model",
                        "hash": "&hash",
                        "prefix": "&",
                        "key": "&key",
                        "expiry": "&expiry",
                    },
                    "MAX_TERMS_SIZE": 1024,
                }
            }
        )

        mock_es_scroll = mocker.patch.object(fetch_aggregated_order_info, "es_scroll")
        mock_es_scroll.return_value = request.getfixturevalue(es_mock_hits)

        task = FetchAggregatedOrderInfo(name="TestAggregatedOrderInfo")
        result = task.process(
            source_frame=input_source_frame,
            resources=Resources(es_client_key="tenant-data"),
        )

        pd.testing.assert_frame_equal(result, request.getfixturevalue(expected_outcome))
