��Y      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�buySell��traderFileIdentifier��sellerFileIdentifier��id�� transactionDetails.priceCurrency��"transactionDetails.tradingCapacity��#transactionDetails.buySellIndicator�� transactionDetails.priceNotation��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��-instrumentDetails.instrument.instrumentIdCode�� priceFormingData.initialQuantity��executionDetails.orderType�� executionDetails.tradingCapacity��executionDetails.orderStatus��!executionDetails.buySellIndicator��#transactionDetails.quantityNotation��!transactionDetails.settlementDate��/instrumentDetails.instrument.instrumentFullName��"priceFormingData.remainingQuantity��priceFormingData.price��executionDetails.validityPeriod�et�b�name�Nu��R�h
�pandas.core.indexes.range��
RangeIndex���}�(h;N�start�K �stop�K�step�Ku��R�e]�(hhK ��h��R�(KKK��h!�]�(�2�hMhMhMhMhM�
id:8082391��
id:8082391��
id:8082391��
id:8082391��
id:8082391��
id:8082391��id:12��id:12��id:12��id:12��id:12��id:12��1245728��1245728��1245728��1245728��1245728��1245728��EUR��EUR��EUR��EUR��EUR��EUR��DEAL��DEAL��DEAL��DEAL��DEAL��DEAL��SELL��SELL��SELL��SELL��SELL��SELL��MONE��MONE��MONE��MONE��MONE��MONE��
id:8082391��
id:7292237��
id:7292237��
id:7292237��
id:8082391��
id:7292237��DE000A2GS5D8��DE000A2GS5D8��DE000A2GS5D8��DE000A2GS5D8��DE000A2GS5D8��DE000A2GS5D8��MKT��MKT��MKT��MKT��MKT��MKT��DEAL��DEAL��DEAL��DEAL��DEAL��DEAL��CHMO��CHMO��CHMO��CHMO��CHMO��NEWO��SELL��SELL��SELL��SELL��SELL��SELL�G�      �UNIT��UNIT��UNIT�G�      �UNIT�G�      �
2022-02-09��
2022-02-09��
2022-02-09�G�      �
2022-02-09�G�      �DERMAPHARM HOLDING SE(DMP GR)��DERMAPHARM HOLDING SE(DMP GR)��DERMAPHARM HOLDING SE(DMP GR)�G�      �DERMAPHARM HOLDING SE(DMP GR)�G�      ]��GTCV�a]��GTCV�a]��GTCV�aG�      ]��GTCV�aet�bhhK ��h��R�(KKK��h�f8�����R�(K�<�NNNJ����J����K t�b�C�     4�@     4�@     4�@     4�@     4�@     4�@      �     4�@      �      �      �     4�@      ������iQ@�����iQ@�����iQ@      ������iQ@�t�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h'h(h)h*h+h,h-h.h0h1h2h3h4h5h6h9et�bh;Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h/h7h8et�bh;Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hJ�mgr_locs�hhK ��h��R�(KK��h�i8�����R�(Kh�NNNJ����J����K t�b�C�                                                                	                     
                                          �t�bu}�(h�h�h�hhK ��h��R�(KK��hމC
                     �t�bueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.