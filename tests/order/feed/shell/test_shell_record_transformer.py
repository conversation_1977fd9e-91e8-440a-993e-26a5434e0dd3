import contextlib
import shutil
from pathlib import Path

import pandas as pd
import pytest
from prefect.engine.signals import SKIP
from se_core_tasks.core.core_dataclasses import FileSplitterResult

from swarm_tasks.order.feed.shell.record_transformer import (
    ShellFrameTransformer,
)

SCRIPT_PATH = Path(__file__).parent
TEST_FILE_PATH = SCRIPT_PATH.joinpath(r"test_file.csv")
EXPECTED_FILE_PATH = SCRIPT_PATH.joinpath(r"expected_result.csv")


class TestShellOrderRecordTransformer:
    """
    Test Shell Order Record Creation Logic
    """

    def test_empty_data_scenario(self):
        with pytest.raises(SKIP):
            task = ShellFrameTransformer(name="test_task")
            task.execute(pre_process_result=dict())

    def test_record_transformation(self):
        task = ShellFrameTransformer(name="test_task")
        file_splitter_result_list = [
            FileSplitterResult(path=TEST_FILE_PATH, batch_index=0)
        ]
        result = task.execute(
            file_splitter_result_list=file_splitter_result_list,
        )
        result_df = pd.read_csv(result[0].path).fillna(pd.NA)
        expected_df = pd.read_csv(EXPECTED_FILE_PATH).fillna(pd.NA)

        assert not pd.testing.assert_frame_equal(result_df, expected_df)

    @staticmethod
    def teardown_method():
        with contextlib.suppress(FileNotFoundError):
            shutil.rmtree(Path(SCRIPT_PATH).joinpath("tmp_dir"))
