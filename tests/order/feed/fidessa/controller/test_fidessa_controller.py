from abc import ABC
from datetime import datetime
from pathlib import Path

import pandas as pd
import pytest
from freezegun import freeze_time
from moto import mock_aws
from prefect import context
from prefect.engine.signals import SKIP
from prefect.utilities.collections import DotDict
from se_core_tasks.abstractions.abstract_mock_s3 import AbstractMockS3
from se_core_tasks.core.core_dataclasses import S3File
from swarm.conf import Settings
from swarm.conf import SettingsCls

from swarm_tasks.order.feed.fidessa.controller.fidessa_controller import (
    FidessaController,
)
from swarm_tasks.order.feed.fidessa.controller.fidessa_controller import Params

Settings.FLOW_ID = "puneeth.uat.steeleye.co:order-feed-fidessa-controller"
context.swarm = DotDict()
context.swarm.targets_dir = Path("/tmp")


CURRENT_PATH = Path(__file__).parent
INPUT_ARC_FILE_PATH = CURRENT_PATH.joinpath("data/arc.csv")
INPUT_MARKET_FILE_PATH = CURRENT_PATH.joinpath("data/market.csv")


@pytest.fixture()
def file_url() -> str:
    return "s3://puneeth.uat.steeleye.co/flows/order-feed-fidessa-controller/ARC_EVENTS-Exchange_Order-20210617-part0001.csv"


@pytest.fixture()
def prefix_arc_input_expected_df() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "ARC_EVENTS_FILE_PATH": [
                "flows/order-feed-fidessa-controller/prefix_ARC_EVENTS-Exchange_Order-20221109-part0001.csv"
            ],
            "MARKET_ORDER_FILE_PATH": [
                "flows/order-feed-fidessa-controller/prefix_MARKET_ORDER-202211091730.csv"
            ],
            "REALM": ["test.dev.steeleye.co"],
        }
    )


@pytest.fixture()
def prefix_market_input_expected_df() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "ARC_EVENTS_FILE_PATH": [
                "flows/order-feed-fidessa-controller/prefix2_ARC_EVENTS-Exchange_Order-20221109-part0001.csv"
            ],
            "MARKET_ORDER_FILE_PATH": [
                "flows/order-feed-fidessa-controller/prefix2_MARKET_ORDER-202211091730.csv"
            ],
            "REALM": ["test.dev.steeleye.co"],
        }
    )


@pytest.fixture()
def input_params() -> Params:
    input_params = Params(
        arc_events_file_name_pattern=r"ARC_EVENTS-Exchange_Order-(?P<src_date>\d{"
        r"8})-\w+.csv",
        source_s3_key_prefix="flows/order-feed-fidessa-controller",
        target_s3_key_prefix="flows/order-feed-fidessa",
    )
    return input_params


class TestFidessaController(object):
    """
    Test cases for "TestFidessaController" class
    """

    task_was_initialized = False

    def test_pair_key_found_valid_timestamps(
        self, file_url: str, input_params: Params, mocker
    ):
        task = FidessaController(name="test-fidessa-controller", params=input_params)
        pd.DataFrame()
        mock_get_pair_key = mocker.patch.object(FidessaController, "_get_pair_file_key")
        mock_get_pair_key.return_value = "test"
        mock_check_pair_file = mocker.patch(
            "swarm_tasks.order.feed.fidessa.controller.fidessa_controller.check_pair_file_on_s3"
        )
        mock_check_ts = mocker.patch("swarm_tasks.utilities.s3.get_last_modified_ts")
        mock_check_ts.side_effect = [
            datetime.strptime("2021-10-19 13:00:00", "%Y-%m-%d %H:%M:%S"),
            datetime.strptime("2021-10-19 12:00:00", "%Y-%m-%d %H:%M:%S"),
        ]
        with mocker.patch(
            "swarm_tasks.order.feed.fidessa.controller.fidessa_controller"
            ".FidessaController._get_pair_file_key",
            mock_get_pair_key,
        ):
            with mocker.patch(
                "swarm_tasks.order.feed.fidessa.controller.fidessa_controller.check_pair_file_on_s3",
                mock_check_pair_file,
            ):
                with mocker.patch(
                    "swarm_tasks.utilities.s3.get_last_modified_ts", mock_check_ts
                ):
                    result = task.execute(file_url=file_url, params=input_params)
        assert isinstance(result, S3File)

    def test_pair_key_found_invalid_timestamps(
        self, file_url: str, input_params: Params, mocker
    ):
        task = FidessaController(name="test-fidessa-controller", params=input_params)
        pd.DataFrame()
        mock_get_pair_key = mocker.patch.object(FidessaController, "_get_pair_file_key")
        mock_get_pair_key.return_value = "test"
        mock_check_pair_file = mocker.patch(
            "swarm_tasks.order.feed.fidessa.controller.fidessa_controller.check_pair_file_on_s3"
        )

        mock_check_ts = mocker.patch("swarm_tasks.utilities.s3.get_last_modified_ts")
        mock_check_ts.side_effect = [
            datetime.strptime("2021-10-19 12:00:00", "%Y-%m-%d %H:%M:%S"),
            datetime.strptime("2021-10-19 13:00:00", "%Y-%m-%d %H:%M:%S"),
        ]
        with mocker.patch(
            "swarm_tasks.order.feed.fidessa.controller.fidessa_controller"
            ".FidessaController._get_pair_file_key",
            mock_get_pair_key,
        ):
            with mocker.patch(
                "swarm_tasks.order.feed.fidessa.controller.fidessa_controller.check_pair_file_on_s3",
                mock_check_pair_file,
            ):
                with mocker.patch(
                    "swarm_tasks.utilities.s3.get_last_modified_ts",
                    mock_check_ts,
                ):
                    with pytest.raises(SKIP):
                        task.execute(file_url=file_url, params=input_params)

    @mock_aws
    @pytest.mark.parametrize(
        "input_file_url,expected_dataframe",
        [
            (
                "s3://test.dev.steeleye.co/flows/order-feed-fidessa-controller/prefix_ARC_EVENTS-Exchange_Order-20221109-part0001.csv",
                "prefix_arc_input_expected_df",
            ),
            (
                "s3://test.dev.steeleye.co/flows/order-feed-fidessa-controller/prefix2_MARKET_ORDER-202211091730.csv",
                "prefix_market_input_expected_df",
            ),
        ],
    )
    def test_pair_key_with_prefix(
        self,
        mocker,
        input_file_url: str,
        input_params: Params,
        expected_dataframe: pd.DataFrame,
        request,
    ):
        self.load_mock_s3(mocker=mocker)
        task = FidessaController(name="test-fidessa-controller", params=input_params)

        result = task.execute(file_url=input_file_url, params=input_params)

        result_df = pd.read_csv(result.file_path.as_posix())

        pd.testing.assert_frame_equal(
            result_df, request.getfixturevalue(expected_dataframe)
        )

    def load_mock_s3(self, mocker):
        fake_bucket_name = "test.dev.steeleye.co"

        if not self.task_was_initialized:
            mock_s3_instance = FidessaMockS3()
            mock_s3_instance.create_mock_bucket(bucket=fake_bucket_name)
            mock_s3_instance.load_data_into_mock_s3()

        self.task_was_initialized = True
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = fake_bucket_name


class FidessaMockS3(AbstractMockS3, ABC):
    def load_data_into_mock_s3(self):

        files_to_load_dict = {
            "prefix_ARC_EVENTS-Exchange_Order-20221109-part0001.csv": {
                "path": INPUT_ARC_FILE_PATH,
                "time": "2022-06-22 12:00:02",
            },
            "prefix_MARKET_ORDER-202211091730.csv": {
                "path": INPUT_MARKET_FILE_PATH,
                "time": "2022-06-22 12:00:01",
            },
            "prefix2_ARC_EVENTS-Exchange_Order-20221109-part0001.csv": {
                "path": INPUT_ARC_FILE_PATH,
                "time": "2022-06-22 12:00:01",
            },
            "prefix2_MARKET_ORDER-202211091730.csv": {
                "path": INPUT_MARKET_FILE_PATH,
                "time": "2022-06-22 12:00:02",
            },
        }

        for file_name, file_info in files_to_load_dict.items():
            with open(file_info.get("path")) as test_file:
                body = test_file.read()

            with freeze_time(file_info.get("time")):
                self.s3.put_object(
                    Bucket=self.bucket,
                    Key=f"flows/order-feed-fidessa-controller/{file_name}",
                    Body=body,
                )
