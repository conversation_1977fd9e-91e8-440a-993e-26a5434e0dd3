from pathlib import Path

import pandas as pd
import pytest
from se_core_tasks.utils.data_manipulation import fetch_column_names_from_csv
from swarm.task.io.read.result import FrameProducerResult

from swarm_tasks.io.read.aws.df_from_s3_csv import DfFromS3Csv
from swarm_tasks.order.feed.fidessa.processor.pre_process_fidessa_data import Params
from swarm_tasks.order.feed.fidessa.processor.pre_process_fidessa_data import static
from swarm_tasks.order.feed.fidessa.static import ArcEventsColumns
from swarm_tasks.order.feed.fidessa.static import DerivedColumns
from swarm_tasks.order.feed.fidessa.static import EventType

test_file_path = Path(__file__).parent.joinpath("data")


@pytest.fixture()
def empty_source_df() -> FrameProducerResult:
    """
    creates empty dataframe
    """
    df = pd.DataFrame()
    return FrameProducerResult(frame=df, batch_index=0)


@pytest.fixture()
def arc_events_data() -> pd.DataFrame:
    df = pd.read_csv(f"{test_file_path}/arc_events_sample.csv")
    return df


@pytest.fixture()
def arc_events_empty() -> pd.DataFrame:
    df = pd.read_csv(f"{test_file_path}/arc_events_empty.csv")
    return df


@pytest.fixture()
def arc_events_data_missing_columns() -> pd.DataFrame:
    df = pd.read_csv(f"{test_file_path}/arc_events_sample.csv")
    df.loc[:, static.ArcEventsColumns.PRIMARY_ID] = pd.NA
    df.loc[:, static.ArcEventsColumns.EXCHANGE_ORDER_ID] = pd.NA
    return df


@pytest.fixture()
def input_params() -> Params:
    """
    creates input params
    """
    params = Params(
        mic_code_mapping_s3_key="mapping_tables/order-feed-fidessa-processor/fidessa-mic-code-mapping.csv"
    )
    return params


@pytest.fixture()
def market_order_data() -> pd.DataFrame:
    df = pd.read_csv(f"{test_file_path}/market_order_sample.csv")
    return df


@pytest.fixture()
def market_order_empty() -> pd.DataFrame:
    df = pd.read_csv(f"{test_file_path}/market_order_empty.csv")
    return df


@pytest.fixture()
def market_order_data_without_header() -> pd.DataFrame:
    names = fetch_column_names_from_csv(
        file_path=Path(f"{test_file_path}/market_orders_header.csv")
    )
    df = pd.read_csv(f"{test_file_path}/market_order_without_header.csv", names=names)
    return df


@pytest.fixture()
def fidessa_controller_output() -> FrameProducerResult:
    df = pd.read_csv(f"{test_file_path}/fidessa_controller_output_sample.csv")
    return FrameProducerResult(frame=df, batch_index=0)


@pytest.fixture()
def mic_code_mapping() -> pd.DataFrame:
    df = pd.read_csv(f"{test_file_path}/fidessa-mic-code-mapping.csv")
    return df


@pytest.fixture()
def expected_result_all_col_in_source_df() -> pd.DataFrame:
    data = {
        "Primary ID": [
            "00021939266FOLO1-EO",
            "00021939266FOLO1-EO",
            "00031853951FELO1",
            "00031853952FELO1",
            "00021943843FOLO1-EO",
            "00021943843FOLO1-EO",
            "00031859368FELO1",
            "00031859369FELO1",
            "00031859370FELO1",
        ],
        "EXCHANGE_ORDER_ID": [
            pd.NA,
            "006MYzkv5w34",
            "006MYzkv5w34",
            "006MYzkv5w34",
            pd.NA,
            "006MYzUVpUht",
            "006MYzUVpUht",
            "006MYzUVpUht",
            "006MYzUVpUht",
        ],
        "Event Type": [
            "ExchangeOrderEntryRequested",
            "ExchangeOrderEntryAccepted",
            "ExchangeOrderFillEntered",
            "ExchangeOrderFillEntered",
            "ExchangeOrderEntryRequested",
            "ExchangeOrderEntryAccepted",
            "ExchangeOrderFillEntered",
            "ExchangeOrderFillEntered",
            "ExchangeOrderFillEntered",
        ],
        "DERIVED_ORDER_ID": [
            "00021939266FOLO1",
            "00021939266FOLO1",
            "00021939266FOLO1",
            "00021939266FOLO1",
            "00021943843FOLO1|006MYzUVpUht",
            "00021943843FOLO1|006MYzUVpUht",
            "00021943843FOLO1|006MYzUVpUht",
            "00021943843FOLO1|006MYzUVpUht",
            "00021943843FOLO1|006MYzUVpUht",
        ],
        "DERIVED_HIERARCHY": [
            "Standalone",
            "Standalone",
            "Standalone",
            "Standalone",
            "Child",
            "Child",
            "Child",
            "Child",
            "Child",
        ],
        "Event Timestamp": [
            "20210617 08:07:57.933551 +0100s",
            "20210617 08:07:57.938786 +0100s",
            "20210617 08:07:57.939037 +0100s",
            "20210617 08:07:57.939871 +0100s",
            "20210617 15:36:16.931956 +0100s",
            "20210617 15:36:16.937320 +0100s",
            "20210617 15:36:16.937787 +0100s",
            "20210617 15:36:16.937956 +0100s",
            "20210617 15:36:16.958267 +0100s",
        ],
        "DERIVED_ORDER_RECEIVED_DT": [
            "20210617 08:07:57.933551 +0100s",
            "20210617 08:07:57.933551 +0100s",
            "20210617 08:07:57.933551 +0100s",
            "20210617 08:07:57.933551 +0100s",
            "20210617 15:36:16.931956 +0100s",
            "20210617 15:36:16.931956 +0100s",
            "20210617 15:36:16.931956 +0100s",
            "20210617 15:36:16.931956 +0100s",
            "20210617 15:36:16.931956 +0100s",
        ],
        "DERIVED_ORDER_SUBMITTED_DT": [
            "20210617 08:07:57.938786 +0100s",
            "20210617 08:07:57.938786 +0100s",
            "20210617 08:07:57.938786 +0100s",
            "20210617 08:07:57.938786 +0100s",
            "20210617 15:36:16.937320 +0100s",
            "20210617 15:36:16.937320 +0100s",
            "20210617 15:36:16.937320 +0100s",
            "20210617 15:36:16.937320 +0100s",
            "20210617 15:36:16.937320 +0100s",
        ],
        "DERIVED_AGGREGATE_ORDER_ID": [
            pd.NA,
            pd.NA,
            pd.NA,
            pd.NA,
            pd.NA,
            pd.NA,
            pd.NA,
            pd.NA,
            pd.NA,
        ],
        static.DerivedColumns.DERIVED_ISIN_CODE: [
            "GB0030517261",
            "GB0030517261",
            "GB0030517261",
            "GB0030517261",
            "IE0033024807",
            "IE0033024807",
            "IE0033024807",
            "IE0033024807",
            "IE0033024807",
        ],
        static.DerivedColumns.DERIVED_BUY_SELL: [
            "B",
            "B",
            "B",
            "B",
            "B",
            "B",
            "B",
            "B",
            "B",
        ],
        static.DerivedColumns.DERIVED_LIMIT_PRICE: [
            1330.0,
            1330.0,
            1330.0,
            1330.0,
            1054.0,
            1054.0,
            1054.0,
            1054.0,
            1054.0,
        ],
        static.DerivedColumns.DERIVED_STOP_PRICE: pd.NA,
        static.DerivedColumns.DERIVED_PARENT_ORDER_ID: [
            pd.NA,
            pd.NA,
            pd.NA,
            pd.NA,
            "00021943843FOLO1",
            "00021943843FOLO1",
            "00021943843FOLO1",
            "00021943843FOLO1",
            "00021943843FOLO1",
        ],
        static.DerivedColumns.DERIVED_INITIAL_QTY: [
            186.0,
            186.0,
            186.0,
            186.0,
            5000.0,
            5000.0,
            5000.0,
            5000.0,
            5000.0,
        ],
        static.DerivedColumns.DERIVED_BOOK_VIEW_CODE: [
            "TRT5",
            "TRT5",
            "TRT5",
            "TRT5",
            "151",
            "151",
            "151",
            "151",
            "151",
        ],
        static.DerivedColumns.DERIVED_ENTERED_BY: [
            "rmirams",
            "rmirams",
            "rmirams",
            "rmirams",
            "Lbarford",
            "Lbarford",
            "Lbarford",
            "Lbarford",
            "Lbarford",
        ],
        static.DerivedColumns.DERIVED_COUNTERPARTY_CODE: [
            "CLRXCHZZXXX",
            "CLRXCHZZXXX",
            "CLRXCHZZXXX",
            "CLRXCHZZXXX",
            "CLRXCHZZXXX",
            "CLRXCHZZXXX",
            "CLRXCHZZXXX",
            "CLRXCHZZXXX",
            "CLRXCHZZXXX",
        ],
        static.DerivedColumns.DERIVED_MARKET_ID: [
            "LSE-STMM",
            "LSE-STMM",
            "LSE-STMM",
            "LSE-STMM",
            "LSE-STMM",
            "LSE-STMM",
            "LSE-STMM",
            "LSE-STMM",
            "LSE-STMM",
        ],
        static.DerivedColumns.DERIVED_INVESTMENT_DECISION: [
            "rmirams",
            "rmirams",
            "rmirams",
            "rmirams",
            "Lbarford",
            "Lbarford",
            "Lbarford",
            "Lbarford",
            "Lbarford",
        ],
        static.DerivedColumns.DERIVED_EXECUTION_DECISION: [
            "rmirams",
            "rmirams",
            "rmirams",
            "rmirams",
            "Lbarford",
            "Lbarford",
            "Lbarford",
            "Lbarford",
            "Lbarford",
        ],
    }
    return pd.DataFrame(data)


@pytest.fixture()
def limit_price_source_df():
    """Fixture for testing limit price logic (_get_limit_price method). The test data
    contains 2 order ids, each of which have multiple event types.
    First 5 records: Order id 00017755351UOLO0
    Last 6 records: Order id 00017757647UOLO0
    """
    df = pd.DataFrame(
        [
            # Record 1: EREQ Record for Order id 00017755351UOLO0
            {
                ArcEventsColumns.LIMIT_PRICE: "170",
                ArcEventsColumns.EVENT_TYPE: EventType.EXCHANGE_ORDER_ENTRY_REQUESTED,
                ArcEventsColumns.EVENT_TIMESTAMP: "20220105 08:02:17.460564 +0000",
                DerivedColumns.DERIVED_ORDER_ID: "00017755351UOLO0",
            },
            # Record 2: EA Record for Order id 00017755351UOLO0
            {
                ArcEventsColumns.LIMIT_PRICE: "172",
                ArcEventsColumns.EVENT_TYPE: EventType.EXCHANGE_ORDER_ENTRY_ACCEPTED,
                ArcEventsColumns.EVENT_TIMESTAMP: "20220105 08:02:17.478587 +0000",
                DerivedColumns.DERIVED_ORDER_ID: "00017755351UOLO0",
            },
            # Record 3: FE Record 1 for Order id 00017755351UOLO0
            {
                ArcEventsColumns.LIMIT_PRICE: pd.NA,
                ArcEventsColumns.EVENT_TYPE: EventType.EXCHANGE_ORDER_FILL_ENTERED,
                ArcEventsColumns.EVENT_TIMESTAMP: "20220105 08:05:31.375046 +0000",
                DerivedColumns.DERIVED_ORDER_ID: "00017755351UOLO0",
            },
            # Record 4: FE Record 2 for Order id 00017755351UOLO0
            {
                ArcEventsColumns.LIMIT_PRICE: pd.NA,
                ArcEventsColumns.EVENT_TYPE: EventType.EXCHANGE_ORDER_FILL_ENTERED,
                ArcEventsColumns.EVENT_TIMESTAMP: "20220105 08:05:31.375046 +0000",
                DerivedColumns.DERIVED_ORDER_ID: "00017755351UOLO0",
            },
            # Record 5: CA Record for Order id 00017755351UOLO0
            {
                ArcEventsColumns.LIMIT_PRICE: "172",
                ArcEventsColumns.EVENT_TYPE: EventType.EXCHANGE_ORDER_CANCEL_ACCEPTED,
                ArcEventsColumns.EVENT_TIMESTAMP: "20220105 08:05:57.866613 +0000 ",
                DerivedColumns.DERIVED_ORDER_ID: "00017755351UOLO0",
            },
            # Record 6: EREQ Record for Order id 00017757647UOLO0
            {
                ArcEventsColumns.LIMIT_PRICE: "3960.5",
                ArcEventsColumns.EVENT_TYPE: EventType.EXCHANGE_ORDER_ENTRY_REQUESTED,
                ArcEventsColumns.EVENT_TIMESTAMP: "20220105 15:23:26.211553 +0000",
                DerivedColumns.DERIVED_ORDER_ID: "00017757647UOLO0",
            },
            # Record 7: EA record for Record id 00017757647UOLO0
            {
                ArcEventsColumns.LIMIT_PRICE: "3960.5",
                ArcEventsColumns.EVENT_TYPE: EventType.EXCHANGE_ORDER_ENTRY_ACCEPTED,
                ArcEventsColumns.EVENT_TIMESTAMP: "20220105 15:23:26.229158 +0000",
                DerivedColumns.DERIVED_ORDER_ID: "00017757647UOLO0",
            },
            # Record 8: RA record for Record id 00017757647UOLO0
            {
                ArcEventsColumns.LIMIT_PRICE: "3958",
                ArcEventsColumns.EVENT_TYPE: EventType.EXCHANGE_ORDER_REPLACE_ACCEPTED,
                ArcEventsColumns.EVENT_TIMESTAMP: "20220105 15:39:25.125494 +0000",
                DerivedColumns.DERIVED_ORDER_ID: "00017757647UOLO0",
            },
            # Record 9: FE record for Record id 00017757647UOLO0
            {
                ArcEventsColumns.LIMIT_PRICE: pd.NA,
                ArcEventsColumns.EVENT_TYPE: EventType.EXCHANGE_ORDER_FILL_ENTERED,
                ArcEventsColumns.EVENT_TIMESTAMP: "20220105 15:39:25.132300 +0000",
                DerivedColumns.DERIVED_ORDER_ID: "00017757647UOLO0",
            },
            # Record 10: RA record 2for Record id 00017757647UOLO0
            {
                ArcEventsColumns.LIMIT_PRICE: "3963",
                ArcEventsColumns.EVENT_TYPE: EventType.EXCHANGE_ORDER_REPLACE_ACCEPTED,
                ArcEventsColumns.EVENT_TIMESTAMP: "20220105 15:41:33.132300 +0000",
                DerivedColumns.DERIVED_ORDER_ID: "00017757647UOLO0",
            },
            # Record 11: FE record 2 for Record id 00017757647UOLO0
            {
                ArcEventsColumns.LIMIT_PRICE: pd.NA,
                ArcEventsColumns.EVENT_TYPE: EventType.EXCHANGE_ORDER_FILL_ENTERED,
                ArcEventsColumns.EVENT_TIMESTAMP: "20220105 15:43:11.132300 +0000",
                DerivedColumns.DERIVED_ORDER_ID: "00017757647UOLO0",
            },
        ]
    )
    return df


@pytest.fixture()
def expected_result_get_limit_price():
    """Expected result for the _get_limit_price() test using the limit_price_source_df
    fixture as input df
    """
    return pd.Series(
        [
            # Record 1: EREQ (Order id 00017755351UOLO0)
            "170",
            # Record 2: EA (Order id 00017755351UOLO0)
            "172",
            # Record 3: First FE (Order id 00017755351UOLO0)
            "172",
            # Record 4: Second FE (Order id 00017755351UOLO0)
            "172",
            # Record 5: CA (Order id 00017755351UOLO0)
            "172",
            # Record 6: EREQ (Order id 00017757647UOLO0)
            "3960.5",
            # Record 7: EA (Order id 00017757647UOLO0)
            "3960.5",
            # Record 8: First RA (Order id 00017757647UOLO0)
            "3958",
            # Record 9: FE (Order id 00017757647UOLO0)
            "3958",
            # Record 10: Second RA (Order id 00017757647UOLO0)
            "3963",
            # Record 11: Second FE (Order id 00017757647UOLO0)
            "3963",
        ]
    )


@pytest.fixture()
def venue_computation_source_df():
    """
    Fixture for testing venue computation and fallback logic (_get_venue).
    Test data is structured in such a way that both code paths are followed.
    (Venue computation using EVENT_TYPE and its fallback computation using DESTINATION_EXCHANGE_ID)
    """
    return pd.DataFrame(
        {
            "Event Type": [
                "ExchangeOrderEntryRequested",
                "ExchangeOrderEntryAccepted",
                "ExchangeOrderFillEntered",
                "ExchangeOrderFillEntered",
                "ExchangeOrderEntryRequested",
                "ExchangeOrderEntryRequested",
            ],
            "EXECUTION_VENUE": [pd.NA, pd.NA, "AQXE", "XLON", pd.NA, pd.NA],
            "DERIVED_ORDER_ID": [
                "00017755343UOLO0",
                "00017755343UOLO0",
                "00017755343UOLO0",
                "00017755343UOLO0",
                "00017755343UOLO0",
                "00017755343UOLO1",
            ],
            "DESTINATION_EXCHANGE_ID": [pd.NA, pd.NA, pd.NA, pd.NA, pd.NA, "LSE"],
        },
    )


@pytest.fixture
def mocked_mic_mapping(mocker, mic_code_mapping):
    """
    Mocker to mock fetching the mic_mapping_file from S3
    """
    mock_mic_mapping_data = mocker.patch.object(DfFromS3Csv, "process")
    mock_mic_mapping_data.return_value = mic_code_mapping
    return mock_mic_mapping_data
