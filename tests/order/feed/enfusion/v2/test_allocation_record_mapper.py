from pathlib import Path

import pytest
from se_core_tasks.core.core_dataclasses import ExtractPathResult

from swarm_tasks.order.feed.enfusion.v2.allocation_record_mapper import (
    AllocationRecordMapper,
)
from swarm_tasks.order.feed.enfusion.v2.allocation_record_mapper import Params
from swarm_tasks.order.feed.enfusion.v2.static import AlloMapKeys

BASE_PATH: Path = Path(__file__).parent
DATA_PATH: Path = BASE_PATH.joinpath("data")
TEST_FILE: Path = DATA_PATH.joinpath("Example_Allocations.csv")


@pytest.fixture()
def expected_result():
    return {
        AlloMapKeys.BUY_SELL: {
            "JP36574000022024-09-03": "1",
            "PHS SEP4 17900C2024-09-03": "1",
            "NIKKEI 225 SEP242024-09-03": "1",
            "TOPIX SEP42024-09-04": "1",
        },
        AlloMapKeys.EARLIER_TRADING_DATE_TIME: {
            "JP36574000022024-09-03": "2024-09-03T00:00:00.000000Z",
            "PHS SEP4 17900C2024-09-03": "2024-09-03T00:00:00.000000Z",
            "NIKKEI 225 SEP242024-09-03": "2024-09-03T00:00:00.000000Z",
            "TOPIX SEP42024-09-04": "2024-09-04T00:00:00.000000Z",
        },
        AlloMapKeys.FIRST_TRANSACTION_REF_NUMBER: {
            "JP36574000022024-09-03": "662324954",
            "PHS SEP4 17900C2024-09-03": "662367554",
            "NIKKEI 225 SEP242024-09-03": "662314419",
            "TOPIX SEP42024-09-04": "662468880",
        },
        AlloMapKeys.MEAN_PRICE: {
            "JP36574000022024-09-03": 1537.2313772893774,
            "PHS SEP4 17900C2024-09-03": 0.0,
            "NIKKEI 225 SEP242024-09-03": 38782.0,
            "TOPIX SEP42024-09-04": 2731.875,
        },
        AlloMapKeys.NET_QUANTITY: {
            "JP36574000022024-09-03": 0.0,
            "PHS SEP4 17900C2024-09-03": 0.0,
            "NIKKEI 225 SEP242024-09-03": 15.0,
            "TOPIX SEP42024-09-04": 8.0,
        },
    }


class TestAllocationRecordMapper:
    def test_empty_result(self):
        extract_result = ExtractPathResult()
        extract_result.path = TEST_FILE

        task = AllocationRecordMapper(name="AllocationRecordMapper")
        params = Params(allocation_aggregation_flag=False)
        result = task.execute(file_url=extract_result, params=params)
        assert result == {}

    def test_aggregation_result(self, expected_result):
        extract_result = ExtractPathResult()
        extract_result.path = TEST_FILE

        task = AllocationRecordMapper(name="AllocationRecordMapper")
        params = Params(allocation_aggregation_flag=True)
        result = task.execute(file_url=extract_result, params=params)
        assert result == expected_result
