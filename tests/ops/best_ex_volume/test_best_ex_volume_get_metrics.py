import os

import pandas as pd
import pytest
from prefect.engine import signals
from swarm.conf import SettingsCls

# need to set these environment variables for the tests to run
os.environ["COGNITO_CLIENT_ID"] = "some_id"
os.environ["COGNITO_CLIENT_SECRET"] = "some_secret"
os.environ["COGNITO_AUTH_URL"] = "some_auth_url"

from swarm_tasks.ops.best_ex_volume.best_ex_volume_get_metrics import (  # noqa: E402
    BestExVolumeGetMetrics,
)


class TestBestExVolumeGetMetrics:
    """
    Test cases for BestExVolumeGetMetrics task
    """

    def test_end_to_end(
        self,
        mocker,
        elastic_raw_result,
        best_ex_volume_metrics_result,
        best_ex_volume_result,
    ) -> None:
        task = BestExVolumeGetMetrics(name="Testing")

        mocker.patch.object(
            SettingsCls,
            "realm",
            new_callable=mocker.PropertyMock,
            return_value="dummy.steeleye.co",
        )

        mocker.patch.object(
            BestExVolumeGetMetrics,
            "search_batch_elasticsearch",
            return_value=elastic_raw_result,
        )

        mocker.patch.object(
            BestExVolumeGetMetrics,
            "execute_best_ex_volume_plugin",
            return_value=best_ex_volume_result,
        )

        result = task.execute(
            flow_args='{"query": "TESTING", "populate_fields": {"transactionDetails.priceNotation": "MONE"}}'
        )
        pd.testing.assert_frame_equal(left=best_ex_volume_metrics_result, right=result)

    def test_best_ex_volume_get_metrics_raises_fail(self) -> None:
        task = BestExVolumeGetMetrics(name="Testing")
        with pytest.raises(signals.FAIL) as error:
            task.execute(flow_args="{}")

        assert error.match(
            "`query` missing in `SWARM_FLOW_ARGS`. It's required to run the task."
        )

    @pytest.mark.asyncio
    async def test_convert_elastic_result_to_df_end_to_end(
        self, elastic_raw_result, expected_elastic_result_df
    ) -> None:
        result = await BestExVolumeGetMetrics.convert_elastic_result_to_df(
            results=elastic_raw_result,
            populate_fields={"transactionDetails.priceNotation": "MONE"},
        )

        pd.testing.assert_frame_equal(left=expected_elastic_result_df, right=result)

    def test_get_best_ex_volumes_meta_frames_return_empty_df(
        self, mocker, expected_elastic_result_df
    ):
        task = BestExVolumeGetMetrics(name="Testing")

        mocker.patch.object(
            SettingsCls,
            "env",
            new_callable=mocker.PropertyMock,
            return_value="uat-blue",
        )

        fx_rates_df = task.get_best_ex_volume_fx_rates_df(df=expected_elastic_result_df)

        assert fx_rates_df.empty
