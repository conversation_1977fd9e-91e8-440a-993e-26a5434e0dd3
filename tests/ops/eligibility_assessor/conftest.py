from pathlib import Path

import pandas as pd
import pytest
from se_elastic_schema.static.mifid2 import RTS22TransactionStatus

from swarm_tasks.generic.instrument_fallback.static import WorkflowFields

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")

ES_RESULTS_DF = TEST_FILES_DIR.joinpath("es_results_df.pkl")
ELIGIBILITY_ASSESSOR_DF = TEST_FILES_DIR.joinpath("eligibility_assessor_result_df.pkl")
FINAL_DF = TEST_FILES_DIR.joinpath("final_result_df.pkl")
FINAL_UPDATE_DF = TEST_FILES_DIR.joinpath("final_result_update_df.pkl")


@pytest.fixture
def search_query():
    return {
        "size": 50,
        "query": {
            "bool": {
                "must_not": [{"exists": {"field": "&expiry"}}],
                "must": [
                    {
                        "match": {
                            WorkflowFields.STATUS: RTS22TransactionStatus.NON_REPORTABLE.value
                        }
                    }
                ],
            }
        },
        "sort": [{"&timestamp": {"order": "asc"}}, {"&id": {"order": "asc"}}],
        "search_after": [123456, "dummy_id"],
    }


@pytest.fixture
def es_results():
    return {
        "took": 5,
        "timed_out": False,
        "_shards": {"total": 1, "successful": 1, "skipped": 0, "failed": 0},
        "hits": {
            "total": 21569,
            "max_score": None,
            "hits": [
                {
                    "_index": "lighthouse_steeleye_co_rts22transaction",
                    "_type": "RTS22Transaction",
                    "_id": "YJYQJHAIRIQ4I5Q10XLXRQA:2021-12-21:NEWT",
                    "_score": None,
                    "_source": {
                        "date": "2021-12-21",
                        "sourceKey": "s3://lighthouse.steeleye.co/flows/tr-feed-beacon-lighthouse/COMPLIANCE_steeleye_2021-12-21.dat",
                        "&id": "YJYQJHAIRIQ4I5Q10XLXRQA:2021-12-21:NEWT",
                        "transactionDetails": {
                            "buySellIndicator": "BUYI",
                            "price": 1.549999952316284,
                            "priceCurrency": "USD",
                            "priceNotation": "MONE",
                            "quantity": 48.0,
                            "quantityCurrency": "USD",
                            "quantityNotation": "UNIT",
                            "tradingCapacity": "AOTC",
                            "tradingDateTime": "2021-12-21T14:54:59.711748Z",
                            "ultimateVenue": "XXXX",
                            "venue": "XXXX",
                        },
                        "transmissionDetails": {"orderTransmissionIndicator": False},
                        "workflow": {
                            "eligibility": {
                                "eligible": False,
                                "executionVenue": "XXXX",
                                "isDefaulted": False,
                                "onFirds": False,
                                "totv": False,
                                "underlyingOnFirds": False,
                                "utotv": False,
                            },
                            "isReported": False,
                            "status": "NON_REPORTABLE",
                            "validationStatus": "FAILED",
                        },
                        "marketIdentifiers": [
                            {
                                "labelId": "XXXXVIXOP2021-12-22 00:00:00",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12 00:00:00",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12-22 00:00:0023.00000000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12-22 00:00:000.23000000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12-22 00:00:000.02300000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12-22 00:00:000.00230000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12 00:00:0023.00000000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12 00:00:000.23000000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12 00:00:000.02300000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12 00:00:000.00230000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "VIXUS12/22/21P23Index",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "lei:549300il8tqt0jmdjj80",
                                "path": "parties.executingEntity",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "id:ms-deriv",
                                "path": "parties.counterparty",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "id:drojas",
                                "path": "parties.trader",
                                "type": "ARRAY",
                            },
                            {
                                "labelId": "id:drojas",
                                "path": "parties.executionWithinFirm",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "id:drojas",
                                "path": "parties.investmentDecisionWithinFirm",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "id:rhill_cash_jpm",
                                "path": "parties.buyer",
                                "type": "ARRAY",
                            },
                            {
                                "labelId": "id:ms-deriv",
                                "path": "parties.seller",
                                "type": "ARRAY",
                            },
                            {
                                "labelId": "lei:549300il8tqt0jmdjj80",
                                "path": "parties.buyerDecisionMaker",
                                "type": "ARRAY",
                            },
                        ],
                        "reportDetails": {
                            "investmentFirmCoveredDirective": True,
                            "reportStatus": "NEWT",
                            "transactionRefNo": "YJYQJHAIRIQ4I5Q10XLXRQA",
                        },
                        "&key": "RTS22Transaction:YJYQJHAIRIQ4I5Q10XLXRQA:2021-12-21:NEWT:1640168389304",
                        "sourceIndex": "17",
                        "dataSourceName": "Beacon OMS",
                        "&model": "RTS22Transaction",
                        "&version": 1,
                        "tradersAlgosWaiversIndicators": {
                            "securitiesFinancingTxnIndicator": False
                        },
                        "instrumentDetails": {
                            "instrument": {
                                "bond": {"maturityDate": "2021-12-22"},
                                "derivative": {
                                    "expiryDate": "2021-12-22",
                                    "strikePrice": 23.0,
                                    "strikePriceCurrency": "USD",
                                },
                                "ext": {
                                    "priceNotation": "MONE",
                                    "quantityNotation": "UNIT",
                                    "strikePriceType": "MntryVal",
                                },
                                "isCreatedThroughFallback": True,
                            }
                        },
                        "&cascadeId": "93929af3-23b7-48df-8f3b-2da95369622f",
                        "parties": {
                            "buyer": [
                                {
                                    "&id": "837cc235-f5f6-468e-bac0-0bba6af9d688",
                                    "&key": "MarketCounterparty:837cc235-f5f6-468e-bac0-0bba6af9d688:1661933204153",
                                    "details": {
                                        "firmStatus": "ACTIVE",
                                        "firmType": "Client",
                                        "leiRegistrationStatus": "ISSUED",
                                        "retailOrProfessional": "N/A",
                                    },
                                    "firmIdentifiers": {
                                        "branchCountry": "GB",
                                        "lei": "549300D4ICPG3QFV4731",
                                    },
                                    "firmLocation": {
                                        "registeredAddress": {
                                            "address": "C/O Walkers Corporate Limited",
                                            "city": "George Town",
                                            "country": "KY",
                                            "postalCode": "KY1-9008",
                                        },
                                        "tradingAddress": {
                                            "address": "C/O Lighthouse Investment Partners LLC",
                                            "city": "Palm Beach Gardens",
                                            "country": "US",
                                            "postalCode": "33410",
                                        },
                                    },
                                    "name": "North Rock SPC - NR 1 SP",
                                    "sinkIdentifiers": {
                                        "tradeFileIdentifiers": [
                                            {"id": "DR1-CASH", "label": "id"},
                                            {"id": " DR1-SWAP", "label": "id"},
                                            {"id": " DR1-FUT", "label": "id"},
                                            {"id": " RH3-CASH", "label": "id"},
                                            {"id": " RH3-SWAP", "label": "id"},
                                            {"id": " RH3-FUT", "label": "id"},
                                            {"id": "RHILL_FUT_JPMEU", "label": "id"},
                                            {"id": "RHILL_CASH_JPM", "label": "id"},
                                            {"id": "RHILL_FUT_JPM", "label": "id"},
                                            {"id": "RHILL_SWAP_JPM", "label": "id"},
                                            {"id": "HILLR_FUT_JPMEU", "label": "id"},
                                            {"id": "HILLR_CASH_JPM", "label": "id"},
                                            {"id": "HILLR_SWAP_JPM", "label": "id"},
                                            {"id": "HILLR_FUT_JPM", "label": "id"},
                                            {"id": "rhill_feo_jpm", "label": "id"},
                                            {"id": "hillr_feo_jpm", "label": "id"},
                                            {"id": "AECCLES_CASH_JPM", "label": "id"},
                                            {"id": "AECCLES_SWAP_JPM", "label": "id"},
                                            {"id": "AECCLES_FUT_JPM", "label": "id"},
                                            {"id": "AECCLES_FEO_JPM", "label": "id"},
                                            {"id": "SSHIELDS_FEO_JPM", "label": "id"},
                                            {"id": "SSHIELDS_FUT_JPM", "label": "id"},
                                            {"id": "SSHIELDS_SWAP_JPM", "label": "id"},
                                            {"id": "SSHIELDS_CASH_JPM", "label": "id"},
                                            {"id": "MJOHNSON_FEO_JPM", "label": "id"},
                                            {"id": "MJOHNSON_FUT_JPM", "label": "id"},
                                            {"id": "MJOHNSON_SWAP_JPM", "label": "id"},
                                            {"id": "MJOHNSON_CASH_JPM", "label": "id"},
                                            {"id": "DFRAUEND_FEO_JPM", "label": "id"},
                                            {"id": "DFRAUEND_FUT_JPM", "label": "id"},
                                            {"id": "DFRAUEND_SWAP_JPM", "label": "id"},
                                            {"id": "DFRAUEND_CASH_JPM", "label": "id"},
                                            {"id": "RoryHillOTC[JPM]", "label": "id"},
                                            {"id": "RoryHillOTC[MS]", "label": "id"},
                                            {"id": "RoryHillRH3-CASH", "label": "id"},
                                            {"id": "RHILL_CASH_JPM", "label": "id"},
                                            {"id": "Rory Hill OTC[JPM]", "label": "id"},
                                            {"id": "Rory Hill OTC[MS]", "label": "id"},
                                            {"id": "RHOCKING_CASH_MS", "label": "id"},
                                            {"id": "RHOCKING_SWAP_MS", "label": "id"},
                                            {"id": "RHOCKING_FUT_MS", "label": "id"},
                                            {"id": "RHOCKING_FEO_MS", "label": "id"},
                                            {"id": "RHOCKING_CASH_JPM", "label": "id"},
                                            {"id": "RHOCKING_SWAP_JPM", "label": "id"},
                                            {"id": "RHARRIS_SWAP_JPM", "label": "id"},
                                            {"id": "RHARRIS_CASH_JPM", "label": "id"},
                                            {"id": "RHARRIS_FEO_JPM", "label": "id"},
                                            {"id": "RHARRIS_FUT_JPM", "label": "id"},
                                            {"id": "RHILL_SWAP_MS", "label": "id"},
                                            {"id": "rhill_swap_ms", "label": "id"},
                                            {"id": "rhill_fef_ms", "label": "id"},
                                            {"id": "rhill_cash_baml", "label": "id"},
                                            {"id": "rory hill otc ms", "label": "id"},
                                            {"id": "rory hill otc ubs", "label": "id"},
                                            {
                                                "id": "rory hill swap&otc ml",
                                                "label": "id",
                                            },
                                            {"id": "rhill_swap_baml", "label": "id"},
                                            {
                                                "id": "rory hill otc ubs",
                                                "label": "account",
                                            },
                                            {
                                                "id": "rory hill otc[jpm]",
                                                "label": "account",
                                            },
                                            {
                                                "id": "Rory Hill OTC MS",
                                                "label": "account",
                                            },
                                            {
                                                "id": "Rory Hill Swap&OTC ML",
                                                "label": "account",
                                            },
                                            {"id": "rhocking_fut_jpm", "label": "id"},
                                            {"id": "rhocking_usf_ms", "label": "id"},
                                            {"id": "rhocking_fef_ms", "label": "id"},
                                            {"id": "rhill_swap_gs", "label": "id"},
                                            {
                                                "id": "rory hill otc citi",
                                                "label": "account",
                                            },
                                            {"id": "namin_swap_ms", "label": "id"},
                                            {"id": "namin_cash_ms", "label": "id"},
                                            {"id": "dhiscox_swap_jpm", "label": "id"},
                                            {"id": "dhiscox_swap_barc", "label": "id"},
                                            {"id": "dhiscox_cash_barc", "label": "id"},
                                            {"id": "dhiscox_cash_jpm", "label": "id"},
                                            {"id": "AARTHURS_CASH_GS", "label": "id"},
                                            {"id": "AARTHURS_FEO_GS", "label": "id"},
                                            {"id": "AARTHURS_FUT_GS", "label": "id"},
                                            {"id": "AARTHURS_SWAP_GS", "label": "id"},
                                            {"id": "ADIOGO_CASH_ML", "label": "id"},
                                            {"id": "ADIOGO_FUT_ML", "label": "id"},
                                            {"id": "ADIOGO_SWAP_ML", "label": "id"},
                                            {"id": "namin_swap_jpm", "label": "id"},
                                            {"id": "dhiscox_feo_jpm", "label": "id"},
                                        ]
                                    },
                                    "uniqueIds": [
                                        "id:aarthurs_fut_gs",
                                        "id:hillr_fut_jpmeu",
                                        "id:rhocking_fut_jpm",
                                        "id:aarthurs_feo_gs",
                                        "id:adiogo_swap_ml",
                                        "id:mjohnson_feo_jpm",
                                        "id:namin_swap_jpm",
                                        "id:rharris_cash_jpm",
                                        "id:sshields_feo_jpm",
                                        "id:hillr_fut_jpm",
                                        "id:rhill_swap_gs",
                                        "id:rhill_swap_jpm",
                                        "id:dhiscox_cash_jpm",
                                        "id:rhill_swap_ms",
                                        "id:rhocking_fut_ms",
                                        "id:rory hill swap&otc ml",
                                        "id:rhill_cash_jpm",
                                        "id:rory hill otc[ms]",
                                        "id:rhill_fut_jpm",
                                        "id:mjohnson_swap_jpm",
                                        "id:hillr_cash_jpm",
                                        "id:dhiscox_cash_barc",
                                        "id:aeccles_swap_jpm",
                                        "id:dhiscox_feo_jpm",
                                        "id:rhocking_feo_ms",
                                        "id:aeccles_cash_jpm",
                                        "id:rharris_swap_jpm",
                                        "id:namin_swap_ms",
                                        "id:roryhillrh3-cash",
                                        "account:rory hill otc citi",
                                        "id:rhill_feo_jpm",
                                        "id:dhiscox_swap_barc",
                                        "id:hillr_swap_jpm",
                                        "id: rh3-cash",
                                        "id: dr1-swap",
                                        "id:sshields_cash_jpm",
                                        "id:rhocking_cash_jpm",
                                        "id:adiogo_cash_ml",
                                        "id:rhill_swap_baml",
                                        "id:rhill_fut_jpmeu",
                                        "account:rory hill otc ubs",
                                        "id:sshields_swap_jpm",
                                        "account:rory hill otc[jpm]",
                                        "id:namin_cash_ms",
                                        "id:dfrauend_fut_jpm",
                                        "id:rory hill otc[jpm]",
                                        "id:rhocking_swap_ms",
                                        "id:rhocking_fef_ms",
                                        "id:rharris_feo_jpm",
                                        "id:rory hill otc ubs",
                                        "id:rhocking_cash_ms",
                                        "id: rh3-swap",
                                        "id:adiogo_fut_ml",
                                        "id: dr1-fut",
                                        "id:roryhillotc[jpm]",
                                        "id: rh3-fut",
                                        "id:aeccles_fut_jpm",
                                        "id:rharris_fut_jpm",
                                        "id:dfrauend_cash_jpm",
                                        "id:mjohnson_cash_jpm",
                                        "id:dfrauend_feo_jpm",
                                        "id:aeccles_feo_jpm",
                                        "id:aarthurs_cash_gs",
                                        "id:aarthurs_swap_gs",
                                        "id:rhill_fef_ms",
                                        "account:rory hill swap&otc ml",
                                        "account:rory hill otc ms",
                                        "id:sshields_fut_jpm",
                                        "id:roryhillotc[ms]",
                                        "id:mjohnson_fut_jpm",
                                        "id:dr1-cash",
                                        "id:dfrauend_swap_jpm",
                                        "id:rhocking_swap_jpm",
                                        "id:rory hill otc ms",
                                        "id:rhill_cash_baml",
                                        "id:rhocking_usf_ms",
                                        "id:hillr_feo_jpm",
                                        "id:dhiscox_swap_jpm",
                                    ],
                                }
                            ],
                            "buyerDecisionMaker": [
                                {
                                    "&id": "$this",
                                    "&key": "AccountFirm:$this:*************",
                                    "client": {
                                        "isAggregatedClientAccount": False,
                                        "metFaceToFace": False,
                                    },
                                    "details": {
                                        "firmStatus": "ACTIVE",
                                        "inEEA": False,
                                        "isEmirDelegatedReporting": False,
                                        "mifidRegistered": True,
                                        "parentOfCollectiveInvestmentSchema": False,
                                        "retailOrProfessional": "N/A",
                                    },
                                    "emirDetails": {
                                        "corporateSector": ["F"],
                                        "isClearingThreshold": False,
                                        "natureOfFirm": "F",
                                    },
                                    "firmIdentifiers": {
                                        "branchCountry": "GB",
                                        "deaAccess": False,
                                        "isIsda": False,
                                        "kycApproved": False,
                                        "lei": "549300IL8TQT0JMDJJ80",
                                    },
                                    "name": "LH NR UK (Management) LLP",
                                    "sinkIdentifiers": {
                                        "orderFileIdentifiers": [
                                            {
                                                "id": "549300IL8TQT0JMDJJ80",
                                                "label": "lei",
                                            }
                                        ],
                                        "tradeFileIdentifiers": [
                                            {
                                                "id": "549300IL8TQT0JMDJJ80",
                                                "label": "lei",
                                            }
                                        ],
                                    },
                                    "uniqueIds": ["lei:549300il8tqt0jmdjj80"],
                                }
                            ],
                            "counterparty": {
                                "&id": "df195438-5a6c-48a5-bbce-0695eb102fba",
                                "&key": "MarketCounterparty:df195438-5a6c-48a5-bbce-0695eb102fba:1639051445420",
                                "details": {
                                    "firmStatus": "ACTIVE",
                                    "firmType": "Counterparty",
                                    "leiRegistrationStatus": "ISSUED",
                                    "retailOrProfessional": "N/A",
                                },
                                "firmCommunications": {
                                    "phoneNumbers": [{"isValid": False}]
                                },
                                "firmIdentifiers": {"lei": "4PQUHN3JPFGFNF3BB653"},
                                "firmLocation": {
                                    "registeredAddress": {
                                        "address": "C/O LEGAL DEPARTMENT",
                                        "city": "LONDON",
                                        "country": "GB",
                                        "postalCode": "E14 4QA",
                                    },
                                    "tradingAddress": {
                                        "address": "C/O Legal Department",
                                        "city": "London",
                                        "country": "GB",
                                        "postalCode": "E14 4QA",
                                    },
                                },
                                "name": "MORGAN STANLEY & CO INTERNATIONAL PLC",
                                "sinkIdentifiers": {
                                    "tradeFileIdentifiers": [
                                        {"id": "MSCO", "label": "id"},
                                        {"id": "MSET-EU-SWAP", "label": "id"},
                                        {"id": "MS-FUT-ALGO-EU", "label": "id"},
                                        {"id": "MS-ALG-OPT", "label": "id"},
                                        {"id": "ms-deriv", "label": "id"},
                                    ]
                                },
                                "uniqueIds": [
                                    "id:msco",
                                    "id:mset-eu-swap",
                                    "id:ms-fut-algo-eu",
                                    "id:ms-alg-opt",
                                    "id:ms-deriv",
                                ],
                            },
                            "executingEntity": {
                                "&id": "$this",
                                "&key": "AccountFirm:$this:*************",
                                "client": {
                                    "isAggregatedClientAccount": False,
                                    "metFaceToFace": False,
                                },
                                "details": {
                                    "firmStatus": "ACTIVE",
                                    "inEEA": False,
                                    "isEmirDelegatedReporting": False,
                                    "mifidRegistered": True,
                                    "parentOfCollectiveInvestmentSchema": False,
                                    "retailOrProfessional": "N/A",
                                },
                                "emirDetails": {
                                    "corporateSector": ["F"],
                                    "isClearingThreshold": False,
                                    "natureOfFirm": "F",
                                },
                                "firmIdentifiers": {
                                    "branchCountry": "GB",
                                    "deaAccess": False,
                                    "isIsda": False,
                                    "kycApproved": False,
                                    "lei": "549300IL8TQT0JMDJJ80",
                                },
                                "name": "LH NR UK (Management) LLP",
                                "sinkIdentifiers": {
                                    "orderFileIdentifiers": [
                                        {"id": "549300IL8TQT0JMDJJ80", "label": "lei"}
                                    ],
                                    "tradeFileIdentifiers": [
                                        {"id": "549300IL8TQT0JMDJJ80", "label": "lei"}
                                    ],
                                },
                                "uniqueIds": ["lei:549300il8tqt0jmdjj80"],
                            },
                            "executionWithinFirm": {
                                "&id": "a2fbae73-dcab-4a89-9da0-a9dfa95a6328",
                                "&key": "AccountPerson:a2fbae73-dcab-4a89-9da0-a9dfa95a6328:*************",
                                "name": "Demetrio Rojas",
                                "officialIdentifiers": {
                                    "branchCountry": "GB",
                                    "mifirId": "GB*********",
                                    "mifirIdSubType": "NIDN",
                                    "mifirIdType": "N",
                                    "nationalIds": [
                                        {
                                            "id": "*********",
                                            "label": "GB - National Insurance",
                                        }
                                    ],
                                },
                                "personalDetails": {
                                    "firstName": "Demetrio",
                                    "lastName": "Rojas",
                                    "nationality": ["GB"],
                                },
                                "retailOrProfessional": "N/A",
                                "sinkIdentifiers": {
                                    "tradeFileIdentifiers": [
                                        {"id": "DROJAS", "label": "id"}
                                    ]
                                },
                                "uniqueIds": ["id:drojas"],
                            },
                            "investmentDecisionWithinFirm": {
                                "&id": "a2fbae73-dcab-4a89-9da0-a9dfa95a6328",
                                "&key": "AccountPerson:a2fbae73-dcab-4a89-9da0-a9dfa95a6328:*************",
                                "name": "Demetrio Rojas",
                                "officialIdentifiers": {
                                    "branchCountry": "GB",
                                    "mifirId": "GB*********",
                                    "mifirIdSubType": "NIDN",
                                    "mifirIdType": "N",
                                    "nationalIds": [
                                        {
                                            "id": "*********",
                                            "label": "GB - National Insurance",
                                        }
                                    ],
                                },
                                "personalDetails": {
                                    "firstName": "Demetrio",
                                    "lastName": "Rojas",
                                    "nationality": ["GB"],
                                },
                                "retailOrProfessional": "N/A",
                                "sinkIdentifiers": {
                                    "tradeFileIdentifiers": [
                                        {"id": "DROJAS", "label": "id"}
                                    ]
                                },
                                "uniqueIds": ["id:drojas"],
                            },
                            "seller": [
                                {
                                    "&id": "df195438-5a6c-48a5-bbce-0695eb102fba",
                                    "&key": "MarketCounterparty:df195438-5a6c-48a5-bbce-0695eb102fba:1639051445420",
                                    "details": {
                                        "firmStatus": "ACTIVE",
                                        "firmType": "Counterparty",
                                        "leiRegistrationStatus": "ISSUED",
                                        "retailOrProfessional": "N/A",
                                    },
                                    "firmCommunications": {
                                        "phoneNumbers": [{"isValid": False}]
                                    },
                                    "firmIdentifiers": {"lei": "4PQUHN3JPFGFNF3BB653"},
                                    "firmLocation": {
                                        "registeredAddress": {
                                            "address": "C/O LEGAL DEPARTMENT",
                                            "city": "LONDON",
                                            "country": "GB",
                                            "postalCode": "E14 4QA",
                                        },
                                        "tradingAddress": {
                                            "address": "C/O Legal Department",
                                            "city": "London",
                                            "country": "GB",
                                            "postalCode": "E14 4QA",
                                        },
                                    },
                                    "name": "MORGAN STANLEY & CO INTERNATIONAL PLC",
                                    "sinkIdentifiers": {
                                        "tradeFileIdentifiers": [
                                            {"id": "MSCO", "label": "id"},
                                            {"id": "MSET-EU-SWAP", "label": "id"},
                                            {"id": "MS-FUT-ALGO-EU", "label": "id"},
                                            {"id": "MS-ALG-OPT", "label": "id"},
                                            {"id": "ms-deriv", "label": "id"},
                                        ]
                                    },
                                    "uniqueIds": [
                                        "id:msco",
                                        "id:mset-eu-swap",
                                        "id:ms-fut-algo-eu",
                                        "id:ms-alg-opt",
                                        "id:ms-deriv",
                                    ],
                                }
                            ],
                        },
                        "&hash": "7f8bf31b13e7f346eb3484308fd9d53617b57f7bd72ff3a4ab81243de26a68c2",
                        "&validationErrors": [
                            {
                                "fieldPath": "instrumentDetails.instrument.instrumentClassification",
                                "message": "`Instrument Classification` must be populated",
                                "fieldName": None,
                                "code": "SE_DV-58",
                                "category": "Instruments",
                                "modulesAffected": [
                                    "Best Execution",
                                    "EMIR",
                                    "Transaction Reporting",
                                ],
                                "action": None,
                                "severity": "HIGH",
                                "source": "steeleye",
                            },
                            {
                                "fieldPath": "instrumentDetails.instrument.instrumentFullName",
                                "message": "`Instrument Name` must be populated",
                                "fieldName": None,
                                "code": "SE_DV-205",
                                "category": "Instruments",
                                "modulesAffected": ["Transaction Reporting"],
                                "action": None,
                                "severity": "HIGH",
                                "source": "steeleye",
                            },
                        ],
                        "&timestamp": 1640168389304,
                        "&user": "system",
                    },
                    "sort": [1640168389304, "YJYQJHAIRIQ4I5Q10XLXRQA:2021-12-21:NEWT"],
                },
                {
                    "_index": "lighthouse_steeleye_co_rts22transaction",
                    "_type": "RTS22Transaction",
                    "_id": "YM0CBJL1SHIJZFZ9UXERLGA:2021-12-21:NEWT",
                    "_score": None,
                    "_source": {
                        "date": "2021-12-21",
                        "sourceKey": "s3://lighthouse.steeleye.co/flows/tr-feed-beacon-lighthouse/COMPLIANCE_steeleye_2021-12-21.dat",
                        "&id": "YM0CBJL1SHIJZFZ9UXERLGA:2021-12-21:NEWT",
                        "transactionDetails": {
                            "buySellIndicator": "SELL",
                            "price": 3.22000002861023,
                            "priceCurrency": "USD",
                            "priceNotation": "MONE",
                            "quantity": 41.0,
                            "quantityCurrency": "USD",
                            "quantityNotation": "UNIT",
                            "tradingCapacity": "AOTC",
                            "tradingDateTime": "2021-12-21T14:54:56.056160Z",
                            "ultimateVenue": "XXXX",
                            "venue": "XXXX",
                        },
                        "transmissionDetails": {"orderTransmissionIndicator": False},
                        "workflow": {
                            "eligibility": {
                                "eligible": False,
                                "executionVenue": "XXXX",
                                "isDefaulted": False,
                                "onFirds": False,
                                "totv": False,
                                "underlyingOnFirds": False,
                                "utotv": False,
                            },
                            "isReported": False,
                            "status": "NON_REPORTABLE",
                            "validationStatus": "FAILED",
                        },
                        "marketIdentifiers": [
                            {
                                "labelId": "XXXXVIXOP2021-12-22 00:00:00",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12 00:00:00",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12-22 00:00:0025.00000000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12-22 00:00:000.25000000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12-22 00:00:000.02500000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12-22 00:00:000.00250000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12 00:00:0025.00000000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12 00:00:000.25000000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12 00:00:000.02500000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12 00:00:000.00250000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "VIXUS12/22/21P25Index",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "lei:549300il8tqt0jmdjj80",
                                "path": "parties.executingEntity",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "id:ms-deriv",
                                "path": "parties.counterparty",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "id:drojas",
                                "path": "parties.trader",
                                "type": "ARRAY",
                            },
                            {
                                "labelId": "id:drojas",
                                "path": "parties.executionWithinFirm",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "id:drojas",
                                "path": "parties.investmentDecisionWithinFirm",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "id:ms-deriv",
                                "path": "parties.buyer",
                                "type": "ARRAY",
                            },
                            {
                                "labelId": "id:rhill_cash_jpm",
                                "path": "parties.seller",
                                "type": "ARRAY",
                            },
                            {
                                "labelId": "lei:549300il8tqt0jmdjj80",
                                "path": "parties.sellerDecisionMaker",
                                "type": "ARRAY",
                            },
                        ],
                        "reportDetails": {
                            "investmentFirmCoveredDirective": True,
                            "reportStatus": "NEWT",
                            "transactionRefNo": "YM0CBJL1SHIJZFZ9UXERLGA",
                        },
                        "&key": "RTS22Transaction:YM0CBJL1SHIJZFZ9UXERLGA:2021-12-21:NEWT:1640168389304",
                        "sourceIndex": "26",
                        "dataSourceName": "Beacon OMS",
                        "&model": "RTS22Transaction",
                        "&version": 1,
                        "tradersAlgosWaiversIndicators": {
                            "securitiesFinancingTxnIndicator": False
                        },
                        "instrumentDetails": {
                            "instrument": {
                                "bond": {"maturityDate": "2021-12-22"},
                                "derivative": {
                                    "expiryDate": "2021-12-22",
                                    "strikePrice": 25.0,
                                    "strikePriceCurrency": "USD",
                                },
                                "ext": {
                                    "priceNotation": "MONE",
                                    "quantityNotation": "UNIT",
                                    "strikePriceType": "MntryVal",
                                },
                                "isCreatedThroughFallback": True,
                            }
                        },
                        "&cascadeId": "93929af3-23b7-48df-8f3b-2da95369622f",
                        "parties": {
                            "buyer": [
                                {
                                    "&id": "df195438-5a6c-48a5-bbce-0695eb102fba",
                                    "&key": "MarketCounterparty:df195438-5a6c-48a5-bbce-0695eb102fba:1639051445420",
                                    "details": {
                                        "firmStatus": "ACTIVE",
                                        "firmType": "Counterparty",
                                        "leiRegistrationStatus": "ISSUED",
                                        "retailOrProfessional": "N/A",
                                    },
                                    "firmCommunications": {
                                        "phoneNumbers": [{"isValid": False}]
                                    },
                                    "firmIdentifiers": {"lei": "4PQUHN3JPFGFNF3BB653"},
                                    "firmLocation": {
                                        "registeredAddress": {
                                            "address": "C/O LEGAL DEPARTMENT",
                                            "city": "LONDON",
                                            "country": "GB",
                                            "postalCode": "E14 4QA",
                                        },
                                        "tradingAddress": {
                                            "address": "C/O Legal Department",
                                            "city": "London",
                                            "country": "GB",
                                            "postalCode": "E14 4QA",
                                        },
                                    },
                                    "name": "MORGAN STANLEY & CO INTERNATIONAL PLC",
                                    "sinkIdentifiers": {
                                        "tradeFileIdentifiers": [
                                            {"id": "MSCO", "label": "id"},
                                            {"id": "MSET-EU-SWAP", "label": "id"},
                                            {"id": "MS-FUT-ALGO-EU", "label": "id"},
                                            {"id": "MS-ALG-OPT", "label": "id"},
                                            {"id": "ms-deriv", "label": "id"},
                                        ]
                                    },
                                    "uniqueIds": [
                                        "id:msco",
                                        "id:mset-eu-swap",
                                        "id:ms-fut-algo-eu",
                                        "id:ms-alg-opt",
                                        "id:ms-deriv",
                                    ],
                                }
                            ],
                            "counterparty": {
                                "&id": "df195438-5a6c-48a5-bbce-0695eb102fba",
                                "&key": "MarketCounterparty:df195438-5a6c-48a5-bbce-0695eb102fba:1639051445420",
                                "details": {
                                    "firmStatus": "ACTIVE",
                                    "firmType": "Counterparty",
                                    "leiRegistrationStatus": "ISSUED",
                                    "retailOrProfessional": "N/A",
                                },
                                "firmCommunications": {
                                    "phoneNumbers": [{"isValid": False}]
                                },
                                "firmIdentifiers": {"lei": "4PQUHN3JPFGFNF3BB653"},
                                "firmLocation": {
                                    "registeredAddress": {
                                        "address": "C/O LEGAL DEPARTMENT",
                                        "city": "LONDON",
                                        "country": "GB",
                                        "postalCode": "E14 4QA",
                                    },
                                    "tradingAddress": {
                                        "address": "C/O Legal Department",
                                        "city": "London",
                                        "country": "GB",
                                        "postalCode": "E14 4QA",
                                    },
                                },
                                "name": "MORGAN STANLEY & CO INTERNATIONAL PLC",
                                "sinkIdentifiers": {
                                    "tradeFileIdentifiers": [
                                        {"id": "MSCO", "label": "id"},
                                        {"id": "MSET-EU-SWAP", "label": "id"},
                                        {"id": "MS-FUT-ALGO-EU", "label": "id"},
                                        {"id": "MS-ALG-OPT", "label": "id"},
                                        {"id": "ms-deriv", "label": "id"},
                                    ]
                                },
                                "uniqueIds": [
                                    "id:msco",
                                    "id:mset-eu-swap",
                                    "id:ms-fut-algo-eu",
                                    "id:ms-alg-opt",
                                    "id:ms-deriv",
                                ],
                            },
                            "executingEntity": {
                                "&id": "$this",
                                "&key": "AccountFirm:$this:*************",
                                "client": {
                                    "isAggregatedClientAccount": False,
                                    "metFaceToFace": False,
                                },
                                "details": {
                                    "firmStatus": "ACTIVE",
                                    "inEEA": False,
                                    "isEmirDelegatedReporting": False,
                                    "mifidRegistered": True,
                                    "parentOfCollectiveInvestmentSchema": False,
                                    "retailOrProfessional": "N/A",
                                },
                                "emirDetails": {
                                    "corporateSector": ["F"],
                                    "isClearingThreshold": False,
                                    "natureOfFirm": "F",
                                },
                                "firmIdentifiers": {
                                    "branchCountry": "GB",
                                    "deaAccess": False,
                                    "isIsda": False,
                                    "kycApproved": False,
                                    "lei": "549300IL8TQT0JMDJJ80",
                                },
                                "name": "LH NR UK (Management) LLP",
                                "sinkIdentifiers": {
                                    "orderFileIdentifiers": [
                                        {"id": "549300IL8TQT0JMDJJ80", "label": "lei"}
                                    ],
                                    "tradeFileIdentifiers": [
                                        {"id": "549300IL8TQT0JMDJJ80", "label": "lei"}
                                    ],
                                },
                                "uniqueIds": ["lei:549300il8tqt0jmdjj80"],
                            },
                            "executionWithinFirm": {
                                "&id": "a2fbae73-dcab-4a89-9da0-a9dfa95a6328",
                                "&key": "AccountPerson:a2fbae73-dcab-4a89-9da0-a9dfa95a6328:*************",
                                "name": "Demetrio Rojas",
                                "officialIdentifiers": {
                                    "branchCountry": "GB",
                                    "mifirId": "GB*********",
                                    "mifirIdSubType": "NIDN",
                                    "mifirIdType": "N",
                                    "nationalIds": [
                                        {
                                            "id": "*********",
                                            "label": "GB - National Insurance",
                                        }
                                    ],
                                },
                                "personalDetails": {
                                    "firstName": "Demetrio",
                                    "lastName": "Rojas",
                                    "nationality": ["GB"],
                                },
                                "retailOrProfessional": "N/A",
                                "sinkIdentifiers": {
                                    "tradeFileIdentifiers": [
                                        {"id": "DROJAS", "label": "id"}
                                    ]
                                },
                                "uniqueIds": ["id:drojas"],
                            },
                            "investmentDecisionWithinFirm": {
                                "&id": "a2fbae73-dcab-4a89-9da0-a9dfa95a6328",
                                "&key": "AccountPerson:a2fbae73-dcab-4a89-9da0-a9dfa95a6328:*************",
                                "name": "Demetrio Rojas",
                                "officialIdentifiers": {
                                    "branchCountry": "GB",
                                    "mifirId": "GB*********",
                                    "mifirIdSubType": "NIDN",
                                    "mifirIdType": "N",
                                    "nationalIds": [
                                        {
                                            "id": "*********",
                                            "label": "GB - National Insurance",
                                        }
                                    ],
                                },
                                "personalDetails": {
                                    "firstName": "Demetrio",
                                    "lastName": "Rojas",
                                    "nationality": ["GB"],
                                },
                                "retailOrProfessional": "N/A",
                                "sinkIdentifiers": {
                                    "tradeFileIdentifiers": [
                                        {"id": "DROJAS", "label": "id"}
                                    ]
                                },
                                "uniqueIds": ["id:drojas"],
                            },
                            "seller": [
                                {
                                    "&id": "837cc235-f5f6-468e-bac0-0bba6af9d688",
                                    "&key": "MarketCounterparty:837cc235-f5f6-468e-bac0-0bba6af9d688:1661933204153",
                                    "details": {
                                        "firmStatus": "ACTIVE",
                                        "firmType": "Client",
                                        "leiRegistrationStatus": "ISSUED",
                                        "retailOrProfessional": "N/A",
                                    },
                                    "firmIdentifiers": {
                                        "branchCountry": "GB",
                                        "lei": "549300D4ICPG3QFV4731",
                                    },
                                    "firmLocation": {
                                        "registeredAddress": {
                                            "address": "C/O Walkers Corporate Limited",
                                            "city": "George Town",
                                            "country": "KY",
                                            "postalCode": "KY1-9008",
                                        },
                                        "tradingAddress": {
                                            "address": "C/O Lighthouse Investment Partners LLC",
                                            "city": "Palm Beach Gardens",
                                            "country": "US",
                                            "postalCode": "33410",
                                        },
                                    },
                                    "name": "North Rock SPC - NR 1 SP",
                                    "sinkIdentifiers": {
                                        "tradeFileIdentifiers": [
                                            {"id": "DR1-CASH", "label": "id"},
                                            {"id": " DR1-SWAP", "label": "id"},
                                            {"id": " DR1-FUT", "label": "id"},
                                            {"id": " RH3-CASH", "label": "id"},
                                            {"id": " RH3-SWAP", "label": "id"},
                                            {"id": " RH3-FUT", "label": "id"},
                                            {"id": "RHILL_FUT_JPMEU", "label": "id"},
                                            {"id": "RHILL_CASH_JPM", "label": "id"},
                                            {"id": "RHILL_FUT_JPM", "label": "id"},
                                            {"id": "RHILL_SWAP_JPM", "label": "id"},
                                            {"id": "HILLR_FUT_JPMEU", "label": "id"},
                                            {"id": "HILLR_CASH_JPM", "label": "id"},
                                            {"id": "HILLR_SWAP_JPM", "label": "id"},
                                            {"id": "HILLR_FUT_JPM", "label": "id"},
                                            {"id": "rhill_feo_jpm", "label": "id"},
                                            {"id": "hillr_feo_jpm", "label": "id"},
                                            {"id": "AECCLES_CASH_JPM", "label": "id"},
                                            {"id": "AECCLES_SWAP_JPM", "label": "id"},
                                            {"id": "AECCLES_FUT_JPM", "label": "id"},
                                            {"id": "AECCLES_FEO_JPM", "label": "id"},
                                            {"id": "SSHIELDS_FEO_JPM", "label": "id"},
                                            {"id": "SSHIELDS_FUT_JPM", "label": "id"},
                                            {"id": "SSHIELDS_SWAP_JPM", "label": "id"},
                                            {"id": "SSHIELDS_CASH_JPM", "label": "id"},
                                            {"id": "MJOHNSON_FEO_JPM", "label": "id"},
                                            {"id": "MJOHNSON_FUT_JPM", "label": "id"},
                                            {"id": "MJOHNSON_SWAP_JPM", "label": "id"},
                                            {"id": "MJOHNSON_CASH_JPM", "label": "id"},
                                            {"id": "DFRAUEND_FEO_JPM", "label": "id"},
                                            {"id": "DFRAUEND_FUT_JPM", "label": "id"},
                                            {"id": "DFRAUEND_SWAP_JPM", "label": "id"},
                                            {"id": "DFRAUEND_CASH_JPM", "label": "id"},
                                            {"id": "RoryHillOTC[JPM]", "label": "id"},
                                            {"id": "RoryHillOTC[MS]", "label": "id"},
                                            {"id": "RoryHillRH3-CASH", "label": "id"},
                                            {"id": "RHILL_CASH_JPM", "label": "id"},
                                            {"id": "Rory Hill OTC[JPM]", "label": "id"},
                                            {"id": "Rory Hill OTC[MS]", "label": "id"},
                                            {"id": "RHOCKING_CASH_MS", "label": "id"},
                                            {"id": "RHOCKING_SWAP_MS", "label": "id"},
                                            {"id": "RHOCKING_FUT_MS", "label": "id"},
                                            {"id": "RHOCKING_FEO_MS", "label": "id"},
                                            {"id": "RHOCKING_CASH_JPM", "label": "id"},
                                            {"id": "RHOCKING_SWAP_JPM", "label": "id"},
                                            {"id": "RHARRIS_SWAP_JPM", "label": "id"},
                                            {"id": "RHARRIS_CASH_JPM", "label": "id"},
                                            {"id": "RHARRIS_FEO_JPM", "label": "id"},
                                            {"id": "RHARRIS_FUT_JPM", "label": "id"},
                                            {"id": "RHILL_SWAP_MS", "label": "id"},
                                            {"id": "rhill_swap_ms", "label": "id"},
                                            {"id": "rhill_fef_ms", "label": "id"},
                                            {"id": "rhill_cash_baml", "label": "id"},
                                            {"id": "rory hill otc ms", "label": "id"},
                                            {"id": "rory hill otc ubs", "label": "id"},
                                            {
                                                "id": "rory hill swap&otc ml",
                                                "label": "id",
                                            },
                                            {"id": "rhill_swap_baml", "label": "id"},
                                            {
                                                "id": "rory hill otc ubs",
                                                "label": "account",
                                            },
                                            {
                                                "id": "rory hill otc[jpm]",
                                                "label": "account",
                                            },
                                            {
                                                "id": "Rory Hill OTC MS",
                                                "label": "account",
                                            },
                                            {
                                                "id": "Rory Hill Swap&OTC ML",
                                                "label": "account",
                                            },
                                            {"id": "rhocking_fut_jpm", "label": "id"},
                                            {"id": "rhocking_usf_ms", "label": "id"},
                                            {"id": "rhocking_fef_ms", "label": "id"},
                                            {"id": "rhill_swap_gs", "label": "id"},
                                            {
                                                "id": "rory hill otc citi",
                                                "label": "account",
                                            },
                                            {"id": "namin_swap_ms", "label": "id"},
                                            {"id": "namin_cash_ms", "label": "id"},
                                            {"id": "dhiscox_swap_jpm", "label": "id"},
                                            {"id": "dhiscox_swap_barc", "label": "id"},
                                            {"id": "dhiscox_cash_barc", "label": "id"},
                                            {"id": "dhiscox_cash_jpm", "label": "id"},
                                            {"id": "AARTHURS_CASH_GS", "label": "id"},
                                            {"id": "AARTHURS_FEO_GS", "label": "id"},
                                            {"id": "AARTHURS_FUT_GS", "label": "id"},
                                            {"id": "AARTHURS_SWAP_GS", "label": "id"},
                                            {"id": "ADIOGO_CASH_ML", "label": "id"},
                                            {"id": "ADIOGO_FUT_ML", "label": "id"},
                                            {"id": "ADIOGO_SWAP_ML", "label": "id"},
                                            {"id": "namin_swap_jpm", "label": "id"},
                                            {"id": "dhiscox_feo_jpm", "label": "id"},
                                        ]
                                    },
                                    "uniqueIds": [
                                        "id:aarthurs_fut_gs",
                                        "id:hillr_fut_jpmeu",
                                        "id:rhocking_fut_jpm",
                                        "id:aarthurs_feo_gs",
                                        "id:adiogo_swap_ml",
                                        "id:mjohnson_feo_jpm",
                                        "id:namin_swap_jpm",
                                        "id:rharris_cash_jpm",
                                        "id:sshields_feo_jpm",
                                        "id:hillr_fut_jpm",
                                        "id:rhill_swap_gs",
                                        "id:rhill_swap_jpm",
                                        "id:dhiscox_cash_jpm",
                                        "id:rhill_swap_ms",
                                        "id:rhocking_fut_ms",
                                        "id:rory hill swap&otc ml",
                                        "id:rhill_cash_jpm",
                                        "id:rory hill otc[ms]",
                                        "id:rhill_fut_jpm",
                                        "id:mjohnson_swap_jpm",
                                        "id:hillr_cash_jpm",
                                        "id:dhiscox_cash_barc",
                                        "id:aeccles_swap_jpm",
                                        "id:dhiscox_feo_jpm",
                                        "id:rhocking_feo_ms",
                                        "id:aeccles_cash_jpm",
                                        "id:rharris_swap_jpm",
                                        "id:namin_swap_ms",
                                        "id:roryhillrh3-cash",
                                        "account:rory hill otc citi",
                                        "id:rhill_feo_jpm",
                                        "id:dhiscox_swap_barc",
                                        "id:hillr_swap_jpm",
                                        "id: rh3-cash",
                                        "id: dr1-swap",
                                        "id:sshields_cash_jpm",
                                        "id:rhocking_cash_jpm",
                                        "id:adiogo_cash_ml",
                                        "id:rhill_swap_baml",
                                        "id:rhill_fut_jpmeu",
                                        "account:rory hill otc ubs",
                                        "id:sshields_swap_jpm",
                                        "account:rory hill otc[jpm]",
                                        "id:namin_cash_ms",
                                        "id:dfrauend_fut_jpm",
                                        "id:rory hill otc[jpm]",
                                        "id:rhocking_swap_ms",
                                        "id:rhocking_fef_ms",
                                        "id:rharris_feo_jpm",
                                        "id:rory hill otc ubs",
                                        "id:rhocking_cash_ms",
                                        "id: rh3-swap",
                                        "id:adiogo_fut_ml",
                                        "id: dr1-fut",
                                        "id:roryhillotc[jpm]",
                                        "id: rh3-fut",
                                        "id:aeccles_fut_jpm",
                                        "id:rharris_fut_jpm",
                                        "id:dfrauend_cash_jpm",
                                        "id:mjohnson_cash_jpm",
                                        "id:dfrauend_feo_jpm",
                                        "id:aeccles_feo_jpm",
                                        "id:aarthurs_cash_gs",
                                        "id:aarthurs_swap_gs",
                                        "id:rhill_fef_ms",
                                        "account:rory hill swap&otc ml",
                                        "account:rory hill otc ms",
                                        "id:sshields_fut_jpm",
                                        "id:roryhillotc[ms]",
                                        "id:mjohnson_fut_jpm",
                                        "id:dr1-cash",
                                        "id:dfrauend_swap_jpm",
                                        "id:rhocking_swap_jpm",
                                        "id:rory hill otc ms",
                                        "id:rhill_cash_baml",
                                        "id:rhocking_usf_ms",
                                        "id:hillr_feo_jpm",
                                        "id:dhiscox_swap_jpm",
                                    ],
                                }
                            ],
                            "sellerDecisionMaker": [
                                {
                                    "&id": "$this",
                                    "&key": "AccountFirm:$this:*************",
                                    "client": {
                                        "isAggregatedClientAccount": False,
                                        "metFaceToFace": False,
                                    },
                                    "details": {
                                        "firmStatus": "ACTIVE",
                                        "inEEA": False,
                                        "isEmirDelegatedReporting": False,
                                        "mifidRegistered": True,
                                        "parentOfCollectiveInvestmentSchema": False,
                                        "retailOrProfessional": "N/A",
                                    },
                                    "emirDetails": {
                                        "corporateSector": ["F"],
                                        "isClearingThreshold": False,
                                        "natureOfFirm": "F",
                                    },
                                    "firmIdentifiers": {
                                        "branchCountry": "GB",
                                        "deaAccess": False,
                                        "isIsda": False,
                                        "kycApproved": False,
                                        "lei": "549300IL8TQT0JMDJJ80",
                                    },
                                    "name": "LH NR UK (Management) LLP",
                                    "sinkIdentifiers": {
                                        "orderFileIdentifiers": [
                                            {
                                                "id": "549300IL8TQT0JMDJJ80",
                                                "label": "lei",
                                            }
                                        ],
                                        "tradeFileIdentifiers": [
                                            {
                                                "id": "549300IL8TQT0JMDJJ80",
                                                "label": "lei",
                                            }
                                        ],
                                    },
                                    "uniqueIds": ["lei:549300il8tqt0jmdjj80"],
                                }
                            ],
                        },
                        "&hash": "52a25b465fde3771c66b23df34f2960b7dabe81266ff9e6bb21a8d7d936f6073",
                        "&validationErrors": [
                            {
                                "fieldPath": "instrumentDetails.instrument.instrumentClassification",
                                "message": "`Instrument Classification` must be populated",
                                "fieldName": None,
                                "code": "SE_DV-58",
                                "category": "Instruments",
                                "modulesAffected": [
                                    "Best Execution",
                                    "EMIR",
                                    "Transaction Reporting",
                                ],
                                "action": None,
                                "severity": "HIGH",
                                "source": "steeleye",
                            },
                            {
                                "fieldPath": "instrumentDetails.instrument.instrumentFullName",
                                "message": "`Instrument Name` must be populated",
                                "fieldName": None,
                                "code": "SE_DV-205",
                                "category": "Instruments",
                                "modulesAffected": ["Transaction Reporting"],
                                "action": None,
                                "severity": "HIGH",
                                "source": "steeleye",
                            },
                        ],
                        "&timestamp": 1640168389304,
                        "&user": "system",
                    },
                    "sort": [1640168389304, "YM0CBJL1SHIJZFZ9UXERLGA:2021-12-21:NEWT"],
                },
                {
                    "_index": "lighthouse_steeleye_co_rts22transaction",
                    "_type": "RTS22Transaction",
                    "_id": "YRK1D8IZREB1NNNYKI8PGA:2021-12-21:NEWT",
                    "_score": None,
                    "_source": {
                        "date": "2021-12-21",
                        "sourceKey": "s3://lighthouse.steeleye.co/flows/tr-feed-beacon-lighthouse/COMPLIANCE_steeleye_2021-12-21.dat",
                        "&id": "YRK1D8IZREB1NNNYKI8PGA:2021-12-21:NEWT",
                        "transactionDetails": {
                            "buySellIndicator": "BUYI",
                            "price": 1.549999952316284,
                            "priceCurrency": "USD",
                            "priceNotation": "MONE",
                            "quantity": 20.0,
                            "quantityCurrency": "USD",
                            "quantityNotation": "UNIT",
                            "tradingCapacity": "AOTC",
                            "tradingDateTime": "2021-12-21T14:55:10.086144Z",
                            "ultimateVenue": "XXXX",
                            "venue": "XXXX",
                        },
                        "transmissionDetails": {"orderTransmissionIndicator": False},
                        "workflow": {
                            "eligibility": {
                                "eligible": False,
                                "executionVenue": "XXXX",
                                "isDefaulted": False,
                                "onFirds": False,
                                "totv": False,
                                "underlyingOnFirds": False,
                                "utotv": False,
                            },
                            "isReported": False,
                            "status": "NON_REPORTABLE",
                            "validationStatus": "FAILED",
                        },
                        "marketIdentifiers": [
                            {
                                "labelId": "XXXXVIXOP2021-12-22 00:00:00",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12 00:00:00",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12-22 00:00:0023.00000000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12-22 00:00:000.23000000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12-22 00:00:000.02300000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12-22 00:00:000.00230000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12 00:00:0023.00000000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12 00:00:000.23000000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12 00:00:000.02300000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXVIXOP2021-12 00:00:000.00230000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "VIXUS12/22/21P23Index",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "lei:549300il8tqt0jmdjj80",
                                "path": "parties.executingEntity",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "id:ms-deriv",
                                "path": "parties.counterparty",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "id:drojas",
                                "path": "parties.trader",
                                "type": "ARRAY",
                            },
                            {
                                "labelId": "id:drojas",
                                "path": "parties.executionWithinFirm",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "id:drojas",
                                "path": "parties.investmentDecisionWithinFirm",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "id:rhill_cash_jpm",
                                "path": "parties.buyer",
                                "type": "ARRAY",
                            },
                            {
                                "labelId": "id:ms-deriv",
                                "path": "parties.seller",
                                "type": "ARRAY",
                            },
                            {
                                "labelId": "lei:549300il8tqt0jmdjj80",
                                "path": "parties.buyerDecisionMaker",
                                "type": "ARRAY",
                            },
                        ],
                        "reportDetails": {
                            "investmentFirmCoveredDirective": True,
                            "reportStatus": "NEWT",
                            "transactionRefNo": "YRK1D8IZREB1NNNYKI8PGA",
                        },
                        "&key": "RTS22Transaction:YRK1D8IZREB1NNNYKI8PGA:2021-12-21:NEWT:1640168389304",
                        "sourceIndex": "15",
                        "dataSourceName": "Beacon OMS",
                        "&model": "RTS22Transaction",
                        "&version": 1,
                        "tradersAlgosWaiversIndicators": {
                            "securitiesFinancingTxnIndicator": False
                        },
                        "instrumentDetails": {
                            "instrument": {
                                "bond": {"maturityDate": "2021-12-22"},
                                "derivative": {
                                    "expiryDate": "2021-12-22",
                                    "strikePrice": 23.0,
                                    "strikePriceCurrency": "USD",
                                },
                                "ext": {
                                    "priceNotation": "MONE",
                                    "quantityNotation": "UNIT",
                                    "strikePriceType": "MntryVal",
                                },
                                "isCreatedThroughFallback": True,
                            }
                        },
                        "&cascadeId": "93929af3-23b7-48df-8f3b-2da95369622f",
                        "parties": {
                            "buyer": [
                                {
                                    "&id": "837cc235-f5f6-468e-bac0-0bba6af9d688",
                                    "&key": "MarketCounterparty:837cc235-f5f6-468e-bac0-0bba6af9d688:1661933204153",
                                    "details": {
                                        "firmStatus": "ACTIVE",
                                        "firmType": "Client",
                                        "leiRegistrationStatus": "ISSUED",
                                        "retailOrProfessional": "N/A",
                                    },
                                    "firmIdentifiers": {
                                        "branchCountry": "GB",
                                        "lei": "549300D4ICPG3QFV4731",
                                    },
                                    "firmLocation": {
                                        "registeredAddress": {
                                            "address": "C/O Walkers Corporate Limited",
                                            "city": "George Town",
                                            "country": "KY",
                                            "postalCode": "KY1-9008",
                                        },
                                        "tradingAddress": {
                                            "address": "C/O Lighthouse Investment Partners LLC",
                                            "city": "Palm Beach Gardens",
                                            "country": "US",
                                            "postalCode": "33410",
                                        },
                                    },
                                    "name": "North Rock SPC - NR 1 SP",
                                    "sinkIdentifiers": {
                                        "tradeFileIdentifiers": [
                                            {"id": "DR1-CASH", "label": "id"},
                                            {"id": " DR1-SWAP", "label": "id"},
                                            {"id": " DR1-FUT", "label": "id"},
                                            {"id": " RH3-CASH", "label": "id"},
                                            {"id": " RH3-SWAP", "label": "id"},
                                            {"id": " RH3-FUT", "label": "id"},
                                            {"id": "RHILL_FUT_JPMEU", "label": "id"},
                                            {"id": "RHILL_CASH_JPM", "label": "id"},
                                            {"id": "RHILL_FUT_JPM", "label": "id"},
                                            {"id": "RHILL_SWAP_JPM", "label": "id"},
                                            {"id": "HILLR_FUT_JPMEU", "label": "id"},
                                            {"id": "HILLR_CASH_JPM", "label": "id"},
                                            {"id": "HILLR_SWAP_JPM", "label": "id"},
                                            {"id": "HILLR_FUT_JPM", "label": "id"},
                                            {"id": "rhill_feo_jpm", "label": "id"},
                                            {"id": "hillr_feo_jpm", "label": "id"},
                                            {"id": "AECCLES_CASH_JPM", "label": "id"},
                                            {"id": "AECCLES_SWAP_JPM", "label": "id"},
                                            {"id": "AECCLES_FUT_JPM", "label": "id"},
                                            {"id": "AECCLES_FEO_JPM", "label": "id"},
                                            {"id": "SSHIELDS_FEO_JPM", "label": "id"},
                                            {"id": "SSHIELDS_FUT_JPM", "label": "id"},
                                            {"id": "SSHIELDS_SWAP_JPM", "label": "id"},
                                            {"id": "SSHIELDS_CASH_JPM", "label": "id"},
                                            {"id": "MJOHNSON_FEO_JPM", "label": "id"},
                                            {"id": "MJOHNSON_FUT_JPM", "label": "id"},
                                            {"id": "MJOHNSON_SWAP_JPM", "label": "id"},
                                            {"id": "MJOHNSON_CASH_JPM", "label": "id"},
                                            {"id": "DFRAUEND_FEO_JPM", "label": "id"},
                                            {"id": "DFRAUEND_FUT_JPM", "label": "id"},
                                            {"id": "DFRAUEND_SWAP_JPM", "label": "id"},
                                            {"id": "DFRAUEND_CASH_JPM", "label": "id"},
                                            {"id": "RoryHillOTC[JPM]", "label": "id"},
                                            {"id": "RoryHillOTC[MS]", "label": "id"},
                                            {"id": "RoryHillRH3-CASH", "label": "id"},
                                            {"id": "RHILL_CASH_JPM", "label": "id"},
                                            {"id": "Rory Hill OTC[JPM]", "label": "id"},
                                            {"id": "Rory Hill OTC[MS]", "label": "id"},
                                            {"id": "RHOCKING_CASH_MS", "label": "id"},
                                            {"id": "RHOCKING_SWAP_MS", "label": "id"},
                                            {"id": "RHOCKING_FUT_MS", "label": "id"},
                                            {"id": "RHOCKING_FEO_MS", "label": "id"},
                                            {"id": "RHOCKING_CASH_JPM", "label": "id"},
                                            {"id": "RHOCKING_SWAP_JPM", "label": "id"},
                                            {"id": "RHARRIS_SWAP_JPM", "label": "id"},
                                            {"id": "RHARRIS_CASH_JPM", "label": "id"},
                                            {"id": "RHARRIS_FEO_JPM", "label": "id"},
                                            {"id": "RHARRIS_FUT_JPM", "label": "id"},
                                            {"id": "RHILL_SWAP_MS", "label": "id"},
                                            {"id": "rhill_swap_ms", "label": "id"},
                                            {"id": "rhill_fef_ms", "label": "id"},
                                            {"id": "rhill_cash_baml", "label": "id"},
                                            {"id": "rory hill otc ms", "label": "id"},
                                            {"id": "rory hill otc ubs", "label": "id"},
                                            {
                                                "id": "rory hill swap&otc ml",
                                                "label": "id",
                                            },
                                            {"id": "rhill_swap_baml", "label": "id"},
                                            {
                                                "id": "rory hill otc ubs",
                                                "label": "account",
                                            },
                                            {
                                                "id": "rory hill otc[jpm]",
                                                "label": "account",
                                            },
                                            {
                                                "id": "Rory Hill OTC MS",
                                                "label": "account",
                                            },
                                            {
                                                "id": "Rory Hill Swap&OTC ML",
                                                "label": "account",
                                            },
                                            {"id": "rhocking_fut_jpm", "label": "id"},
                                            {"id": "rhocking_usf_ms", "label": "id"},
                                            {"id": "rhocking_fef_ms", "label": "id"},
                                            {"id": "rhill_swap_gs", "label": "id"},
                                            {
                                                "id": "rory hill otc citi",
                                                "label": "account",
                                            },
                                            {"id": "namin_swap_ms", "label": "id"},
                                            {"id": "namin_cash_ms", "label": "id"},
                                            {"id": "dhiscox_swap_jpm", "label": "id"},
                                            {"id": "dhiscox_swap_barc", "label": "id"},
                                            {"id": "dhiscox_cash_barc", "label": "id"},
                                            {"id": "dhiscox_cash_jpm", "label": "id"},
                                            {"id": "AARTHURS_CASH_GS", "label": "id"},
                                            {"id": "AARTHURS_FEO_GS", "label": "id"},
                                            {"id": "AARTHURS_FUT_GS", "label": "id"},
                                            {"id": "AARTHURS_SWAP_GS", "label": "id"},
                                            {"id": "ADIOGO_CASH_ML", "label": "id"},
                                            {"id": "ADIOGO_FUT_ML", "label": "id"},
                                            {"id": "ADIOGO_SWAP_ML", "label": "id"},
                                            {"id": "namin_swap_jpm", "label": "id"},
                                            {"id": "dhiscox_feo_jpm", "label": "id"},
                                        ]
                                    },
                                    "uniqueIds": [
                                        "id:aarthurs_fut_gs",
                                        "id:hillr_fut_jpmeu",
                                        "id:rhocking_fut_jpm",
                                        "id:aarthurs_feo_gs",
                                        "id:adiogo_swap_ml",
                                        "id:mjohnson_feo_jpm",
                                        "id:namin_swap_jpm",
                                        "id:rharris_cash_jpm",
                                        "id:sshields_feo_jpm",
                                        "id:hillr_fut_jpm",
                                        "id:rhill_swap_gs",
                                        "id:rhill_swap_jpm",
                                        "id:dhiscox_cash_jpm",
                                        "id:rhill_swap_ms",
                                        "id:rhocking_fut_ms",
                                        "id:rory hill swap&otc ml",
                                        "id:rhill_cash_jpm",
                                        "id:rory hill otc[ms]",
                                        "id:rhill_fut_jpm",
                                        "id:mjohnson_swap_jpm",
                                        "id:hillr_cash_jpm",
                                        "id:dhiscox_cash_barc",
                                        "id:aeccles_swap_jpm",
                                        "id:dhiscox_feo_jpm",
                                        "id:rhocking_feo_ms",
                                        "id:aeccles_cash_jpm",
                                        "id:rharris_swap_jpm",
                                        "id:namin_swap_ms",
                                        "id:roryhillrh3-cash",
                                        "account:rory hill otc citi",
                                        "id:rhill_feo_jpm",
                                        "id:dhiscox_swap_barc",
                                        "id:hillr_swap_jpm",
                                        "id: rh3-cash",
                                        "id: dr1-swap",
                                        "id:sshields_cash_jpm",
                                        "id:rhocking_cash_jpm",
                                        "id:adiogo_cash_ml",
                                        "id:rhill_swap_baml",
                                        "id:rhill_fut_jpmeu",
                                        "account:rory hill otc ubs",
                                        "id:sshields_swap_jpm",
                                        "account:rory hill otc[jpm]",
                                        "id:namin_cash_ms",
                                        "id:dfrauend_fut_jpm",
                                        "id:rory hill otc[jpm]",
                                        "id:rhocking_swap_ms",
                                        "id:rhocking_fef_ms",
                                        "id:rharris_feo_jpm",
                                        "id:rory hill otc ubs",
                                        "id:rhocking_cash_ms",
                                        "id: rh3-swap",
                                        "id:adiogo_fut_ml",
                                        "id: dr1-fut",
                                        "id:roryhillotc[jpm]",
                                        "id: rh3-fut",
                                        "id:aeccles_fut_jpm",
                                        "id:rharris_fut_jpm",
                                        "id:dfrauend_cash_jpm",
                                        "id:mjohnson_cash_jpm",
                                        "id:dfrauend_feo_jpm",
                                        "id:aeccles_feo_jpm",
                                        "id:aarthurs_cash_gs",
                                        "id:aarthurs_swap_gs",
                                        "id:rhill_fef_ms",
                                        "account:rory hill swap&otc ml",
                                        "account:rory hill otc ms",
                                        "id:sshields_fut_jpm",
                                        "id:roryhillotc[ms]",
                                        "id:mjohnson_fut_jpm",
                                        "id:dr1-cash",
                                        "id:dfrauend_swap_jpm",
                                        "id:rhocking_swap_jpm",
                                        "id:rory hill otc ms",
                                        "id:rhill_cash_baml",
                                        "id:rhocking_usf_ms",
                                        "id:hillr_feo_jpm",
                                        "id:dhiscox_swap_jpm",
                                    ],
                                }
                            ],
                            "buyerDecisionMaker": [
                                {
                                    "&id": "$this",
                                    "&key": "AccountFirm:$this:*************",
                                    "client": {
                                        "isAggregatedClientAccount": False,
                                        "metFaceToFace": False,
                                    },
                                    "details": {
                                        "firmStatus": "ACTIVE",
                                        "inEEA": False,
                                        "isEmirDelegatedReporting": False,
                                        "mifidRegistered": True,
                                        "parentOfCollectiveInvestmentSchema": False,
                                        "retailOrProfessional": "N/A",
                                    },
                                    "emirDetails": {
                                        "corporateSector": ["F"],
                                        "isClearingThreshold": False,
                                        "natureOfFirm": "F",
                                    },
                                    "firmIdentifiers": {
                                        "branchCountry": "GB",
                                        "deaAccess": False,
                                        "isIsda": False,
                                        "kycApproved": False,
                                        "lei": "549300IL8TQT0JMDJJ80",
                                    },
                                    "name": "LH NR UK (Management) LLP",
                                    "sinkIdentifiers": {
                                        "orderFileIdentifiers": [
                                            {
                                                "id": "549300IL8TQT0JMDJJ80",
                                                "label": "lei",
                                            }
                                        ],
                                        "tradeFileIdentifiers": [
                                            {
                                                "id": "549300IL8TQT0JMDJJ80",
                                                "label": "lei",
                                            }
                                        ],
                                    },
                                    "uniqueIds": ["lei:549300il8tqt0jmdjj80"],
                                }
                            ],
                            "counterparty": {
                                "&id": "df195438-5a6c-48a5-bbce-0695eb102fba",
                                "&key": "MarketCounterparty:df195438-5a6c-48a5-bbce-0695eb102fba:1639051445420",
                                "details": {
                                    "firmStatus": "ACTIVE",
                                    "firmType": "Counterparty",
                                    "leiRegistrationStatus": "ISSUED",
                                    "retailOrProfessional": "N/A",
                                },
                                "firmCommunications": {
                                    "phoneNumbers": [{"isValid": False}]
                                },
                                "firmIdentifiers": {"lei": "4PQUHN3JPFGFNF3BB653"},
                                "firmLocation": {
                                    "registeredAddress": {
                                        "address": "C/O LEGAL DEPARTMENT",
                                        "city": "LONDON",
                                        "country": "GB",
                                        "postalCode": "E14 4QA",
                                    },
                                    "tradingAddress": {
                                        "address": "C/O Legal Department",
                                        "city": "London",
                                        "country": "GB",
                                        "postalCode": "E14 4QA",
                                    },
                                },
                                "name": "MORGAN STANLEY & CO INTERNATIONAL PLC",
                                "sinkIdentifiers": {
                                    "tradeFileIdentifiers": [
                                        {"id": "MSCO", "label": "id"},
                                        {"id": "MSET-EU-SWAP", "label": "id"},
                                        {"id": "MS-FUT-ALGO-EU", "label": "id"},
                                        {"id": "MS-ALG-OPT", "label": "id"},
                                        {"id": "ms-deriv", "label": "id"},
                                    ]
                                },
                                "uniqueIds": [
                                    "id:msco",
                                    "id:mset-eu-swap",
                                    "id:ms-fut-algo-eu",
                                    "id:ms-alg-opt",
                                    "id:ms-deriv",
                                ],
                            },
                            "executingEntity": {
                                "&id": "$this",
                                "&key": "AccountFirm:$this:*************",
                                "client": {
                                    "isAggregatedClientAccount": False,
                                    "metFaceToFace": False,
                                },
                                "details": {
                                    "firmStatus": "ACTIVE",
                                    "inEEA": False,
                                    "isEmirDelegatedReporting": False,
                                    "mifidRegistered": True,
                                    "parentOfCollectiveInvestmentSchema": False,
                                    "retailOrProfessional": "N/A",
                                },
                                "emirDetails": {
                                    "corporateSector": ["F"],
                                    "isClearingThreshold": False,
                                    "natureOfFirm": "F",
                                },
                                "firmIdentifiers": {
                                    "branchCountry": "GB",
                                    "deaAccess": False,
                                    "isIsda": False,
                                    "kycApproved": False,
                                    "lei": "549300IL8TQT0JMDJJ80",
                                },
                                "name": "LH NR UK (Management) LLP",
                                "sinkIdentifiers": {
                                    "orderFileIdentifiers": [
                                        {"id": "549300IL8TQT0JMDJJ80", "label": "lei"}
                                    ],
                                    "tradeFileIdentifiers": [
                                        {"id": "549300IL8TQT0JMDJJ80", "label": "lei"}
                                    ],
                                },
                                "uniqueIds": ["lei:549300il8tqt0jmdjj80"],
                            },
                            "executionWithinFirm": {
                                "&id": "a2fbae73-dcab-4a89-9da0-a9dfa95a6328",
                                "&key": "AccountPerson:a2fbae73-dcab-4a89-9da0-a9dfa95a6328:*************",
                                "name": "Demetrio Rojas",
                                "officialIdentifiers": {
                                    "branchCountry": "GB",
                                    "mifirId": "GB*********",
                                    "mifirIdSubType": "NIDN",
                                    "mifirIdType": "N",
                                    "nationalIds": [
                                        {
                                            "id": "*********",
                                            "label": "GB - National Insurance",
                                        }
                                    ],
                                },
                                "personalDetails": {
                                    "firstName": "Demetrio",
                                    "lastName": "Rojas",
                                    "nationality": ["GB"],
                                },
                                "retailOrProfessional": "N/A",
                                "sinkIdentifiers": {
                                    "tradeFileIdentifiers": [
                                        {"id": "DROJAS", "label": "id"}
                                    ]
                                },
                                "uniqueIds": ["id:drojas"],
                            },
                            "investmentDecisionWithinFirm": {
                                "&id": "a2fbae73-dcab-4a89-9da0-a9dfa95a6328",
                                "&key": "AccountPerson:a2fbae73-dcab-4a89-9da0-a9dfa95a6328:*************",
                                "name": "Demetrio Rojas",
                                "officialIdentifiers": {
                                    "branchCountry": "GB",
                                    "mifirId": "GB*********",
                                    "mifirIdSubType": "NIDN",
                                    "mifirIdType": "N",
                                    "nationalIds": [
                                        {
                                            "id": "*********",
                                            "label": "GB - National Insurance",
                                        }
                                    ],
                                },
                                "personalDetails": {
                                    "firstName": "Demetrio",
                                    "lastName": "Rojas",
                                    "nationality": ["GB"],
                                },
                                "retailOrProfessional": "N/A",
                                "sinkIdentifiers": {
                                    "tradeFileIdentifiers": [
                                        {"id": "DROJAS", "label": "id"}
                                    ]
                                },
                                "uniqueIds": ["id:drojas"],
                            },
                            "seller": [
                                {
                                    "&id": "df195438-5a6c-48a5-bbce-0695eb102fba",
                                    "&key": "MarketCounterparty:df195438-5a6c-48a5-bbce-0695eb102fba:1639051445420",
                                    "details": {
                                        "firmStatus": "ACTIVE",
                                        "firmType": "Counterparty",
                                        "leiRegistrationStatus": "ISSUED",
                                        "retailOrProfessional": "N/A",
                                    },
                                    "firmCommunications": {
                                        "phoneNumbers": [{"isValid": False}]
                                    },
                                    "firmIdentifiers": {"lei": "4PQUHN3JPFGFNF3BB653"},
                                    "firmLocation": {
                                        "registeredAddress": {
                                            "address": "C/O LEGAL DEPARTMENT",
                                            "city": "LONDON",
                                            "country": "GB",
                                            "postalCode": "E14 4QA",
                                        },
                                        "tradingAddress": {
                                            "address": "C/O Legal Department",
                                            "city": "London",
                                            "country": "GB",
                                            "postalCode": "E14 4QA",
                                        },
                                    },
                                    "name": "MORGAN STANLEY & CO INTERNATIONAL PLC",
                                    "sinkIdentifiers": {
                                        "tradeFileIdentifiers": [
                                            {"id": "MSCO", "label": "id"},
                                            {"id": "MSET-EU-SWAP", "label": "id"},
                                            {"id": "MS-FUT-ALGO-EU", "label": "id"},
                                            {"id": "MS-ALG-OPT", "label": "id"},
                                            {"id": "ms-deriv", "label": "id"},
                                        ]
                                    },
                                    "uniqueIds": [
                                        "id:msco",
                                        "id:mset-eu-swap",
                                        "id:ms-fut-algo-eu",
                                        "id:ms-alg-opt",
                                        "id:ms-deriv",
                                    ],
                                }
                            ],
                        },
                        "&hash": "a6b326911656b2d1ce1a2c8d75d17c12989a65f755b459788f918d79bb13329a",
                        "&validationErrors": [
                            {
                                "fieldPath": "instrumentDetails.instrument.instrumentClassification",
                                "message": "`Instrument Classification` must be populated",
                                "fieldName": None,
                                "code": "SE_DV-58",
                                "category": "Instruments",
                                "modulesAffected": [
                                    "Best Execution",
                                    "EMIR",
                                    "Transaction Reporting",
                                ],
                                "action": None,
                                "severity": "HIGH",
                                "source": "steeleye",
                            },
                            {
                                "fieldPath": "instrumentDetails.instrument.instrumentFullName",
                                "message": "`Instrument Name` must be populated",
                                "fieldName": None,
                                "code": "SE_DV-205",
                                "category": "Instruments",
                                "modulesAffected": ["Transaction Reporting"],
                                "action": None,
                                "severity": "HIGH",
                                "source": "steeleye",
                            },
                        ],
                        "&timestamp": 1640168389304,
                        "&user": "system",
                    },
                    "sort": [1640168389304, "YRK1D8IZREB1NNNYKI8PGA:2021-12-21:NEWT"],
                },
                {
                    "_index": "lighthouse_steeleye_co_rts22transaction",
                    "_type": "RTS22Transaction",
                    "_id": "10159110:2021-11-24:NEWT",
                    "_score": None,
                    "_source": {
                        "date": "2021-11-24",
                        "sourceKey": "s3://lighthouse.steeleye.co/flows/tr-feed-beacon-lighthouse/LH_OTC.dat",
                        "&id": "10159110:2021-11-24:NEWT",
                        "transactionDetails": {
                            "buySellIndicator": "SELL",
                            "price": 0.875,
                            "priceCurrency": "EUR",
                            "priceNotation": "MONE",
                            "quantity": 2000.0,
                            "quantityCurrency": "EUR",
                            "quantityNotation": "UNIT",
                            "tradingCapacity": "AOTC",
                            "tradingDateTime": "2021-11-24T11:52:15Z",
                            "ultimateVenue": "XXXX",
                            "venue": "XXXX",
                        },
                        "transmissionDetails": {"orderTransmissionIndicator": False},
                        "workflow": {
                            "eligibility": {
                                "eligible": False,
                                "executionVenue": "XXXX",
                                "isDefaulted": False,
                                "onFirds": False,
                                "totv": False,
                                "underlyingOnFirds": False,
                                "utotv": False,
                            },
                            "isReported": False,
                            "status": "NON_REPORTABLE",
                            "validationStatus": "PASSED",
                        },
                        "marketIdentifiers": [
                            {
                                "labelId": "XXXXEU0009658426OC2021-12-17 00:00:00",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXEU0009658426OC2021-12 00:00:00",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXEU0009658426OC2021-12-17 00:00:00105.00000000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXEU0009658426OC2021-12-17 00:00:001.05000000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXEU0009658426OC2021-12-17 00:00:000.10500000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXEU0009658426OC2021-12-17 00:00:000.01050000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXEU0009658426OC2021-12 00:00:00105.00000000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXEU0009658426OC2021-12 00:00:001.05000000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXEU0009658426OC2021-12 00:00:000.10500000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXEU0009658426OC2021-12 00:00:000.01050000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "lei:549300il8tqt0jmdjj80",
                                "path": "parties.executingEntity",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "id:jphe",
                                "path": "parties.counterparty",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "id:roryhill",
                                "path": "parties.trader",
                                "type": "ARRAY",
                            },
                            {
                                "labelId": "id:roryhill",
                                "path": "parties.executionWithinFirm",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "id:roryhill",
                                "path": "parties.investmentDecisionWithinFirm",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "id:jphe",
                                "path": "parties.buyer",
                                "type": "ARRAY",
                            },
                            {
                                "labelId": "id:roryhillrh3-cash",
                                "path": "parties.seller",
                                "type": "ARRAY",
                            },
                            {
                                "labelId": "lei:549300il8tqt0jmdjj80",
                                "path": "parties.sellerDecisionMaker",
                                "type": "ARRAY",
                            },
                        ],
                        "reportDetails": {
                            "investmentFirmCoveredDirective": True,
                            "reportStatus": "NEWT",
                            "transactionRefNo": "10159110",
                        },
                        "&key": "RTS22Transaction:10159110:2021-11-24:NEWT:*************",
                        "sourceIndex": "0",
                        "dataSourceName": "Beacon OMS",
                        "&model": "RTS22Transaction",
                        "&version": 1,
                        "tradersAlgosWaiversIndicators": {
                            "securitiesFinancingTxnIndicator": False
                        },
                        "instrumentDetails": {
                            "instrument": {
                                "&id": "EZNMHW305QF9EURXXXX",
                                "&key": "AnnaDsbInstrument:EZNMHW305QF9EURXXXX:1639901284",
                                "cfiAttribute1": "Index",
                                "cfiAttribute2": "European Call",
                                "cfiAttribute3": "Vanilla",
                                "cfiAttribute4": "Cash",
                                "cfiCategory": "Non-listed and complex listed options",
                                "cfiGroup": "Equity",
                                "commoditiesOrEmissionAllowanceDerivativeInd": False,
                                "derivative": {
                                    "deliveryType": "CASH",
                                    "expiryDate": "2021-12-17",
                                    "optionExerciseStyle": "EURO",
                                    "optionType": "CALL",
                                    "priceMultiplier": 1.0,
                                    "strikePrice": 105.0,
                                    "strikePriceCurrency": "EUR",
                                    "underlyingIndexName": "EU0009658426",
                                    "underlyingIndexTerm": "DAYS",
                                    "underlyingIndexTermValue": "0",
                                    "underlyingInstruments": [
                                        {"underlyingInstrumentCode": "EU0009658426"}
                                    ],
                                },
                                "ext": {
                                    "aii": {
                                        "daily": "XXXXEU0009658426OC2021-12-17 00:00:00"
                                    },
                                    "alternativeInstrumentIdentifier": "XXXXEU0009658426OC2021-12-17 00:00:00",
                                    "bestExAssetClassMain": "Equity Derivatives",
                                    "bestExAssetClassSub": "Swaps and other equity derivatives",
                                    "emirEligible": True,
                                    "instrumentIdCodeType": "ID",
                                    "instrumentUniqueIdentifier": "EZNMHW305QF9EURXXXX",
                                    "mifirEligible": False,
                                    "onFIRDS": False,
                                    "otcFlag": True,
                                    "pricingReferences": {
                                        "ICE": "isin/EZNMHW305QF9/EUR"
                                    },
                                    "strikePriceType": "MntryVal",
                                    "venueInEEA": False,
                                    "venueOutsideEEA": False,
                                },
                                "instrumentClassification": "HEIAVC",
                                "instrumentClassificationEMIRAssetClass": "EQ",
                                "instrumentClassificationEMIRContractType": "OT",
                                "instrumentClassificationEMIRProductType": "EquityOption",
                                "instrumentFullName": "Equity Option Single_Index EU0009658426 EUR 20211217",
                                "instrumentIdCode": "EZNMHW305QF9",
                                "isCreatedThroughFallback": False,
                                "notionalCurrency1": "EUR",
                                "sourceKey": "https://prod.anna-dsb.com/file-download/isin/20211218/Equity/Equity-20211218.records",
                                "venue": {
                                    "financialInstrumentShortName": "NA/O Idx Call Epn EUR 20211217",
                                    "tradingVenue": "XXXX",
                                },
                            }
                        },
                        "&cascadeId": "93929af3-23b7-48df-8f3b-2da95369622f",
                        "parties": {
                            "buyer": [
                                {
                                    "&id": "34ce47bc-00d1-4242-8efa-b483c4bc246d",
                                    "&key": "MarketCounterparty:34ce47bc-00d1-4242-8efa-b483c4bc246d:*************",
                                    "details": {
                                        "firmStatus": "ACTIVE",
                                        "firmType": "Counterparty",
                                        "leiRegistrationStatus": "ISSUED",
                                        "retailOrProfessional": "N/A",
                                    },
                                    "firmIdentifiers": {"lei": "K6Q0W1PS1L1O4IQL9C32"},
                                    "firmLocation": {
                                        "registeredAddress": {
                                            "address": "25 BANK STREET",
                                            "city": "LONDON",
                                            "country": "GB",
                                            "postalCode": "E14 5JP",
                                        },
                                        "tradingAddress": {
                                            "address": "25 Bank Street",
                                            "city": "London",
                                            "country": "GB",
                                            "postalCode": "E14 5JP",
                                        },
                                    },
                                    "name": "JP MORGAN SECURITIES PLC",
                                    "sinkIdentifiers": {
                                        "tradeFileIdentifiers": [
                                            {"id": "JPD1", "label": "id"},
                                            {"id": "JPMA", "label": "id"},
                                            {"id": "JPME", "label": "id"},
                                            {"id": "JPMS", "label": "id"},
                                            {"id": "JPMS2", "label": "id"},
                                            {"id": "JPMX", "label": "id"},
                                            {"id": "JPMS-SWAP-ALGO", "label": "id"},
                                            {"id": "jpm pb", "label": "id"},
                                            {"id": "jpmpb", "label": "id"},
                                            {"id": "FUT-SSO-EURO-CARE", "label": "id"},
                                            {"id": "FUT-AMST-SSO-CARE", "label": "id"},
                                            {"id": "fut-opt-euro-care", "label": "id"},
                                            {"id": "JPME", "label": "id"},
                                            {"id": "fut-par-sso-care", "label": "id"},
                                            {"id": "jpms:euro", "label": "id"},
                                            {"id": "JPHE", "label": "id"},
                                            {"id": "JPHU", "label": "id"},
                                            {"id": "fut-opt-euro-algo", "label": "id"},
                                            {"id": "fut-opt-lifice-dma", "label": "id"},
                                            {"id": "fut-opt-euro-dma", "label": "id"},
                                            {"id": "fut-amst-sso-dma", "label": "id"},
                                            {"id": "fut-sso-euro-dma", "label": "id"},
                                            {
                                                "id": "fut-opt-lifice-care",
                                                "label": "id",
                                            },
                                            {"id": "fut-par-sso-dma", "label": "id"},
                                            {"id": "fut-opt-hk-dma", "label": "id"},
                                            {"id": "fut-meff-sso-dma", "label": "id"},
                                            {"id": "fut-meff-sso-dma", "label": "id"},
                                            {"id": "jple", "label": "id"},
                                            {"id": "jpm-mleg-dma", "label": "id"},
                                            {"id": "jpms:euro-cash", "label": "id"},
                                            {"id": "jpfe", "label": "id"},
                                            {"id": "jpm-opt-algo", "label": "id"},
                                            {"id": "xpaf-care", "label": "id"},
                                            {"id": "jphe", "label": "account"},
                                            {"id": "fut-meff-sso-care", "label": "id"},
                                            {"id": "jp:eu:swap:algo", "label": "id"},
                                        ]
                                    },
                                    "uniqueIds": [
                                        "id:fut-sso-euro-care",
                                        "id:jpme",
                                        "id:jpmx",
                                        "id:jp:eu:swap:algo",
                                        "id:jpm-mleg-dma",
                                        "id:xpaf-care",
                                        "id:fut-opt-euro-dma",
                                        "id:jple",
                                        "id:fut-opt-euro-algo",
                                        "id:jpd1",
                                        "id:jpmpb",
                                        "id:jpma",
                                        "id:fut-opt-euro-care",
                                        "id:jpm pb",
                                        "id:fut-amst-sso-dma",
                                        "id:fut-meff-sso-dma",
                                        "id:jpms-swap-algo",
                                        "id:jpms:euro-cash",
                                        "id:fut-par-sso-care",
                                        "id:fut-opt-hk-dma",
                                        "id:fut-sso-euro-dma",
                                        "id:jphe",
                                        "id:jpm-opt-algo",
                                        "account:jphe",
                                        "id:fut-meff-sso-care",
                                        "id:jpms2",
                                        "id:jphu",
                                        "id:fut-opt-lifice-care",
                                        "id:jpms",
                                        "id:fut-opt-lifice-dma",
                                        "id:jpms:euro",
                                        "id:fut-amst-sso-care",
                                        "id:fut-par-sso-dma",
                                        "id:jpfe",
                                    ],
                                }
                            ],
                            "counterparty": {
                                "&id": "34ce47bc-00d1-4242-8efa-b483c4bc246d",
                                "&key": "MarketCounterparty:34ce47bc-00d1-4242-8efa-b483c4bc246d:*************",
                                "details": {
                                    "firmStatus": "ACTIVE",
                                    "firmType": "Counterparty",
                                    "leiRegistrationStatus": "ISSUED",
                                    "retailOrProfessional": "N/A",
                                },
                                "firmIdentifiers": {"lei": "K6Q0W1PS1L1O4IQL9C32"},
                                "firmLocation": {
                                    "registeredAddress": {
                                        "address": "25 BANK STREET",
                                        "city": "LONDON",
                                        "country": "GB",
                                        "postalCode": "E14 5JP",
                                    },
                                    "tradingAddress": {
                                        "address": "25 Bank Street",
                                        "city": "London",
                                        "country": "GB",
                                        "postalCode": "E14 5JP",
                                    },
                                },
                                "name": "JP MORGAN SECURITIES PLC",
                                "sinkIdentifiers": {
                                    "tradeFileIdentifiers": [
                                        {"id": "JPD1", "label": "id"},
                                        {"id": "JPMA", "label": "id"},
                                        {"id": "JPME", "label": "id"},
                                        {"id": "JPMS", "label": "id"},
                                        {"id": "JPMS2", "label": "id"},
                                        {"id": "JPMX", "label": "id"},
                                        {"id": "JPMS-SWAP-ALGO", "label": "id"},
                                        {"id": "jpm pb", "label": "id"},
                                        {"id": "jpmpb", "label": "id"},
                                        {"id": "FUT-SSO-EURO-CARE", "label": "id"},
                                        {"id": "FUT-AMST-SSO-CARE", "label": "id"},
                                        {"id": "fut-opt-euro-care", "label": "id"},
                                        {"id": "JPME", "label": "id"},
                                        {"id": "fut-par-sso-care", "label": "id"},
                                        {"id": "jpms:euro", "label": "id"},
                                        {"id": "JPHE", "label": "id"},
                                        {"id": "JPHU", "label": "id"},
                                        {"id": "fut-opt-euro-algo", "label": "id"},
                                        {"id": "fut-opt-lifice-dma", "label": "id"},
                                        {"id": "fut-opt-euro-dma", "label": "id"},
                                        {"id": "fut-amst-sso-dma", "label": "id"},
                                        {"id": "fut-sso-euro-dma", "label": "id"},
                                        {"id": "fut-opt-lifice-care", "label": "id"},
                                        {"id": "fut-par-sso-dma", "label": "id"},
                                        {"id": "fut-opt-hk-dma", "label": "id"},
                                        {"id": "fut-meff-sso-dma", "label": "id"},
                                        {"id": "fut-meff-sso-dma", "label": "id"},
                                        {"id": "jple", "label": "id"},
                                        {"id": "jpm-mleg-dma", "label": "id"},
                                        {"id": "jpms:euro-cash", "label": "id"},
                                        {"id": "jpfe", "label": "id"},
                                        {"id": "jpm-opt-algo", "label": "id"},
                                        {"id": "xpaf-care", "label": "id"},
                                        {"id": "jphe", "label": "account"},
                                        {"id": "fut-meff-sso-care", "label": "id"},
                                        {"id": "jp:eu:swap:algo", "label": "id"},
                                    ]
                                },
                                "uniqueIds": [
                                    "id:fut-sso-euro-care",
                                    "id:jpme",
                                    "id:jpmx",
                                    "id:jp:eu:swap:algo",
                                    "id:jpm-mleg-dma",
                                    "id:xpaf-care",
                                    "id:fut-opt-euro-dma",
                                    "id:jple",
                                    "id:fut-opt-euro-algo",
                                    "id:jpd1",
                                    "id:jpmpb",
                                    "id:jpma",
                                    "id:fut-opt-euro-care",
                                    "id:jpm pb",
                                    "id:fut-amst-sso-dma",
                                    "id:fut-meff-sso-dma",
                                    "id:jpms-swap-algo",
                                    "id:jpms:euro-cash",
                                    "id:fut-par-sso-care",
                                    "id:fut-opt-hk-dma",
                                    "id:fut-sso-euro-dma",
                                    "id:jphe",
                                    "id:jpm-opt-algo",
                                    "account:jphe",
                                    "id:fut-meff-sso-care",
                                    "id:jpms2",
                                    "id:jphu",
                                    "id:fut-opt-lifice-care",
                                    "id:jpms",
                                    "id:fut-opt-lifice-dma",
                                    "id:jpms:euro",
                                    "id:fut-amst-sso-care",
                                    "id:fut-par-sso-dma",
                                    "id:jpfe",
                                ],
                            },
                            "executingEntity": {
                                "&id": "$this",
                                "&key": "AccountFirm:$this:*************",
                                "client": {
                                    "isAggregatedClientAccount": False,
                                    "metFaceToFace": False,
                                },
                                "details": {
                                    "firmStatus": "ACTIVE",
                                    "inEEA": False,
                                    "isEmirDelegatedReporting": False,
                                    "mifidRegistered": True,
                                    "parentOfCollectiveInvestmentSchema": False,
                                    "retailOrProfessional": "N/A",
                                },
                                "emirDetails": {
                                    "corporateSector": ["F"],
                                    "isClearingThreshold": False,
                                    "natureOfFirm": "F",
                                },
                                "firmIdentifiers": {
                                    "branchCountry": "GB",
                                    "deaAccess": False,
                                    "isIsda": False,
                                    "kycApproved": False,
                                    "lei": "549300IL8TQT0JMDJJ80",
                                },
                                "name": "LH NR UK (Management) LLP",
                                "sinkIdentifiers": {
                                    "orderFileIdentifiers": [
                                        {"id": "549300IL8TQT0JMDJJ80", "label": "lei"}
                                    ],
                                    "tradeFileIdentifiers": [
                                        {"id": "549300IL8TQT0JMDJJ80", "label": "lei"}
                                    ],
                                },
                                "uniqueIds": ["lei:549300il8tqt0jmdjj80"],
                            },
                            "executionWithinFirm": {
                                "&id": "eda30df2-0083-4a27-9593-f6b3e588780f",
                                "&key": "AccountPerson:eda30df2-0083-4a27-9593-f6b3e588780f:*************",
                                "communications": {
                                    "phoneNumbers": [{"isValid": False}]
                                },
                                "name": "Rory Hill",
                                "officialIdentifiers": {
                                    "branchCountry": "GB",
                                    "mifirId": "GB*********",
                                    "mifirIdSubType": "NIDN",
                                    "mifirIdType": "N",
                                    "nationalIds": [
                                        {
                                            "id": "*********",
                                            "label": "GB - National Insurance",
                                        }
                                    ],
                                },
                                "personalDetails": {
                                    "firstName": "Rory",
                                    "lastName": "Hill",
                                    "nationality": ["GB"],
                                },
                                "retailOrProfessional": "N/A",
                                "sinkIdentifiers": {
                                    "tradeFileIdentifiers": [
                                        {"id": "RHILL", "label": "id"},
                                        {
                                            "id": "lh nr uk management llp",
                                            "label": "id",
                                        },
                                        {"id": "roryhill", "label": "id"},
                                        {"id": "hillr", "label": "id"},
                                    ]
                                },
                                "uniqueIds": [
                                    "none",
                                    "id:rhill",
                                    "id:lh nr uk management llp",
                                    "id:roryhill",
                                    "id:hillr",
                                ],
                            },
                            "investmentDecisionWithinFirm": {
                                "&id": "eda30df2-0083-4a27-9593-f6b3e588780f",
                                "&key": "AccountPerson:eda30df2-0083-4a27-9593-f6b3e588780f:*************",
                                "communications": {
                                    "phoneNumbers": [{"isValid": False}]
                                },
                                "name": "Rory Hill",
                                "officialIdentifiers": {
                                    "branchCountry": "GB",
                                    "mifirId": "GB*********",
                                    "mifirIdSubType": "NIDN",
                                    "mifirIdType": "N",
                                    "nationalIds": [
                                        {
                                            "id": "*********",
                                            "label": "GB - National Insurance",
                                        }
                                    ],
                                },
                                "personalDetails": {
                                    "firstName": "Rory",
                                    "lastName": "Hill",
                                    "nationality": ["GB"],
                                },
                                "retailOrProfessional": "N/A",
                                "sinkIdentifiers": {
                                    "tradeFileIdentifiers": [
                                        {"id": "RHILL", "label": "id"},
                                        {
                                            "id": "lh nr uk management llp",
                                            "label": "id",
                                        },
                                        {"id": "roryhill", "label": "id"},
                                        {"id": "hillr", "label": "id"},
                                    ]
                                },
                                "uniqueIds": [
                                    "none",
                                    "id:rhill",
                                    "id:lh nr uk management llp",
                                    "id:roryhill",
                                    "id:hillr",
                                ],
                            },
                            "seller": [
                                {
                                    "&id": "837cc235-f5f6-468e-bac0-0bba6af9d688",
                                    "&key": "MarketCounterparty:837cc235-f5f6-468e-bac0-0bba6af9d688:1661933204153",
                                    "details": {
                                        "firmStatus": "ACTIVE",
                                        "firmType": "Client",
                                        "leiRegistrationStatus": "ISSUED",
                                        "retailOrProfessional": "N/A",
                                    },
                                    "firmIdentifiers": {
                                        "branchCountry": "GB",
                                        "lei": "549300D4ICPG3QFV4731",
                                    },
                                    "firmLocation": {
                                        "registeredAddress": {
                                            "address": "C/O Walkers Corporate Limited",
                                            "city": "George Town",
                                            "country": "KY",
                                            "postalCode": "KY1-9008",
                                        },
                                        "tradingAddress": {
                                            "address": "C/O Lighthouse Investment Partners LLC",
                                            "city": "Palm Beach Gardens",
                                            "country": "US",
                                            "postalCode": "33410",
                                        },
                                    },
                                    "name": "North Rock SPC - NR 1 SP",
                                    "sinkIdentifiers": {
                                        "tradeFileIdentifiers": [
                                            {"id": "DR1-CASH", "label": "id"},
                                            {"id": " DR1-SWAP", "label": "id"},
                                            {"id": " DR1-FUT", "label": "id"},
                                            {"id": " RH3-CASH", "label": "id"},
                                            {"id": " RH3-SWAP", "label": "id"},
                                            {"id": " RH3-FUT", "label": "id"},
                                            {"id": "RHILL_FUT_JPMEU", "label": "id"},
                                            {"id": "RHILL_CASH_JPM", "label": "id"},
                                            {"id": "RHILL_FUT_JPM", "label": "id"},
                                            {"id": "RHILL_SWAP_JPM", "label": "id"},
                                            {"id": "HILLR_FUT_JPMEU", "label": "id"},
                                            {"id": "HILLR_CASH_JPM", "label": "id"},
                                            {"id": "HILLR_SWAP_JPM", "label": "id"},
                                            {"id": "HILLR_FUT_JPM", "label": "id"},
                                            {"id": "rhill_feo_jpm", "label": "id"},
                                            {"id": "hillr_feo_jpm", "label": "id"},
                                            {"id": "AECCLES_CASH_JPM", "label": "id"},
                                            {"id": "AECCLES_SWAP_JPM", "label": "id"},
                                            {"id": "AECCLES_FUT_JPM", "label": "id"},
                                            {"id": "AECCLES_FEO_JPM", "label": "id"},
                                            {"id": "SSHIELDS_FEO_JPM", "label": "id"},
                                            {"id": "SSHIELDS_FUT_JPM", "label": "id"},
                                            {"id": "SSHIELDS_SWAP_JPM", "label": "id"},
                                            {"id": "SSHIELDS_CASH_JPM", "label": "id"},
                                            {"id": "MJOHNSON_FEO_JPM", "label": "id"},
                                            {"id": "MJOHNSON_FUT_JPM", "label": "id"},
                                            {"id": "MJOHNSON_SWAP_JPM", "label": "id"},
                                            {"id": "MJOHNSON_CASH_JPM", "label": "id"},
                                            {"id": "DFRAUEND_FEO_JPM", "label": "id"},
                                            {"id": "DFRAUEND_FUT_JPM", "label": "id"},
                                            {"id": "DFRAUEND_SWAP_JPM", "label": "id"},
                                            {"id": "DFRAUEND_CASH_JPM", "label": "id"},
                                            {"id": "RoryHillOTC[JPM]", "label": "id"},
                                            {"id": "RoryHillOTC[MS]", "label": "id"},
                                            {"id": "RoryHillRH3-CASH", "label": "id"},
                                            {"id": "RHILL_CASH_JPM", "label": "id"},
                                            {"id": "Rory Hill OTC[JPM]", "label": "id"},
                                            {"id": "Rory Hill OTC[MS]", "label": "id"},
                                            {"id": "RHOCKING_CASH_MS", "label": "id"},
                                            {"id": "RHOCKING_SWAP_MS", "label": "id"},
                                            {"id": "RHOCKING_FUT_MS", "label": "id"},
                                            {"id": "RHOCKING_FEO_MS", "label": "id"},
                                            {"id": "RHOCKING_CASH_JPM", "label": "id"},
                                            {"id": "RHOCKING_SWAP_JPM", "label": "id"},
                                            {"id": "RHARRIS_SWAP_JPM", "label": "id"},
                                            {"id": "RHARRIS_CASH_JPM", "label": "id"},
                                            {"id": "RHARRIS_FEO_JPM", "label": "id"},
                                            {"id": "RHARRIS_FUT_JPM", "label": "id"},
                                            {"id": "RHILL_SWAP_MS", "label": "id"},
                                            {"id": "rhill_swap_ms", "label": "id"},
                                            {"id": "rhill_fef_ms", "label": "id"},
                                            {"id": "rhill_cash_baml", "label": "id"},
                                            {"id": "rory hill otc ms", "label": "id"},
                                            {"id": "rory hill otc ubs", "label": "id"},
                                            {
                                                "id": "rory hill swap&otc ml",
                                                "label": "id",
                                            },
                                            {"id": "rhill_swap_baml", "label": "id"},
                                            {
                                                "id": "rory hill otc ubs",
                                                "label": "account",
                                            },
                                            {
                                                "id": "rory hill otc[jpm]",
                                                "label": "account",
                                            },
                                            {
                                                "id": "Rory Hill OTC MS",
                                                "label": "account",
                                            },
                                            {
                                                "id": "Rory Hill Swap&OTC ML",
                                                "label": "account",
                                            },
                                            {"id": "rhocking_fut_jpm", "label": "id"},
                                            {"id": "rhocking_usf_ms", "label": "id"},
                                            {"id": "rhocking_fef_ms", "label": "id"},
                                            {"id": "rhill_swap_gs", "label": "id"},
                                            {
                                                "id": "rory hill otc citi",
                                                "label": "account",
                                            },
                                            {"id": "namin_swap_ms", "label": "id"},
                                            {"id": "namin_cash_ms", "label": "id"},
                                            {"id": "dhiscox_swap_jpm", "label": "id"},
                                            {"id": "dhiscox_swap_barc", "label": "id"},
                                            {"id": "dhiscox_cash_barc", "label": "id"},
                                            {"id": "dhiscox_cash_jpm", "label": "id"},
                                            {"id": "AARTHURS_CASH_GS", "label": "id"},
                                            {"id": "AARTHURS_FEO_GS", "label": "id"},
                                            {"id": "AARTHURS_FUT_GS", "label": "id"},
                                            {"id": "AARTHURS_SWAP_GS", "label": "id"},
                                            {"id": "ADIOGO_CASH_ML", "label": "id"},
                                            {"id": "ADIOGO_FUT_ML", "label": "id"},
                                            {"id": "ADIOGO_SWAP_ML", "label": "id"},
                                            {"id": "namin_swap_jpm", "label": "id"},
                                            {"id": "dhiscox_feo_jpm", "label": "id"},
                                        ]
                                    },
                                    "uniqueIds": [
                                        "id:aarthurs_fut_gs",
                                        "id:hillr_fut_jpmeu",
                                        "id:rhocking_fut_jpm",
                                        "id:aarthurs_feo_gs",
                                        "id:adiogo_swap_ml",
                                        "id:mjohnson_feo_jpm",
                                        "id:namin_swap_jpm",
                                        "id:rharris_cash_jpm",
                                        "id:sshields_feo_jpm",
                                        "id:hillr_fut_jpm",
                                        "id:rhill_swap_gs",
                                        "id:rhill_swap_jpm",
                                        "id:dhiscox_cash_jpm",
                                        "id:rhill_swap_ms",
                                        "id:rhocking_fut_ms",
                                        "id:rory hill swap&otc ml",
                                        "id:rhill_cash_jpm",
                                        "id:rory hill otc[ms]",
                                        "id:rhill_fut_jpm",
                                        "id:mjohnson_swap_jpm",
                                        "id:hillr_cash_jpm",
                                        "id:dhiscox_cash_barc",
                                        "id:aeccles_swap_jpm",
                                        "id:dhiscox_feo_jpm",
                                        "id:rhocking_feo_ms",
                                        "id:aeccles_cash_jpm",
                                        "id:rharris_swap_jpm",
                                        "id:namin_swap_ms",
                                        "id:roryhillrh3-cash",
                                        "account:rory hill otc citi",
                                        "id:rhill_feo_jpm",
                                        "id:dhiscox_swap_barc",
                                        "id:hillr_swap_jpm",
                                        "id: rh3-cash",
                                        "id: dr1-swap",
                                        "id:sshields_cash_jpm",
                                        "id:rhocking_cash_jpm",
                                        "id:adiogo_cash_ml",
                                        "id:rhill_swap_baml",
                                        "id:rhill_fut_jpmeu",
                                        "account:rory hill otc ubs",
                                        "id:sshields_swap_jpm",
                                        "account:rory hill otc[jpm]",
                                        "id:namin_cash_ms",
                                        "id:dfrauend_fut_jpm",
                                        "id:rory hill otc[jpm]",
                                        "id:rhocking_swap_ms",
                                        "id:rhocking_fef_ms",
                                        "id:rharris_feo_jpm",
                                        "id:rory hill otc ubs",
                                        "id:rhocking_cash_ms",
                                        "id: rh3-swap",
                                        "id:adiogo_fut_ml",
                                        "id: dr1-fut",
                                        "id:roryhillotc[jpm]",
                                        "id: rh3-fut",
                                        "id:aeccles_fut_jpm",
                                        "id:rharris_fut_jpm",
                                        "id:dfrauend_cash_jpm",
                                        "id:mjohnson_cash_jpm",
                                        "id:dfrauend_feo_jpm",
                                        "id:aeccles_feo_jpm",
                                        "id:aarthurs_cash_gs",
                                        "id:aarthurs_swap_gs",
                                        "id:rhill_fef_ms",
                                        "account:rory hill swap&otc ml",
                                        "account:rory hill otc ms",
                                        "id:sshields_fut_jpm",
                                        "id:roryhillotc[ms]",
                                        "id:mjohnson_fut_jpm",
                                        "id:dr1-cash",
                                        "id:dfrauend_swap_jpm",
                                        "id:rhocking_swap_jpm",
                                        "id:rory hill otc ms",
                                        "id:rhill_cash_baml",
                                        "id:rhocking_usf_ms",
                                        "id:hillr_feo_jpm",
                                        "id:dhiscox_swap_jpm",
                                    ],
                                }
                            ],
                            "sellerDecisionMaker": [
                                {
                                    "&id": "$this",
                                    "&key": "AccountFirm:$this:*************",
                                    "client": {
                                        "isAggregatedClientAccount": False,
                                        "metFaceToFace": False,
                                    },
                                    "details": {
                                        "firmStatus": "ACTIVE",
                                        "inEEA": False,
                                        "isEmirDelegatedReporting": False,
                                        "mifidRegistered": True,
                                        "parentOfCollectiveInvestmentSchema": False,
                                        "retailOrProfessional": "N/A",
                                    },
                                    "emirDetails": {
                                        "corporateSector": ["F"],
                                        "isClearingThreshold": False,
                                        "natureOfFirm": "F",
                                    },
                                    "firmIdentifiers": {
                                        "branchCountry": "GB",
                                        "deaAccess": False,
                                        "isIsda": False,
                                        "kycApproved": False,
                                        "lei": "549300IL8TQT0JMDJJ80",
                                    },
                                    "name": "LH NR UK (Management) LLP",
                                    "sinkIdentifiers": {
                                        "orderFileIdentifiers": [
                                            {
                                                "id": "549300IL8TQT0JMDJJ80",
                                                "label": "lei",
                                            }
                                        ],
                                        "tradeFileIdentifiers": [
                                            {
                                                "id": "549300IL8TQT0JMDJJ80",
                                                "label": "lei",
                                            }
                                        ],
                                    },
                                    "uniqueIds": ["lei:549300il8tqt0jmdjj80"],
                                }
                            ],
                        },
                        "&hash": "ba025bf45c9dfc3baf86267ce21afc97b61fbe1edaebe5a6e1468aa670ebf6f5",
                        "&validationErrors": None,
                        "&timestamp": *************,
                        "&user": "system",
                    },
                    "sort": [*************, "10159110:2021-11-24:NEWT"],
                },
                {
                    "_index": "lighthouse_steeleye_co_rts22transaction",
                    "_type": "RTS22Transaction",
                    "_id": "10159114:2021-11-24:NEWT",
                    "_score": None,
                    "_source": {
                        "date": "2021-11-24",
                        "sourceKey": "s3://lighthouse.steeleye.co/flows/tr-feed-beacon-lighthouse/LH_OTC.dat",
                        "&id": "10159114:2021-11-24:NEWT",
                        "transactionDetails": {
                            "buySellIndicator": "BUYI",
                            "price": 0.52,
                            "priceCurrency": "EUR",
                            "priceNotation": "MONE",
                            "quantity": 1000.0,
                            "quantityCurrency": "EUR",
                            "quantityNotation": "UNIT",
                            "tradingCapacity": "AOTC",
                            "tradingDateTime": "2021-11-24T12:00:03Z",
                            "ultimateVenue": "XXXX",
                            "venue": "XXXX",
                        },
                        "transmissionDetails": {"orderTransmissionIndicator": False},
                        "workflow": {
                            "eligibility": {
                                "eligible": False,
                                "executionVenue": "XXXX",
                                "isDefaulted": False,
                                "onFirds": False,
                                "totv": False,
                                "underlyingOnFirds": False,
                                "utotv": False,
                            },
                            "isReported": False,
                            "status": "NON_REPORTABLE",
                            "validationStatus": "PASSED",
                        },
                        "marketIdentifiers": [
                            {
                                "labelId": "XXXXEU0009658426OC2021-12-17 00:00:00",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXEU0009658426OC2021-12 00:00:00",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXEU0009658426OC2021-12-17 00:00:00107.00000000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXEU0009658426OC2021-12-17 00:00:001.07000000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXEU0009658426OC2021-12-17 00:00:000.10700000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXEU0009658426OC2021-12-17 00:00:000.01070000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXEU0009658426OC2021-12 00:00:00107.00000000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXEU0009658426OC2021-12 00:00:001.07000000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXEU0009658426OC2021-12 00:00:000.10700000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "XXXXEU0009658426OC2021-12 00:00:000.01070000",
                                "path": "instrumentDetails.instrument",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "lei:549300il8tqt0jmdjj80",
                                "path": "parties.executingEntity",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "id:jphe",
                                "path": "parties.counterparty",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "id:roryhill",
                                "path": "parties.trader",
                                "type": "ARRAY",
                            },
                            {
                                "labelId": "id:roryhill",
                                "path": "parties.executionWithinFirm",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "id:roryhill",
                                "path": "parties.investmentDecisionWithinFirm",
                                "type": "OBJECT",
                            },
                            {
                                "labelId": "id:roryhillrh3-cash",
                                "path": "parties.buyer",
                                "type": "ARRAY",
                            },
                            {
                                "labelId": "id:jphe",
                                "path": "parties.seller",
                                "type": "ARRAY",
                            },
                            {
                                "labelId": "lei:549300il8tqt0jmdjj80",
                                "path": "parties.buyerDecisionMaker",
                                "type": "ARRAY",
                            },
                        ],
                        "reportDetails": {
                            "investmentFirmCoveredDirective": True,
                            "reportStatus": "NEWT",
                            "transactionRefNo": "10159114",
                        },
                        "&key": "RTS22Transaction:10159114:2021-11-24:NEWT:*************",
                        "sourceIndex": "1",
                        "dataSourceName": "Beacon OMS",
                        "&model": "RTS22Transaction",
                        "&version": 1,
                        "tradersAlgosWaiversIndicators": {
                            "securitiesFinancingTxnIndicator": False
                        },
                        "instrumentDetails": {
                            "instrument": {
                                "&id": "EZNMHW305QF9EURXXXX",
                                "&key": "AnnaDsbInstrument:EZNMHW305QF9EURXXXX:1639901284",
                                "cfiAttribute1": "Index",
                                "cfiAttribute2": "European Call",
                                "cfiAttribute3": "Vanilla",
                                "cfiAttribute4": "Cash",
                                "cfiCategory": "Non-listed and complex listed options",
                                "cfiGroup": "Equity",
                                "commoditiesOrEmissionAllowanceDerivativeInd": False,
                                "derivative": {
                                    "deliveryType": "CASH",
                                    "expiryDate": "2021-12-17",
                                    "optionExerciseStyle": "EURO",
                                    "optionType": "CALL",
                                    "priceMultiplier": 1.0,
                                    "strikePrice": 107.0,
                                    "strikePriceCurrency": "EUR",
                                    "underlyingIndexName": "EU0009658426",
                                    "underlyingIndexTerm": "DAYS",
                                    "underlyingIndexTermValue": "0",
                                    "underlyingInstruments": [
                                        {"underlyingInstrumentCode": "EU0009658426"}
                                    ],
                                },
                                "ext": {
                                    "aii": {
                                        "daily": "XXXXEU0009658426OC2021-12-17 00:00:00"
                                    },
                                    "alternativeInstrumentIdentifier": "XXXXEU0009658426OC2021-12-17 00:00:00",
                                    "bestExAssetClassMain": "Equity Derivatives",
                                    "bestExAssetClassSub": "Swaps and other equity derivatives",
                                    "emirEligible": True,
                                    "instrumentIdCodeType": "ID",
                                    "instrumentUniqueIdentifier": "EZNMHW305QF9EURXXXX",
                                    "mifirEligible": False,
                                    "onFIRDS": False,
                                    "otcFlag": True,
                                    "pricingReferences": {
                                        "ICE": "isin/EZNMHW305QF9/EUR"
                                    },
                                    "strikePriceType": "MntryVal",
                                    "venueInEEA": False,
                                    "venueOutsideEEA": False,
                                },
                                "instrumentClassification": "HEIAVC",
                                "instrumentClassificationEMIRAssetClass": "EQ",
                                "instrumentClassificationEMIRContractType": "OT",
                                "instrumentClassificationEMIRProductType": "EquityOption",
                                "instrumentFullName": "Equity Option Single_Index EU0009658426 EUR 20211217",
                                "instrumentIdCode": "EZNMHW305QF9",
                                "isCreatedThroughFallback": False,
                                "notionalCurrency1": "EUR",
                                "sourceKey": "https://prod.anna-dsb.com/file-download/isin/20211218/Equity/Equity-20211218.records",
                                "venue": {
                                    "financialInstrumentShortName": "NA/O Idx Call Epn EUR 20211217",
                                    "tradingVenue": "XXXX",
                                },
                            }
                        },
                        "&cascadeId": "93929af3-23b7-48df-8f3b-2da95369622f",
                        "parties": {
                            "buyer": [
                                {
                                    "&id": "837cc235-f5f6-468e-bac0-0bba6af9d688",
                                    "&key": "MarketCounterparty:837cc235-f5f6-468e-bac0-0bba6af9d688:1661933204153",
                                    "details": {
                                        "firmStatus": "ACTIVE",
                                        "firmType": "Client",
                                        "leiRegistrationStatus": "ISSUED",
                                        "retailOrProfessional": "N/A",
                                    },
                                    "firmIdentifiers": {
                                        "branchCountry": "GB",
                                        "lei": "549300D4ICPG3QFV4731",
                                    },
                                    "firmLocation": {
                                        "registeredAddress": {
                                            "address": "C/O Walkers Corporate Limited",
                                            "city": "George Town",
                                            "country": "KY",
                                            "postalCode": "KY1-9008",
                                        },
                                        "tradingAddress": {
                                            "address": "C/O Lighthouse Investment Partners LLC",
                                            "city": "Palm Beach Gardens",
                                            "country": "US",
                                            "postalCode": "33410",
                                        },
                                    },
                                    "name": "North Rock SPC - NR 1 SP",
                                    "sinkIdentifiers": {
                                        "tradeFileIdentifiers": [
                                            {"id": "DR1-CASH", "label": "id"},
                                            {"id": " DR1-SWAP", "label": "id"},
                                            {"id": " DR1-FUT", "label": "id"},
                                            {"id": " RH3-CASH", "label": "id"},
                                            {"id": " RH3-SWAP", "label": "id"},
                                            {"id": " RH3-FUT", "label": "id"},
                                            {"id": "RHILL_FUT_JPMEU", "label": "id"},
                                            {"id": "RHILL_CASH_JPM", "label": "id"},
                                            {"id": "RHILL_FUT_JPM", "label": "id"},
                                            {"id": "RHILL_SWAP_JPM", "label": "id"},
                                            {"id": "HILLR_FUT_JPMEU", "label": "id"},
                                            {"id": "HILLR_CASH_JPM", "label": "id"},
                                            {"id": "HILLR_SWAP_JPM", "label": "id"},
                                            {"id": "HILLR_FUT_JPM", "label": "id"},
                                            {"id": "rhill_feo_jpm", "label": "id"},
                                            {"id": "hillr_feo_jpm", "label": "id"},
                                            {"id": "AECCLES_CASH_JPM", "label": "id"},
                                            {"id": "AECCLES_SWAP_JPM", "label": "id"},
                                            {"id": "AECCLES_FUT_JPM", "label": "id"},
                                            {"id": "AECCLES_FEO_JPM", "label": "id"},
                                            {"id": "SSHIELDS_FEO_JPM", "label": "id"},
                                            {"id": "SSHIELDS_FUT_JPM", "label": "id"},
                                            {"id": "SSHIELDS_SWAP_JPM", "label": "id"},
                                            {"id": "SSHIELDS_CASH_JPM", "label": "id"},
                                            {"id": "MJOHNSON_FEO_JPM", "label": "id"},
                                            {"id": "MJOHNSON_FUT_JPM", "label": "id"},
                                            {"id": "MJOHNSON_SWAP_JPM", "label": "id"},
                                            {"id": "MJOHNSON_CASH_JPM", "label": "id"},
                                            {"id": "DFRAUEND_FEO_JPM", "label": "id"},
                                            {"id": "DFRAUEND_FUT_JPM", "label": "id"},
                                            {"id": "DFRAUEND_SWAP_JPM", "label": "id"},
                                            {"id": "DFRAUEND_CASH_JPM", "label": "id"},
                                            {"id": "RoryHillOTC[JPM]", "label": "id"},
                                            {"id": "RoryHillOTC[MS]", "label": "id"},
                                            {"id": "RoryHillRH3-CASH", "label": "id"},
                                            {"id": "RHILL_CASH_JPM", "label": "id"},
                                            {"id": "Rory Hill OTC[JPM]", "label": "id"},
                                            {"id": "Rory Hill OTC[MS]", "label": "id"},
                                            {"id": "RHOCKING_CASH_MS", "label": "id"},
                                            {"id": "RHOCKING_SWAP_MS", "label": "id"},
                                            {"id": "RHOCKING_FUT_MS", "label": "id"},
                                            {"id": "RHOCKING_FEO_MS", "label": "id"},
                                            {"id": "RHOCKING_CASH_JPM", "label": "id"},
                                            {"id": "RHOCKING_SWAP_JPM", "label": "id"},
                                            {"id": "RHARRIS_SWAP_JPM", "label": "id"},
                                            {"id": "RHARRIS_CASH_JPM", "label": "id"},
                                            {"id": "RHARRIS_FEO_JPM", "label": "id"},
                                            {"id": "RHARRIS_FUT_JPM", "label": "id"},
                                            {"id": "RHILL_SWAP_MS", "label": "id"},
                                            {"id": "rhill_swap_ms", "label": "id"},
                                            {"id": "rhill_fef_ms", "label": "id"},
                                            {"id": "rhill_cash_baml", "label": "id"},
                                            {"id": "rory hill otc ms", "label": "id"},
                                            {"id": "rory hill otc ubs", "label": "id"},
                                            {
                                                "id": "rory hill swap&otc ml",
                                                "label": "id",
                                            },
                                            {"id": "rhill_swap_baml", "label": "id"},
                                            {
                                                "id": "rory hill otc ubs",
                                                "label": "account",
                                            },
                                            {
                                                "id": "rory hill otc[jpm]",
                                                "label": "account",
                                            },
                                            {
                                                "id": "Rory Hill OTC MS",
                                                "label": "account",
                                            },
                                            {
                                                "id": "Rory Hill Swap&OTC ML",
                                                "label": "account",
                                            },
                                            {"id": "rhocking_fut_jpm", "label": "id"},
                                            {"id": "rhocking_usf_ms", "label": "id"},
                                            {"id": "rhocking_fef_ms", "label": "id"},
                                            {"id": "rhill_swap_gs", "label": "id"},
                                            {
                                                "id": "rory hill otc citi",
                                                "label": "account",
                                            },
                                            {"id": "namin_swap_ms", "label": "id"},
                                            {"id": "namin_cash_ms", "label": "id"},
                                            {"id": "dhiscox_swap_jpm", "label": "id"},
                                            {"id": "dhiscox_swap_barc", "label": "id"},
                                            {"id": "dhiscox_cash_barc", "label": "id"},
                                            {"id": "dhiscox_cash_jpm", "label": "id"},
                                            {"id": "AARTHURS_CASH_GS", "label": "id"},
                                            {"id": "AARTHURS_FEO_GS", "label": "id"},
                                            {"id": "AARTHURS_FUT_GS", "label": "id"},
                                            {"id": "AARTHURS_SWAP_GS", "label": "id"},
                                            {"id": "ADIOGO_CASH_ML", "label": "id"},
                                            {"id": "ADIOGO_FUT_ML", "label": "id"},
                                            {"id": "ADIOGO_SWAP_ML", "label": "id"},
                                            {"id": "namin_swap_jpm", "label": "id"},
                                            {"id": "dhiscox_feo_jpm", "label": "id"},
                                        ]
                                    },
                                    "uniqueIds": [
                                        "id:aarthurs_fut_gs",
                                        "id:hillr_fut_jpmeu",
                                        "id:rhocking_fut_jpm",
                                        "id:aarthurs_feo_gs",
                                        "id:adiogo_swap_ml",
                                        "id:mjohnson_feo_jpm",
                                        "id:namin_swap_jpm",
                                        "id:rharris_cash_jpm",
                                        "id:sshields_feo_jpm",
                                        "id:hillr_fut_jpm",
                                        "id:rhill_swap_gs",
                                        "id:rhill_swap_jpm",
                                        "id:dhiscox_cash_jpm",
                                        "id:rhill_swap_ms",
                                        "id:rhocking_fut_ms",
                                        "id:rory hill swap&otc ml",
                                        "id:rhill_cash_jpm",
                                        "id:rory hill otc[ms]",
                                        "id:rhill_fut_jpm",
                                        "id:mjohnson_swap_jpm",
                                        "id:hillr_cash_jpm",
                                        "id:dhiscox_cash_barc",
                                        "id:aeccles_swap_jpm",
                                        "id:dhiscox_feo_jpm",
                                        "id:rhocking_feo_ms",
                                        "id:aeccles_cash_jpm",
                                        "id:rharris_swap_jpm",
                                        "id:namin_swap_ms",
                                        "id:roryhillrh3-cash",
                                        "account:rory hill otc citi",
                                        "id:rhill_feo_jpm",
                                        "id:dhiscox_swap_barc",
                                        "id:hillr_swap_jpm",
                                        "id: rh3-cash",
                                        "id: dr1-swap",
                                        "id:sshields_cash_jpm",
                                        "id:rhocking_cash_jpm",
                                        "id:adiogo_cash_ml",
                                        "id:rhill_swap_baml",
                                        "id:rhill_fut_jpmeu",
                                        "account:rory hill otc ubs",
                                        "id:sshields_swap_jpm",
                                        "account:rory hill otc[jpm]",
                                        "id:namin_cash_ms",
                                        "id:dfrauend_fut_jpm",
                                        "id:rory hill otc[jpm]",
                                        "id:rhocking_swap_ms",
                                        "id:rhocking_fef_ms",
                                        "id:rharris_feo_jpm",
                                        "id:rory hill otc ubs",
                                        "id:rhocking_cash_ms",
                                        "id: rh3-swap",
                                        "id:adiogo_fut_ml",
                                        "id: dr1-fut",
                                        "id:roryhillotc[jpm]",
                                        "id: rh3-fut",
                                        "id:aeccles_fut_jpm",
                                        "id:rharris_fut_jpm",
                                        "id:dfrauend_cash_jpm",
                                        "id:mjohnson_cash_jpm",
                                        "id:dfrauend_feo_jpm",
                                        "id:aeccles_feo_jpm",
                                        "id:aarthurs_cash_gs",
                                        "id:aarthurs_swap_gs",
                                        "id:rhill_fef_ms",
                                        "account:rory hill swap&otc ml",
                                        "account:rory hill otc ms",
                                        "id:sshields_fut_jpm",
                                        "id:roryhillotc[ms]",
                                        "id:mjohnson_fut_jpm",
                                        "id:dr1-cash",
                                        "id:dfrauend_swap_jpm",
                                        "id:rhocking_swap_jpm",
                                        "id:rory hill otc ms",
                                        "id:rhill_cash_baml",
                                        "id:rhocking_usf_ms",
                                        "id:hillr_feo_jpm",
                                        "id:dhiscox_swap_jpm",
                                    ],
                                }
                            ],
                            "buyerDecisionMaker": [
                                {
                                    "&id": "$this",
                                    "&key": "AccountFirm:$this:*************",
                                    "client": {
                                        "isAggregatedClientAccount": False,
                                        "metFaceToFace": False,
                                    },
                                    "details": {
                                        "firmStatus": "ACTIVE",
                                        "inEEA": False,
                                        "isEmirDelegatedReporting": False,
                                        "mifidRegistered": True,
                                        "parentOfCollectiveInvestmentSchema": False,
                                        "retailOrProfessional": "N/A",
                                    },
                                    "emirDetails": {
                                        "corporateSector": ["F"],
                                        "isClearingThreshold": False,
                                        "natureOfFirm": "F",
                                    },
                                    "firmIdentifiers": {
                                        "branchCountry": "GB",
                                        "deaAccess": False,
                                        "isIsda": False,
                                        "kycApproved": False,
                                        "lei": "549300IL8TQT0JMDJJ80",
                                    },
                                    "name": "LH NR UK (Management) LLP",
                                    "sinkIdentifiers": {
                                        "orderFileIdentifiers": [
                                            {
                                                "id": "549300IL8TQT0JMDJJ80",
                                                "label": "lei",
                                            }
                                        ],
                                        "tradeFileIdentifiers": [
                                            {
                                                "id": "549300IL8TQT0JMDJJ80",
                                                "label": "lei",
                                            }
                                        ],
                                    },
                                    "uniqueIds": ["lei:549300il8tqt0jmdjj80"],
                                }
                            ],
                            "counterparty": {
                                "&id": "34ce47bc-00d1-4242-8efa-b483c4bc246d",
                                "&key": "MarketCounterparty:34ce47bc-00d1-4242-8efa-b483c4bc246d:*************",
                                "details": {
                                    "firmStatus": "ACTIVE",
                                    "firmType": "Counterparty",
                                    "leiRegistrationStatus": "ISSUED",
                                    "retailOrProfessional": "N/A",
                                },
                                "firmIdentifiers": {"lei": "K6Q0W1PS1L1O4IQL9C32"},
                                "firmLocation": {
                                    "registeredAddress": {
                                        "address": "25 BANK STREET",
                                        "city": "LONDON",
                                        "country": "GB",
                                        "postalCode": "E14 5JP",
                                    },
                                    "tradingAddress": {
                                        "address": "25 Bank Street",
                                        "city": "London",
                                        "country": "GB",
                                        "postalCode": "E14 5JP",
                                    },
                                },
                                "name": "JP MORGAN SECURITIES PLC",
                                "sinkIdentifiers": {
                                    "tradeFileIdentifiers": [
                                        {"id": "JPD1", "label": "id"},
                                        {"id": "JPMA", "label": "id"},
                                        {"id": "JPME", "label": "id"},
                                        {"id": "JPMS", "label": "id"},
                                        {"id": "JPMS2", "label": "id"},
                                        {"id": "JPMX", "label": "id"},
                                        {"id": "JPMS-SWAP-ALGO", "label": "id"},
                                        {"id": "jpm pb", "label": "id"},
                                        {"id": "jpmpb", "label": "id"},
                                        {"id": "FUT-SSO-EURO-CARE", "label": "id"},
                                        {"id": "FUT-AMST-SSO-CARE", "label": "id"},
                                        {"id": "fut-opt-euro-care", "label": "id"},
                                        {"id": "JPME", "label": "id"},
                                        {"id": "fut-par-sso-care", "label": "id"},
                                        {"id": "jpms:euro", "label": "id"},
                                        {"id": "JPHE", "label": "id"},
                                        {"id": "JPHU", "label": "id"},
                                        {"id": "fut-opt-euro-algo", "label": "id"},
                                        {"id": "fut-opt-lifice-dma", "label": "id"},
                                        {"id": "fut-opt-euro-dma", "label": "id"},
                                        {"id": "fut-amst-sso-dma", "label": "id"},
                                        {"id": "fut-sso-euro-dma", "label": "id"},
                                        {"id": "fut-opt-lifice-care", "label": "id"},
                                        {"id": "fut-par-sso-dma", "label": "id"},
                                        {"id": "fut-opt-hk-dma", "label": "id"},
                                        {"id": "fut-meff-sso-dma", "label": "id"},
                                        {"id": "fut-meff-sso-dma", "label": "id"},
                                        {"id": "jple", "label": "id"},
                                        {"id": "jpm-mleg-dma", "label": "id"},
                                        {"id": "jpms:euro-cash", "label": "id"},
                                        {"id": "jpfe", "label": "id"},
                                        {"id": "jpm-opt-algo", "label": "id"},
                                        {"id": "xpaf-care", "label": "id"},
                                        {"id": "jphe", "label": "account"},
                                        {"id": "fut-meff-sso-care", "label": "id"},
                                        {"id": "jp:eu:swap:algo", "label": "id"},
                                    ]
                                },
                                "uniqueIds": [
                                    "id:fut-sso-euro-care",
                                    "id:jpme",
                                    "id:jpmx",
                                    "id:jp:eu:swap:algo",
                                    "id:jpm-mleg-dma",
                                    "id:xpaf-care",
                                    "id:fut-opt-euro-dma",
                                    "id:jple",
                                    "id:fut-opt-euro-algo",
                                    "id:jpd1",
                                    "id:jpmpb",
                                    "id:jpma",
                                    "id:fut-opt-euro-care",
                                    "id:jpm pb",
                                    "id:fut-amst-sso-dma",
                                    "id:fut-meff-sso-dma",
                                    "id:jpms-swap-algo",
                                    "id:jpms:euro-cash",
                                    "id:fut-par-sso-care",
                                    "id:fut-opt-hk-dma",
                                    "id:fut-sso-euro-dma",
                                    "id:jphe",
                                    "id:jpm-opt-algo",
                                    "account:jphe",
                                    "id:fut-meff-sso-care",
                                    "id:jpms2",
                                    "id:jphu",
                                    "id:fut-opt-lifice-care",
                                    "id:jpms",
                                    "id:fut-opt-lifice-dma",
                                    "id:jpms:euro",
                                    "id:fut-amst-sso-care",
                                    "id:fut-par-sso-dma",
                                    "id:jpfe",
                                ],
                            },
                            "executingEntity": {
                                "&id": "$this",
                                "&key": "AccountFirm:$this:*************",
                                "client": {
                                    "isAggregatedClientAccount": False,
                                    "metFaceToFace": False,
                                },
                                "details": {
                                    "firmStatus": "ACTIVE",
                                    "inEEA": False,
                                    "isEmirDelegatedReporting": False,
                                    "mifidRegistered": True,
                                    "parentOfCollectiveInvestmentSchema": False,
                                    "retailOrProfessional": "N/A",
                                },
                                "emirDetails": {
                                    "corporateSector": ["F"],
                                    "isClearingThreshold": False,
                                    "natureOfFirm": "F",
                                },
                                "firmIdentifiers": {
                                    "branchCountry": "GB",
                                    "deaAccess": False,
                                    "isIsda": False,
                                    "kycApproved": False,
                                    "lei": "549300IL8TQT0JMDJJ80",
                                },
                                "name": "LH NR UK (Management) LLP",
                                "sinkIdentifiers": {
                                    "orderFileIdentifiers": [
                                        {"id": "549300IL8TQT0JMDJJ80", "label": "lei"}
                                    ],
                                    "tradeFileIdentifiers": [
                                        {"id": "549300IL8TQT0JMDJJ80", "label": "lei"}
                                    ],
                                },
                                "uniqueIds": ["lei:549300il8tqt0jmdjj80"],
                            },
                            "executionWithinFirm": {
                                "&id": "eda30df2-0083-4a27-9593-f6b3e588780f",
                                "&key": "AccountPerson:eda30df2-0083-4a27-9593-f6b3e588780f:*************",
                                "communications": {
                                    "phoneNumbers": [{"isValid": False}]
                                },
                                "name": "Rory Hill",
                                "officialIdentifiers": {
                                    "branchCountry": "GB",
                                    "mifirId": "GB*********",
                                    "mifirIdSubType": "NIDN",
                                    "mifirIdType": "N",
                                    "nationalIds": [
                                        {
                                            "id": "*********",
                                            "label": "GB - National Insurance",
                                        }
                                    ],
                                },
                                "personalDetails": {
                                    "firstName": "Rory",
                                    "lastName": "Hill",
                                    "nationality": ["GB"],
                                },
                                "retailOrProfessional": "N/A",
                                "sinkIdentifiers": {
                                    "tradeFileIdentifiers": [
                                        {"id": "RHILL", "label": "id"},
                                        {
                                            "id": "lh nr uk management llp",
                                            "label": "id",
                                        },
                                        {"id": "roryhill", "label": "id"},
                                        {"id": "hillr", "label": "id"},
                                    ]
                                },
                                "uniqueIds": [
                                    "none",
                                    "id:rhill",
                                    "id:lh nr uk management llp",
                                    "id:roryhill",
                                    "id:hillr",
                                ],
                            },
                            "investmentDecisionWithinFirm": {
                                "&id": "eda30df2-0083-4a27-9593-f6b3e588780f",
                                "&key": "AccountPerson:eda30df2-0083-4a27-9593-f6b3e588780f:*************",
                                "communications": {
                                    "phoneNumbers": [{"isValid": False}]
                                },
                                "name": "Rory Hill",
                                "officialIdentifiers": {
                                    "branchCountry": "GB",
                                    "mifirId": "GB*********",
                                    "mifirIdSubType": "NIDN",
                                    "mifirIdType": "N",
                                    "nationalIds": [
                                        {
                                            "id": "*********",
                                            "label": "GB - National Insurance",
                                        }
                                    ],
                                },
                                "personalDetails": {
                                    "firstName": "Rory",
                                    "lastName": "Hill",
                                    "nationality": ["GB"],
                                },
                                "retailOrProfessional": "N/A",
                                "sinkIdentifiers": {
                                    "tradeFileIdentifiers": [
                                        {"id": "RHILL", "label": "id"},
                                        {
                                            "id": "lh nr uk management llp",
                                            "label": "id",
                                        },
                                        {"id": "roryhill", "label": "id"},
                                        {"id": "hillr", "label": "id"},
                                    ]
                                },
                                "uniqueIds": [
                                    "none",
                                    "id:rhill",
                                    "id:lh nr uk management llp",
                                    "id:roryhill",
                                    "id:hillr",
                                ],
                            },
                            "seller": [
                                {
                                    "&id": "34ce47bc-00d1-4242-8efa-b483c4bc246d",
                                    "&key": "MarketCounterparty:34ce47bc-00d1-4242-8efa-b483c4bc246d:*************",
                                    "details": {
                                        "firmStatus": "ACTIVE",
                                        "firmType": "Counterparty",
                                        "leiRegistrationStatus": "ISSUED",
                                        "retailOrProfessional": "N/A",
                                    },
                                    "firmIdentifiers": {"lei": "K6Q0W1PS1L1O4IQL9C32"},
                                    "firmLocation": {
                                        "registeredAddress": {
                                            "address": "25 BANK STREET",
                                            "city": "LONDON",
                                            "country": "GB",
                                            "postalCode": "E14 5JP",
                                        },
                                        "tradingAddress": {
                                            "address": "25 Bank Street",
                                            "city": "London",
                                            "country": "GB",
                                            "postalCode": "E14 5JP",
                                        },
                                    },
                                    "name": "JP MORGAN SECURITIES PLC",
                                    "sinkIdentifiers": {
                                        "tradeFileIdentifiers": [
                                            {"id": "JPD1", "label": "id"},
                                            {"id": "JPMA", "label": "id"},
                                            {"id": "JPME", "label": "id"},
                                            {"id": "JPMS", "label": "id"},
                                            {"id": "JPMS2", "label": "id"},
                                            {"id": "JPMX", "label": "id"},
                                            {"id": "JPMS-SWAP-ALGO", "label": "id"},
                                            {"id": "jpm pb", "label": "id"},
                                            {"id": "jpmpb", "label": "id"},
                                            {"id": "FUT-SSO-EURO-CARE", "label": "id"},
                                            {"id": "FUT-AMST-SSO-CARE", "label": "id"},
                                            {"id": "fut-opt-euro-care", "label": "id"},
                                            {"id": "JPME", "label": "id"},
                                            {"id": "fut-par-sso-care", "label": "id"},
                                            {"id": "jpms:euro", "label": "id"},
                                            {"id": "JPHE", "label": "id"},
                                            {"id": "JPHU", "label": "id"},
                                            {"id": "fut-opt-euro-algo", "label": "id"},
                                            {"id": "fut-opt-lifice-dma", "label": "id"},
                                            {"id": "fut-opt-euro-dma", "label": "id"},
                                            {"id": "fut-amst-sso-dma", "label": "id"},
                                            {"id": "fut-sso-euro-dma", "label": "id"},
                                            {
                                                "id": "fut-opt-lifice-care",
                                                "label": "id",
                                            },
                                            {"id": "fut-par-sso-dma", "label": "id"},
                                            {"id": "fut-opt-hk-dma", "label": "id"},
                                            {"id": "fut-meff-sso-dma", "label": "id"},
                                            {"id": "fut-meff-sso-dma", "label": "id"},
                                            {"id": "jple", "label": "id"},
                                            {"id": "jpm-mleg-dma", "label": "id"},
                                            {"id": "jpms:euro-cash", "label": "id"},
                                            {"id": "jpfe", "label": "id"},
                                            {"id": "jpm-opt-algo", "label": "id"},
                                            {"id": "xpaf-care", "label": "id"},
                                            {"id": "jphe", "label": "account"},
                                            {"id": "fut-meff-sso-care", "label": "id"},
                                            {"id": "jp:eu:swap:algo", "label": "id"},
                                        ]
                                    },
                                    "uniqueIds": [
                                        "id:fut-sso-euro-care",
                                        "id:jpme",
                                        "id:jpmx",
                                        "id:jp:eu:swap:algo",
                                        "id:jpm-mleg-dma",
                                        "id:xpaf-care",
                                        "id:fut-opt-euro-dma",
                                        "id:jple",
                                        "id:fut-opt-euro-algo",
                                        "id:jpd1",
                                        "id:jpmpb",
                                        "id:jpma",
                                        "id:fut-opt-euro-care",
                                        "id:jpm pb",
                                        "id:fut-amst-sso-dma",
                                        "id:fut-meff-sso-dma",
                                        "id:jpms-swap-algo",
                                        "id:jpms:euro-cash",
                                        "id:fut-par-sso-care",
                                        "id:fut-opt-hk-dma",
                                        "id:fut-sso-euro-dma",
                                        "id:jphe",
                                        "id:jpm-opt-algo",
                                        "account:jphe",
                                        "id:fut-meff-sso-care",
                                        "id:jpms2",
                                        "id:jphu",
                                        "id:fut-opt-lifice-care",
                                        "id:jpms",
                                        "id:fut-opt-lifice-dma",
                                        "id:jpms:euro",
                                        "id:fut-amst-sso-care",
                                        "id:fut-par-sso-dma",
                                        "id:jpfe",
                                    ],
                                }
                            ],
                        },
                        "&hash": "ef6d126b9e99a92fde8cfc4bafa6e27f069c0ec64d0b8c362a520ca0b80c3a5f",
                        "&validationErrors": None,
                        "&timestamp": *************,
                        "&user": "system",
                    },
                    "sort": [*************, "10159114:2021-11-24:NEWT"],
                },
            ],
        },
    }


@pytest.fixture
def es_results_df() -> pd.DataFrame:
    return pd.read_pickle(ES_RESULTS_DF)


@pytest.fixture
def eligibility_assessor_result_df() -> pd.DataFrame:
    return pd.read_pickle(ELIGIBILITY_ASSESSOR_DF)


@pytest.fixture
def final_df() -> pd.DataFrame:
    return pd.read_pickle(FINAL_DF)


@pytest.fixture
def final_update_df() -> pd.DataFrame:
    return pd.read_pickle(FINAL_UPDATE_DF)
