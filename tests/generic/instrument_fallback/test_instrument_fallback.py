from se_trades_tasks.order_and_tr.instrument.fallback.instrument_fallback import (
    InstrumentFallback,
)
from se_trades_tasks.order_and_tr.instrument.fallback.instrument_fallback import (
    run_instrument_fallback,
)


class TestInstrumentFallback(object):
    """
    Test cases for "TestInstrumentFallback" class
    """

    def test_instrument_fallback_import(self):
        assert InstrumentFallback
        assert run_instrument_fallback
