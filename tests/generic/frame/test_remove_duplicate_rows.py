import pandas as pd
import pytest

from swarm_tasks.generic.frame import remove_duplicate_rows
from swarm_tasks.generic.frame.remove_duplicate_rows import Params


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """
    creates empty dataframe
    """
    df = pd.DataFrame()
    return df


@pytest.fixture()
def all_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "STATUS": ["NEWO", "FILL", pd.NA, "NEWO"],
            "ORDER_ID": ["123", "456", "123", "123"],
            "transactionDetails.tradingCapacity": ["AOTC", "DEAL", "MTCH", pd.NA],
            "transactionDetails.buySellIndicator": ["BUYI", "SELL", "BUYI", "SELL"],
            "transactionDetails.positionEffect": ["Close", "Default", "Default", pd.NA],
            "transactionDetails.quantityNotation": ["MONE", "UNIT", "MONE", pd.NA],
        }
    )
    return df


@pytest.fixture()
def missing_some_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "STATUS": ["NEWO", "FILL", pd.NA, "NEWO"],
            "transactionDetails.tradingCapacity": ["AOTC", "DEAL", "MTCH", pd.NA],
            "transactionDetails.buySellIndicator": ["BUYI", "SELL", "BUYI", "SELL"],
            "transactionDetails.positionEffect": ["Close", "Default", "Default", pd.NA],
            "transactionDetails.quantityNotation": ["MONE", "UNIT", "MONE", pd.NA],
        }
    )
    return df


class TestRemoveDuplicateRows(object):
    """
    Test cases for "TestRemoveDuplicateRows" class
    """

    def test_empty_input_df(self, empty_source_df):
        params = Params(subset=["ORDER_ID"])
        task = remove_duplicate_rows.RemoveDuplicateRows(
            name="test-remove-dup-rows", params=params
        )
        result = task.execute(empty_source_df, params=params)
        assert result.empty

    def test_all_col_in_source_df(self, all_col_in_source_df: pd.DataFrame):
        params = Params(subset=["ORDER_ID"], query="`STATUS`=='NEWO'")
        task = remove_duplicate_rows.RemoveDuplicateRows(
            name="test-remove-dup-rows", params=params
        )
        result = task.execute(all_col_in_source_df, params=params)
        expected_result = pd.DataFrame(
            {
                "STATUS": ["NEWO", "FILL", pd.NA],
                "ORDER_ID": ["123", "456", "123"],
                "transactionDetails.tradingCapacity": ["AOTC", "DEAL", "MTCH"],
                "transactionDetails.buySellIndicator": ["BUYI", "SELL", "BUYI"],
                "transactionDetails.positionEffect": ["Close", "Default", "Default"],
                "transactionDetails.quantityNotation": ["MONE", "UNIT", "MONE"],
            }
        )
        assert result.equals(expected_result)

    def test_missing_some_col_in_source_df(
        self, missing_some_col_in_source_df: pd.DataFrame
    ):
        params = Params(subset=["ORDER_ID"], query="`STATUS`=='NEWO'")
        task = remove_duplicate_rows.RemoveDuplicateRows(
            name="test-remove-dup-rows", params=params
        )
        result = task.execute(missing_some_col_in_source_df, params=params)
        assert result.equals(missing_some_col_in_source_df)
