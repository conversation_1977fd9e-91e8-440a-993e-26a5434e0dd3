import pandas as pd
import prefect
import pytest

from swarm_tasks.generic.frame.evaluate_frame_columns import EvaluateFrameColumns
from swarm_tasks.generic.frame.evaluate_frame_columns import Params


@pytest.fixture
def source_frame():
    n = 6
    dicts = [str({"foo": "bar"}) for _ in range(n)]
    lists = [str([1, 2, 3]) for _ in range(n)]
    mixed = [str({"a": "b"}) for _ in range(n // 2)] + [
        str([1, 2]) for _ in range(n // 2)
    ]
    mixed_with_invalid_types = mixed[:-3] + [123, None, "1"]
    numeric = list(range(n))

    data = {
        "dicts": dicts,
        "lists": lists,
        "mixed": mixed,
        "numeric": numeric,
        "mixed_with_invalid_types": mixed_with_invalid_types,
    }
    df = pd.DataFrame(data)
    return df


class TestEvaluateFrameColumn:
    def test_task_with_valid_supported_dtyped_input_columns(self, source_frame):
        params = Params(source_attributes=["dicts", "lists", "mixed"])
        task = EvaluateFrameColumns(name="EvaluateFrameColumns", params=params)
        result = task.execute(source_frame, params=params)

        assert result.dicts.apply(type).isin([dict]).all()
        assert result.lists.apply(type).isin([list]).all()
        assert result.mixed.apply(type).isin([list, dict]).all()

    def test_task_with_valid_and_invalid_input_types_ignores_invalid_input_values(
        self, source_frame
    ):
        column = "mixed_with_invalid_types"
        valid_data_mask = source_frame[column].astype(str).str.contains(r"^[\{\[]")

        params = Params(source_attributes=[column])
        task = EvaluateFrameColumns(name="EvaluateFrameColumns", params=params)
        result = task.execute(source_frame, params=params)

        assert result.loc[valid_data_mask, column].apply(type).isin([list, dict]).all()
        assert result.loc[~valid_data_mask, column].isna().all()

    def test_task_with_invalid_supported_dtyped_input_columns_raises_exception(
        self, source_frame
    ):
        invalid_dict = "{'foo: 1"
        source_frame["dicts"] = invalid_dict

        params = Params(source_attributes=["dicts", "lists", "mixed"])
        task = EvaluateFrameColumns(name="EvaluateFrameColumns", params=params)

        with pytest.raises(prefect.engine.signals.FAIL):
            task.execute(source_frame, params=params)

    def test_task_with_unsupported_dtype_input_returns_null(self, source_frame):
        params = Params(source_attributes=["numeric"])
        task = EvaluateFrameColumns(name="EvaluateFrameColumns", params=params)
        result = task.execute(source_frame, params=params)
        assert result["numeric"].isna().all()

    def test_task_with_column_missing_from_source_frame_returns_column__with_null(
        self, source_frame
    ):
        params = Params(source_attributes=["missing_from_source_frame"])
        task = EvaluateFrameColumns(name="EvaluateFrameColumns", params=params)
        result = task.execute(source_frame, params=params)
        assert result["missing_from_source_frame"].isna().all()
