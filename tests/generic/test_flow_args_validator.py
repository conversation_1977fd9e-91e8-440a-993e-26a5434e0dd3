import json
from json import JSONDecodeError

import pytest
from pydantic import ValidationError

from swarm_tasks.generic.flow_args_validator import FlowArgsValidator


class TestFlowArgsValidator:
    """
    Test cases for the Generic FlowArgsValidator class
    """

    def test_existing_class(self):
        params = FlowArgsValidator.params_class(
            flow_args_model="swarm_tasks.generic.flow_args_validator.AbstractFlowArgs"
        )
        task = FlowArgsValidator(name=self.__class__.__name__, params=params)
        task.execute(params=params, flow_args="{}")

    def test_json_validation_error(self):
        """
        Test invalid json string
        """
        params = FlowArgsValidator.params_class(
            flow_args_model="swarm_tasks.generic.flow_args_validator.AbstractFlowArgs"
        )
        task = FlowArgsValidator(name=self.__class__.__name__, params=params)

        with pytest.raises(JSONDecodeError):
            task.execute(params=params, flow_args="INVALID JSON STRING")

    def test_pydantic_validation_error(self):
        """
        Test pydantic validation error
        """
        params = FlowArgsValidator.params_class(
            flow_args_model="swarm_tasks.generic.flow_args_validator.AbstractFlowArgs"
        )
        task = FlowArgsValidator(name=self.__class__.__name__, params=params)

        with pytest.raises(ValidationError):
            task.execute(params=params, flow_args=json.dumps({"asd": 123}))
