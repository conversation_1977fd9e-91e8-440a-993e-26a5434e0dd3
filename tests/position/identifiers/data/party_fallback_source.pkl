���7      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�additionalInformation��
amount.native��amount.nativeCurrency��date��amount.amountInChf��amount.amountInEur��amount.amountInGbp��amount.amountInJpy��amount.amountInUsd��dataSourceName��	direction��level��marketIdentifiers.instrument��marketIdentifiers.parties��marketIdentifiers��__meta_model__��parties.type��quantity��quantityNotation��sourceIndex��	sourceKey��	__party__��__ric__��__isin__��
parties.value�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK2��h�i8�����R�(K�<�NNNJ����J����K t�b�B�                                                                  	       
                     
                                                                                                                                             !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       �t�bh?�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK2��h!�]�(�pandas._libs.missing��NA���hbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbhbet�b�_dtype�hU�StringDtype���)��ubhW)��}�(hZhhK ��h��R�(KK2��h!�]�(�USD�hohohohohohohohohohohohohohohohohohohohohohohohohohohohohohohohohohohohohohohohohohohohohohohohohoet�bhdhf)��ubhW)��}�(hZhhK ��h��R�(KK2��h!�]�(�112147��99286��88816��55756��55198��57058��67701��72641��120115��93047��115927��124375��99519��121728��87191��54509��77529��164579��115089��103619��238185��176017��202695��125111��81566��130964��203567��205185��163845��202207��287231��228589��307016��298267��300953��247797��223607��253805��478540��592860��819��6256��1094��1559��1623��9910��24084��17555��34936��5410�et�bhdhf)��ubhhK ��h��R�(KKK2��h�f8�����R�(KhMNNNJ����J����K t�b�B`	      ��oA   �IllA   ` >iA   @b�_A   @c}_A   ��`A    ��cA    ��dA   �/qA   ���jA   ��pA   ��qA   `�kA   p;rA    �MjA    E`A   `[4gA   �dxA   ��pA   �}6nA   ��dA   �� ^A    ��`A   @OUA    ��KA   @��UA   ��bA   ���aA   @�[A   ��gaA   @��hA   `~6dA   ��omA   ��#nA    �mA   `m3iA   �{JgA    ��jA   ��dxA    ��{A    ��@     ��@     ��@    ���@    ���@    pc�@     �
A    @�A    ̎A     ��@9��PkmA8�v��HjA�
�zcgA+�Y+�{]A�b F�L]A�5���]A�_8�)bApN&Z`$cA0Ҏ�pA�)U�hAEwm�H7oA���gtpAO{�L�iApcI���pAW���&ehA���^�`^Aq���9�eA���q}vA�:�w��nA�G�"�kA���aM�bA�A���[Ad??�Qj_A��'v�SA{�e�m�IA�8�׮HTA���`�`A 9��m`AF�q�ZA�α*`A��H�[!gA� �2�bA:5:H~&kAU��Iu�kA�i��kA#7���gA�3a5ʥeA�ײ��|hA�5s�KvAd֡��yA��F���@����|�@}j��6��@|�z��{�@5u~���@
�5����@;��HiA����A�=��=2A.N�	���@�g���mA/�20�jA�K�l�gA
�u��]A~�!�]A��N�DK^Ax�5RbA�a�_�pcA��E�LpA�r�geiA�1i�oAa��{�pAeL���+jA�g+/�pAl�-K�yhA $�95^AZ䷬5ueA��,Ϙ~vA?W���3oA�����kAND���bA����S\A�"�_A�}0n�SAh����FJA�՝�ϐTA�l����`A`�j�`A����^lZA�g��l`A��Z�@�gAUUU�@cA�[)蛅kA��X��lAp�
k�kAԏ�*egA������eA��x�~hA�r|?�vAK�a,q�yA��mi�j�@������@2����>�@�������@Jx���@�}s���@��t�/�A�&r�N;AM�VD�qAy�A,�@�l��!jA"��^lgA�)���dA-���MZA�*:�.ZA{i�"�ZA#�.�!9`A�W@��=aA�~�F��lA[O�^-^fA�x11lA[Tߚ��mA[�)�
gAW��=�mA 
UJ�eA�^b��ZA/\�]�
cA���4��sA�C{��OkAS/�SC|hA8��?��`A��|�XAlAf�d#\AYP��{QAlQ%�5GA�QRA�O�u�^AL�~T�]A:�8��CWA��Q'}�\A���PL�dA��b} aA$c���;hA�X����hA�^/_КhAa�� ��dA��%�# cAq�B�eAi`�ҲsAL>.ҰvAF������@�N�e�b�@#�৐��@A;Ii�@*e$�Е�@���&/�@���
��A����Aܳ�,
 A�'���+�@R�Ε�d�A,�7iJ�A���( �A�	��q�A�7j	ng�AO�����A�!�[�A�Ysɹ\�AY3�83��A�^j���AӍ���w�A�mz6j�Av�2���Aή���Ae�2�E�A>�.s���At>$N�A��ɉ���AM�[k�	�A���WW�A�� j��A�~)��A6@�$�}�A.>�����A�����A<���I��A���]e��A�h�P�U�Aɻ&#y�AU�.���A;�;�!��A  �&@�A`�qd�A"L9�/*�A_��4��A������A�H��\d�Af�x�t��Aw_����A�14��A�a���0Ap�x�_A �k��56A��L�?A�!�u�@A����QwiAr	͇�~AGR�;vA����AN�'��H[A    ��oA   �IllA   ` >iA   @b�_A   @c}_A   ��`A    ��cA    ��dA   �/qA   ���jA   ��pA   ��qA   `�kA   p;rA    �MjA    E`A   `[4gA   �dxA   ��pA   �}6nA   ��dA   �� ^A    ��`A   @OUA    ��KA   @��UA   ��bA   ���aA   @�[A   ��gaA   @��hA   `~6dA   ��omA   ��#nA    �mA   `m3iA   �{JgA    ��jA   ��dxA    ��{A    ��@     ��@     ��@    ���@    ���@    pc�@     �
A    @�A    ̎A     ��@�t�bhhK ��h��R�(KKK2��hL�B�                                                                  	       
                     
                                                                                                                                             !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       �t�bhhK ��h��R�(KKK2��h!�]�(�
2022-12-20��
2022-12-21��
2022-12-22��
2022-12-23��
2022-12-27��
2022-12-28��
2022-12-29��
2022-12-30��
2023-01-03��
2023-01-04��
2023-01-05��
2023-01-06��
2023-01-09��
2023-01-10��
2023-01-11��
2023-01-12��
2023-01-13��
2023-01-17��
2023-01-18��
2023-01-19��
2022-12-20��
2022-12-21��
2022-12-22��
2022-12-23��
2022-12-27��
2022-12-28��
2022-12-29��
2022-12-30��
2023-01-03��
2023-01-04��
2023-01-05��
2023-01-06��
2023-01-09��
2023-01-10��
2023-01-11��
2023-01-12��
2023-01-13��
2023-01-17��
2023-01-18��
2023-01-19��
2022-12-20��
2022-12-21��
2022-12-22��
2022-12-23��
2022-12-27��
2022-12-28��
2022-12-29��
2022-12-30��
2023-01-03��
2023-01-04��Universal Position Blotter�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�LONG��SHORT�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��Trader�h��Client�h�h�h�h�h�h�h��Fund�h�h�h�h��Risk Entity�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��Account�h�h��	Portfolio�h�h�h�]�}�(�labelId��US00846U1016��path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�ua]�}�(h�j   j  j  j  j	  ua]�}�(h�j   j  j  j  j	  ua]�}�(h�j   j  j  j  j	  ua]�}�(h�j   j  j  j  j	  ua]�}�(h�j   j  j  j  j	  ua]�}�(h�j   j  j  j  j	  ua]�}�(h�j   j  j  j  j	  ua]�}�(h�j   j  j  j  j	  ua]�}�(h�j   j  j  j  j	  ua]�}�(h�j   j  j  j  j	  ua]�}�(h�j   j  j  j  j	  ua]�}�(h�j   j  j  j  j	  ua]�}�(h�j   j  j  j  j	  ua]�}�(h�j   j  j  j  j	  ua]�}�(h�j   j  j  j  j	  ua]�}�(h�j   j  j  j  j	  ua]�}�(h�j   j  j  j  j	  ua]�}�(h�j   j  j  j  j	  ua]�}�(h�j   j  j  j  j	  ua]�}�(h��US0138721065�j  j  j  j	  ua]�}�(h�j2  j  j  j  j	  ua]�}�(h�j2  j  j  j  j	  ua]�}�(h�j2  j  j  j  j	  ua]�}�(h�j2  j  j  j  j	  ua]�}�(h�j2  j  j  j  j	  ua]�}�(h�j2  j  j  j  j	  ua]�}�(h�j2  j  j  j  j	  ua]�}�(h�j2  j  j  j  j	  ua]�}�(h�j2  j  j  j  j	  ua]�}�(h�j2  j  j  j  j	  ua]�}�(h�j2  j  j  j  j	  ua]�}�(h�j2  j  j  j  j	  ua]�}�(h�j2  j  j  j  j	  ua]�}�(h�j2  j  j  j  j	  ua]�}�(h�j2  j  j  j  j	  ua]�}�(h�j2  j  j  j  j	  ua]�}�(h�j2  j  j  j  j	  ua]�}�(h�j2  j  j  j  j	  ua]�}�(h�j2  j  j  j  j	  ua]�}�(h��KYG330321061�j  j  j  j	  ua]�}�(h�j[  j  j  j  j	  ua]�}�(h�j[  j  j  j  j	  ua]�}�(h�j[  j  j  j  j	  ua]�}�(h�j[  j  j  j  j	  ua]�}�(h�j[  j  j  j  j	  ua]�}�(h�j[  j  j  j  j	  ua]�}�(h�j[  j  j  j  j	  ua]�}�(h�j[  j  j  j  j	  ua]�}�(h�j[  j  j  j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��id:new_fund_id�j  h=j  j	  ua]�}�(h��id:new_fund_id�j  h=j  j	  ua]�}�(h��id:new_fund_id�j  h=j  j	  ua]�}�(h��id:new_fund_id�j  h=j  j	  ua]�}�(h��id:new_fund_id�j  h=j  j	  ua]�}�(h��id:new_fund_id�j  h=j  j	  ua]�}�(h��id:new_fund_id�j  h=j  j	  ua]�}�(h��id:new_fund_id�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader2�j  h=j  j	  ua]�}�(h��
id:trader1�j  h=j  j	  ua]�}�(h��
id:trader1�j  h=j  j	  ua]�}�(h��
id:trader1�j  h=j  j	  ua]�}�(h��
id:trader1�j  h=j  j	  ua]�}�(h��
id:trader1�j  h=j  j	  ua]�}�(h��
id:trader1�j  h=j  j	  ua]�}�(h��
id:trader1�j  h=j  j	  ua]�}�(h��
id:trader1�j  h=j  j	  ua]�}�(h��
id:trader1�j  h=j  j	  ua]�}�(h��
id:trader1�j  h=j  j	  ua]�(h�jo  e]�(j  jr  e]�(j
  ju  e]�(j  jx  e]�(j  j{  e]�(j  j~  e]�(j  j�  e]�(j  j�  e]�(j  j�  e]�(j  j�  e]�(j  j�  e]�(j  j�  e]�(j!  j�  e]�(j#  j�  e]�(j%  j�  e]�(j'  j�  e]�(j)  j�  e]�(j+  j�  e]�(j-  j�  e]�(j/  j�  e]�(j1  j�  e]�(j4  j�  e]�(j6  j�  e]�(j8  j�  e]�(j:  j�  e]�(j<  j�  e]�(j>  j�  e]�(j@  j�  e]�(jB  j�  e]�(jD  j�  e]�(jF  j�  e]�(jH  j�  e]�(jJ  j�  e]�(jL  j�  e]�(jN  j�  e]�(jP  j�  e]�(jR  j�  e]�(jT  j�  e]�(jV  j�  e]�(jX  j�  e]�(jZ  j�  e]�(j]  j�  e]�(j_  j�  e]�(ja  j�  e]�(jc  j�  e]�(je  j�  e]�(jg  j�  e]�(ji  j�  e]�(jk  j�  e]�(jm  j  e�Position�j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��se_schema.static.mifid2��QuantityNotation����UNIT���R�j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  j<  �H/Users/<USER>/Documents/Work/local_data/positions/storey_test_data.csv�j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  j=  �trader2��trader2��new_fund_id��new_fund_id��new_fund_id��new_fund_id��new_fund_id��new_fund_id��new_fund_id��new_fund_id��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��A.N�jp  jp  jp  jp  jp  jp  jp  jp  jp  jp  jp  jp  jp  jp  jp  jp  jp  jp  jp  �AA.N�jq  jq  jq  jq  jq  jq  jq  jq  jq  jq  jq  jq  jq  jq  jq  jq  jq  jq  jq  �AAC.N�jr  jr  jr  jr  jr  jr  jr  jr  jr  j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j2  j[  j[  j[  j[  j[  j[  j[  j[  j[  j[  }�(�&id��$155c266d-afac-4b36-9409-b4329da44f3a��retailOrProfessional��PROFESSIONAL��&key��?MarketPerson:155c266d-afac-4b36-9409-b4329da44f3a:1674715395055��	uniqueIds�]��
id:trader2�a�name��SHRENIK Parakh��personalDetails.firstName��SHRENIK��personalDetails.lastName��Parakh��$sinkIdentifiers.tradeFileIdentifiers�]�}�(�id��trader2��label��id�uaujs  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      js  js  js  js  js  js  js  js  js  js  js  js  js  js  js  js  js  js  js  js  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bh?Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bh?Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bh?Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h&h)h*h+h,h-et�bh?Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bh?Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h(h.h/h0h1h2h3h4h5h7h9h:h;h<h=et�bh?Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hX�mgr_locs��builtins��slice���K KK��R�u}�(j�  hhj�  j�  KKK��R�u}�(j�  hrj�  j�  KKK��R�u}�(j�  h�j�  hhK ��h��R�(KK��hL�C0                                          �t�bu}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  hhK ��h��R�(KK��hL�Cx       	       
                     
                                                                      �t�bueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.