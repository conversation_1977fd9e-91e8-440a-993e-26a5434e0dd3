from pathlib import Path
from typing import List

import addict
import pandas as pd
import pytest
from se_core_tasks.core.core_dataclasses import ExtractPathResult

from swarm_tasks.io.write.elastic_bulk_writer import ElasticBulkWriter
from swarm_tasks.io.write.elastic_bulk_writer import Params
from swarm_tasks.io.write.elastic_bulk_writer import Resources

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
QUARANTINE_FILES_DIR = TEST_FILES_DIR.joinpath("quarantine")
TEST_FILE_PATH = TEST_FILES_DIR.joinpath(r"compare_cache_test.pkl")
EXPECTED_FILE_PATH = TEST_FILES_DIR.joinpath(r"compare_cache_expected.pkl")
EMPTY_EXPECTED_PATH = TEST_FILES_DIR.joinpath(r"compare_cache_empty_expected.pkl")


def expected_scroll_responses_and_results() -> List[tuple]:
    """
    Expected es.scroll responses and _compare_hash() results
    :return: responses and results for pytest.mark.parametrize
    """
    default_df = pd.read_pickle(EXPECTED_FILE_PATH)
    empty_df = pd.read_pickle(EMPTY_EXPECTED_PATH)

    return [
        ("default_steeleye_co", default_df),
        ("empty_steeleye_co", empty_df),
    ]


@pytest.fixture()
def compare_hash_df() -> pd.DataFrame:
    return pd.read_pickle(TEST_FILE_PATH)


@pytest.fixture()
def content():
    content_str = (
        '{"create": {"_id": "************|213016:SELL:50666:77271167:REME:1250.0","_index": '
        '"shrenik-order-alias"}} {"&hash": '
        '"d31bdf9fd90ef2de6a9db6af21d2feeb855179b596095c284323bee13a7615f9",'
        '"&id": "************|213016:SELL:50666:77271167:REME:1250.0","&key": '
        '"Order:************|213016:SELL:50666:77271167:REME:1250.0:1654085676649","&model": '
        '"Order","&parent": "************|213016:SELL:NEWO","&timestamp": 1654085676649,'
        '"&user": "system","&version": 1,"buySell": "SELL","id": "************|213016","orderIdentifiers": '
        '{"internalOrderIdCode": "16745221","orderIdCode": "************|213016",}} '
    )
    return content_str


@pytest.fixture()
def elastic_bulk_response():
    res = {
        "took": "79",
        "errors": True,
        "items": [
            {
                "create": {
                    "_index": "shrenik_uat_steeleye_co_order",
                    "_id": "************|213016:SELL:50666:77271167:REME:1250.0",
                    "status": 409,
                    "error": {
                        "type": "version_conflict_engine_exception",
                        "reason": "[Order][************|213016:SELL:50666:77271167:REME:1250.0]: version conflict, document already exists (current version [1])",
                        "index_uuid": "bTfmMXOqTsGL057z6EGlNg",
                        "shard": "0",
                        "index": "shrenik_uat_steeleye_co_order",
                    },
                }
            }
        ],
    }
    return res


@pytest.fixture()
def record_indices():
    index = {
        "file_index": 0,
        "raw_index": 0,
        "model": "Order",
        "hash": "d31bdf9fd90ef2de6a9db6af21d2feeb855179b596095c284323bee13a7615f9",
    }
    return [index]


@pytest.fixture()
def expected_flush_content_with_empty_cache_df():
    data = [
        {
            "file_index": 0,
            "raw_index": 0,
            "model": "Order",
            "hash": "d31bdf9fd90ef2de6a9db6af21d2feeb855179b596095c284323bee13a7615f9",
            "id": "************|213016:SELL:50666:77271167:REME:1250.0",
            "es_index": "shrenik_uat_steeleye_co_order",
            "error_type": "version_conflict_engine_exception",
            "error_reason": "[Order][************|213016:SELL:50666:77271167:REME:1250.0]: version conflict, "
            "document already exists (current version [1])",
            "error_caused_by_type": None,
            "error_caused_by_reason": None,
            "status": "version_conflict",
        }
    ]
    return pd.DataFrame(data)


class MockES:
    meta = addict.Dict(
        {"hash": "&hash", "id": "&id", "version": "&version", "expiry": "&expiry"}
    )

    @staticmethod
    def scroll(query, index, as_dataframe):
        if index == "default_steeleye_co":
            return pd.DataFrame(
                {
                    "&id": {
                        0: "3437457:1:3437457432191419IA07713437457F1134542320220216BUYI:PARF:2022-02-16T20:46:47Z"
                    },
                    "&version": {0: 1},
                    "&hash": {
                        0: "c67e28fa868382c8eb09a18d486fa0076259d0492ca0b81097d634ecf5abedd3"
                    },
                }
            )
        elif index == "empty_steeleye_co":
            return pd.DataFrame({"error": ["error"]})


@pytest.fixture()
def quarantine_record_status_frame() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "file_index": 0,
                "raw_index": 0,
                "model": "QuarantinedOrder",
                "hash": "TEST_HASH_ONE",
                "id": "TEST_ID_ONE",
                "es_index": "test_quarantined_order",
                "error_type": None,
                "error_reason": None,
                "error_caused_by_type": None,
                "error_caused_by_reason": None,
                "status": "created",
            },
            {
                "file_index": 0,
                "raw_index": 1,
                "model": "QuarantinedOrder",
                "hash": "TEST_HASH_TWO",
                "id": "TEST_ID_TWO",
                "es_index": "test_quarantined_order",
                "error_type": "version_conflict_engine_exception",
                "error_reason": "[QuarantinedOrder][TEST_ID_TWO]: version conflict, document already exists (current version [1])",
                "error_caused_by_type": None,
                "error_caused_by_reason": None,
                "status": "duplicate",
            },
        ]
    )


class TestElasticBulkWriter:
    @pytest.mark.parametrize(
        "es_type, expected_result", expected_scroll_responses_and_results()
    )
    def test_compare_cache(
        self, compare_hash_df: pd.DataFrame, es_type: str, expected_result: pd.DataFrame
    ):
        params = Params(payload_size=10000, quarantined=True)
        task = ElasticBulkWriter(name="ElasticBulkWriter", params=params)
        compare_hash_result = task._compare_hash(
            es=MockES(), status_frame=compare_hash_df, alias=es_type
        )
        compare_hash_expected = expected_result

        assert not pd.testing.assert_frame_equal(
            compare_hash_result, compare_hash_expected
        )

    def test_flush_content(
        self,
        mocker,
        content,
        record_indices,
        elastic_bulk_response,
        expected_flush_content_with_empty_cache_df,
    ):
        es_obj = mocker.patch(
            "swarm.client.utilities.get_repository_by_cluster_version",
        )

        params = Params(payload_size=10000, quarantined=True)
        task = ElasticBulkWriter(name="ElasticBulkWriter", params=params)

        es_obj.scroll.return_value = pd.DataFrame()
        es_obj.client.bulk.return_value = elastic_bulk_response
        es_obj.meta.id = "&id"
        es_obj.meta.hash = "&hash"
        flush_content = task._flush_content(
            es=es_obj,
            params=params,
            content=content,
            record_indices=record_indices,
            actions=1,
            action_type="create",
        )
        assert flush_content.equals(expected_flush_content_with_empty_cache_df)

    def test_should_skip_sink_record_audit_end_to_end(
        self, quarantine_record_status_frame: pd.DataFrame
    ) -> None:
        # Case 1 - Quarantine created successfully. Shouldn't SKIP
        (
            skip_sink_audit_one,
            reason_one,
        ) = ElasticBulkWriter.should_skip_sink_record_audit(
            quarantine_status_frame=quarantine_record_status_frame,
            sink_raw_index=0,
        )
        assert skip_sink_audit_one is False and not reason_one

        # Case 2 - Duplicate Quarantine - Should be skipped.
        (
            skip_sink_audit_two,
            reason_two,
        ) = ElasticBulkWriter.should_skip_sink_record_audit(
            quarantine_status_frame=quarantine_record_status_frame,
            sink_raw_index=1,
        )
        assert skip_sink_audit_two is True
        assert (
            reason_two is not None
            and reason_two
            == "[QuarantinedOrder][TEST_ID_TWO]: version conflict, document already exists (current version [1])"
        )

    def test_elastic_bulk_writer_end_to_end(
        self, mocker, quarantine_record_status_frame: pd.DataFrame
    ):
        params = Params(payload_size=1000000, quarantined=True)
        task = ElasticBulkWriter(
            name="Test", params=params, resources=Resources(es_client_key="test")
        )

        mocker.patch.object(ElasticBulkWriter, "clients", return_value={})
        mocker.patch.object(
            ElasticBulkWriter,
            "_flush_content",
            return_value=quarantine_record_status_frame.tail(1),
        )

        result = task.execute(
            result=ExtractPathResult(path=QUARANTINE_FILES_DIR),
            params=params,
            resources=Resources(es_client_key="test"),
        )

        assert result.frame.equals(quarantine_record_status_frame.tail(1))
        assert result.quarantined is True
