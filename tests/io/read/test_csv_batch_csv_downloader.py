from pathlib import Path
from typing import NoReturn

import boto3
import pandas as pd
import pytest
from moto import mock_aws

from swarm_tasks.io.read import csv_batch_csv_downloader
from swarm_tasks.io.read.csv_batch_csv_downloader import Params


CURRENT_PATH = Path(__file__).parent
LOCAL_BUCKET_PATH = CURRENT_PATH.joinpath("data/csv_batch_csv_downloader/bucket")


@pytest.fixture()
def source_frame() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "s3_audio_file_url": [
                "s3://test.steeleye.co/flows/comms-truphone-controller/sample_file.zip",
                "s3://test.steeleye.co/flows/comms-truphone-controller/sample_file.zip",
            ],
            "s3_csv_file_url": [
                "s3://test.steeleye.co/flows/comms-truphone-controller/sample.csv",
                "s3://test.steeleye.co/flows/comms-truphone-controller/missing-file.csv",
            ],
        }
    )


class TestCSVBatchCSVDownloader:
    @mock_aws
    def test_end_to_end_csv_batch_csv_downloader(
        self,
        source_frame: pd.DataFrame,
    ):
        create_and_add_objects_to_s3_bucket(bucket_name="test.steeleye.co")

        params = Params(
            metadata_column="s3_csv_file_url", dataframe_columns=["additional_column"]
        )

        task = csv_batch_csv_downloader.CSVBatchCSVDownloader(
            "csv-batch-csv-downloader", params=params
        )

        result: pd.DataFrame = task.execute(source_frame=source_frame, params=params)

        pd.testing.assert_frame_equal(
            left=result,
            right=pd.DataFrame(
                data={
                    "City": ["Braga", "Porto", "Lisbon"],
                    "Sport": ["Cycling", "Football", "Tenis"],
                    "s3_csv_file_url": [
                        "s3://test.steeleye.co/flows/comms-truphone-controller/sample.csv",
                        "s3://test.steeleye.co/flows/comms-truphone-controller/sample.csv",
                        "s3://test.steeleye.co/flows/comms-truphone-controller/sample.csv",
                    ],
                    "additional_column": [pd.NA] * 3,
                }
            ),
        )


def create_and_add_objects_to_s3_bucket(bucket_name: str) -> NoReturn:
    """
    Recreate the s3 bucket, and copy all the files from `LOCAL_BUCKET_PATH` to s3 mocked bucket.

    :param bucket_name: Bucket name of Mock S3 bucket
    :return: None
    """

    # Create bucket
    s3 = boto3.client("s3", region_name="us-east-1")
    s3.create_bucket(Bucket=bucket_name)

    for file_ in LOCAL_BUCKET_PATH.rglob("*"):
        if file_.is_file():
            _path = file_.as_posix().replace(f"{LOCAL_BUCKET_PATH}/", "")

            with open(file_, "rb") as f:
                s3.put_object(
                    Bucket=bucket_name,
                    Key=_path,
                    # noqa E501
                    Body=f.read(),
                )
