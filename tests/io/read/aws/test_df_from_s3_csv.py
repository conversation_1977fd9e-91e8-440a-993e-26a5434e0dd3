from abc import ABC
from pathlib import Path

import pandas as pd
import pytest
from moto import mock_aws
from se_core_tasks.abstractions.abstract_mock_s3 import AbstractMockS3
from swarm.conf import SettingsCls

from swarm_tasks.io.read.aws.df_from_s3_csv import DfFromS3Csv
from swarm_tasks.io.read.aws.df_from_s3_csv import Params

TEST_FILE_PATH = Path(__file__).parent


@pytest.fixture
def expected_df_from_s3_csv():
    df = pd.DataFrame(
        {"col_1": ["data_11", "data_21"], "col_2": ["data_12", "data_22"]}
    )
    return df


@mock_aws
class TestDfFromS3Csv:
    task_was_initialized = False

    def test_end_to_end_df_from_s3_csv(self, mocker, expected_df_from_s3_csv):
        self.init_task(mocker=mocker)
        params = Params(
            s3_bucket="test.dev.steeleye.co",
            s3_key="prefix/path_to_csv/sample_file.csv",
        )
        task = DfFromS3Csv(name="DfFromS3Csv", params=params)
        result = task.execute(params=params)
        assert result.equals(expected_df_from_s3_csv)

    def init_task(self, mocker, no_files=False):
        fake_bucket_name = "test.dev.steeleye.co"

        if not self.task_was_initialized:
            mock_s3_instance = DfFromS3CsvMockS3()
            mock_s3_instance.create_mock_bucket(bucket=fake_bucket_name)
            if not no_files:
                mock_s3_instance.load_data_into_mock_s3()

        self.task_was_initialized = True
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = fake_bucket_name


class DfFromS3CsvMockS3(AbstractMockS3, ABC):
    def load_data_into_mock_s3(self):

        file_name = "sample_file.csv"
        with open(
            Path.joinpath(TEST_FILE_PATH, "data", file_name).as_posix(), "r"
        ) as sample_file:
            body = sample_file.read()

        self.s3.put_object(
            Bucket=self.bucket,
            Key=f"prefix/path_to_csv/{file_name}",
            Body=body,
        )
