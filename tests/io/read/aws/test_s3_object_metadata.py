import pytest
from prefect.engine import signals

from swarm_tasks.io.read.aws.s3_object_metadata import Params
from swarm_tasks.io.read.aws.s3_object_metadata import S3ObjectMetadata


@pytest.fixture
def s3_head_object_response():
    return {
        "ResponseMetadata": {
            "RequestId": "16CCC0AB0101984F",
            "HostId": "HJTtY7XcQlYn4oXFCeWcsHhoQJEgzsT0/fOqofC4Z4KWZbIf5KIH+MtsCTU/JkDuv8FHyvE42Ro=",
            "HTTPStatusCode": 200,
            "HTTPHeaders": {
                "x-amz-id-2": "HJTtY7XcQlYn4oXFCeWcsHhoQJEgzsT0/fOqofC4Z4KWZbIf5KIH+MtsCTU/JkDuv8FHyvE42Ro=",
                "x-amz-request-id": "16CCC0AB0101984F",
                "date": "Wed, 19 Aug 2020 09:34:01 GMT",
                "last-modified": "Mon, 17 Aug 2020 10:27:44 GMT",
                "etag": '"05f5063f763917191ec31f812273b4b9"',
                "x-amz-version-id": "4piM2r9d_TbWxGC2et2BtiOa.BDsKPSB",
                "accept-ranges": "bytes",
                "content-type": "audio/mpeg",
                "content-length": "166464",
                "server": "AmazonS3",
            },
            "RetryAttempts": 0,
        },
        "AcceptRanges": "bytes",
        "ContentLength": 166464,
        "ETag": '"05f5063f763917191ec31f812273b4b9"',
        "VersionId": "4piM2r9d_TbWxGC2et2BtiOa.BDsKPSB",
        "ContentType": "audio/mpeg",
        "Metadata": {},
    }


class TestS3ObjectMetadata:
    def test_s3_object_metadata_local_file_url_raises_value_error(self):
        file_url = "/tmp/foo/bar.txt"
        task = S3ObjectMetadata(name="S3ObjectMetadata")
        with pytest.raises(ValueError):
            task.execute(file_url=file_url, params=Params())

    def test_s3_object_metadata_returns_expected_values(
        self, mocker, s3_head_object_response
    ):
        def dummy(self, bucket, key, skip):
            return s3_head_object_response

        file_url = "s3://bucketname/foo/bar.txt"
        mocker.patch(
            "swarm_tasks.io.read.aws.s3_object_metadata.S3ObjectMetadata._get_metadata",
            dummy,
        )
        task = S3ObjectMetadata(name="S3ObjectMetadata")

        expected_keys = s3_head_object_response.keys()

        response = task.execute(file_url=file_url, params=Params())
        actual_keys = response.keys()
        assert expected_keys == actual_keys

    def test_s3_object_metadata_raise_skip(self, mocker, s3_head_object_response):
        file_url = "s3://bucketname/foo/bar.txt"
        task = S3ObjectMetadata(name="S3ObjectMetadata")
        with pytest.raises(signals.SKIP):
            task.execute(file_url=file_url, params=Params(skip_on_s3_exception=True))
