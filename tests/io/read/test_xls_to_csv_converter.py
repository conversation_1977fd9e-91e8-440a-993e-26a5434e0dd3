from pathlib import Path

import pandas as pd
import pytest
from prefect.engine import signals
from se_core_tasks.core.core_dataclasses import ExtractPathResult

from swarm_tasks.io.read.xls_to_csv_converter import Params
from swarm_tasks.io.read.xls_to_csv_converter import XlsToCsvConverter

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data/xls")

EXAMPLE_FILE_PATH = ExtractPathResult(path=TEST_FILES_DIR.joinpath(r"example.xls"))

NON_EXISTENT_FILE_PATH = ExtractPathResult(
    path=TEST_FILES_DIR.joinpath(r"non-existent-file.xls")
)

WRONG_FORMAT_FILE_PATH = ExtractPathResult(
    path=TEST_FILES_DIR.joinpath(r"example.json")
)


class TestXlsToCsvConverter:
    def test_non_existent_file_raises_file_not_found_exception(self):
        params = Params()
        task = XlsToCsvConverter(name="XlsToCsvConverter", params=params)

        with pytest.raises(signals.SKIP):
            task.execute(params=params, extractor_result=NON_EXISTENT_FILE_PATH)

    def test_wrong_format_raises_value_error(self):
        params = Params()
        task = XlsToCsvConverter(name="XlsToCsvConverter", params=params)
        with pytest.raises(signals.SKIP):
            task.execute(params=params, extractor_result=WRONG_FORMAT_FILE_PATH)

    def test_example_file_is_converted_to_csv(self):
        params = Params(
            target_date_format="%Y-%m-%dT%H:%M:%S.%fZ", target_float_format="%.5f"
        )
        task = XlsToCsvConverter(name="XlsToCsvConverter", params=params)
        result = task.execute(params=params, extractor_result=EXAMPLE_FILE_PATH)

        original_path = str(EXAMPLE_FILE_PATH.path)
        original_xls_df = pd.read_excel(original_path)
        csv_result_df = pd.read_csv(str(result.path))
        csv_result_df["Instructed On"] = (
            csv_result_df["Instructed On"].str.replace("T", " ").str.rstrip("Z")
        )
        csv_result_df = csv_result_df.round(5)
        original_xls_df["Instructed On"] = original_xls_df["Instructed On"].apply(
            lambda x: str(x)
        )
        assert original_xls_df.equals(csv_result_df)

    def test_both_interpret_as_str_and_source_convert_columns_raises_value_error(
        self,
    ):

        with pytest.raises(ValueError) as e:
            Params(
                interpret_as_str=True,
                source_convert_columns={"ISIN": "string"},
                source_date_columns=["Instructed On"],
            )
        assert (
            e.value.errors()[0]["msg"]
            == "Please use either interpret_as_str or source_convert_columns, not both!"
        )
