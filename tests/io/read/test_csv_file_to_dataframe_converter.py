from pathlib import Path

import pandas as pd
from swarm.task.transform.result import TransformResult

from swarm_tasks.io.read.csv_file_to_dataframe_converter import (
    CsvFileToDataframeConverter,
)
from swarm_tasks.io.read.csv_file_to_dataframe_converter import Params

BASE_PATH: Path = Path(__file__).parent
DATA_PATH: Path = BASE_PATH.joinpath("data")


class TestCsvFileToDataframeConverter:
    def test_import(self):
        assert CsvFileToDataframeConverter

    def test_it_returns_transform_result(self):
        task = CsvFileToDataframeConverter(name="CsvFileToDataframeConverter")

        result = task.execute(
            path=DATA_PATH.joinpath("csv/batch-csv-stream.csv"),
            params=Params(),
        )

        assert isinstance(result, TransformResult)

    def test_it_can_return_dataframe(self):
        task = CsvFileToDataframeConverter(name="CsvFileToDataframeConverter")

        result = task.execute(
            path=DATA_PATH.joinpath("csv/batch-csv-stream.csv"),
            params=Params(return_transform_result=False),
        )

        assert isinstance(result, pd.DataFrame)
