���#      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�Allocations��
Quantity_x��
AssetClass��	AssetType��Country��CreateDateTime��DestinationCode��DestinationDisplayName��DestinationLegalName��DestinationLongName��
Executions��ID��LastModifiedDateTime��Limit��
Quantity_y��SettlementCurrency��Status��__source_index__��ExecutionPrice��
LimitPrice�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��Float64Index���}�(hhhK ��h��R�(KK��h�f8�����R�(K�<�NNNJ����J����K t�b�C0              �?       @      @      @      ��t�bh:Nu��R�e]�(hhK ��h��R�(KKK��h!�]�(]�(}�(�AllocationBookType��	Custodian��AllocationIdentifier��:Order:23783|Route:22880|Portfolio:Account A|Custodian:MSCO��BaseAccountCurrency��USD��	Custodian��MSCO��ExecBrokerCode��CITI��ExecBrokerDisplayName��CITI��ExecBrokerLegalName��	CitiGroup��ExecBrokerLongName��	CitiGroup��ExecutedQuantity�J�� �GrossAmount�K �	NetAmount�J j �	Portfolio��1��
PortfolioName��	Account A��Price�K �Quantity�J�� �TotalCommissions�J j �	TotalFees�K u}�(�AllocationBookType��Strategy��AllocationIdentifier��>Order:23783|Route:22880|Portfolio:Account A|Strategy:NONE-NONE��BaseAccountCurrency��USD��ExecBrokerCode��CITI��ExecBrokerDisplayName��CITI��ExecBrokerLegalName��	CitiGroup��ExecBrokerLongName��	CitiGroup��ExecutedQuantity�J�� �GrossAmount�K �	NetAmount�J j �	Portfolio�hj�
PortfolioName��	Account A��Price�K �Quantity�J�� �TotalCommissions�J j �	TotalFees�K ue]�(}�(�AllocationBookType��	Custodian��AllocationIdentifier��:Order:23784|Route:22881|Portfolio:Account A|Custodian:MSCO��BaseAccountCurrency��USD��	Custodian��MSCO��ExecBrokerCode��CITI��ExecBrokerDisplayName��CITI��ExecBrokerLegalName��	CitiGroup��ExecBrokerLongName��	CitiGroup��ExecutedQuantity�J�$ �GrossAmount�JP�� �	NetAmount�J�� �	Portfolio�hj�
PortfolioName��	Account A��Price�G@e&fffff�Quantity�J?< �TotalCommissions�M`�	TotalFees�K u}�(�AllocationBookType��Strategy��AllocationIdentifier��>Order:23784|Route:22881|Portfolio:Account A|Strategy:NONE-NONE��BaseAccountCurrency��USD��ExecBrokerCode��CITI��ExecBrokerDisplayName��CITI��ExecBrokerLegalName��	CitiGroup��ExecBrokerLongName��	CitiGroup��ExecutedQuantity�J�$ �GrossAmount�JP�� �	NetAmount�J�� �	Portfolio�hj�
PortfolioName��	Account A��Price�G@e&fffff�Quantity�J?< �TotalCommissions�M`�	TotalFees�K ue]�(}�(�AllocationBookType��	Custodian��AllocationIdentifier��9Order:23785|Route:NONE|Portfolio:Account A|Custodian:MSCO��BaseAccountCurrency��USD��	Custodian��MSCO��ExecutedQuantity�K �GrossAmount�K �	NetAmount�K �	Portfolio�hj�
PortfolioName��	Account A��Price�K �Quantity�JlA �TotalCommissions�K �	TotalFees�K u}�(�AllocationBookType��Strategy��AllocationIdentifier��=Order:23785|Route:NONE|Portfolio:Account A|Strategy:NONE-NONE��BaseAccountCurrency��USD��ExecutedQuantity�K �GrossAmount�K �	NetAmount�K �	Portfolio�hj�
PortfolioName��	Account A��Price�K �Quantity�JlA �TotalCommissions�K �	TotalFees�K ue]�(}�(�AllocationBookType��	Custodian��AllocationIdentifier��:Order:24084|Route:23479|Portfolio:Account A|Custodian:MSCO��BaseAccountCurrency��USD��	Custodian��MSCO��ExecBrokerCode��CITI��ExecBrokerDisplayName��CITI��ExecBrokerLegalName��	CitiGroup��ExecBrokerLongName��	CitiGroup��ExecutedQuantity�J�� �GrossAmount�GA�������	NetAmount�GA�i3333�	Portfolio�hj�
PortfolioName��	Account A��Price�G@�������Quantity�J�� �TotalCommissions�G@�������	TotalFees�K u}�(�AllocationBookType��Strategy��AllocationIdentifier��>Order:24084|Route:23479|Portfolio:Account A|Strategy:NONE-NONE��BaseAccountCurrency��USD��ExecBrokerCode��CITI��ExecBrokerDisplayName��CITI��ExecBrokerLegalName��	CitiGroup��ExecBrokerLongName��	CitiGroup��ExecutedQuantity�J�� �GrossAmount�GA�������	NetAmount�GA�i3333�	Portfolio�hj�
PortfolioName��	Account A��Price�G@�������Quantity�J�� �TotalCommissions�G@�������	TotalFees�K ue]�(}�(�AllocationBookType��	Custodian��AllocationIdentifier��:Order:24086|Route:24382|Portfolio:Account A|Custodian:MSCO��BaseAccountCurrency��USD��	Custodian��MSCO��ExecBrokerCode��CITI��ExecBrokerDisplayName��CITI��ExecBrokerLegalName��	CitiGroup��ExecBrokerLongName��	CitiGroup��ExecutedQuantity�J�� �GrossAmount�J�k� �	NetAmount�J�k� �	Portfolio�hj�
PortfolioName��	Account A��Price�G@V0     �Quantity�J�� �TotalCommissions�K �	TotalFees�K u}�(�AllocationBookType��Strategy��AllocationIdentifier��>Order:24086|Route:24382|Portfolio:Account A|Strategy:NONE-NONE��BaseAccountCurrency��USD��ExecBrokerCode��CITI��ExecBrokerDisplayName��CITI��ExecBrokerLegalName��	CitiGroup��ExecBrokerLongName��	CitiGroup��ExecutedQuantity�J�� �GrossAmount�J�k� �	NetAmount�J�k� �	Portfolio�hj�
PortfolioName��	Account A��Price�G@V0     �Quantity�J�� �TotalCommissions�K �	TotalFees�K ue]�(}�(�AllocationBookType��	Custodian��AllocationIdentifier��9Order:24384|Route:NONE|Portfolio:Account A|Custodian:MSCO��BaseAccountCurrency��USD��	Custodian��MSCO��ExecutedQuantity�K �GrossAmount�K �	NetAmount�K �	Portfolio�hj�
PortfolioName��	Account A��Price�K �Quantity�J�� �TotalCommissions�K �	TotalFees�K u}�(�AllocationBookType��Strategy��AllocationIdentifier��=Order:24384|Route:NONE|Portfolio:Account A|Strategy:NONE-NONE��BaseAccountCurrency��USD��ExecutedQuantity�K �GrossAmount�K �	NetAmount�K �	Portfolio�hj�
PortfolioName��	Account A��Price�K �Quantity�J�� �TotalCommissions�K �	TotalFees�K ueet�bhhK ��h��R�(KKK��h�i8�����R�(KhHNNNJ����J����K t�b�C0��     ?<     lA     ��     ��     ��     �t�bhhK ��h��R�(KKK��h!�]�(�Equity��Equity��Equity��Equity��Fixed Income��Equity��
Equity Basket��
Equity Basket��Common Stock��
Equity Basket��	Corp Bond��
Equity Basket��JPN��JPN��GBR��ZAF��DEU��ZAF�et�bhhK ��h��R�(KKK��h!�]�(�2023-07-18T18:38:48.473Z��2023-07-18T18:54:41.760Z�G�      �2023-07-18T18:20:23.970Z��2023-07-18T18:57:14.170Z�G�      �CITI��CITI�G�      �CITI��CITI�G�      �CITI��CITI�G�      �CITI��CITI�G�      �	CitiGroup��	CitiGroup�G�      �	CitiGroup��	CitiGroup�G�      �	CitiGroup��	CitiGroup�G�      �	CitiGroup��	CitiGroup�G�      ]�}�(�CounterpartyCode��CITI��CounterpartyDisplayName��CITI��CounterpartyLegalName��	CitiGroup��CounterpartyLongName��	CitiGroup��ExecDateTime��2023-07-18T18:38:47.591Z��ExecutionID��211907��ExecutionPrice�K �Quantity�J�� ua]�}�(�CounterpartyCode��CITI��CounterpartyDisplayName��CITI��CounterpartyLegalName��	CitiGroup��CounterpartyLongName��	CitiGroup��ExecDateTime��2023-07-18T18:54:55.876Z��ExecutionID��212809��ExecutionPrice�G@e&fffff�Quantity�J�$ uaG�      ]�}�(�CounterpartyCode��CITI��CounterpartyDisplayName��CITI��CounterpartyLegalName��	CitiGroup��CounterpartyLongName��	CitiGroup��ExecDateTime��2023-07-18T18:20:26.600Z��ExecutionID��212507��ExecutionPrice�G@�������LocaltoSettlementFXRate�G?�z�G�{�Quantity�J�� ua]�}�(�CounterpartyCode��CITI��CounterpartyDisplayName��CITI��CounterpartyLegalName��	CitiGroup��CounterpartyLongName��	CitiGroup��ExecDateTime��2023-07-18T18:57:30.599Z��ExecutionID��212508��ExecutionPrice�G@V0     �Quantity�J�� uaG�      �22880��22881�G�      �23479��24382�G�      �2023-07-19T16:16:12.589Z��2023-07-19T16:16:28.982Z�G�      �2023-07-18T18:22:39.236Z��2023-07-19T16:24:20.443Z�G�      et�bhhK ��h��R�(KKK��h!�]�(��G�      ��G�      et�bhhK ��h��R�(KKK��hG�C0    ��>A    ?<7A      �    ���@     j�@      ��t�bhhK ��h��R�(KKK��h!�]�(�JPY��JPY�G�      �ZAR��EUR�G�      �Manual��Manual�G�      �Manual��Manual�G�      et�bhhK ��h��R�(KKK��hG�C0      �fffff&e@      �������@     0V@      ��t�bhhK ��h��R�(KKK��hG�C0      �      �      �������@      �      ��t�bhhK ��h��R�(KKK��jz  �C0                                           �t�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h'h(h)et�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h*h+h,h-h.h/h0h1et�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h4h5et�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bh:Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bh:Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hQ�mgr_locs��builtins��slice���K KK��R�u}�(j�  jv  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KK
K��R�u}�(j�  j�  j�  j�  K
KK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j!  j�  j�  KKK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.