from pathlib import Path

import pandas as pd
import pytest
from prefect.engine import signals
from se_core_tasks.core.core_dataclasses import ExtractPathResult

from swarm_tasks.io.read.emsx.emsx_pre_processor import EmsxPreProcessor

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data/csv")

EXAMPLE_ORDER_FILE_PATH = ExtractPathResult(
    path=TEST_FILES_DIR.joinpath(r"example_emsx_order.csv")
)
EXAMPLE_FILL_FILE_PATH = ExtractPathResult(
    path=TEST_FILES_DIR.joinpath(r"example_emsx_fill.csv")
)

EXPECTED_ORDER_FILE_PATH = ExtractPathResult(
    path=TEST_FILES_DIR.joinpath(r"emsx_order.csv")
)
EXPECTED_FILL_FILE_PATH = ExtractPathResult(
    path=TEST_FILES_DIR.joinpath(r"emsx_fill.csv")
)

NON_EXISTENT_FILE_PATH = ExtractPathResult(
    path=TEST_FILES_DIR.joinpath(r"non-existent-file.xls")
)

WRONG_FORMAT_FILE_PATH = ExtractPathResult(
    path=TEST_FILES_DIR.joinpath(r"example.json")
)


@pytest.fixture()
def input_dataframe() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "TYPE": ["ORDER", "ROUTE"],
            "Order Number": [9082491, 9082491],
            "Status": ["Filled", pd.NA],
            "Route Status": [pd.NA, "Filled"],
        }
    )
    return df


@pytest.fixture()
def expected_dataframe() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "TYPE": ["ORDER", "ROUTE"],
            "Order Number": [9082491, 9082491],
            "Status": ["Filled", pd.NA],
            "Route Status": [pd.NA, "Filled"],
            "IS CANCEL": [False, False],
        }
    )
    return df


class TestEmsxPreProcessor:
    def test_non_existent_file_raises_file_not_found_exception(self):
        task = EmsxPreProcessor(name="EmsxPreProcessor")

        with pytest.raises(signals.SKIP):
            task.execute(extractor_result=NON_EXISTENT_FILE_PATH)

    def test_wrong_format_raises_value_error(self):
        task = EmsxPreProcessor(name="EmsxPreProcessor")
        with pytest.raises(signals.SKIP):
            task.execute(extractor_result=WRONG_FORMAT_FILE_PATH)

    def test_example_order_converted_to_csv(self):
        task = EmsxPreProcessor(name="XlsToCsvConverter")
        result = task.execute(extractor_result=EXAMPLE_ORDER_FILE_PATH)

        result_df = pd.read_csv(str(result.path))
        expected_df = pd.read_csv(str(EXPECTED_ORDER_FILE_PATH.path))

        assert result_df.equals(expected_df)

    def test_example_fill_converted_to_csv(self):
        task = EmsxPreProcessor(name="XlsToCsvConverter")
        result = task.execute(extractor_result=EXAMPLE_FILL_FILE_PATH)

        result_df = pd.read_csv(str(result.path))
        expected_df = pd.read_csv(str(EXPECTED_FILL_FILE_PATH.path))

        assert result_df.equals(expected_df)

    def test_example_order_file_with_no_cancel_records(
        self, input_dataframe, expected_dataframe
    ):
        task = EmsxPreProcessor(name="EmsxPreProcessor")
        result = task.create_cancel_duplicates(dataframe=input_dataframe)
        assert result.equals(expected_dataframe)
