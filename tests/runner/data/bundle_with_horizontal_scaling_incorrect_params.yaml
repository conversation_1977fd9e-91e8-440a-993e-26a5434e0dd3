id: flow-with-horizontal-scaling
name: A kickass horizontally scaling flow
image: swarm_tasks:4.0.3
infra:
- name: tenant-data
  type: ELASTICSEARCH
- name: reference-data
  type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
- name: file_url
  envVar: SWARM_FILE_URL
  description: url of the file to process
controlFlows:
- name: S3OrLocalFile
  conditionTaskName: ParametersFlowController
  trueTaskName: S3DownloadFile
  falseTaskName: LocalFile
  merge: true
- name: FileTooBig # Depending on chunksize, if the input file is greater than X rows, it will be split into different files of X rows and uploaded to S3
  conditionTaskName: HorizontalBatchController
  trueTaskName: Task1
  falseTaskName: MergeAndChunkCsvFiles
  mapped: false
  merge: false
tasks:
- path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
  name: ParametersFlowController
  params:
    arg_name: file_url
    starts_with: s3://
    condition: AND
    ignore_case: true
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
  name: S3DownloadFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.local_file:LocalFile
  name: LocalFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm.task.base:BaseTask
  name: Task1
  upstreamTasks:
    - taskName: S3OrLocalFile
      mapped: false
      key: extractor_result
- path: swarm_tasks.io.read.horizontal_batch_controller:HorizontalBatchController
  name: HorizontalBatchController
  params:
    max_chunk_size: 5
  upstreamTasks:
    - taskName: CsvFileSplitter
      mapped: false
      key: list_of_batches
- path: swarm_tasks.io.read.merge_and_chunk_csv_files:MergeAndChunkCsvFiles
  name: MergeAndChunkCsvFiles
  params:
    max_chunk_size: 10
  upstreamTasks:
    - taskName: Task1
      mapped: false
      key: file_splitter_result_list
- path: swarm_tasks.transform.extract_path.s3_file_list_from_frame_splitter_result_list:S3FileListFileSplitterResultList
  name: S3FileListFileSplitterResultList
  params:
    s3_key_prefix: "flows/order-universal-steeleye-trade-blotter/batches"
    datetime_field_in_file_path: false
  upstreamTasks:
    - taskName: MergeAndChunkCsvFiles
      mapped: false
      key: file_splitter_result_list
    - taskName: file_url
      mapped: false
      key: file_url
