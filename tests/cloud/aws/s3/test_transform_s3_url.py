from pathlib import Path

import pytest

from swarm_tasks.cloud.aws.s3.transform_s3_url import Params
from swarm_tasks.cloud.aws.s3.transform_s3_url import TransformS3Url


@pytest.fixture
def dummy_file_url():
    return "s3://pinafore.steeleye.co/path/to/filename.ext"


class TestTransformS3Url:
    def test_task_with_no_params_raises_value_error(self, dummy_file_url):
        with pytest.raises(ValueError):
            TransformS3Url(name="AttachmentPath")

    def test_task(self, dummy_file_url):
        key_prefix = "dummy/prefix"
        extension = ".mp4"
        expected = f"s3://pinafore.steeleye.co/{key_prefix}/{Path(dummy_file_url).with_suffix(extension).name}"

        params = Params(file_extension=extension, s3_key_prefix=key_prefix)
        task = TransformS3Url(name="AttachmentPath", params=params)
        result = task.execute(file_url=dummy_file_url, params=params)

        actual = result[0]
        assert expected == actual

    @pytest.mark.parametrize("ext", ["txt", ".txt", "mp4", ".mp4", "json", ".json"])
    def test_task_file_extension_formatted_as_pathlib_suffix(self, dummy_file_url, ext):
        params = Params(file_extension=ext, s3_key_prefix="dummy/prefix")
        task = TransformS3Url(name="AttachmentPath", params=params)
        result = task.execute(file_url=dummy_file_url, params=params)

        expected = f".{ext}".replace("..", ".")
        actual = Path(result[0]).suffix

        assert expected == actual
