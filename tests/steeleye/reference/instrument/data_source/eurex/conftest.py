from pathlib import Path

import pandas as pd
import pytest
import xmltodict
from se_core_tasks.io.read.xml_file_splitter import XMLFileSplitter

from swarm_tasks.steeleye.reference.instrument.data_source.eurex.static import (
    EurexXMLColumns,
)

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data/sample_eurex_xml.xml")


@pytest.fixture
def empty_source_df() -> pd.DataFrame:
    return pd.DataFrame()


@pytest.fixture()
def all_col_in_source_df() -> pd.DataFrame:
    with open(
        TEST_FILES_DIR,
        encoding="utf-8",
    ) as f:
        raw_data = f.read()
    xml = xmltodict.parse(raw_data)
    data_path = "FIXML.Batch.SecDef"
    xml = XMLFileSplitter()._nested_read(xml, *data_path.split("."))
    df = pd.DataFrame(xml)
    return df.astype(str)


@pytest.fixture()
def missing_some_col_in_source_df(all_col_in_source_df: pd.DataFrame) -> pd.DataFrame:
    df = all_col_in_source_df.drop(columns=[EurexXMLColumns.INSTRMT])
    return df


@pytest.fixture()
def pre_processed_all_col_in_source_df() -> dict:
    data = {
        "@ID": {
            0: "5578478",
            1: "5894212",
            2: "6183273",
            3: "72059101571448894",
            4: "72059101571448895",
            5: "72059101571448896",
            6: "6197796",
            7: "6034220",
            8: "6034221",
            9: "6208188",
            10: "6208189",
        },
        "@SecTyp": {
            0: "FUT",
            1: "FUT",
            2: "FUT",
            3: "MLEG",
            4: "MLEG",
            5: "MLEG",
            6: "OPT",
            7: "OPT",
            8: "OPT",
            9: "OPT",
            10: "OPT",
        },
        "@Desc": {
            0: "CONF SI 20210608 PS",
            1: "CONF SI 20210908 PS",
            2: "CONF SI 20211208 PS",
            3: "CONF.S.JUN21.SEP21",
            4: "CONF.S.JUN21.DEC21",
            5: "CONF.S.SEP21.DEC21",
            6: "TTF SI 20221216 PS EU P 84.00 0",
            7: "OQDE SI 20220218 PS AM C 137.00 0",
            8: "OQDE SI 20220218 PS AM P 137.00 0",
            9: "OQDE SI 20220318 PS AM C 115.00 0",
            10: "OQDE SI 20220318 PS AM P 115.00 0",
        },
        "@CFI": {
            0: "FFDPSX",
            1: "FFDPSX",
            2: "FFDPSX",
            3: "KMXXXX",
            4: "KMXXXX",
            5: "KMXXXX",
            6: "OPESPS",
            7: "OCAIPS",
            8: "OPAIPS",
            9: "OCAIPS",
            10: "OPAIPS",
        },
        "@CntrctDt": {
            0: "2021-06-08",
            1: "2021-09-08",
            2: "2021-12-08",
            3: pd.NA,
            4: pd.NA,
            5: pd.NA,
            6: "2022-12-16",
            7: "2022-02-18",
            8: "2022-02-18",
            9: "2022-03-18",
            10: "2022-03-18",
        },
        "@Mult": {
            0: 1.0,
            1: 1.0,
            2: 1.0,
            3: pd.NA,
            4: pd.NA,
            5: pd.NA,
            6: 100.0,
            7: 100.0,
            8: 100.0,
            9: 100.0,
            10: 100.0,
        },
        "@SettlMeth": {
            0: "P",
            1: "P",
            2: "P",
            3: pd.NA,
            4: pd.NA,
            5: pd.NA,
            6: "P",
            7: "P",
            8: "P",
            9: "P",
            10: "P",
        },
        "AID": {
            0: [
                {"@AltID": "17236010", "@AltIDSrc": "M"},
                {"@AltID": "DE000C5RQD52", "@AltIDSrc": "4"},
            ],
            1: [
                {"@AltID": "17236011", "@AltIDSrc": "M"},
                {"@AltID": "DE000C52GU94", "@AltIDSrc": "4"},
            ],
            2: [
                {"@AltID": "17236012", "@AltIDSrc": "M"},
                {"@AltID": "DE000C58EEB1", "@AltIDSrc": "4"},
            ],
            3: pd.NA,
            4: pd.NA,
            5: pd.NA,
            6: [
                {"@AltID": "123082927", "@AltIDSrc": "M"},
                {"@AltID": "DE000C58Q2M1", "@AltIDSrc": "4"},
            ],
            7: [
                {"@AltID": "281875378", "@AltIDSrc": "M"},
                {"@AltID": "DE000C55BF20", "@AltIDSrc": "4"},
            ],
            8: [
                {"@AltID": "281875379", "@AltIDSrc": "M"},
                {"@AltID": "DE000C55BF38", "@AltIDSrc": "4"},
            ],
            9: [
                {"@AltID": "281875604", "@AltIDSrc": "M"},
                {"@AltID": "DE000C58WDC3", "@AltIDSrc": "4"},
            ],
            10: [
                {"@AltID": "281875605", "@AltIDSrc": "M"},
                {"@AltID": "DE000C58WDD1", "@AltIDSrc": "4"},
            ],
        },
        "@StrkPx": {
            0: pd.NA,
            1: pd.NA,
            2: pd.NA,
            3: pd.NA,
            4: pd.NA,
            5: pd.NA,
            6: 84.0,
            7: 137.0,
            8: 137.0,
            9: 115.0,
            10: 115.0,
        },
        "@PutCall": {
            0: pd.NA,
            1: pd.NA,
            2: pd.NA,
            3: pd.NA,
            4: pd.NA,
            5: pd.NA,
            6: "0",
            7: "1",
            8: "0",
            9: "1",
            10: "0",
        },
        "@ExerStyle": {
            0: pd.NA,
            1: pd.NA,
            2: pd.NA,
            3: pd.NA,
            4: pd.NA,
            5: pd.NA,
            6: "0",
            7: "1",
            8: "1",
            9: "1",
            10: "1",
        },
        "InstrAttrib": {
            0: [
                {"@Typ": "123", "@Val": "N"},
                {"@Typ": "106", "@Val": "XEUR"},
                {"@Typ": "112", "@Val": "N"},
                {"@Typ": "129", "@Val": "3"},
                {"@Typ": "113", "@Val": "N"},
                {"@Typ": "115", "@Val": "N"},
            ],
            1: [
                {"@Typ": "123", "@Val": "N"},
                {"@Typ": "106", "@Val": "XEUR"},
                {"@Typ": "112", "@Val": "N"},
                {"@Typ": "129", "@Val": "3"},
                {"@Typ": "113", "@Val": "N"},
                {"@Typ": "115", "@Val": "N"},
            ],
            2: [
                {"@Typ": "123", "@Val": "N"},
                {"@Typ": "106", "@Val": "XEUR"},
                {"@Typ": "112", "@Val": "N"},
                {"@Typ": "129", "@Val": "3"},
                {"@Typ": "113", "@Val": "N"},
                {"@Typ": "115", "@Val": "N"},
            ],
            3: [
                {"@Typ": "106", "@Val": "XEUR"},
                {"@Typ": "112", "@Val": "N"},
                {"@Typ": "129", "@Val": "3"},
                {"@Typ": "113", "@Val": "N"},
                {"@Typ": "123", "@Val": "N"},
            ],
            4: [
                {"@Typ": "106", "@Val": "XEUR"},
                {"@Typ": "112", "@Val": "N"},
                {"@Typ": "129", "@Val": "3"},
                {"@Typ": "113", "@Val": "N"},
                {"@Typ": "123", "@Val": "N"},
            ],
            5: [
                {"@Typ": "106", "@Val": "XEUR"},
                {"@Typ": "112", "@Val": "N"},
                {"@Typ": "129", "@Val": "3"},
                {"@Typ": "113", "@Val": "N"},
                {"@Typ": "123", "@Val": "N"},
            ],
            6: [
                {"@Typ": "124", "@Val": "3000"},
                {"@Typ": "123", "@Val": "Y"},
                {"@Typ": "106", "@Val": "XEUR"},
                {"@Typ": "112", "@Val": "N"},
                {"@Typ": "129", "@Val": "3"},
                {"@Typ": "113", "@Val": "Y"},
                {"@Typ": "115", "@Val": "Y"},
            ],
            7: [
                {"@Typ": "123", "@Val": "N"},
                {"@Typ": "106", "@Val": "XEUR"},
                {"@Typ": "112", "@Val": "N"},
                {"@Typ": "113", "@Val": "Y"},
                {"@Typ": "115", "@Val": "Y"},
            ],
            8: [
                {"@Typ": "123", "@Val": "N"},
                {"@Typ": "106", "@Val": "XEUR"},
                {"@Typ": "112", "@Val": "N"},
                {"@Typ": "113", "@Val": "Y"},
                {"@Typ": "115", "@Val": "Y"},
            ],
            9: [
                {"@Typ": "123", "@Val": "N"},
                {"@Typ": "106", "@Val": "XEUR"},
                {"@Typ": "112", "@Val": "N"},
                {"@Typ": "113", "@Val": "Y"},
                {"@Typ": "115", "@Val": "Y"},
            ],
            10: [
                {"@Typ": "123", "@Val": "N"},
                {"@Typ": "106", "@Val": "XEUR"},
                {"@Typ": "112", "@Val": "N"},
                {"@Typ": "113", "@Val": "Y"},
                {"@Typ": "115", "@Val": "Y"},
            ],
        },
        "@MktSegID": {
            0: "351",
            1: "351",
            2: "351",
            3: "351",
            4: "351",
            5: "351",
            6: "1754",
            7: "61545",
            8: "61545",
            9: "61545",
            10: "61545",
        },
        "instrumentIdCode": {
            0: "DE000C5RQD52",
            1: "DE000C52GU94",
            2: "DE000C58EEB1",
            3: pd.NA,
            4: pd.NA,
            5: pd.NA,
            6: "DE000C58Q2M1",
            7: "DE000C55BF20",
            8: "DE000C55BF38",
            9: "DE000C58WDC3",
            10: "DE000C58WDD1",
        },
        "instrumentClassification": {
            0: "FFDPSX",
            1: "FFDPSX",
            2: "FFDPSX",
            3: "KMXXXX",
            4: "KMXXXX",
            5: "KMXXXX",
            6: "OPESPS",
            7: "OCAIPS",
            8: "OPAIPS",
            9: "OCAIPS",
            10: "OPAIPS",
        },
        "venue.tradingVenue": {
            0: "XEUR",
            1: "XEUR",
            2: "XEUR",
            3: "XEUR",
            4: "XEUR",
            5: "XEUR",
            6: "XEUR",
            7: "XEUR",
            8: "XEUR",
            9: "XEUR",
            10: "XEUR",
        },
        "ext.bestExAssetClassMain": {
            0: "Credit Derivatives",
            1: "Credit Derivatives",
            2: "Credit Derivatives",
            3: "Other Instruments",
            4: "Other Instruments",
            5: "Other Instruments",
            6: "Equity Derivatives",
            7: "Equity Derivatives",
            8: "Equity Derivatives",
            9: "Equity Derivatives",
            10: "Equity Derivatives",
        },
        "ext.bestExAssetClassSub": {
            0: "Futures and options admitted to trading on a trading venue",
            1: "Futures and options admitted to trading on a trading venue",
            2: "Futures and options admitted to trading on a trading venue",
            3: pd.NA,
            4: pd.NA,
            5: pd.NA,
            6: "Futures and options admitted to trading on a trading venue",
            7: "Futures and options admitted to trading on a trading venue",
            8: "Futures and options admitted to trading on a trading venue",
            9: "Futures and options admitted to trading on a trading venue",
            10: "Futures and options admitted to trading on a trading venue",
        },
        "@AltID": {
            0: pd.NA,
            1: pd.NA,
            2: pd.NA,
            3: pd.NA,
            4: pd.NA,
            5: pd.NA,
            6: pd.NA,
            7: pd.NA,
            8: pd.NA,
            9: pd.NA,
            10: pd.NA,
        },
        "@AltIDSrc": {
            0: pd.NA,
            1: pd.NA,
            2: pd.NA,
            3: pd.NA,
            4: pd.NA,
            5: pd.NA,
            6: pd.NA,
            7: pd.NA,
            8: pd.NA,
            9: pd.NA,
            10: pd.NA,
        },
        "Instrmt": {
            0: pd.NA,
            1: pd.NA,
            2: pd.NA,
            3: pd.NA,
            4: pd.NA,
            5: pd.NA,
            6: pd.NA,
            7: pd.NA,
            8: pd.NA,
            9: pd.NA,
            10: pd.NA,
        },
        "InstrmtExt": {
            0: pd.NA,
            1: pd.NA,
            2: pd.NA,
            3: pd.NA,
            4: pd.NA,
            5: pd.NA,
            6: pd.NA,
            7: pd.NA,
            8: pd.NA,
            9: pd.NA,
            10: pd.NA,
        },
        "@Typ": {
            0: pd.NA,
            1: pd.NA,
            2: pd.NA,
            3: pd.NA,
            4: pd.NA,
            5: pd.NA,
            6: pd.NA,
            7: pd.NA,
            8: pd.NA,
            9: pd.NA,
            10: pd.NA,
        },
        "@Val": {
            0: pd.NA,
            1: pd.NA,
            2: pd.NA,
            3: pd.NA,
            4: pd.NA,
            5: pd.NA,
            6: pd.NA,
            7: pd.NA,
            8: pd.NA,
            9: pd.NA,
            10: pd.NA,
        },
        "MktSegGrp": {
            0: pd.NA,
            1: pd.NA,
            2: pd.NA,
            3: pd.NA,
            4: pd.NA,
            5: pd.NA,
            6: pd.NA,
            7: pd.NA,
            8: pd.NA,
            9: pd.NA,
            10: pd.NA,
        },
    }

    return data


@pytest.fixture()
def expected_result_all_col_in_source_df_and_some_existing_instruments() -> dict:
    return {
        "@ID": {7: "6034220", 8: "6034221", 9: "6208188", 10: "6208189"},
        "@SecTyp": {7: "OPT", 8: "OPT", 9: "OPT", 10: "OPT"},
        "@Desc": {
            7: "OQDE SI 20220218 PS AM C 137.00 0",
            8: "OQDE SI 20220218 PS AM P 137.00 0",
            9: "OQDE SI 20220318 PS AM C 115.00 0",
            10: "OQDE SI 20220318 PS AM P 115.00 0",
        },
        "@CFI": {7: "OCAIPS", 8: "OPAIPS", 9: "OCAIPS", 10: "OPAIPS"},
        "@CntrctDt": {
            7: "2022-02-18",
            8: "2022-02-18",
            9: "2022-03-18",
            10: "2022-03-18",
        },
        "@Mult": {7: 100.0, 8: 100.0, 9: 100.0, 10: 100.0},
        "@SettlMeth": {7: "P", 8: "P", 9: "P", 10: "P"},
        "AID": {
            7: [
                {"@AltID": "281875378", "@AltIDSrc": "M"},
                {"@AltID": "DE000C55BF20", "@AltIDSrc": "4"},
            ],
            8: [
                {"@AltID": "281875379", "@AltIDSrc": "M"},
                {"@AltID": "DE000C55BF38", "@AltIDSrc": "4"},
            ],
            9: [
                {"@AltID": "281875604", "@AltIDSrc": "M"},
                {"@AltID": "DE000C58WDC3", "@AltIDSrc": "4"},
            ],
            10: [
                {"@AltID": "281875605", "@AltIDSrc": "M"},
                {"@AltID": "DE000C58WDD1", "@AltIDSrc": "4"},
            ],
        },
        "@StrkPx": {7: 137.0, 8: 137.0, 9: 115.0, 10: 115.0},
        "@PutCall": {7: "1", 8: "0", 9: "1", 10: "0"},
        "@ExerStyle": {7: "1", 8: "1", 9: "1", 10: "1"},
        "InstrAttrib": {
            7: [
                {"@Typ": "123", "@Val": "N"},
                {"@Typ": "106", "@Val": "XEUR"},
                {"@Typ": "112", "@Val": "N"},
                {"@Typ": "113", "@Val": "Y"},
                {"@Typ": "115", "@Val": "Y"},
            ],
            8: [
                {"@Typ": "123", "@Val": "N"},
                {"@Typ": "106", "@Val": "XEUR"},
                {"@Typ": "112", "@Val": "N"},
                {"@Typ": "113", "@Val": "Y"},
                {"@Typ": "115", "@Val": "Y"},
            ],
            9: [
                {"@Typ": "123", "@Val": "N"},
                {"@Typ": "106", "@Val": "XEUR"},
                {"@Typ": "112", "@Val": "N"},
                {"@Typ": "113", "@Val": "Y"},
                {"@Typ": "115", "@Val": "Y"},
            ],
            10: [
                {"@Typ": "123", "@Val": "N"},
                {"@Typ": "106", "@Val": "XEUR"},
                {"@Typ": "112", "@Val": "N"},
                {"@Typ": "113", "@Val": "Y"},
                {"@Typ": "115", "@Val": "Y"},
            ],
        },
        "instrumentIdCode": {
            7: "DE000C55BF20",
            8: "DE000C55BF38",
            9: "DE000C58WDC3",
            10: "DE000C58WDD1",
        },
        "instrumentClassification": {
            7: "OCAIPS",
            8: "OPAIPS",
            9: "OCAIPS",
            10: "OPAIPS",
        },
        "venue.tradingVenue": {7: "XEUR", 8: "XEUR", 9: "XEUR", 10: "XEUR"},
        "ext.bestExAssetClassMain": {
            7: "Equity Derivatives",
            8: "Equity Derivatives",
            9: "Equity Derivatives",
            10: "Equity Derivatives",
        },
        "ext.bestExAssetClassSub": {
            7: "Futures and options admitted to trading on a trading venue",
            8: "Futures and options admitted to trading on a trading venue",
            9: "Futures and options admitted to trading on a trading venue",
            10: "Futures and options admitted to trading on a trading venue",
        },
        "ext.instrumentUniqueIdentifier": {
            7: "XEURDE000C55BF20EUR",
            8: "XEURDE000C55BF38EUR",
            9: "XEURDE000C58WDC3GBP",
            10: "XEURDE000C58WDD1USD",
        },
        "@MktSegID": {7: "61545", 8: "61545", 9: "61545", 10: "61545"},
        "MktSegGrp": {7: 0, 8: 0, 9: 0, 10: 0},
        "@AltID": {7: 0, 8: 0, 9: 0, 10: 0},
        "@AltIDSrc": {7: 0, 8: 0, 9: 0, 10: 0},
        "Instrmt": {7: 0, 8: 0, 9: 0, 10: 0},
        "InstrmtExt": {7: 0, 8: 0, 9: 0, 10: 0},
        "@Typ": {7: 0, 8: 0, 9: 0, 10: 0},
        "@Val": {7: 0, 8: 0, 9: 0, 10: 0},
        "__currency__": {7: "EUR", 8: "EUR", 9: "GBX", 10: "USD"},
        "__major_currency__": {7: "EUR", 8: "EUR", 9: "GBP", 10: "USD"},
    }


@pytest.fixture()
def expected_filtered_data_no_existing_instrument():
    data = {
        "@ID": {
            0: "5578478",
            1: "5894212",
            2: "6183273",
            6: "6197796",
            7: "6034220",
            8: "6034221",
            9: "6208188",
            10: "6208189",
        },
        "@SecTyp": {
            0: "FUT",
            1: "FUT",
            2: "FUT",
            6: "OPT",
            7: "OPT",
            8: "OPT",
            9: "OPT",
            10: "OPT",
        },
        "@Desc": {
            0: "CONF SI 20210608 PS",
            1: "CONF SI 20210908 PS",
            2: "CONF SI 20211208 PS",
            6: "TTF SI 20221216 PS EU P 84.00 0",
            7: "OQDE SI 20220218 PS AM C 137.00 0",
            8: "OQDE SI 20220218 PS AM P 137.00 0",
            9: "OQDE SI 20220318 PS AM C 115.00 0",
            10: "OQDE SI 20220318 PS AM P 115.00 0",
        },
        "@CFI": {
            0: "FFDPSX",
            1: "FFDPSX",
            2: "FFDPSX",
            6: "OPESPS",
            7: "OCAIPS",
            8: "OPAIPS",
            9: "OCAIPS",
            10: "OPAIPS",
        },
        "@CntrctDt": {
            0: "2021-06-08",
            1: "2021-09-08",
            2: "2021-12-08",
            6: "2022-12-16",
            7: "2022-02-18",
            8: "2022-02-18",
            9: "2022-03-18",
            10: "2022-03-18",
        },
        "@Mult": {
            0: 1.0,
            1: 1.0,
            2: 1.0,
            6: 100.0,
            7: 100.0,
            8: 100.0,
            9: 100.0,
            10: 100.0,
        },
        "@SettlMeth": {0: "P", 1: "P", 2: "P", 6: "P", 7: "P", 8: "P", 9: "P", 10: "P"},
        "AID": {
            0: [
                {"@AltID": "17236010", "@AltIDSrc": "M"},
                {"@AltID": "DE000C5RQD52", "@AltIDSrc": "4"},
            ],
            1: [
                {"@AltID": "17236011", "@AltIDSrc": "M"},
                {"@AltID": "DE000C52GU94", "@AltIDSrc": "4"},
            ],
            2: [
                {"@AltID": "17236012", "@AltIDSrc": "M"},
                {"@AltID": "DE000C58EEB1", "@AltIDSrc": "4"},
            ],
            6: [
                {"@AltID": "123082927", "@AltIDSrc": "M"},
                {"@AltID": "DE000C58Q2M1", "@AltIDSrc": "4"},
            ],
            7: [
                {"@AltID": "281875378", "@AltIDSrc": "M"},
                {"@AltID": "DE000C55BF20", "@AltIDSrc": "4"},
            ],
            8: [
                {"@AltID": "281875379", "@AltIDSrc": "M"},
                {"@AltID": "DE000C55BF38", "@AltIDSrc": "4"},
            ],
            9: [
                {"@AltID": "281875604", "@AltIDSrc": "M"},
                {"@AltID": "DE000C58WDC3", "@AltIDSrc": "4"},
            ],
            10: [
                {"@AltID": "281875605", "@AltIDSrc": "M"},
                {"@AltID": "DE000C58WDD1", "@AltIDSrc": "4"},
            ],
        },
        "@StrkPx": {0: 0, 1: 0, 2: 0, 6: 84.0, 7: 137.0, 8: 137.0, 9: 115.0, 10: 115.0},
        "@PutCall": {0: 0, 1: 0, 2: 0, 6: "0", 7: "1", 8: "0", 9: "1", 10: "0"},
        "@ExerStyle": {0: 0, 1: 0, 2: 0, 6: "0", 7: "1", 8: "1", 9: "1", 10: "1"},
        "InstrAttrib": {
            0: [
                {"@Typ": "123", "@Val": "N"},
                {"@Typ": "106", "@Val": "XEUR"},
                {"@Typ": "112", "@Val": "N"},
                {"@Typ": "129", "@Val": "3"},
                {"@Typ": "113", "@Val": "N"},
                {"@Typ": "115", "@Val": "N"},
            ],
            1: [
                {"@Typ": "123", "@Val": "N"},
                {"@Typ": "106", "@Val": "XEUR"},
                {"@Typ": "112", "@Val": "N"},
                {"@Typ": "129", "@Val": "3"},
                {"@Typ": "113", "@Val": "N"},
                {"@Typ": "115", "@Val": "N"},
            ],
            2: [
                {"@Typ": "123", "@Val": "N"},
                {"@Typ": "106", "@Val": "XEUR"},
                {"@Typ": "112", "@Val": "N"},
                {"@Typ": "129", "@Val": "3"},
                {"@Typ": "113", "@Val": "N"},
                {"@Typ": "115", "@Val": "N"},
            ],
            6: [
                {"@Typ": "124", "@Val": "3000"},
                {"@Typ": "123", "@Val": "Y"},
                {"@Typ": "106", "@Val": "XEUR"},
                {"@Typ": "112", "@Val": "N"},
                {"@Typ": "129", "@Val": "3"},
                {"@Typ": "113", "@Val": "Y"},
                {"@Typ": "115", "@Val": "Y"},
            ],
            7: [
                {"@Typ": "123", "@Val": "N"},
                {"@Typ": "106", "@Val": "XEUR"},
                {"@Typ": "112", "@Val": "N"},
                {"@Typ": "113", "@Val": "Y"},
                {"@Typ": "115", "@Val": "Y"},
            ],
            8: [
                {"@Typ": "123", "@Val": "N"},
                {"@Typ": "106", "@Val": "XEUR"},
                {"@Typ": "112", "@Val": "N"},
                {"@Typ": "113", "@Val": "Y"},
                {"@Typ": "115", "@Val": "Y"},
            ],
            9: [
                {"@Typ": "123", "@Val": "N"},
                {"@Typ": "106", "@Val": "XEUR"},
                {"@Typ": "112", "@Val": "N"},
                {"@Typ": "113", "@Val": "Y"},
                {"@Typ": "115", "@Val": "Y"},
            ],
            10: [
                {"@Typ": "123", "@Val": "N"},
                {"@Typ": "106", "@Val": "XEUR"},
                {"@Typ": "112", "@Val": "N"},
                {"@Typ": "113", "@Val": "Y"},
                {"@Typ": "115", "@Val": "Y"},
            ],
        },
        "@MktSegID": {
            0: "351",
            1: "351",
            2: "351",
            6: "1754",
            7: "61545",
            8: "61545",
            9: "61545",
            10: "61545",
        },
        "instrumentIdCode": {
            0: "DE000C5RQD52",
            1: "DE000C52GU94",
            2: "DE000C58EEB1",
            6: "DE000C58Q2M1",
            7: "DE000C55BF20",
            8: "DE000C55BF38",
            9: "DE000C58WDC3",
            10: "DE000C58WDD1",
        },
        "instrumentClassification": {
            0: "FFDPSX",
            1: "FFDPSX",
            2: "FFDPSX",
            6: "OPESPS",
            7: "OCAIPS",
            8: "OPAIPS",
            9: "OCAIPS",
            10: "OPAIPS",
        },
        "venue.tradingVenue": {
            0: "XEUR",
            1: "XEUR",
            2: "XEUR",
            6: "XEUR",
            7: "XEUR",
            8: "XEUR",
            9: "XEUR",
            10: "XEUR",
        },
        "ext.bestExAssetClassMain": {
            0: "Credit Derivatives",
            1: "Credit Derivatives",
            2: "Credit Derivatives",
            6: "Equity Derivatives",
            7: "Equity Derivatives",
            8: "Equity Derivatives",
            9: "Equity Derivatives",
            10: "Equity Derivatives",
        },
        "ext.bestExAssetClassSub": {
            0: "Futures and options admitted to trading on a trading venue",
            1: "Futures and options admitted to trading on a trading venue",
            2: "Futures and options admitted to trading on a trading venue",
            6: "Futures and options admitted to trading on a trading venue",
            7: "Futures and options admitted to trading on a trading venue",
            8: "Futures and options admitted to trading on a trading venue",
            9: "Futures and options admitted to trading on a trading venue",
            10: "Futures and options admitted to trading on a trading venue",
        },
        "@AltID": {0: 0, 1: 0, 2: 0, 6: 0, 7: 0, 8: 0, 9: 0, 10: 0},
        "@AltIDSrc": {0: 0, 1: 0, 2: 0, 6: 0, 7: 0, 8: 0, 9: 0, 10: 0},
        "Instrmt": {0: 0, 1: 0, 2: 0, 6: 0, 7: 0, 8: 0, 9: 0, 10: 0},
        "InstrmtExt": {0: 0, 1: 0, 2: 0, 6: 0, 7: 0, 8: 0, 9: 0, 10: 0},
        "@Typ": {0: 0, 1: 0, 2: 0, 6: 0, 7: 0, 8: 0, 9: 0, 10: 0},
        "@Val": {0: 0, 1: 0, 2: 0, 6: 0, 7: 0, 8: 0, 9: 0, 10: 0},
        "MktSegGrp": {0: 0, 1: 0, 2: 0, 6: 0, 7: 0, 8: 0, 9: 0, 10: 0},
        "__currency__": {
            0: "EUR",
            1: "EUR",
            2: "USc",
            6: "EUR",
            7: "EUR",
            8: "EUR",
            9: "GBX",
            10: "USD",
        },
        "__major_currency__": {
            0: "EUR",
            1: "EUR",
            2: "USD",
            6: "EUR",
            7: "EUR",
            8: "EUR",
            9: "GBP",
            10: "USD",
        },
        "ext.instrumentUniqueIdentifier": {
            0: "XEURDE000C5RQD52EUR",
            1: "XEURDE000C52GU94EUR",
            2: "XEURDE000C58EEB1USD",
            6: "XEURDE000C58Q2M1EUR",
            7: "XEURDE000C55BF20EUR",
            8: "XEURDE000C55BF38EUR",
            9: "XEURDE000C58WDC3GBP",
            10: "XEURDE000C58WDD1USD",
        },
    }
    return data
