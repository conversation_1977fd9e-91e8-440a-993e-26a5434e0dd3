import pandas as pd
import pytest
from prefect.engine.signals import FAIL

from swarm_tasks.steeleye.reference.instrument.data_source.eurex.pre_process_eurex_instrument import (
    PreProcessEurexInstrument,
)
from swarm_tasks.steeleye.reference.instrument.data_source.eurex.pre_process_eurex_instrument import (
    Resources,
)


class TestPreProcessEurexInstrument(object):
    """
    Test cases for "TestPreProcessEurexInstrument" class
    """

    def test_empty_input_df_without_source_columns(self, empty_source_df: pd.DataFrame):
        resources = Resources(es_client_key="tenant-data")
        task = PreProcessEurexInstrument(name="test-pre-process-data")
        result = task.execute(empty_source_df, resources=resources)
        assert result.empty

    def test_all_col_in_source_df(
        self,
        all_col_in_source_df: pd.DataFrame,
        pre_processed_all_col_in_source_df,
    ):
        task = PreProcessEurexInstrument(name="test-pre-process-data")
        resources = Resources(es_client_key="tenant-data")
        result = task.execute(all_col_in_source_df, resources=resources)
        assert len(result) == 11
        assert result.fillna(pd.NA).to_dict() == pre_processed_all_col_in_source_df

    def test_missing_some_col_in_source_df(
        self, missing_some_col_in_source_df: pd.DataFrame
    ):
        task = PreProcessEurexInstrument(name="test-pre-process-data")
        resources = Resources(es_client_key="tenant-data")
        with pytest.raises(FAIL) as e:
            task.execute(missing_some_col_in_source_df, resources=resources)
        assert e.type == FAIL
