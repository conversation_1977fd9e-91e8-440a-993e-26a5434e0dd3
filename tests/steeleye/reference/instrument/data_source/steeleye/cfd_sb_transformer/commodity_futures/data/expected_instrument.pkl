���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK)��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�
cfiAttribute1��
cfiAttribute2��__cfd.cfiAttribute3��__sb.cfiAttribute3��
cfiAttribute4��cfiCategory��cfiGroup��+commoditiesOrEmissionAllowanceDerivativeInd��derivative.deliveryType��derivative.priceMultiplier��Ederivative.underlyingInstruments.0.underlyingInstrumentClassification��;derivative.underlyingInstruments.0.underlyingInstrumentCode��)__cfd.ext.alternativeInstrumentIdentifier��(__sb.ext.alternativeInstrumentIdentifier��ext.bestExAssetClassMain��ext.bestExAssetClassSub��ext.emirEligible��ext.instrumentIdCodeType��$__cfd.ext.instrumentUniqueIdentifier��#__sb.ext.instrumentUniqueIdentifier��ext.priceNotation��ext.pricingReferences.RIC��ext.quantityNotation��1ext.underlyingInstruments.0.derivative.expiryDate��,ext.underlyingInstruments.0.instrumentIdCode��.ext.underlyingInstruments.0.venue.tradingVenue��instrumentClassification��&instrumentClassificationEMIRAssetClass��.__cfd.instrumentClassificationEMIRContractType��-__sb.instrumentClassificationEMIRContractType��-__cfd.instrumentClassificationEMIRProductType��,__sb.instrumentClassificationEMIRProductType��__cfd.instrumentFullName��__sb.instrumentFullName��__meta_model__��notionalCurrency1��__cfd.sourceKey��__sb.sourceKey��(__cfd.venue.financialInstrumentShortName��'__sb.venue.financialInstrumentShortName��venue.tradingVenue�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C        �t�bhO�__swarm_raw_index__�u��R�e]�(hhK ��h��R�(KK	K��h!�]�(�Energy��Not Applicable/Undefined��Contract for difference�hk�Cash��Forwards��Commodities��True��CASH�et�bhhK ��h��R�(KKK��h�i8�����R�(Kh]NNNJ����J����K t�b�C       �t�bhhK ��h��R�(KKK��h!�]�(�FCECSX��GB00H1JWQM19��XXXXGB00H1JWQM19CFD��XXXXGB00H1JWQM19SB��;Commodities derivatives and emission allowances Derivatives��AOther commodities derivatives and emission allowances derivatives�et�bhhK ��h��R�(KKK��h�b1�����R�(Kh"NNNJ����J����K t�b�C�t�bhhK ��h��R�(KKK��h!�]�(�OTHR��XXXXGB00H1JWQM19USDCFD��XXXXGB00H1JWQM19USDSB��MONE��pandas._libs.missing��NA����UNIT��
2021-07-30�h��IFEU��JTJXXC��CO��CD��SB��CFD��Forward��B-Brent Crude Future CFD��B-Brent Crude Future SB��SteelEyeInstrument��USD�� FULINS_F_20201226_01of01.xml_cfd��FULINS_F_20201226_01of01.xml_sb��ICE/FUT 20210730 IFEU:B CFD��ICE/FUT 20210730 IFEU:B SB��XXXX�et�be]�(h
h}�(hhhK ��h��R�(KK	��h!�]�(h%h&h'h(h)h*h+h,h-et�bhONu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bhONu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h/h0h1h2h3h4et�bhONu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhONu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h6h7h8h9h:h;h<h=h>h?h@hAhBhChDhEhFhGhHhIhJhKhLhMet�bhONu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hg�mgr_locs��builtins��slice���K K	K��R�u}�(h�huh�h�K	K
K��R�u}�(h�hh�h�K
KK��R�u}�(h�h�h�h�KKK��R�u}�(h�h�h�h�KK)K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.