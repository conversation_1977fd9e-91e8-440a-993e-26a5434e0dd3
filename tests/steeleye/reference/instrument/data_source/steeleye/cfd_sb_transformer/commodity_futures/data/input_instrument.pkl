��'      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK7��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�	sourceKey��&id��instrumentFullName��&model��'instrumentClassificationEMIRProductType��
cfiAttribute1��
cfiAttribute2��
cfiAttribute3��
cfiAttribute4��
&timestamp��&instrumentClassificationEMIRAssetClass��cfiGroup��instrumentIdCode��cfiCategory��+commoditiesOrEmissionAllowanceDerivativeInd��&key��notionalCurrency1��(instrumentClassificationEMIRContractType�� issuerOrOperatorOfTradingVenueId��instrumentClassification��&user��(venue.admissionToTradingOrFirstTradeDate��venue.terminationDate��venue.tradingVenue��(venue.issuerRequestForAdmissionToTrading��"venue.financialInstrumentShortName��$venue.admissionToTradingApprovalDate��#venue.admissionToTradingRequestDate��derivative.expiryDate��derivative.deliveryType��derivative.priceMultiplier��ext.aii.mic��ext.mifirEligible��ext.pricingReferences.ICE��ext.bestExAssetClassSub��ext.emirEligible��ext.bestExAssetClassMain��ext.venueInEEA��#ext.alternativeInstrumentIdentifier��ext.instrumentUniqueIdentifier��ext.onFIRDS��ext.instrumentIdCodeType��.commodityAndEmissionAllowances.transactionType��-commodityAndEmissionAllowances.finalPriceType��)commodityAndEmissionAllowances.subProduct��_meta.id��*commodityAndEmissionAllowances.baseProduct��ext.pricingReferences.RIC��
ext.aii.daily��	_meta.key��
_meta.user��0commodityAndEmissionAllowances.furtherSubProduct��_meta.timestamp�� derivative.underlyingInstruments��_meta.model�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C        �t�bh]�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h!�]��pandas._libs.missing��NA���at�b�_dtype�hs�StringDtype���)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]�h�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]�h�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]�h�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]�h�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]�h�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]�h�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]�h�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]�h�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]�h�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]�h�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��FULINS_F_20201226_01of01.xml�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��GB00H1JWQM19USDIFEU�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��B-Brent Crude Future�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��FirdsInstrument�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��FUT�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��Extraction Resources�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��Cash�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��Standardized�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��Not Applicable/Undefined�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��
1609017510000�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��CO�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��Commodities futures�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��GB00H1JWQM19�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��Futures�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��True�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��1FirdsInstrument:GB00H1JWQM19USDIFEU:1609017510000�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��USD�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��FU�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��549300UF4R84F48NCH34�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��FCECSX�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��spider-firds-task�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��2014-05-30T00:00:00�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��2021-07-30T23:59:59�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��IFEU�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��True�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��ICE/FUT 20210730 IFEU:B�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��2014-05-30T00:00:00�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��2014-05-30T00:00:00�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��
2021-07-30�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��CASH�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��1000.0�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��IFEU�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��True�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��isin/GB00H1JWQM19/USD�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��:Futures and options admitted to trading on a trading venue�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��True�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��;Commodities derivatives and emission allowances Derivatives�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��True�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��IFEUBFF2021-07 00:00:00�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��GB00H1JWQM19USDIFEU�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��True�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��ID�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��FUTR�at�bh�h�)��ubhu)��}�(hxhhK ��h��R�(KK��h!�]��EXOF�at�bh�h�)��ube]�(h
h}�(hhhK ��h��R�(KK��h!�]�hQat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hRat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hSat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hTat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hUat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hVat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hWat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hXat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hYat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hZat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h[at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h(at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h)at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h+at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h,at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h0at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h1at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h9at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h:at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h<at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h=at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h?at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h@at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hBat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hCat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hDat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hEat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hFat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hGat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hHat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hIat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hJat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hKat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hLat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hMat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hNat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hOat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hPat�bh]Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hv�mgr_locs��builtins��slice���K,K-K��R�u}�(j�  h�j�  j�  K-K.K��R�u}�(j�  h�j�  j�  K.K/K��R�u}�(j�  h�j�  j�  K/K0K��R�u}�(j�  h�j�  j�  K0K1K��R�u}�(j�  h�j�  j�  K1K2K��R�u}�(j�  h�j�  j�  K2K3K��R�u}�(j�  h�j�  j�  K3K4K��R�u}�(j�  h�j�  j�  K4K5K��R�u}�(j�  h�j�  j�  K5K6K��R�u}�(j�  h�j�  j�  K6K7K��R�u}�(j�  h�j�  j�  K KK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j&  j�  j�  KKK��R�u}�(j�  j0  j�  j�  KK	K��R�u}�(j�  j:  j�  j�  K	K
K��R�u}�(j�  jD  j�  j�  K
KK��R�u}�(j�  jN  j�  j�  KKK��R�u}�(j�  jX  j�  j�  KK
K��R�u}�(j�  jb  j�  j�  K
KK��R�u}�(j�  jl  j�  j�  KKK��R�u}�(j�  jv  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j  j�  j�  KK K��R�u}�(j�  j   j�  j�  K K!K��R�u}�(j�  j*  j�  j�  K!K"K��R�u}�(j�  j4  j�  j�  K"K#K��R�u}�(j�  j>  j�  j�  K#K$K��R�u}�(j�  jH  j�  j�  K$K%K��R�u}�(j�  jR  j�  j�  K%K&K��R�u}�(j�  j\  j�  j�  K&K'K��R�u}�(j�  jf  j�  j�  K'K(K��R�u}�(j�  jp  j�  j�  K(K)K��R�u}�(j�  jz  j�  j�  K)K*K��R�u}�(j�  j�  j�  j�  K*K+K��R�u}�(j�  j�  j�  j�  K+K,K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.