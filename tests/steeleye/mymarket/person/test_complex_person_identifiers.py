import pandas as pd
import pytest

from swarm_tasks.steeleye.mymarket.person.complex_person_identifiers import (
    ComplexPersonIdentifiers,
)
from swarm_tasks.steeleye.mymarket.person.complex_person_identifiers import Params


@pytest.fixture()
def empty_df() -> pd.DataFrame:
    """Empty source data frame"""
    df = pd.DataFrame({})
    return df


@pytest.fixture()
def source_df() -> pd.DataFrame:
    """Source data frame containing 3 records"""
    df = pd.DataFrame(
        {
            "TYPE": ["Client", "Contact", "Employee", "Employee", "Employee"],
            "FIRSTNAME": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"],
            "LASTNAME": ["<PERSON>er", "Hey<PERSON>", "Bellingham", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"],
            "EMAIL": [
                "<EMAIL>",
                pd.NA,
                "<EMAIL>",
                pd.NA,
                pd.NA,
            ],
            "EMAIL.1": ["<EMAIL>", pd.NA, pd.NA, pd.NA, pd.NA],
            "PHONENUMBER_WORK": ["*************", "+************", pd.NA, pd.NA, pd.NA],
            "PHONENUMBER_MOBILE": ["01241242912", pd.NA, pd.NA, pd.NA, pd.NA],
            "PHONENUMBER_OTHER": ["***********", pd.NA, "+************", pd.NA, pd.NA],
            "PHONENUMBER_EXT": ["1531", "1421", "2122", "1234", pd.NA],
            "TRADEFILEIDENTIFIERS": [
                "ksworder",
                "sheyworth",
                pd.NA,
                pd.NA,
                "Hilde Schliemann",
            ],
            "TRADEFILEIDENTIFIERS.1": ["ksworder2", pd.NA, pd.NA, pd.NA, pd.NA],
            "CHATID_BBG": ["ksworder", pd.NA, pd.NA, pd.NA, "Hilde S"],
            "CHATID_MSTEAMS": [pd.NA, "sheyworth2", "sbellingham", pd.NA, pd.NA],
            "CHATID_OTHER": ["ksworder", "sheyworth", pd.NA, pd.NA, pd.NA],
            "CHATID_OTHER.1": ["ksworder2", pd.NA, pd.NA, pd.NA, pd.NA],
            "DESK": ["Sample1", "Sample2", pd.NA, pd.NA, pd.NA],
            "DESK.1": ["Sample3", pd.NA, pd.NA, pd.NA, pd.NA],
        }
    )
    return df


@pytest.fixture()
def expected_result_for_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "communications.phoneNumbers": [
                # Record 1
                [
                    {"extension": "1531"},
                    {"label": "MOBILE", "number": "+************", "dialingCode": "GB"},
                    {"label": "OTHER", "number": "***********"},
                    {"label": "WORK", "number": "*************"},
                ],
                # Record 2
                [
                    {"extension": "1421"},
                    {"label": "WORK", "number": "+************", "dialingCode": "DE"},
                ],
                # Record 3
                [
                    {"extension": "2122"},
                    {"label": "OTHER", "number": "+************", "dialingCode": "GB"},
                ],
                # Record 4
                [{"extension": "1234"}],
                # Record 5
                pd.NA,
            ],
            "communications.emails": [
                # Record 1
                ["<EMAIL>", "<EMAIL>"],
                # Record 2
                pd.NA,
                # Record 3
                ["<EMAIL>"],
                # Record 4
                pd.NA,
                # Record 5
                pd.NA,
            ],
            "communications.imAccounts": [
                # Record 1
                [
                    {"label": "BBG", "id": "ksworder"},
                    {"label": "Live Chat", "id": "ksworder"},
                    {"label": "Live Chat", "id": "ksworder2"},
                ],
                # Record 2
                [
                    # TODO: The label has to be changed back to MS Teams
                    # after it is safe to merge the schema change
                    {"label": "Live Chat", "id": "sheyworth2"},
                    {"label": "Live Chat", "id": "sheyworth"},
                ],
                # Record 3
                [{"label": "Live Chat", "id": "sbellingham"}],
                # Record 4
                pd.NA,
                # Record 5
                [{"label": "BBG", "id": "Hilde S"}],
            ],
            "sinkIdentifiers.tradeFileIdentifiers": [
                # Record 1
                [
                    {"id": "ksworder", "label": "id"},
                    {"id": "ksworder", "label": "account"},
                    {"id": "ksworder2", "label": "id"},
                    {"id": "ksworder2", "label": "account"},
                ],
                # Record 2
                [
                    {"id": "sheyworth", "label": "id"},
                    {"id": "sheyworth", "label": "account"},
                ],
                # Record 3
                pd.NA,
                # Record 4
                pd.NA,
                # Record 5
                [
                    {"id": "Hilde Schliemann", "label": "id"},
                    {"id": "Hilde Schliemann", "label": "account"},
                ],
            ],
            "uniqueIds": [
                # Record 1
                [
                    "+************",
                    "*************",
                    "1531",
                    "***********",
                    "account:ksworder",
                    "account:ksworder2",
                    "id:ksworder",
                    "id:ksworder2",
                    "ksworder",
                    "<EMAIL>",
                    "<EMAIL>",
                    "ksworder2",
                ],
                # Record 2
                [
                    "+************",
                    "1421",
                    "account:sheyworth",
                    "id:sheyworth",
                    "sheyworth",
                    "sheyworth2",
                ],
                # Record 3
                ["+************", "2122", "sbellingham", "<EMAIL>"],
                # Record 4
                ["1234"],
                # Record 5
                ["account:hilde schliemann", "hildes", "id:hilde schliemann"],
            ],
            "structure.desks": [
                # Record 1
                [
                    {"id": "Sample1", "name": "Sample1"},
                    {"id": "Sample3", "name": "Sample3"},
                ],
                # Record 2
                [{"id": "Sample2", "name": "Sample2"}],
                # Record 3
                pd.NA,
                # Record 4
                pd.NA,
                # Record 5
                pd.NA,
            ],
        }
    )
    return df


@pytest.fixture()
def params_fixture() -> Params:
    params = Params(
        **{
            "source_phonenumber_work_col": "PHONENUMBER_WORK",
            "source_phonenumber_mobile_col": "PHONENUMBER_MOBILE",
            "source_phonenumber_other_col": "PHONENUMBER_OTHER",
            "source_phonenumber_extension_col": "PHONENUMBER_EXT",
            "source_chatid_bbg_col": "CHATID_BBG",
            "source_chatid_msteams_col": "CHATID_MSTEAMS",
            "source_chatid_other_col_prefix": "CHATID_OTHER",
            "source_email_col_prefix": "EMAIL",
            "source_trade_file_id_col_prefix": "TRADEFILEIDENTIFIERS",
            "source_desk_col_prefix": "DESK",
            "target_phone_numbers_col": "communications.phoneNumbers",
            "target_emails_col": "communications.emails",
            "target_imaccounts_col": "communications.imAccounts",
            "target_trade_file_ids_col": "sinkIdentifiers.tradeFileIdentifiers",
            "target_unique_ids_col": "uniqueIds",
            "target_desks_col": "structure.desks",
        }
    )
    return params


class TestComplexPersonIdentifiers:
    """Test suite for ComplexPersonIdentifiers"""

    def test_empty_source_df(self, empty_df, params_fixture):
        """Test for the case where the source data frame is empty"""
        task = ComplexPersonIdentifiers(
            name="PopulateComplexPersonIdentifiers", params=params_fixture
        )
        result = task.execute(empty_df, params=params_fixture)
        assert result.empty

    def test_values_for_all_columns(
        self, source_df, expected_result_for_source_df, params_fixture
    ):
        """Test for the case where the different columns are populated. Different
        tests are conducted for the 3 records in the source_df
        """
        task = ComplexPersonIdentifiers(
            name="PopulateComplexPersonIdentifiers", params=params_fixture
        )
        # Sort lists in the result df before compare
        result = task.execute(source_df, params=params_fixture)
        for col in ["uniqueIds", "communications.emails"]:
            result.loc[:, col] = result.loc[:, col].apply(
                lambda x: sorted(x) if isinstance(x, list) else pd.NA
            )
        # Sort phoneNumbers lists in the result df before compare
        col = "communications.phoneNumbers"
        result[col] = result[col].apply(
            lambda v: self.__sort_list_of_dicts(v) if isinstance(v, list) else v
        )

        pd.testing.assert_frame_equal(result, expected_result_for_source_df)

    def __sort_list_of_dicts(self, list_to_sort: list) -> list:
        if len(list_to_sort) == 0:
            return []

        list_of_tuples = sorted([tuple(item.items()) for item in list_to_sort])
        return [dict(item) for item in list_of_tuples]
