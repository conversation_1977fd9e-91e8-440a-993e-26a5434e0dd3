from unittest.mock import MagicMock
from unittest.mock import patch

import pandas as pd
import pytest
from prefect.engine import signals
from se_schema_meta import ID

from swarm_tasks.steeleye.mymarket.person.split_universal_duplicated_person import (
    ActionType,
)
from swarm_tasks.steeleye.mymarket.person.split_universal_duplicated_person import (
    SplitUniversalDuplicated<PERSON>erson,
)
from swarm_tasks.steeleye.mymarket.person.split_universal_duplicated_person import (
    TempColumns,
)


@pytest.fixture()
def empty_df() -> pd.DataFrame:
    return pd.DataFrame({})


@pytest.fixture()
def empty_es_response_df() -> pd.DataFrame:
    df = pd.DataFrame({})
    df[ID] = pd.NA
    return df


@pytest.fixture()
def source_to_create_df() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "personalDetails.firstName": "Jane",
                "personalDetails.lastName": "<PERSON><PERSON>",
                "counterparty.name": pd.NA,
                "structure.department": pd.NA,
                "structure.role": pd.NA,
                "__update_action__": pd.NA,
                "officialIdentifiers.branchCountry": pd.NA,
                "personalDetails.nationality": pd.NA,
                "officialIdentifiers.concatId": pd.NA,
                "officialIdentifiers.mifirId": pd.NA,
                "officialIdentifiers.mifirIdSubType": pd.NA,
                "officialIdentifiers.mifirIdType": pd.NA,
                "communications.phoneNumbers": pd.NA,
                "communications.emails": ["<EMAIL>"],
                "communications.imAccounts": pd.NA,
                "sinkIdentifiers.tradeFileIdentifiers": pd.NA,
                "uniqueIds": ["<EMAIL>"],
                "officialIdentifiers.nationalIds": pd.NA,
                "officialIdentifiers.passports": pd.NA,
                "&traitFqn": "account/person",
                "structure.type": pd.NA,
                "officialIdentifiers.clientMandate": pd.NA,
                "retailOrProfessional": pd.NA,
                "sourceKey": "s3://source_file.csv",
                "name": "Jane Doe",
                "sourceIndex": 1,
                "__meta_model__": "AccountPerson",
                "personalDetails.dob": pd.NA,
            },
            {
                "personalDetails.firstName": "David",
                "personalDetails.lastName": "Jones",
                "counterparty.name": pd.NA,
                "structure.department": pd.NA,
                "structure.role": pd.NA,
                "__update_action__": pd.NA,
                "officialIdentifiers.branchCountry": pd.NA,
                "personalDetails.nationality": pd.NA,
                "officialIdentifiers.concatId": pd.NA,
                "officialIdentifiers.mifirId": pd.NA,
                "officialIdentifiers.mifirIdSubType": pd.NA,
                "officialIdentifiers.mifirIdType": pd.NA,
                "communications.phoneNumbers": pd.NA,
                "communications.emails": ["<EMAIL>"],
                "communications.imAccounts": pd.NA,
                "sinkIdentifiers.tradeFileIdentifiers": pd.NA,
                "uniqueIds": ["<EMAIL>"],
                "officialIdentifiers.nationalIds": pd.NA,
                "officialIdentifiers.passports": pd.NA,
                "&traitFqn": "market/person",
                "structure.type": "Client",
                "officialIdentifiers.clientMandate": pd.NA,
                "retailOrProfessional": pd.NA,
                "sourceKey": "s3://source_file.csv",
                "name": "David Jones",
                "sourceIndex": 3,
                "__meta_model__": "MarketPerson",
                "personalDetails.dob": pd.NA,
            },
        ]
    )


@pytest.fixture()
def source_to_update_df() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "personalDetails.firstName": "John",
                "personalDetails.lastName": "Smith",
                "counterparty.name": pd.NA,
                "structure.department": pd.NA,
                "structure.role": pd.NA,
                "__update_action__": pd.NA,
                "officialIdentifiers.branchCountry": pd.NA,
                "personalDetails.nationality": pd.NA,
                "officialIdentifiers.concatId": pd.NA,
                "officialIdentifiers.mifirId": pd.NA,
                "officialIdentifiers.mifirIdSubType": pd.NA,
                "officialIdentifiers.mifirIdType": pd.NA,
                "communications.phoneNumbers": pd.NA,
                "communications.emails": [
                    "<EMAIL>",
                    "<EMAIL>",
                ],
                "communications.imAccounts": pd.NA,
                "sinkIdentifiers.tradeFileIdentifiers": pd.NA,
                "uniqueIds": ["<EMAIL>", "<EMAIL>"],
                "officialIdentifiers.nationalIds": pd.NA,
                "officialIdentifiers.passports": pd.NA,
                "&traitFqn": "account/person",
                "structure.type": pd.NA,
                "officialIdentifiers.clientMandate": pd.NA,
                "retailOrProfessional": pd.NA,
                "sourceKey": "s3://source_file.csv",
                "name": "John Smith",
                "sourceIndex": 0,
                "__meta_model__": "AccountPerson",
                "personalDetails.dob": pd.NA,
            },
            {
                "personalDetails.firstName": "Emily",
                "personalDetails.lastName": "Brown",
                "counterparty.name": pd.NA,
                "structure.department": pd.NA,
                "structure.role": pd.NA,
                "__update_action__": pd.NA,
                "officialIdentifiers.branchCountry": pd.NA,
                "personalDetails.nationality": pd.NA,
                "officialIdentifiers.concatId": pd.NA,
                "officialIdentifiers.mifirId": pd.NA,
                "officialIdentifiers.mifirIdSubType": pd.NA,
                "officialIdentifiers.mifirIdType": pd.NA,
                "communications.phoneNumbers": pd.NA,
                "communications.emails": ["<EMAIL>"],
                "communications.imAccounts": pd.NA,
                "sinkIdentifiers.tradeFileIdentifiers": pd.NA,
                "uniqueIds": ["<EMAIL>"],
                "officialIdentifiers.nationalIds": pd.NA,
                "officialIdentifiers.passports": pd.NA,
                "&traitFqn": "market/person",
                "structure.type": "Client",
                "officialIdentifiers.clientMandate": pd.NA,
                "retailOrProfessional": pd.NA,
                "sourceKey": "s3://source_file.csv",
                "name": "Emily Brown",
                "sourceIndex": 2,
                "__meta_model__": "MarketPerson",
                "personalDetails.dob": pd.NA,
            },
        ]
    )


@pytest.fixture()
def expected_create_df(source_to_create_df) -> pd.DataFrame:
    df = source_to_create_df.drop(columns=["sourceIndex", TempColumns.DUPLICATE_ACTION])
    df[TempColumns.ACTION] = pd.NA
    return df


@pytest.fixture()
def expected_update_df(source_to_update_df) -> pd.DataFrame:
    df = source_to_update_df.drop(columns=["sourceIndex"])
    df[TempColumns.GROUP_BY_INDEX] = pd.Series([0, 1])
    return df.reset_index(drop=True)


@pytest.fixture()
def source_df(source_to_create_df, source_to_update_df) -> pd.DataFrame:
    df = pd.concat(
        [source_to_create_df, source_to_update_df],
        ignore_index=True,
    )
    return df.reset_index(drop=True)


@pytest.fixture()
def es_person_df() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "sourceKey": "s3://source_file.csv",
                "&id": "fb3932d1-b8a9-4bc9-807e-760554634ca8",
                "&traitFqn": "account/person",
                "&uniqueProps": ["+*********", "<EMAIL>"],
                "retailOrProfessional": "N/A",
                "&key": "AccountPerson:fb3932d1-b8a9-4bc9-807e-760554634ca8:*************",
                "uniqueIds": ["+*********", "<EMAIL>"],
                "&model": "AccountPerson",
                "&version": 1,
                "name": "John Smith",
                "&hash": "119cf2643987a980b46c48381e2a6b00b658f9cf150173d22247f56d86c249f1",
                "&timestamp": *************,
                "&user": "system",
                "structure.department": "Australian Equities Small / Mid",
                "structure.role": "Hedge Fund Manager",
                "communications.emails": ["<EMAIL>"],
                "communications.phoneNumbers": [
                    {"dialingCode": "IR", "label": "MOBILE", "number": "+*********"}
                ],
                "counterparty.name": "HSBC",
                "personalDetails.firstName": "John",
                "personalDetails.lastName": "Smith",
            },
            {
                "sourceKey": "s3://source_file.csv",
                "&id": "03b591b4-520b-48a3-aff8-8268d28f64ef",
                "&traitFqn": "market/person",
                "&uniqueProps": ["+*********", "<EMAIL>"],
                "retailOrProfessional": "N/A",
                "&key": "MarketPerson:03b591b4-520b-48a3-aff8-8268d28f64ef",
                "uniqueIds": ["+*********", "<EMAIL>"],
                "&model": "MarketPerson",
                "&version": 1,
                "name": "Emily Brown",
                "&hash": "9065424b9f01217b599033f80c4478aa76e4738f9cdcbe5bb55a58f7511dd7f2",
                "&timestamp": *************,
                "&user": "system",
                "structure.department": "Emerging Companies",
                "structure.role": "Relationship Manager/Wealth Manager",
                "communications.emails": ["<EMAIL>"],
                "communications.phoneNumbers": [
                    {"dialingCode": "IR", "label": "MOBILE", "number": "+*********"}
                ],
                "counterparty.name": "Bank of America Corp",
                "personalDetails.firstName": "Emily",
                "personalDetails.lastName": "Brown",
            },
        ]
    )


@pytest.fixture()
def expected_es_df(es_person_df) -> pd.DataFrame:
    expected_es_df = es_person_df.copy()
    expected_es_df[TempColumns.MATCHED_FEED_INDEX] = pd.Series([0, 1]).astype(object)
    return expected_es_df.reset_index(drop=True)


class TestSplitUniversalDuplicatedPerson:
    def test_empty_source_df(self, empty_df):
        with pytest.raises(signals.SKIP):
            SplitUniversalDuplicatedPerson.process(
                source_frame=empty_df,
            )

    def test_no_class_model_in_df(self, source_df):
        no_meta_model_df = source_df.drop(columns="__meta_model__")
        with pytest.raises(signals.SKIP):
            SplitUniversalDuplicatedPerson.process(
                source_frame=no_meta_model_df,
            )

    @patch.object(SplitUniversalDuplicatedPerson, "_fetch_person_from_es")
    def test_only_create_df(
        self,
        mocked_elastic_result,
        source_to_create_df,
        empty_es_response_df,
        expected_create_df,
    ):
        mocked_elastic_result.return_value = empty_es_response_df
        split_task_result = SplitUniversalDuplicatedPerson.process(
            source_frame=source_to_create_df,
            es_client=MagicMock(),
        )
        assert split_task_result.get(ActionType.UPDATE).empty
        assert split_task_result.get(ActionType.ES_DATA).empty

        create_df = split_task_result.get(ActionType.CREATE).target
        pd.testing.assert_frame_equal(create_df, expected_create_df)

    @patch.object(SplitUniversalDuplicatedPerson, "_fetch_person_from_es")
    def test_only_update_df(
        self,
        mocked_elastic_result,
        source_to_update_df,
        es_person_df,
        empty_es_response_df,
        expected_update_df,
        expected_es_df,
    ):
        mocked_elastic_result.side_effect = self._es_calls_responses(
            es_person_df, empty_es_response_df
        )
        split_task_result = SplitUniversalDuplicatedPerson.process(
            source_frame=source_to_update_df,
            es_client=MagicMock(),
        )
        assert split_task_result.get(ActionType.CREATE).target.empty

        es_df = split_task_result.get(ActionType.ES_DATA).reset_index(drop=True)
        pd.testing.assert_frame_equal(es_df, expected_es_df)

        update_df = split_task_result.get(ActionType.UPDATE).reset_index(drop=True)
        pd.testing.assert_frame_equal(update_df, expected_update_df)

    @patch.object(SplitUniversalDuplicatedPerson, "_fetch_person_from_es")
    def test_match_and_split_df(
        self,
        mocked_elastic_result,
        source_df,
        es_person_df,
        expected_es_df,
        empty_es_response_df,
        expected_create_df,
        expected_update_df,
    ):
        expected_update_df[TempColumns.GROUP_BY_INDEX] = pd.Series([2, 3])
        expected_es_df[TempColumns.MATCHED_FEED_INDEX] = pd.Series([2, 3]).astype(
            object
        )

        mocked_elastic_result.side_effect = self._es_calls_responses(
            es_person_df, empty_es_response_df
        )
        split_task_result = SplitUniversalDuplicatedPerson.process(
            source_frame=source_df,
            es_client=MagicMock(),
        )

        update_df = split_task_result.get(ActionType.UPDATE).reset_index(drop=True)
        pd.testing.assert_frame_equal(update_df, expected_update_df)

        es_df = split_task_result.get(ActionType.ES_DATA).reset_index(drop=True)
        pd.testing.assert_frame_equal(es_df, expected_es_df)

        create_df = split_task_result.get(ActionType.CREATE).target
        pd.testing.assert_frame_equal(create_df, expected_create_df)

    def _es_calls_responses(self, es_person_df, empty_es_response_df):
        yield es_person_df.iloc[[0]]
        yield es_person_df.iloc[[1]]
        while True:
            yield empty_es_response_df
