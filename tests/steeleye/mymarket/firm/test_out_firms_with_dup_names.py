from unittest.mock import patch

import addict
import pandas as pd
import pytest
from prefect.engine import signals
from swarm.conf import SettingsCls
from swarm.task.auditor import Auditor

from swarm_tasks.steeleye.mymarket.firm import filter_out_firms_with_dup_names
from swarm_tasks.steeleye.mymarket.firm.filter_out_firms_with_dup_names import (
    Params,
)


@pytest.fixture
def empty_df():
    data = {}
    return pd.DataFrame(data)


@pytest.fixture()
def source_df() -> pd.DataFrame:
    """Source data frame containing 5 records"
    Row 1: is a new record (name not present in Elastic), should end up in target
    Row 2: duplicate row in file (dup of Row 1) based on name and should be dropped
    Row 3: no name, dropped
    Row 4: the name is already present in Elastic
    Row 5: new record
    """
    df = pd.DataFrame(
        [
            # Row 1: new record
            {"name": "<PERSON><PERSON>", "row": 1},
            # Row 2: duplicate of row 1 (name)
            {"name": "<PERSON><PERSON>", "row": 2},
            # Row 3: Null name
            {"name": pd.NA, "row": 3},
            # Row 4: record present in Elastic
            {"name": "Sparkasse", "row": 4},
            # Row 5: new record
            {"name": "Edeka", "row": 5},
        ]
    )
    return df


@pytest.fixture()
def source_df_keep_to_update() -> pd.DataFrame:
    """Source data frame containing 5 records"
    Row 1: is a new record (name not present in Elastic), should end up in target
    Row 2: duplicate row in file (dup of Row 1) based on name and should be dropped
    Row 4: the name is already present in Elastic, but has __to_update__ == True
    """
    df = pd.DataFrame(
        [
            # Row 1: new record
            {"name": "Barclays", "row": 1, "__to_update__": False},
            # Row 2: duplicate of row 1 (name)
            {"name": "Barclays", "row": 2, "__to_update__": False},
            # Row 3: Null name
            {"name": pd.NA, "row": 3, "__to_update__": False},
            # Row 4: record present in Elastic, but to update
            {"name": "Sparkasse", "row": 4, "__to_update__": True},
        ]
    )
    return df


@pytest.fixture()
def expected_result_some_dupes_some_in_elastic() -> pd.DataFrame:
    df = pd.DataFrame(
        [
            # Row 1 in source_df
            {"name": "Barclays", "row": 1},
            # Row 5 in source_df
            {"name": "Edeka", "row": 5},
        ],
        index=[0, 2],
    )
    return df


@pytest.fixture()
def expected_result_some_dupes_no_records_in_elastic() -> pd.DataFrame:
    df = pd.DataFrame(
        [
            # Row 1: new record
            {"name": "Barclays", "row": 1},
            # Row 4: record present in Elastic
            {"name": "Sparkasse", "row": 4},
            # Row 5: new record
            {"name": "Edeka", "row": 5},
        ]
    )
    return df


@pytest.fixture()
def expected_result_keep_to_update() -> pd.DataFrame:
    df = pd.DataFrame(
        [
            # Row 1: new record
            {"name": "Barclays", "row": 1, "__to_update__": False},
            # Row 4: record present in Elastic, but to update
            {"name": "Sparkasse", "row": 4, "__to_update__": True},
        ]
    )
    return df


@pytest.fixture()
def params_fixture() -> Params:
    params = Params(
        **{
            "name_column": "name",
        }
    )
    return params


class TestFilterOutFirmsWithDupNames:
    """Test suite for FilterOutDuplicateFirms"""

    def test_empty_source_df(self, mocker, empty_df, params_fixture):
        """Test for the case where the source data frame is empty"""
        task = self._init_task(mocker=mocker, params=params_fixture)
        with pytest.raises(signals.SKIP):
            task.execute(
                source_frame=empty_df,
                params=params_fixture,
            )

    @patch.object(
        filter_out_firms_with_dup_names,
        "fetch_counterparty_records_from_elastic",
    )
    def test_with_some_duplicate_rows_some_in_elastic(
        self,
        mock_elastic_results,
        mocker,
        source_df,
        expected_result_some_dupes_some_in_elastic,
        params_fixture,
    ):
        """Test for the case where 5 records are present in the input. 1 is a duplicate
        in the file, 1 is already present in Elastic and 1 has null name. Expected
        result should have 2 record
        """
        task = self._init_task(mocker=mocker, params=params_fixture)
        mock_elastic_results.return_value = pd.DataFrame({"name": ["Sparkasse"]})
        result = task.execute(
            source_frame=source_df,
            params=params_fixture,
        )

        assert result.equals(expected_result_some_dupes_some_in_elastic)

    @patch.object(
        filter_out_firms_with_dup_names,
        "fetch_counterparty_records_from_elastic",
    )
    def test_with_all_leis_already_in_elastic(
        self,
        mock_elastic_results,
        mocker,
        source_df,
        params_fixture,
    ):
        """Test for the case where 5 records are present in the input, and all are either duplicates,
        have null name, or are in Elastic. A SKIP signal is expected to be raised
        """
        task = self._init_task(mocker=mocker, params=params_fixture)
        # All records are in Elastic
        mock_elastic_results.return_value = pd.DataFrame(
            {"name": ["Sparkasse", "Edeka", "Barclays"]}
        )

        with pytest.raises(signals.SKIP):
            task.execute(
                source_frame=source_df,
                params=params_fixture,
            )

    @patch.object(
        filter_out_firms_with_dup_names,
        "fetch_counterparty_records_from_elastic",
    )
    def test_with_no_rows_in_elastic(
        self,
        mock_elastic_results,
        mocker,
        source_df,
        expected_result_some_dupes_no_records_in_elastic,
        params_fixture,
    ):
        """Test for the case where 5 records are present in the input. 1 is a duplicate
        in the file, and 1 has null name. Expected result should have 3 records
        """
        task = self._init_task(mocker=mocker, params=params_fixture)

        mock_elastic_results.return_value = pd.DataFrame()

        result = task.execute(
            source_frame=source_df,
            params=params_fixture,
        )
        assert result.equals(expected_result_some_dupes_no_records_in_elastic)

    @patch.object(
        filter_out_firms_with_dup_names,
        "fetch_counterparty_records_from_elastic",
    )
    def test_keep_to_update(
        self,
        mock_elastic_results,
        mocker,
        source_df_keep_to_update,
        expected_result_keep_to_update,
        params_fixture,
    ):
        """Test for the case where a record with as name already in ES is
        to be updated instead of discarded
        """
        params_fixture.keep_firms_to_update = True
        task = self._init_task(mocker=mocker, params=params_fixture)

        mock_elastic_results.return_value = pd.DataFrame(
            {"name": ["Sparkasse", "Edeka"]}
        )

        result = task.execute(
            source_frame=source_df_keep_to_update,
            params=params_fixture,
        )
        assert result.equals(expected_result_keep_to_update)

    @staticmethod
    def _init_task(mocker, params):
        mock_connections = mocker.patch.object(
            SettingsCls, "connections", new_callable=mocker.PropertyMock
        )
        mock_connections.return_value = addict.Dict(
            {
                "tenant-data": {
                    "meta": {
                        "id": "&id",
                        "model": "&model",
                        "hash": "&hash",
                        "prefix": "&",
                        "key": "&key",
                    },
                    "MAX_TERMS_SIZE": 1024,
                },
                "reference-data": {
                    "meta": {
                        "id": "&id",
                        "model": "&model",
                        "hash": "&hash",
                        "prefix": "&",
                        "key": "&key",
                    },
                    "MAX_TERMS_SIZE": 1024,
                },
            }
        )
        mock_auditor = mocker.patch.object(
            filter_out_firms_with_dup_names.FilterOutFirmsWithDupNames,
            "auditor",
        )
        mock_auditor.return_value = Auditor(
            task_name="FilterOutDuplicateAndInvalidFirms"
        )
        task = filter_out_firms_with_dup_names.FilterOutFirmsWithDupNames(
            name="FilterOutFirmsWithDupNames", params=params
        )

        return task
