import pandas as pd
import pytest

from swarm_tasks.steeleye.mymarket.firm.create_firm_identifiers import (
    CreateFirmIdentifiers,
)
from swarm_tasks.steeleye.mymarket.firm.create_firm_identifiers import Params


@pytest.fixture()
def empty_df() -> pd.DataFrame:
    """Empty source data frame"""
    df = pd.DataFrame({})
    return df


@pytest.fixture()
def source_df() -> pd.DataFrame:
    """Source data frame containing 3 records"""
    df = pd.DataFrame(
        {
            "LEI": ["549300IL8TQT0JMDJJ80", "1ZU7M6R6N6PXYJ6V0C83", "1ZU7M3", pd.NA],
            "NAME": ["HSBC", "HDFC", "JP Morgan", "Fidelity"],
            "TRADEFILEIDENTIFIERS": ["ksworder", pd.NA, "sheyworth", pd.NA],
            "TRADEFILEIDENTIFIERS.1": ["ksworder2", pd.NA, pd.NA, pd.NA],
        }
    )
    return df


@pytest.fixture()
def source_df_update_ids() -> pd.DataFrame:
    """Source data frame containing 2 records, one to create, one to update"""
    df = pd.DataFrame(
        {
            "LEI": ["549300IL8TQT0JMDJJ80", "1ZU7M6R6N6PXYJ6V0C83"],
            "NAME": ["HSBC", "HDFC"],
            "TRADEFILEIDENTIFIERS": ["ksworder", "id2"],
            "TRADEFILEIDENTIFIERS.1": ["ksworder2", pd.NA],
            "__to_update__": [False, True],
            "es_trade_file_identifiers": [[], [{"id": "id1", "label": "id"}]],
            "es_trade_ids": [[], ["id:id1"]],
        }
    )
    return df


@pytest.fixture()
def expected_result_for_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "firmIdentifiers.lei": [
                "549300IL8TQT0JMDJJ80",
                "1ZU7M6R6N6PXYJ6V0C83",
                pd.NA,
                pd.NA,
            ],
            "sinkIdentifiers.tradeFileIdentifiers": [
                # Record 1
                [
                    {"id": "549300IL8TQT0JMDJJ80", "label": "lei"},
                    {"id": "ksworder", "label": "id"},
                    {"id": "ksworder", "label": "account"},
                    {"id": "ksworder2", "label": "id"},
                    {"id": "ksworder2", "label": "account"},
                ],
                # Record 2
                [{"id": "1ZU7M6R6N6PXYJ6V0C83", "label": "lei"}],
                # Record 3
                [
                    {"id": "sheyworth", "label": "id"},
                    {"id": "sheyworth", "label": "account"},
                ],
                # Record 4
                pd.NA,
            ],
            "uniqueIds": [
                # Record 1
                [
                    "lei:549300il8tqt0jmdjj80",
                    "id:ksworder2",
                    "account:ksworder2",
                    "account:ksworder",
                    "id:ksworder",
                ],
                # Record 2
                ["lei:1zu7m6r6n6pxyj6v0c83"],
                # Record 3
                ["account:sheyworth", "id:sheyworth"],
                # Record 4
                pd.NA,
            ],
        }
    )
    return df


@pytest.fixture()
def expected_result_update_ids() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "firmIdentifiers.lei": [
                "549300IL8TQT0JMDJJ80",
                "1ZU7M6R6N6PXYJ6V0C83",
            ],
            "sinkIdentifiers.tradeFileIdentifiers": [
                # Record 1
                [
                    {"id": "549300IL8TQT0JMDJJ80", "label": "lei"},
                    {"id": "ksworder", "label": "id"},
                    {"id": "ksworder", "label": "account"},
                    {"id": "ksworder2", "label": "id"},
                    {"id": "ksworder2", "label": "account"},
                ],
                # Record 2
                [
                    {"id": "1ZU7M6R6N6PXYJ6V0C83", "label": "lei"},
                    {"id": "id2", "label": "id"},
                    {"id": "id2", "label": "account"},
                    {"id": "id1", "label": "id"},
                ],
            ],
            "uniqueIds": [
                # Record 1
                [
                    "lei:549300il8tqt0jmdjj80",
                    "id:ksworder2",
                    "account:ksworder2",
                    "account:ksworder",
                    "id:ksworder",
                ],
                # Record 2
                [
                    "account:id2",
                    "id:id1",
                    "id:id2",
                    "lei:1zu7m6r6n6pxyj6v0c83",
                ],
            ],
        }
    )
    return df


@pytest.fixture()
def params_fixture() -> Params:
    params = Params(
        **{
            "source_lei_col": "LEI",
            "source_trade_file_id_col_prefix": "TRADEFILEIDENTIFIERS",
            "source_name_col": "NAME",
            "target_lei_col": "firmIdentifiers.lei",
            "target_trade_file_ids_col": "sinkIdentifiers.tradeFileIdentifiers",
            "target_unique_ids_col": "uniqueIds",
        }
    )
    return params


class TestCreateFirmIdentifiers:
    """Test suite for CreateFirmIdentifiers"""

    def test_empty_source_df(self, empty_df, params_fixture):
        """Test for the case where the source data frame is empty"""
        task = CreateFirmIdentifiers(
            name="CreateFirmIdentifiers", params=params_fixture
        )
        result = task.execute(empty_df, params=params_fixture)
        assert result.empty

    def test_values_for_all_columns(
        self, source_df, expected_result_for_source_df, params_fixture
    ):
        """Test for the case where the different columns are populated. Different
        tests are conducted for the 3 records in the source_df
        """
        task = CreateFirmIdentifiers(
            name="CreateFirmIdentifiers", params=params_fixture
        )
        result = task.execute(source_df, params=params_fixture)

        # Sort list columns for the test because the sorting can be non-deterministic

        result.loc[:, "uniqueIds"] = result.loc[:, "uniqueIds"].apply(
            lambda x: sorted(x) if isinstance(x, list) else pd.NA
        )
        expected_result_for_source_df.loc[
            :, "uniqueIds"
        ] = expected_result_for_source_df.loc[:, "uniqueIds"].apply(
            lambda x: sorted(x) if isinstance(x, list) else pd.NA
        )

        assert result.equals(expected_result_for_source_df)

    def test_update_with_es_result(
        self, source_df_update_ids, expected_result_update_ids, params_fixture
    ):
        """Test for the case where one row updates identifiers with provided results from ES"""
        params_fixture.update_with_es_result = True
        task = CreateFirmIdentifiers(
            name="CreateFirmIdentifiers", params=params_fixture
        )
        result = task.execute(source_df_update_ids, params=params_fixture)

        # Sort list columns for the test because the sorting can be non-deterministic
        result.loc[:, "uniqueIds"] = result.loc[:, "uniqueIds"].apply(
            lambda x: sorted(x) if isinstance(x, list) else pd.NA
        )
        expected_result_update_ids.loc[:, "uniqueIds"] = expected_result_update_ids.loc[
            :, "uniqueIds"
        ].apply(lambda x: sorted(x) if isinstance(x, list) else pd.NA)

        assert result.equals(expected_result_update_ids)
