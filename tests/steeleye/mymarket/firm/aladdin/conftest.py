import pandas as pd
import pytest
from swarm.task.io.read import FrameProducerResult


@pytest.fixture()
def empty_frame() -> FrameProducerResult:
    return FrameProducerResult(frame=pd.DataFrame())


@pytest.fixture()
def cpty_desk_id_missing_frame() -> FrameProducerResult:
    return FrameProducerResult(frame=pd.DataFrame({"cptyDeskId": ["123"]}))


@pytest.fixture()
def issuer_long_name_missing_frame() -> FrameProducerResult:
    return FrameProducerResult(frame=pd.DataFrame({"issuerLongName": ["ABC"]}))


@pytest.fixture()
def source_frame() -> FrameProducerResult:
    return FrameProducerResult(
        frame=pd.DataFrame(
            [
                {"cptyDeskId": "2676", "cptyId": "3796", "issuerLongName": "UBS AG"},
                {"cptyDeskId": "2677", "cptyId": "3796", "issuerLongName": "UBS AG"},
                {"cptyDeskId": "2678", "cptyId": "3796", "issuerLongName": "UBS AG"},
                {
                    "cptyDeskId": "856",
                    "cptyId": "2157",
                    "issuerLongName": "MERRILL LYNCH INTERNATIONAL",
                },
                {
                    "cptyDeskId": "857",
                    "cptyId": "2157",
                    "issuerLongName": "MERRILL LYNCH INTERNATIONAL",
                },
                {
                    "cptyDeskId": "609",
                    "cptyId": "2027",
                    "issuerLongName": "CITIGROUP GLOBAL",
                },
            ]
        )
    )


@pytest.fixture()
def source_frame_with_new_values_in_existing_firms() -> FrameProducerResult:
    return FrameProducerResult(
        frame=pd.DataFrame(
            [
                {"cptyDeskId": "2676", "cptyId": "3796", "issuerLongName": "UBS AG"},
                {"cptyDeskId": "2677", "cptyId": "3796", "issuerLongName": "UBS AG"},
                {"cptyDeskId": "2000", "cptyId": "3796", "issuerLongName": "UBS AG"},
                {
                    "cptyDeskId": "901",
                    "cptyId": "2157",
                    "issuerLongName": "MERRILL LYNCH INTERNATIONAL",
                },
                {
                    "cptyDeskId": "902",
                    "cptyId": "2157",
                    "issuerLongName": "MERRILL LYNCH INTERNATIONAL",
                },
                {
                    "cptyDeskId": "609",
                    "cptyId": "2027",
                    "issuerLongName": "CITIGROUP GLOBAL",
                },
                {
                    "cptyDeskId": "610",
                    "cptyId": "2027",
                    "issuerLongName": "CITIGROUP GLOBAL",
                },
            ]
        )
    )


@pytest.fixture()
def source_frame_with_some_new_firms_and_updates() -> FrameProducerResult:
    return FrameProducerResult(
        frame=pd.DataFrame(
            [
                {"cptyDeskId": "2000", "cptyId": "3796", "issuerLongName": "UBS AG"},
                {
                    "cptyDeskId": "901",
                    "cptyId": "2157",
                    "issuerLongName": "MERRILL LYNCH INTERNATIONAL",
                },
                {
                    "cptyDeskId": "902",
                    "cptyId": "2157",
                    "issuerLongName": "MERRILL LYNCH INTERNATIONAL",
                },
                {
                    "cptyDeskId": "1111",
                    "cptyId": "1989",
                    "issuerLongName": "BNP PARIBAS",
                },
                {
                    "cptyDeskId": "2222",
                    "cptyId": "1989",
                    "issuerLongName": "BNP PARIBAS",
                },
            ]
        )
    )


@pytest.fixture()
def elastic_df() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "&id": "070ce5e1-90cd-4fe3-8421-911b29197b79",
                "&uniqueProps": ["id:609", "id:2027"],
                "&version": 1,
                "name": "CITIGROUP GLOBAL",
                "&key": "MarketCounterparty:070ce5e1-90cd-4fe3-8421-911b29197b79:1704468179134",
                "&hash": "8eb87235e9d829dd8eafdb001c53aa18385cf3c6652df376960a8e8926b47256",
                "uniqueIds": ["id:609", "id:2027"],
                "&model": "MarketCounterparty",
                "&timestamp": 1704468179134,
                "&user": "system",
                "sinkIdentifiers.tradeFileIdentifiers": [
                    {"id": "609", "label": "id"},
                    {"id": "2027", "label": "id"},
                ],
            },
            {
                "&id": "ff0caba6-c2e0-4196-a65c-effa26fb4ca8",
                "&uniqueProps": ["id:856", "id:857", "id:2157"],
                "&version": 1,
                "name": "MERRILL LYNCH INTERNATIONAL",
                "&key": "MarketCounterparty:ff0caba6-c2e0-4196-a65c-effa26fb4ca8:1704468179134",
                "&hash": "e95897bc43ad2b9a2c3bb8088959348a8b57352a2888d0c5743d401d0fff8174",
                "uniqueIds": ["id:856", "id:857", "id:2157"],
                "&model": "MarketCounterparty",
                "&timestamp": 1704468179134,
                "&user": "system",
                "sinkIdentifiers.tradeFileIdentifiers": [
                    {"id": "856", "label": "id"},
                    {"id": "857", "label": "id"},
                    {"id": "2157", "label": "id"},
                ],
            },
            {
                "&id": "ba869618-f2c9-4f2e-ab96-48b5c4825ae7",
                "&uniqueProps": [
                    "id:2676",
                    "id:2677",
                    "id:2678",
                    "id:3796",
                ],
                "&version": 1,
                "name": "UBS AG",
                "&key": "MarketCounterparty:ba869618-f2c9-4f2e-ab96-48b5c4825ae7:1704468179134",
                "&hash": "0670f81dd37a4cfb78e5e90fd422f051bae02939189aeb20bdc972fcf4224bd1",
                "uniqueIds": [
                    "id:2676",
                    "id:2677",
                    "id:2678",
                    "id:3796",
                ],
                "&model": "MarketCounterparty",
                "&timestamp": 1704468179134,
                "&user": "system",
                "sinkIdentifiers.tradeFileIdentifiers": [
                    {"id": "2676", "label": "id"},
                    {"id": "2677", "label": "id"},
                    {"id": "2678", "label": "id"},
                    {"id": "3796", "label": "id"},
                ],
            },
        ]
    )


@pytest.fixture()
def expected_frame_creates_only() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "issuerLongName": "CITIGROUP GLOBAL",
                "allCptyIds": ["609", "2027"],
                "cptyPrefixIds": ["id:609", "id:2027"],
            },
            {
                "issuerLongName": "MERRILL LYNCH INTERNATIONAL",
                "allCptyIds": ["856", "857", "2157"],
                "cptyPrefixIds": ["id:856", "id:857", "id:2157"],
            },
            {
                "issuerLongName": "UBS AG",
                "allCptyIds": ["2676", "2677", "2678", "3796"],
                "cptyPrefixIds": ["id:2676", "id:2677", "id:2678", "id:3796"],
            },
        ]
    )


@pytest.fixture()
def expected_result_new_values_updated() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "&id": "070ce5e1-90cd-4fe3-8421-911b29197b79",
                "&uniqueProps": ["id:609", "id:610", "id:2027"],
                "&version": 1,
                "name": "CITIGROUP GLOBAL",
                "&key": "MarketCounterparty:070ce5e1-90cd-4fe3-8421-911b29197b79:1704468179134",
                "&hash": "8eb87235e9d829dd8eafdb001c53aa18385cf3c6652df376960a8e8926b47256",
                "uniqueIds": ["id:609", "id:610", "id:2027"],
                "&model": "MarketCounterparty",
                "&timestamp": 1704468179134,
                "&user": "system",
                "sinkIdentifiers.tradeFileIdentifiers": [
                    {"id": "609", "label": "id"},
                    {"id": "610", "label": "id"},
                    {"id": "2027", "label": "id"},
                ],
            },
            {
                "&id": "ff0caba6-c2e0-4196-a65c-effa26fb4ca8",
                "&uniqueProps": ["id:856", "id:857", "id:901", "id:902", "id:2157"],
                "&version": 1,
                "name": "MERRILL LYNCH INTERNATIONAL",
                "&key": "MarketCounterparty:ff0caba6-c2e0-4196-a65c-effa26fb4ca8:1704468179134",
                "&hash": "e95897bc43ad2b9a2c3bb8088959348a8b57352a2888d0c5743d401d0fff8174",
                "uniqueIds": ["id:856", "id:857", "id:901", "id:902", "id:2157"],
                "&model": "MarketCounterparty",
                "&timestamp": 1704468179134,
                "&user": "system",
                "sinkIdentifiers.tradeFileIdentifiers": [
                    {"id": "856", "label": "id"},
                    {"id": "857", "label": "id"},
                    {"id": "2157", "label": "id"},
                    {"id": "901", "label": "id"},
                    {"id": "902", "label": "id"},
                ],
            },
            {
                "&id": "ba869618-f2c9-4f2e-ab96-48b5c4825ae7",
                "&uniqueProps": [
                    "id:2676",
                    "id:2677",
                    "id:2678",
                    "id:3796",
                    "id:2000",
                ],
                "&version": 1,
                "name": "UBS AG",
                "&key": "MarketCounterparty:ba869618-f2c9-4f2e-ab96-48b5c4825ae7:1704468179134",
                "&hash": "0670f81dd37a4cfb78e5e90fd422f051bae02939189aeb20bdc972fcf4224bd1",
                "uniqueIds": [
                    "id:2676",
                    "id:2677",
                    "id:2678",
                    "id:3796",
                    "id:2000",
                ],
                "&model": "MarketCounterparty",
                "&timestamp": 1704468179134,
                "&user": "system",
                "sinkIdentifiers.tradeFileIdentifiers": [
                    {"id": "2676", "label": "id"},
                    {"id": "2677", "label": "id"},
                    {"id": "2678", "label": "id"},
                    {"id": "3796", "label": "id"},
                    {"id": "2000", "label": "id"},
                ],
            },
        ]
    )


@pytest.fixture()
def expected_result_creates_and_updates_creates_df() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "issuerLongName": "BNP PARIBAS",
                "allCptyIds": ["1111", "2222", "1989"],
                "cptyPrefixIds": ["id:1111", "id:2222", "id:1989"],
            }
        ]
    )


@pytest.fixture()
def expected_result_creates_and_updates_updates_df() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "&id": "ff0caba6-c2e0-4196-a65c-effa26fb4ca8",
                "&uniqueProps": ["id:901", "id:857", "id:856", "id:902", "id:2157"],
                "&version": 1,
                "name": "MERRILL LYNCH INTERNATIONAL",
                "&key": "MarketCounterparty:ff0caba6-c2e0-4196-a65c-effa26fb4ca8:1704468179134",
                "&hash": "e95897bc43ad2b9a2c3bb8088959348a8b57352a2888d0c5743d401d0fff8174",
                "uniqueIds": ["id:901", "id:857", "id:856", "id:902", "id:2157"],
                "&model": "MarketCounterparty",
                "&timestamp": 1704468179134.0,
                "&user": "system",
                "sinkIdentifiers.tradeFileIdentifiers": [
                    {"id": "856", "label": "id"},
                    {"id": "857", "label": "id"},
                    {"id": "2157", "label": "id"},
                    {
                        "id": "901",
                        "label": "id",
                    },
                    {"id": "902", "label": "id"},
                ],
            },
            {
                "&id": "ba869618-f2c9-4f2e-ab96-48b5c4825ae7",
                "&uniqueProps": ["id:2677", "id:2676", "id:2678", "id:3796", "id:2000"],
                "&version": 1,
                "name": "UBS AG",
                "&key": "MarketCounterparty:ba869618-f2c9-4f2e-ab96-48b5c4825ae7:1704468179134",
                "&hash": "0670f81dd37a4cfb78e5e90fd422f051bae02939189aeb20bdc972fcf4224bd1",
                "uniqueIds": ["id:2677", "id:2676", "id:2678", "id:3796", "id:2000"],
                "&model": "MarketCounterparty",
                "&timestamp": 1704468179134.0,
                "&user": "system",
                "sinkIdentifiers.tradeFileIdentifiers": [
                    {"id": "2676", "label": "id"},
                    {"id": "2677", "label": "id"},
                    {"id": "2678", "label": "id"},
                    {"id": "3796", "label": "id"},
                    {"id": "2000", "label": "id"},
                ],
            },
        ]
    )
