from unittest.mock import patch

import addict
import pandas as pd
import pytest
from prefect.engine import signals
from swarm.conf import SettingsCls
from swarm.task.auditor import Auditor

from swarm_tasks.steeleye.mymarket.firm import filter_out_duplicate_and_invalid_firms
from swarm_tasks.steeleye.mymarket.firm.filter_out_duplicate_and_invalid_firms import (
    Params,
)


@pytest.fixture
def empty_df():
    data = {}
    return pd.DataFrame(data)


@pytest.fixture()
def source_df() -> pd.DataFrame:
    """Source data frame containing 11 records"
    Row 1: is a new record (lei and name not present in Elastic), should end up in target

    Row 2: duplicate row in file (dup of Row 1) based on LEI and NAME and should be dropped
    Row 3: duplicate row in file (dup of Row 1) based on LEI
    Row 4: no name, it should end up in the target

    Row 5: new record and should end up in the target

    Row 6: the LEI is already present in Elastic
    Row 7: the LEI is null, but the NAME is not. It should end up in the target
    Row 8: the LEI and NAME are null. It should be dropped
    """
    df = pd.DataFrame(
        [
            # Row 1: new record
            {"LEI": "lei1", "NAME": "Barclays", "row": 1},
            # Row 2: duplicate of row 1 (NAME and LEI)
            {"LEI": "lei1", "NAME": "Barclays", "row": 2},
            # Row 3: duplicate of row 1 (LEI, but not NAME)
            {"LEI": "lei1", "NAME": "Barclays2", "row": 3},
            # Row 4: null NAME, ends up in target
            {"LEI": "lei3", "NAME": pd.NA, "row": 4},
            # Row 5: new record
            {"LEI": "lei4", "NAME": "Sparkasse", "row": 5},
            # Row 6: LEI present in Elastic
            {"LEI": "lei6", "NAME": "Commerzbank", "row": 6},
            # Row 7: LEI null, NAME not null
            {"LEI": pd.NA, "NAME": "Monoprix", "row": 7},
            # Row 8: LEI null, NAME null
            {"LEI": pd.NA, "NAME": pd.NA, "row": 8},
        ]
    )
    return df


@pytest.fixture()
def source_df_without_null_leis() -> pd.DataFrame:
    """Source data frame containing 2 records, both of which are in Elastic" """
    df = pd.DataFrame(
        [
            # Row 1: new record
            {"LEI": "lei4", "NAME": "Sparkasse", "row": 5},
            # Row 2: LEI present in Elastic
            {"LEI": "lei6", "NAME": "Commerzbank", "row": 6},
        ]
    )
    return df


@pytest.fixture()
def source_df_with_ids() -> pd.DataFrame:
    """Source data frame containing 2 records, one new, one in Elastic" """
    df = pd.DataFrame(
        [
            # Row 1: new record
            {"LEI": "lei4", "NAME": "Sparkasse", "row": 5},
            # Row 2: LEI present in Elastic
            {
                "LEI": "lei6",
                "NAME": "Commerzbank",
                "row": 6,
                "firmwideClientId": "id1",
                "groupwideClientId": "id2",
            },
        ]
    )
    return df


@pytest.fixture()
def expected_result_some_dupes_some_in_elastic() -> pd.DataFrame:
    df = pd.DataFrame(
        [
            # Row 1 in source_df
            {"LEI": "lei1", "NAME": "Barclays", "row": 1},
            # Row 4 in source_df
            {"LEI": "lei3", "NAME": pd.NA, "row": 4},
            # Row 5 in source_df
            {"LEI": "lei4", "NAME": "Sparkasse", "row": 5},
            # Row 7 in source_df
            {"LEI": pd.NA, "NAME": "Monoprix", "row": 7},
        ]
    )
    return df


@pytest.fixture()
def expected_result_with_ids() -> pd.DataFrame:
    df = pd.DataFrame(
        [
            # LEI to create
            {
                "LEI": "lei4",
                "NAME": "Sparkasse",
                "row": 5,
                "firmwideClientId": pd.NA,
                "groupwideClientId": pd.NA,
                "sinkIdentifiers.tradeFileIdentifiers": pd.NA,
                "__to_update__": False,
            },
            # LEI to update
            {
                "LEI": "lei6",
                "NAME": "Commerzbank",
                "row": 6,
                "firmwideClientId": "id1",
                "groupwideClientId": "id2",
                "sinkIdentifiers.tradeFileIdentifiers": [{"id": "id1", "label": "id"}],
                "__to_update__": True,
            },
        ]
    )
    return df


@pytest.fixture()
def expected_result_some_dupes_no_records_in_elastic() -> pd.DataFrame:
    df = pd.DataFrame(
        [
            # Row 1 in source_df
            {"LEI": "lei1", "NAME": "Barclays", "row": 1},
            # Row 4 in source_df
            {"LEI": "lei3", "NAME": pd.NA, "row": 4},
            # Row 5 in source_df
            {"LEI": "lei4", "NAME": "Sparkasse", "row": 5},
            # Row 6 in source_df
            {"LEI": "lei6", "NAME": "Commerzbank", "row": 6},
            # Row 7 in source_df
            {"LEI": pd.NA, "NAME": "Monoprix", "row": 7},
        ]
    )
    return df


@pytest.fixture()
def expected_result_all_leis_in_elastic_one_null_lei() -> pd.DataFrame:
    df = pd.DataFrame(
        [
            # Row 7 in source_df
            {"LEI": pd.NA, "NAME": "Monoprix", "row": 7},
        ]
    )
    return df


@pytest.fixture()
def params_fixture() -> Params:
    params = Params(
        **{
            "source_name_column": "NAME",
            "source_lei_column": "LEI",
            "elastic_lei_lookup_column": "firmIdentifiers.lei",
        }
    )
    return params


class TestFilterOutDuplicateAndInvalidFirms:
    """Test suite for FilterOutDuplicateFirms"""

    def test_empty_source_df(self, mocker, empty_df, params_fixture):
        """Test for the case where the source data frame is empty"""
        task = self._init_task(mocker=mocker, params=params_fixture)
        with pytest.raises(signals.SKIP):
            task.execute(
                source_frame=empty_df,
                params=params_fixture,
            )

    @patch.object(
        filter_out_duplicate_and_invalid_firms,
        "fetch_counterparty_records_from_elastic",
    )
    def test_with_some_duplicate_rows_some_in_elastic(
        self,
        mock_elastic_results,
        mocker,
        source_df,
        expected_result_some_dupes_some_in_elastic,
        params_fixture,
    ):
        """Test for the case where 8 records are present in the input. 2 are duplicates
        in the file, 1 is already present in Elastic and 1 has null NAME and LEI. Expected
        result should have 4 records
        """
        task = self._init_task(mocker=mocker, params=params_fixture)
        mock_elastic_results.return_value = pd.DataFrame(
            {"firmIdentifiers.lei": ["lei5", "lei6"]}
        )
        result = task.execute(
            source_frame=source_df,
            params=params_fixture,
        )

        assert result.equals(expected_result_some_dupes_some_in_elastic)

    @patch.object(
        filter_out_duplicate_and_invalid_firms,
        "fetch_counterparty_records_from_elastic",
    )
    def test_with_all_leis_already_in_elastic_but_with_one_null_lei(
        self,
        mock_elastic_results,
        mocker,
        source_df,
        params_fixture,
        expected_result_all_leis_in_elastic_one_null_lei,
    ):
        """Test for the case where 8 records are present in the input. 2 are duplicates
        in the file, and 1 has null NAME and LEI, and the remaining leis are in elastic
        Only the record with null LEI is present in the output
        """
        task = self._init_task(mocker=mocker, params=params_fixture)
        # All records are in Elastic
        mock_elastic_results.return_value = pd.DataFrame(
            {"firmIdentifiers.lei": ["lei1", "lei3", "lei4", "lei6"]}
        )

        result = task.execute(
            source_frame=source_df,
            params=params_fixture,
        )
        assert result.equals(expected_result_all_leis_in_elastic_one_null_lei)

    @patch.object(
        filter_out_duplicate_and_invalid_firms,
        "fetch_counterparty_records_from_elastic",
    )
    def test_with_all_leis_already_in_elastic(
        self,
        mock_elastic_results,
        mocker,
        source_df_without_null_leis,
        params_fixture,
    ):
        """Test for the case where 2 records are present in the input, and both are present in
        Elastic. A SKIP signal is expected to be raised
        """
        task = self._init_task(mocker=mocker, params=params_fixture)
        # All records are in Elastic
        mock_elastic_results.return_value = pd.DataFrame(
            {"firmIdentifiers.lei": ["lei4", "lei6"]}
        )

        with pytest.raises(signals.SKIP):
            task.execute(
                source_frame=source_df_without_null_leis,
                params=params_fixture,
            )

    @patch.object(
        filter_out_duplicate_and_invalid_firms,
        "fetch_counterparty_records_from_elastic",
    )
    def test_with_no_rows_in_elastic(
        self,
        mock_elastic_results,
        mocker,
        source_df,
        expected_result_some_dupes_no_records_in_elastic,
        params_fixture,
    ):
        """Test for the case where 8 records are present in the input. 2 are duplicates
        in the file, and 1 has null NAME and LEI. Expected result should have 5 records
        """
        task = self._init_task(mocker=mocker, params=params_fixture)

        mock_elastic_results.return_value = pd.DataFrame()

        result = task.execute(
            source_frame=source_df,
            params=params_fixture,
        )
        assert result.equals(expected_result_some_dupes_no_records_in_elastic)

    @patch.object(
        filter_out_duplicate_and_invalid_firms,
        "fetch_counterparty_records_from_elastic",
    )
    def test_with_some_duplicate_rows_some_in_elastic_scenario_2(
        self,
        mock_elastic_results,
        mocker,
        source_df,
        expected_result_some_dupes_some_in_elastic,
    ):
        """Test for some records already present in elastic and also source data
        and elastic result both have a column called 'name'. The target should have the original
        column 'name'.
        """
        params = Params(
            source_name_column="name",
            source_lei_column="lei",
            elastic_lei_lookup_column="firmIdentifiers.lei",
        )
        task = self._init_task(mocker=mocker, params=params)
        # add a column "name" which is also present in source_df
        mock_elastic_results.return_value = pd.DataFrame(
            {"firmIdentifiers.lei": ["lei5", "lei6"], "name": ["name_5", "name_6"]}
        )
        source_df.columns = source_df.columns.str.lower()
        expected_result_some_dupes_some_in_elastic.columns = (
            expected_result_some_dupes_some_in_elastic.columns.str.lower()
        )
        expected_result_some_dupes_some_in_elastic["name_y"] = pd.NA
        result = task.execute(
            source_frame=source_df,
            params=params,
        )
        assert result.equals(expected_result_some_dupes_some_in_elastic)

    @patch.object(
        filter_out_duplicate_and_invalid_firms,
        "fetch_counterparty_records_from_elastic",
    )
    def test_with_same_lei_but_different_ids(
        self,
        mock_elastic_results,
        mocker,
        source_df_with_ids,
        expected_result_with_ids,
        params_fixture,
    ):
        """Test where lei is in ES but not all the IDS match"""
        params_fixture.keep_firms_to_update = True
        task = self._init_task(mocker=mocker, params=params_fixture)

        mock_elastic_results.return_value = pd.DataFrame(
            {
                "firmIdentifiers.lei": ["lei6"],
                "sinkIdentifiers.tradeFileIdentifiers": [
                    [{"id": "id1", "label": "id"}]
                ],
            }
        )

        result = task.execute(
            source_frame=source_df_with_ids,
            params=params_fixture,
        )
        assert result.equals(expected_result_with_ids)

    @staticmethod
    def _init_task(mocker, params):
        mock_connections = mocker.patch.object(
            SettingsCls, "connections", new_callable=mocker.PropertyMock
        )
        mock_connections.return_value = addict.Dict(
            {
                "tenant-data": {
                    "meta": {
                        "id": "&id",
                        "model": "&model",
                        "hash": "&hash",
                        "prefix": "&",
                        "key": "&key",
                    },
                    "MAX_TERMS_SIZE": 1024,
                },
                "reference-data": {
                    "meta": {
                        "id": "&id",
                        "model": "&model",
                        "hash": "&hash",
                        "prefix": "&",
                        "key": "&key",
                    },
                    "MAX_TERMS_SIZE": 1024,
                },
            }
        )
        mock_auditor = mocker.patch.object(
            filter_out_duplicate_and_invalid_firms.FilterOutDuplicateAndInvalidFirms,
            "auditor",
        )
        mock_auditor.return_value = Auditor(
            task_name="FilterOutDuplicateAndInvalidFirms"
        )
        task = filter_out_duplicate_and_invalid_firms.FilterOutDuplicateAndInvalidFirms(
            name="FilterOutDuplicateAndInvalidFirms", params=params
        )

        return task
