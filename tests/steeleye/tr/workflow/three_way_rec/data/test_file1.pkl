���      �pandas.core.frame��	DataFrame���)��}�(�_data��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�__reconciliation.nca.sourceKey��__reportDetails.reportStatus��__Id��&id��&key��&hash��&model��transactionDetails.venue�� transactionDetails.priceCurrency��transactionDetails.quantity��"transactionDetails.tradingCapacity��transactionDetails.price��#transactionDetails.quantityNotation��"transactionDetails.tradingDateTime�� transactionDetails.priceNotation��=tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator��workflow.isReported��6instrumentDetails.instrument.ext.underlyingInstruments��5instrumentDetails.instrument.isCreatedThroughFallback��/instrumentDetails.instrument.instrumentFullName��;instrumentDetails.instrument.derivative.underlyingIndexTerm��4instrumentDetails.instrument.derivative.deliveryType��7instrumentDetails.instrument.derivative.priceMultiplier��.instrumentDetails.instrument.notionalCurrency1��5instrumentDetails.instrument.instrumentClassification��reportDetails.reportStatus��reportDetails.transactionRefNo��
parties.buyer��#isCreatedThroughReconciliation.flag�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C        �t�bhC�__swarm_raw_index__�u��R�e]�(hhK ��h��R�(KKK��h!�]�(�n/Users/<USER>/Desktop/se-data/IAPS-35-V2/GB_EXTTRA_I213800WC2DNEK5NLOB97_45573-01Z01_21.122528490881001.xml��NEWT��122528490881001��122528490881001:2021-06-30:NEWT��@RTS22Transaction:122528490881001:2021-06-30:NEWT:1626951728384.0��@db233d12e67e6a525dcb5483a8cae32d7a6f1d2c4cfd7d63632052ceb9d3167d��RTS22Transaction��XXXX��USD��DEAL��UNIT��2021-06-30T00:02:01Z��MONE��false�]�}��
derivative�}�(�underlyingIndexName��Nasdaq 100 Index��underlyingIndexTerm��1DAYS�usa�US TECH 100 ROLLING BUX CFD��1DAYS��CASH��USD��JEIXCC��NEWT��122528490881001�]�}�(�name��MATTHIEU DRIESSE��personalDetails�}�(�	firstName��MATTHIEU��lastName��DRIESSE��dob��
1970-09-02�u�retailOrProfessional��N/A�h)�-MarketPerson:afdbbc55b3a7807673d29ee5531450ac��officialIdentifiers�}�(�mifirIdSubType��CCPT��mifirIdType��N��mifirId��NLIGB343DJ9��
branchCountry��GB�uua�pandas._libs.missing��NA���et�bhhK ��h��R�(KKK��h�f8�����R�(KhQNNNJ����J����K t�b�C��G�}�j?=
ף���@      4@�t�bhhK ��h��R�(KKK��h�b1�����R�(Kh"NNNJ����J����K t�b�C  �t�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h'h(h)h*h+h,h-h/h1h2h3h5h6h8h9h:h<h=h>h?h@hAet�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h.h0h;et�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h4h7et�bhCNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h[�mgr_locs�hhK ��h��R�(KK��hP�C�                                                                
              
                                                                                           �t�bu}�(h�h�h�hhK ��h��R�(KK��hP�C	                     �t�bu}�(h�h�hЌbuiltins��slice���KKK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.