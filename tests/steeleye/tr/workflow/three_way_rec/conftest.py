import pandas as pd
import pytest
from addict import Dict

from swarm_tasks.steeleye.tr.static import ARMReport
from swarm_tasks.steeleye.tr.static import NCAReport
from swarm_tasks.steeleye.tr.static import RecFields
from swarm_tasks.steeleye.tr.static import RTS22Transaction
from swarm_tasks.steeleye.tr.static import StaticValues


@pytest.fixture()
def tenant_config_w_tr_pi_enrichment_enabled_false() -> Dict:
    return Dict({"trPIEnrichmentEnabled": False})


@pytest.fixture()
def se_transaction_details_quantity_notation_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            RTS22Transaction.TRANSACTION_DETAILS_QUANTITY_NOTATION: [
                "UNIT",
                "MONE",
                pd.NA,
                "NOML",
                "MONE",
                "UNIT",
            ]
        }
    )
    return df


@pytest.fixture()
def se_parse_price_source_frame_df() -> pd.DataFrame:
    return pd.DataFrame(
        {
            RTS22Transaction.TRANSACTION_DETAILS_PRICE: ["1.11111111111111111111111111"]
            * 4,
            RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOTATION: [
                "MONE",
                "PERC",
                "YIEL",
                "BAPO",
            ],
        }
    )


@pytest.fixture()
def arm_transaction_details_quantity_notation_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            ARMReport.QUANTITY_TYPE: [
                "UNIT_VAL",
                "MONETARYVALUE",
                pd.NA,
                "NOMINALVALUE",
                "MONE",
                "UNIT",
            ]
        }
    )
    return df


@pytest.fixture()
def se_transaction_details_strike_price_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_EXT_STRIKE_PRICE_TYPE: [
                "Yld",
                "PCTG",
                "Yld",
                "MntryVal",
                "BSISPTS",
            ]
        }
    )
    return df


@pytest.fixture()
def arm_transaction_details_strike_price_type_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            ARMReport.STRIKE_PRICE_TYPE: [
                "Pctg",
                "Pctg",
                "Yld",
                "MntryValAmt",
                "BsisPts",
            ]
        }
    )
    return df


@pytest.fixture()
def se_transaction_details_price_notation_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOTATION: [
                StaticValues.MONE,
                StaticValues.YIEL,
                pd.NA,
                StaticValues.PERC,
                StaticValues.BAPO,
                StaticValues.BAPO,
            ],
            RTS22Transaction.TRANSACTION_DETAILS_PRICE: [5, 20, pd.NA, 3, 50, 10],
        }
    )
    return df


@pytest.fixture()
def arm_transaction_details_price_notation_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            ARMReport.PRICE_TYPE: [
                "MONEY",
                StaticValues.YLD,
                pd.NA,
                StaticValues.PCTG,
                StaticValues.BSISPTS,
                StaticValues.BSISPTS,
            ]
        }
    )
    return df


@pytest.fixture()
def se_transaction_details_price_pending_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            RTS22Transaction.TRANSACTION_DETAILS_PRICE_PENDING: [
                True,
                False,
                False,
                pd.NA,
                True,
            ],
            RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOT_APPLICABLE: [
                False,
                True,
                False,
                pd.NA,
                False,
            ],
        }
    )
    return df


@pytest.fixture()
def arm_transaction_details_price_pending_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            ARMReport.PRICE_TYPE: [
                StaticValues.PRICE_PENDING,
                StaticValues.PRICE_NOT_AVAILABLE,
                "NOT_POPULATED",
                pd.NA,
                StaticValues.PRICE_NOT_AVAILABLE,
            ]
        }
    )
    return df


@pytest.fixture()
def nca_transaction_details_price_pending_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            NCAReport.TX_PRIC_NOPRIC_PDG: [
                StaticValues.PNDG,
                StaticValues.NOAP,
                False,
                pd.NA,
                StaticValues.NOAP,
            ]
        }
    )
    return df


@pytest.fixture()
def arm_seller_id() -> pd.DataFrame:
    df = pd.DataFrame({ARMReport.SELLER_ID: ["ID1", "LEI2", "ID3", "LEI4", "ID5"]})
    return df


@pytest.fixture()
def se_parties_seller_id() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            RTS22Transaction.PARTIES_SELLER: [
                [
                    {
                        "officialIdentifiers": {"mifirId": "ID1"},
                        "&key": "AccountPerson:id:timestamp",
                    }
                ],
                [
                    {
                        "firmIdentifiers": {"lei": "LEI2"},
                        "&key": "MarketCounterparty:id:timestamp",
                    }
                ],
                [
                    {
                        "officialIdentifiers": {"mifirId": "ID3"},
                        "&key": "MarketPerson:id:timestamp",
                    }
                ],
                [
                    {
                        "firmIdentifiers": {"lei": "LEI4"},
                        "&key": "AccountFirm:id:timestamp",
                    }
                ],
                [
                    {
                        "officialIdentifiers": {"mifirId": "ID5"},
                        "&key": "AccountPerson:id:timestamp",
                    }
                ],
            ]
        }
    )
    return df


@pytest.fixture()
def nca_transaction_details_quantity_notation_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            NCAReport.TX_QTY_UNIT: ["5", pd.NA, pd.NA, pd.NA, pd.NA],
            NCAReport.TX_QTY_NMNLVAL: [pd.NA, pd.NA, pd.NA, "3", pd.NA],
            NCAReport.TX_QTY_MNTRYVAL: [pd.NA, "20", pd.NA, pd.NA, "50"],
        }
    )
    return df


@pytest.fixture()
def nca_transaction_details_price_notation_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            NCAReport.TX_PRIC_PRIC_MNTRYVAL_AMT: [
                "5",
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
                "10",
            ],
            NCAReport.TX_PRIC_PRIC_PCTG: [pd.NA, pd.NA, pd.NA, "3", pd.NA, pd.NA],
            NCAReport.TX_PRIC_PRIC_YLD: [pd.NA, "20", pd.NA, pd.NA, pd.NA, pd.NA],
            NCAReport.TX_PRIC_PRIC_BSISPTS: [pd.NA, pd.NA, pd.NA, pd.NA, "50", pd.NA],
        }
    )
    return df


@pytest.fixture()
def nca_transaction_details_quantity_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            NCAReport.TX_QTY_UNIT: ["5", pd.NA, pd.NA, pd.NA, pd.NA],
            NCAReport.TX_QTY_NMNLVAL: [pd.NA, pd.NA, pd.NA, "3.001", pd.NA],
            NCAReport.TX_QTY_MNTRYVAL: [
                pd.NA,
                "20.0423455",
                pd.NA,
                pd.NA,
                "500000000000000",
            ],
        }
    )
    return df


@pytest.fixture()
def se_transaction_details_quantity_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            RTS22Transaction.TRANSACTION_DETAILS_QUANTITY: [
                5.000,
                "20.0423455",
                pd.NA,
                3.001,
                500000000000000,
            ]
        }
    )
    return df


@pytest.fixture()
def nca_execution_within_firm_country_of_branch_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            NCAReport.EXCTGPRSN_ALGO: ["AMTH01", pd.NA, pd.NA, "AMTH01", pd.NA],
            NCAReport.EXCTGPRSN_PRSN_CTRYOFBRNCH: [pd.NA, "ES", "PT", "IT", pd.NA],
        }
    )
    return df


@pytest.fixture()
def se_execution_within_firm_country_of_branch_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            RTS22Transaction.PARTIES_EXECUTION_WITHIN_FIRM_OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY: [
                "GB",
                "ES",
                "PT",
                "US",
                pd.NA,
            ],
        }
    )
    return df


@pytest.fixture()
def nca_firm_execution_id_df() -> pd.DataFrame:
    df = pd.DataFrame({NCAReport.EXCTGPRSN_CLNT: ["NORE", pd.NA, "NORE"]})
    return df


@pytest.fixture()
def arm_firm_execution_id_df() -> pd.DataFrame:
    df = pd.DataFrame({ARMReport.FIRM_EXECUTION_ID: ["NORE", pd.NA, "NORE"]})
    return df


@pytest.fixture()
def se_firm_execution_id_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            RTS22Transaction.MARKET_IDENTIFIERS: [
                [
                    {
                        "path": "parties.executionWithinFirm",
                        "labelId": "clnt:nore",
                        "type": "OBJECT",
                    },
                    {
                        "path": "parties.trader",
                        "labelId": "account:random name",
                        "type": "ARRAY",
                    },
                ],
                [
                    {
                        "path": "parties.executionWithinFirm",
                        "labelId": "clnt:nore",
                        "type": "OBJECT",
                    },
                ],
                [
                    {
                        "path": "parties.executionWithinFirm",
                        "labelId": "wrong",
                        "type": "OBJECT",
                    },
                ],
            ]
        }
    )
    return df


@pytest.fixture()
def arm_ul_index_name_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            ARMReport.UNDERLYING_INDEX_NAME: ["RIGHT", "RIGHT", "RIGHT", "RIGHT"],
        }
    )
    return df


@pytest.fixture()
def nca_ul_index_name_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_OTHR_SNGL_INDX_NM_REFRATE_NM: [
                "RIGHT",
                "RIGHT",
                "RIGHT",
                "RIGHT",
            ],
        }
    )
    return df


@pytest.fixture()
def se_ul_index_name_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_UL_INDEX_NAME: [
                "RIGHT",
                pd.NA,
                pd.NA,
                pd.NA,
            ],
            RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_EXT_UL_INSTRUMENTS: [
                pd.NA,
                [{"ext": {"underlyingIndexName": "RIGHT"}}],
                pd.NA,
                [{"derivative": {"underlyingIndexName": "WRONG"}}],
            ],
        }
    )
    return df


@pytest.fixture()
def arm_ul_index_name_empty_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            ARMReport.UNDERLYING_INDEX_NAME: [pd.NA, pd.NA, pd.NA, pd.NA],
        }
    )
    return df


@pytest.fixture()
def nca_ul_index_name_empty_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            NCAReport.FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_OTHR_SNGL_INDX_NM_REFRATE_NM: [
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
            ],
        }
    )
    return df


@pytest.fixture()
def se_ul_inst_partial_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_UL_INSTRUMENTS: [
                pd.NA,
                [{"underlyingInstrumentCode": "RIGHT"}],
                pd.NA,
                [{"underlyingInstrumentCode": "WRONG"}],
            ]
        }
    )
    return df


@pytest.fixture()
def se_ul_inst_name_partial_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_UL_INDEX_NAME: [
                pd.NA,
                "FOO",
                pd.NA,
                "BAR",
            ]
        }
    )
    return df


@pytest.fixture()
def se_validate_strike_price_type_custom_mapping_series() -> pd.Series:
    series = pd.Series(
        [
            pd.NaT,
            pd.NaT,
            pd.NaT,
            pd.NaT,
            pd.NaT,
            pd.NaT,
        ]
    )
    return series


@pytest.fixture()
def se_validate_strike_price_type_series() -> pd.Series:
    series = pd.Series(
        [
            pd.NA,
            pd.NA,
            "MntryVal",
            "Yld",
            pd.NA,
            pd.NA,
        ]
    )
    return series


@pytest.fixture()
def target_df_with_no_field_breaks_na_df() -> pd.DataFrame:
    data = pd.DataFrame(
        data={
            "&id": ["id1", "id2", "id3"],
            "&model": ["RTS22Transaction", "RTS22Transaction", "RTS22Transaction"],
            "&hash": ["hash1", "hash2", "hash3"],
            RecFields.FIELD_BREAKS: [pd.NA, pd.NA, pd.NA],
            RecFields.RECONCILIATION_NCA_FIELD_BREAK: [False, False, False],
            RecFields.TEMP_RECONCILIATION_NCA_SOURCE_KEY: [
                "test_file.xml",
                "test_file.xml",
                "test_file.xml",
            ],
        }
    )
    return data


@pytest.fixture()
def target_df_with_no_field_breaks_list_df() -> pd.DataFrame:
    data = pd.DataFrame(
        data={
            "&id": ["id1", "id2", "id3"],
            "&model": ["RTS22Transaction", "RTS22Transaction", "RTS22Transaction"],
            "&hash": ["hash1", "hash2", "hash3"],
            RecFields.FIELD_BREAKS: [[], [], []],
            RecFields.RECONCILIATION_ARM_FIELD_BREAK: [False, False, False],
            RecFields.TEMP_RECONCILIATION_NCA_SOURCE_KEY: [
                "test_file.xml",
                "test_file.xml",
                "test_file.xml",
            ],
        }
    )
    return data


@pytest.fixture()
def target_df_with_nca_field_breaks_df() -> pd.DataFrame:
    data = pd.DataFrame(
        data={
            "&id": [
                "id1",
                "id2",
            ],
            "&model": [
                "RTS22Transaction",
                "RTS22Transaction",
            ],
            "&hash": [
                "hash1",
                "hash2",
            ],
            RecFields.FIELD_BREAKS: [
                [
                    {
                        "field": "transactionDetails.quantity",
                        "value": {"se": 1, "nca": 2},
                    }
                ],
                [
                    {
                        "field": "transactionDetails.price",
                        "value": {"se": 100, "nca": 101},
                    }
                ],
            ],
            RecFields.RECONCILIATION_ARM_FIELD_BREAK: [False, True],
            RecFields.TEMP_RECONCILIATION_NCA_SOURCE_KEY: [
                "test_file.xml",
                "test_file.xml",
            ],
        }
    )
    return data


@pytest.fixture()
def target_df_with_arm_field_breaks_df() -> pd.DataFrame:
    data = pd.DataFrame(
        data={
            "&id": [
                "id1",
                "id2",
            ],
            "&model": [
                "RTS22Transaction",
                "RTS22Transaction",
            ],
            "&hash": [
                "hash1",
                "hash2",
            ],
            RecFields.FIELD_BREAKS: [
                [
                    {
                        "field": "transactionDetails.quantity",
                        "value": {"se": 1, "arm": 2},
                    }
                ],
                [
                    {
                        "field": "transactionDetails.price",
                        "value": {"se": 100, "arm": 101},
                    }
                ],
            ],
            RecFields.RECONCILIATION_ARM_FIELD_BREAK: [True, False],
            RecFields.TEMP_RECONCILIATION_NCA_SOURCE_KEY: [
                "test_file.xml",
                "test_file.xml",
            ],
        }
    )
    return data


@pytest.fixture()
def target_df_with_both_nca_and_arm_field_breaks_df() -> pd.DataFrame:
    data = pd.DataFrame(
        data={
            "&id": ["id1", "id2", "id3"],
            "&model": ["RTS22Transaction", "RTS22Transaction", "RTS22Transaction"],
            "&hash": ["hash1", "hash2", "hash3"],
            RecFields.FIELD_BREAKS: [
                [
                    {
                        "field": "transactionDetails.quantity",
                        "value": {"se": 1, "nca": 2},
                    }
                ],
                [
                    {
                        "field": "transactionDetails.price",
                        "value": {"se": 100, "arm": 101},
                    }
                ],
                [
                    {
                        "field": "reportDetails.transactionRefNo",
                        "value": {"se": 1, "nca": 2, "arm": 3},
                    }
                ],
            ],
            RecFields.RECONCILIATION_ARM_FIELD_BREAK: [False, False, False],
            RecFields.TEMP_RECONCILIATION_NCA_SOURCE_KEY: [
                "test_file.xml",
                "test_file.xml",
                "test_file.xml",
            ],
        }
    )
    return data
