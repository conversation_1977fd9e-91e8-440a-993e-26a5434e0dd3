import pandas as pd

from swarm_tasks.steeleye.tr.workflow.three_way_rec.arm_unavista_report_mechanism_reconciliation import (
    ArmUnavistaReportMechanismReconciliation,
)


class TestBuildArmTransactions:
    def test_transaction_details_quantity_notation(
        self,
        arm_transaction_details_quantity_notation_df: pd.DataFrame,
    ):
        target = pd.DataFrame(index=arm_transaction_details_quantity_notation_df.index)
        task = ArmUnavistaReportMechanismReconciliation()
        task.populate_additional_custom_fields(
            source_data=arm_transaction_details_quantity_notation_df, target=target
        )

        # Note that unlike NCA, which infers the enum for quantityNotation based on certain fields being populated,
        # the ARM logic uses the values directly from the report
        assert list(target["transactionDetails.quantityNotation"].values) == [
            "UNIT_VAL",
            "MONE",
            pd.NA,
            "NOML",
            "MON<PERSON>",
            "UNIT",
        ]
