import pandas as pd
from addict import addict
from addict import Dict

from swarm_tasks.steeleye.tr.static import RecFields
from swarm_tasks.steeleye.tr.static import RTS22Transaction
from swarm_tasks.steeleye.tr.workflow.three_way_rec.nca_fca_report_mechanism_reconciliation import (
    NcaFcaReportMechanismReconciliation,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.reconcile_report_transactions import (
    Params as ReconRptTrParams,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.reconcile_report_transactions import (
    ReconcileReportTransactions,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.enums import ReportTypeEnum
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.report_mechanism_mappings import (
    REPORT_MECHANISM_MAP,
)


class TestReconcileNCATransactions:
    def test_transaction_details_quantity_notation(
        self,
        se_transaction_details_quantity_notation_df: pd.DataFrame,
        nca_transaction_details_quantity_notation_df: pd.DataFrame,
    ):
        df = pd.concat(
            [
                se_transaction_details_quantity_notation_df,
                nca_transaction_details_quantity_notation_df,
            ],
            axis=1,
        )
        target = pd.DataFrame(index=df.index)
        target.loc[:, RecFields.FIELD_BREAKS] = pd.NA
        target.loc[:, RecFields.RECONCILIATION_NCA_FIELD_BREAK] = False

        task = NcaFcaReportMechanismReconciliation()
        task.reconcile_compound_general_fields(data_to_reconcile=df, target=target)

        breaks = list(target.loc[:, "reconciliation.fieldBreaks"].values)
        fields_breaking = set(
            [
                [list_item["field"] for list_item in x][0]
                for x in breaks
                if not pd.isna(x)
            ]
        )

        assert (
            RTS22Transaction.TRANSACTION_DETAILS_QUANTITY_NOTATION
            not in fields_breaking
        )

    def test_transaction_details_price_notation(
        self,
        se_transaction_details_price_notation_df: pd.DataFrame,
        nca_transaction_details_price_notation_df: pd.DataFrame,
    ):
        df = pd.concat(
            [
                se_transaction_details_price_notation_df,
                nca_transaction_details_price_notation_df,
            ],
            axis=1,
        )
        target = pd.DataFrame(index=df.index)
        target.loc[:, RecFields.FIELD_BREAKS] = pd.NA
        target.loc[:, RecFields.RECONCILIATION_NCA_FIELD_BREAK] = False

        task = NcaFcaReportMechanismReconciliation()
        task.reconcile_compound_general_fields(data_to_reconcile=df, target=target)

        breaks = list(target.loc[:4, "reconciliation.fieldBreaks"].values)
        fields_breaking = set(
            [
                [list_item["field"] for list_item in x][0]
                for x in breaks
                if not pd.isna(x)
            ]
        )

        assert (
            RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOTATION not in fields_breaking
        )

    def test_transaction_details_price_pending_not_applicable(
        self,
        se_transaction_details_price_pending_df: pd.DataFrame,
        nca_transaction_details_price_pending_df: pd.DataFrame,
    ):
        df = pd.concat(
            [
                se_transaction_details_price_pending_df,
                nca_transaction_details_price_pending_df,
            ],
            axis=1,
        )
        target = pd.DataFrame(index=df.index)
        target.loc[:, RecFields.FIELD_BREAKS] = pd.NA
        target.loc[:, RecFields.RECONCILIATION_NCA_FIELD_BREAK] = False

        task = NcaFcaReportMechanismReconciliation()
        task.reconcile_custom_general_fields(
            data_to_reconcile=df,
            target=target,
            tenant_configuration=addict.Dict({"trPIEnrichmentEnabled": False}),
        )

        na_breaks = list(target.loc[:3, "reconciliation.fieldBreaks"].values)
        fields_not_breaking = set(
            [
                [list_item["field"] for list_item in x][0]
                for x in na_breaks
                if not pd.isna(x)
            ]
        )

        assert (
            RTS22Transaction.TRANSACTION_DETAILS_PRICE_PENDING
            not in fields_not_breaking
        )
        assert (
            RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOT_APPLICABLE
            not in fields_not_breaking
        )

        breaks = list(target.loc[4, "reconciliation.fieldBreaks"])
        fields_breaking = set([list_item["field"] for list_item in breaks])

        assert RTS22Transaction.TRANSACTION_DETAILS_PRICE_PENDING in fields_breaking
        assert (
            RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOT_APPLICABLE in fields_breaking
        )

        assert breaks[0][
            "field"
        ] == RTS22Transaction.TRANSACTION_DETAILS_PRICE_PENDING and breaks[0][
            "value"
        ] == {
            "nca": "false",
            "se": "true",
        }

        assert breaks[1][
            "field"
        ] == RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOT_APPLICABLE and breaks[1][
            "value"
        ] == {
            "nca": "true",
            "se": "false",
        }

    def test_execution_within_firm_country_of_branch(
        self,
        se_execution_within_firm_country_of_branch_df: pd.DataFrame,
        nca_execution_within_firm_country_of_branch_df: pd.DataFrame,
    ):
        df = pd.concat(
            [
                se_execution_within_firm_country_of_branch_df,
                nca_execution_within_firm_country_of_branch_df,
            ],
            axis=1,
        )
        target = pd.DataFrame(index=df.index)
        target.loc[:, RecFields.FIELD_BREAKS] = pd.NA

        task = NcaFcaReportMechanismReconciliation()
        task.reconcile_custom_general_fields(
            data_to_reconcile=df,
            target=target,
            tenant_configuration=addict.Dict({"trPIEnrichmentEnabled": False}),
        )

        breaks = list(target.loc[:, "reconciliation.fieldBreaks"].values)

        assert len(set(breaks)) == 1

        # For the referenced fixtures, it must not have field breaks
        assert pd.isna(breaks[0])

    def test_transaction_details_quantity(
        self,
        se_transaction_details_quantity_df: pd.DataFrame,
        nca_transaction_details_quantity_df: pd.DataFrame,
    ):
        df = pd.concat(
            [se_transaction_details_quantity_df, nca_transaction_details_quantity_df],
            axis=1,
        )
        target = pd.DataFrame(index=df.index)
        target.loc[:, RecFields.FIELD_BREAKS] = pd.NA

        task = NcaFcaReportMechanismReconciliation()
        task.reconcile_compound_general_fields(data_to_reconcile=df, target=target)

        breaks = list(target.loc[:, "reconciliation.fieldBreaks"].values)

        fields_breaking = set(
            [
                [list_item["field"] for list_item in x][0]
                for x in breaks
                if not pd.isna(x)
            ]
        )

        assert RTS22Transaction.TRANSACTION_DETAILS_QUANTITY not in fields_breaking

    def test_firm_execution_id(
        self,
        se_firm_execution_id_df: pd.DataFrame,
        nca_firm_execution_id_df: pd.DataFrame,
        tenant_config_w_tr_pi_enrichment_enabled_false: Dict,
    ):
        df = pd.concat(
            [se_firm_execution_id_df, nca_firm_execution_id_df],
            axis=1,
        )
        target = pd.DataFrame(index=df.index)
        target.loc[:, RecFields.FIELD_BREAKS] = pd.NA

        task = NcaFcaReportMechanismReconciliation()
        task.reconcile_compound_party_fields(
            data_to_reconcile=df,
            target=target,
            tenant_configuration=tenant_config_w_tr_pi_enrichment_enabled_false,
        )

        breaks = list(target.loc[:, "reconciliation.fieldBreaks"].values)

        fields_breaking = set(
            [
                [list_item["field"] for list_item in x][0]
                for x in breaks
                if not pd.isna(x)
            ]
        )

        # Assert first row of df does not have field breaks, and the other rows do
        assert breaks.count(pd.NA) == 1
        assert breaks[0] is pd.NA

        assert (
            RTS22Transaction.MARKET_IDENTIFIERS_PARTIES_EXECUTION_WITHIN_FIRM
            and RTS22Transaction.PARTIES_EXECUTION_WITHIN_FIRM_MIFIR_ID
            in fields_breaking
        )

    def test_ul_index_name(
        self,
        se_ul_index_name_df: pd.DataFrame,
        nca_ul_index_name_df: pd.DataFrame,
        tenant_config_w_tr_pi_enrichment_enabled_false: Dict,
    ):
        df = pd.concat(
            [se_ul_index_name_df, nca_ul_index_name_df],
            axis=1,
        )
        target = pd.DataFrame(index=df.index)
        target.loc[:, RecFields.FIELD_BREAKS] = pd.NA

        task = ReconcileReportTransactions(
            name="ReconcileReportTransactions",
            params=ReconRptTrParams(**{"report_type": ReportTypeEnum.NCA_FCA.value}),
        )
        task.report_mechanism_reconciliation = REPORT_MECHANISM_MAP[
            ReportTypeEnum.NCA_FCA.value
        ]()
        task._reconcile_underlying_instruments_mapping_fields(
            data_to_reconcile=df,
            target=target,
        )

        breaks = list(target.loc[:, "reconciliation.fieldBreaks"].values)

        fields_breaking = set(
            [
                [list_item["field"] for list_item in x][0]
                for x in breaks
                if not pd.isna(x)
            ]
        )

        assert breaks.count(pd.NA) == 2
        assert breaks[0] is pd.NA
        assert breaks[1] is pd.NA

        assert (
            RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_UL_INDEX_NAME
            and f"{RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_EXT_UL_INSTRUMENTS}[].{RTS22Transaction.DERIVATIVE_UDERLYING_INDEX_NAME}"
            in fields_breaking
        )

    def test_ul_inst_partial(
        self,
        se_ul_inst_partial_df: pd.DataFrame,
        nca_ul_index_name_empty_df: pd.DataFrame,
        tenant_config_w_tr_pi_enrichment_enabled_false: Dict,
    ):
        df = pd.concat(
            [se_ul_inst_partial_df, nca_ul_index_name_empty_df],
            axis=1,
        )
        target = pd.DataFrame(index=df.index)
        target.loc[:, RecFields.FIELD_BREAKS] = pd.NA

        task = ReconcileReportTransactions(
            name="ReconcileReportTransactions",
            params=ReconRptTrParams(**{"report_type": ReportTypeEnum.NCA_FCA.value}),
        )
        task.report_mechanism_reconciliation = REPORT_MECHANISM_MAP[
            ReportTypeEnum.NCA_FCA.value
        ]()
        task._reconcile_underlying_instruments_mapping_fields(
            data_to_reconcile=df,
            target=target,
        )

        breaks = list(target.loc[:, "reconciliation.fieldBreaks"].values)

        fields_breaking = set(
            [
                [list_item["field"] for list_item in x][0]
                for x in breaks
                if not pd.isna(x)
            ]
        )

        assert breaks.count(pd.NA) == 4
        assert breaks[0] is pd.NA
        assert breaks[1] is pd.NA
        assert breaks[2] is pd.NA
        assert breaks[3] is pd.NA

        assert fields_breaking == set()

    def test_nca_dates_only_field_break(self):
        """
        When only a datetime field break is found, it is dropped.
        In such cases, the fieldbreak indicator must be set to False.
        :return:
        """
        se_series = pd.Series(["2021-06-30T00:02:01Z"])
        report_series = pd.Series(["2021-06-30T00:02:01.000000Z"])
        steeleye_field_names = pd.Series(
            [
                "transactionDetails.tradingDateTime|Document.FinInstrmRptgTxRpt.Tx.New.Tx.TradDt"
            ]
        )
        df = pd.concat(
            [se_series, report_series],
            axis=1,
        )
        target = pd.DataFrame(index=df.index)
        target.loc[:, RecFields.FIELD_BREAKS] = pd.NA
        task = NcaFcaReportMechanismReconciliation()
        task.compare_report_and_steeleye_fields(
            report_series=report_series,
            steeleye_series=se_series,
            target=target,
            steeleye_field_names=steeleye_field_names,
        )
        assert target[RecFields.RECONCILIATION_NCA_FIELD_BREAK][0] is False

    def test_ul_index_name_partial(
        self,
        se_ul_inst_name_partial_df: pd.DataFrame,
        nca_ul_index_name_empty_df: pd.DataFrame,
        tenant_config_w_tr_pi_enrichment_enabled_false: Dict,
    ):
        df = pd.concat(
            [se_ul_inst_name_partial_df, nca_ul_index_name_empty_df],
            axis=1,
        )
        target = pd.DataFrame(index=df.index)
        target.loc[:, RecFields.FIELD_BREAKS] = pd.NA

        task = ReconcileReportTransactions(
            name="ReconcileReportTransactions",
            params=ReconRptTrParams(**{"report_type": ReportTypeEnum.NCA_FCA.value}),
        )
        task.report_mechanism_reconciliation = REPORT_MECHANISM_MAP[
            ReportTypeEnum.NCA_FCA.value
        ]()
        task._reconcile_underlying_instruments_mapping_fields(
            data_to_reconcile=df,
            target=target,
        )

        breaks = list(target.loc[:, "reconciliation.fieldBreaks"].values)

        fields_breaking = set(
            [
                [list_item["field"] for list_item in x][0]
                for x in breaks
                if not pd.isna(x)
            ]
        )

        assert breaks.count(pd.NA) == 4
        assert breaks[0] is pd.NA
        assert breaks[1] is pd.NA
        assert breaks[2] is pd.NA
        assert breaks[3] is pd.NA

        assert fields_breaking == set()
