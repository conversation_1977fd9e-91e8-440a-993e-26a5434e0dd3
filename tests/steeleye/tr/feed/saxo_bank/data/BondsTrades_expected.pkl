��R      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�dataSourceName��date��__meta_model__��,reportDetails.investmentFirmCoveredDirective��reportDetails.reportStatus��sourceIndex��	sourceKey��=tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator��#transactionDetails.buySellIndicator��transactionDetails.netAmount��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime��transactionDetails.venue�� transactionDetails.ultimateVenue��.transmissionDetails.orderTransmissionIndicator��reportDetails.transactionRefNo��__asset_class__��__currency__��	__venue__��marketIdentifiers.instrument��marketIdentifiers.parties��marketIdentifiers��__expiry_date__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C@                                                         �t�bhC�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�False�hhhhhhhhhhhhhhet�b�_dtype�hY�StringDtype���)��ubhhK ��h��R�(KKK��h�b1�����R�(Kh"NNNJ����J����K t�b�C        �t�bhhK ��h��R�(KKK��h�f8�����R�(KhQNNNJ����J����K t�b�C�ףp���.Aףp���.Aףp���.Aq=
��.A���SA�G����.A=
ף��.Aףp=ʶ.A��ъRiY@/O�RiY@��ъRiY@������X@��Y�Y@���3�Y@���
S�Y@sx�EǅY@    ��.A    ��.A    ��.A    ��.A    �SA    ��.A    ��.A    ��.A�t�bhhK ��h��R�(KKK��hP�C@                                                         �t�bhhK ��h��R�(KKK��h!�]�(�Saxobank�h�h�h�h�h�h�h��
2022-01-14��
2022-01-18��
2022-01-18��
2022-02-08��
2022-04-04��
2022-04-21��
2022-04-29��
2022-05-04��RTS22Transaction�h�h�h�h�h�h�h��NEWT�h�h�h�h�h�h�h��w/Users/<USER>/Desktop/steeleye/se-prefect-dir/swarm-tasks/tests/steeleye/tr/feed/saxo_bank/data/BondsTrades_test.pkl�h�h�h�h�h�h�h��BUYI�h��SELL�h�h�h�h�h��USD�h�h�h�h��EUR�h�h��PERC�h�h�h�h�h�h�h��USD�h�h�h�h��EUR�h�h��MONE�h�h�h�h�h�h�h��AOTC�h�h�h�h�h�h�h��2022-01-14T13:52:22.387000Z��2022-01-18T04:24:23.200000Z��2022-01-18T04:24:22.950000Z��2022-02-08T16:19:56.757000Z��2022-04-04T13:11:07.230000Z��2022-04-21T07:00:17.343000Z��2022-04-29T07:08:24.023000Z��2022-05-04T07:49:04.807000Z��XOFF�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��$51026391962022011413522220220114BUYI��$51043118222022011804242320220118BUYI��$51043118212022011804242220220118SELL��$51331569382022020816195620220208BUYI��$51920514062022040413110720220404BUYI��$52072255972022042107001720220421BUYI��$52150056072022042907082420220429BUYI��$52189781252022050407490420220504BUYI��bond�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��pandas._libs.missing��NA���h�h�h�h�h�h�h�]�}�(�labelId��US912828N308��path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�ua]�}�(h�h�h�h�h�h�ua]�}�(h�h�h�h�h�h�ua]�}�(h��US91282CCN92�h�h�h�h�ua]�}�(h��US912828TJ95�h�h�h�h�ua]�}�(h��DE0001102366�h�h�h�h�ua]�}�(h�h�h�h�h�h�ua]�}�(h�h�h�h�h�h�ua]�(}�(h��lei:549300tl5406ic1xkd09�h��parties.counterparty�h�h�u}�(h��id:16152148�h��parties.trader�h�h��ARRAY���R�u}�(h��lei:549300tl5406ic1xkd09�h��parties.seller�h�h�ue]�(}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�ue]�(}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h��
parties.buyer�h�h�ue]�(}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��id:16152148�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�ue]�(}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��id:16152148�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�ue]�(}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��id:16152148�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�ue]�(}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��id:16152148�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�ue]�(}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��id:16152148�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�ue]�(h�h�h�h�e]�(h�h�h�e]�(h�h�h�e]�(h�h�h�h�e]�(h�h�h�h�e]�(h�h�h�j   e]�(h�j  j  j  e]�(h�j
  j  j  eet�bhhK ��h��R�(KKK��h!�]�(h�h�h�h�h�h�h�h�et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h9at�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h(h,et�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h.h/h2et�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h'h)h+h-h0h1h3h4h5h6h7h8h:h;h<h=h>h?h@et�bhCNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bhCNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h\�mgr_locs��builtins��slice���KKK��R�u}�(j]  hpj^  ja  KKK��R�u}�(j]  hzj^  hhK ��h��R�(KK��h�i8�����R�(KhQNNNJ����J����K t�b�C	       
       
       �t�bu}�(j]  h�j^  ja  KKK��R�u}�(j]  h�j^  hhK ��h��R�(KK��jn  �C�                                                                                                                                                    �t�bu}�(j]  j  j^  ja  KKK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.