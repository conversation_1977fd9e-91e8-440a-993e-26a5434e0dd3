import os
from pathlib import Path

import pandas as pd
import pytest
from prefect import context
from swarm.conf import SettingsCls
from swarm.task.auditor import Auditor

from swarm_tasks.steeleye.tr.transformations.rts22_transform_maps import (
    saxo_bank_transform_map,
)

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
TEST_CFD_FILE_PATH = TEST_FILES_DIR.joinpath(r"CFDOptionTrades_test.pkl")
EXPECTED_CFD_FILE_PATH = TEST_FILES_DIR.joinpath(r"CFDOptionTrades_expected.pkl")
TEST_FUTURES_FILE_PATH = TEST_FILES_DIR.joinpath(r"FuturesTrades_test.pkl")
EXPECTED_FUTURES_FILE_PATH = TEST_FILES_DIR.joinpath(r"FuturesTrades_expected.pkl")
EXPECTED_FUTURES_THORNBRIDGE_FILE_PATH = TEST_FILES_DIR.joinpath(
    r"FuturesTrades_thornbridge_expected.pkl"
)
TEST_FX_OPTION_FILE_PATH = TEST_FILES_DIR.joinpath(r"FXOptionTrades_test.pkl")
EXPECTED_FX_OPTION_FILE_PATH = TEST_FILES_DIR.joinpath(r"FXOptionTrades_expected.pkl")
TEST_FX_FILE_PATH = TEST_FILES_DIR.joinpath(r"FXTrades_test.pkl")
EXPECTED_FX_FILE_PATH = TEST_FILES_DIR.joinpath(r"FXTrades_expected.pkl")
TEST_SHARE_FILE_PATH = TEST_FILES_DIR.joinpath(r"ShareTrades_test.pkl")
EXPECTED_SHARE_FILE_PATH = TEST_FILES_DIR.joinpath(r"ShareTrades_expected.pkl")
TEST_BONDS_FILE_PATH = TEST_FILES_DIR.joinpath(r"BondsTrades_test.pkl")
EXPECTED_BONDS_FILE_PATH = TEST_FILES_DIR.joinpath(r"BondsTrades_expected.pkl")


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


class TestSaxoBankTransformations:
    """
    Text SaxoBankTransformations
    """

    # TODO: add tests for Bonds, Mutuals and SPs when data is available
    @pytest.mark.parametrize(
        "test_file_path, expected_file_path",
        [
            (TEST_CFD_FILE_PATH, EXPECTED_CFD_FILE_PATH),
            (TEST_FUTURES_FILE_PATH, EXPECTED_FUTURES_FILE_PATH),
            (TEST_FX_OPTION_FILE_PATH, EXPECTED_FX_OPTION_FILE_PATH),
            (TEST_FX_FILE_PATH, EXPECTED_FX_FILE_PATH),
            (TEST_SHARE_FILE_PATH, EXPECTED_SHARE_FILE_PATH),
            (TEST_BONDS_FILE_PATH, EXPECTED_BONDS_FILE_PATH),
        ],
    )
    def test_end_to_end_transformations(
        self, mocker, test_file_path, expected_file_path, auditor
    ):
        os.environ["SWARM_FILE_URL"] = str(test_file_path)

        # mock realm to chose default transformation
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "default.dev.steeleye.co"

        source_frame = pd.read_pickle(test_file_path)
        task = saxo_bank_transform_map.transformation(tenant="foo")(
            source_frame=source_frame, logger=context.get("logger"), auditor=auditor
        )
        result = task.process()
        expected = pd.read_pickle(expected_file_path)

        assert not pd.testing.assert_frame_equal(
            result.drop(["sourceKey"], axis=1), expected.drop(["sourceKey"], axis=1)
        )

    def test_thornbridge_overrides(self, mocker, auditor):
        os.environ["SWARM_FILE_URL"] = str(TEST_FUTURES_FILE_PATH)

        # mock realm to chose thornbridge transformation
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "thornbridge.steeleye.co"

        source_frame = pd.read_pickle(TEST_FUTURES_FILE_PATH)
        task = saxo_bank_transform_map.transformation(tenant="thornbridge")(
            source_frame=source_frame, logger=context.get("logger"), auditor=auditor
        )
        result = task.process()
        expected = pd.read_pickle(EXPECTED_FUTURES_THORNBRIDGE_FILE_PATH)

        result_party_ids = result[["marketIdentifiers.parties"]]
        expected_party_ids = expected[["marketIdentifiers.parties"]]

        assert not pd.testing.assert_frame_equal(result_party_ids, expected_party_ids)
