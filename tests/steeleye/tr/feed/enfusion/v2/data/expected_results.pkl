���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�dataSourceName��date��__meta_model__��reportDetails.reportStatus��sourceIndex��	sourceKey��3tradersAlgosWaiversIndicators.otcPostTradeIndicator��=tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator��3tradersAlgosWaiversIndicators.shortSellingIndicator��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��transactionDetails.netAmount��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��transactionDetails.venue��.transmissionDetails.orderTransmissionIndicator��reportDetails.transactionRefNo��+reportDetails.tradingVenueTransactionIdCode��marketIdentifiers.instrument��marketIdentifiers.parties��marketIdentifiers��__option_strike_price_type__��__asset_class__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(h�numpy.core.numeric��_frombuffer���(�                      �h�i8�����R�(K�<�NNNJ����J����K t�bK���C�t�R�hD�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�js3://puneeth.uat.steeleye.co/flows/tr-feed-enfusion-v2-controller/FifthDelta_Executions_20221207220242.csv�hket�b�_dtype�h\�StringDtype���)��ubh^)��}�(hahhK ��h��R�(KK��hh�]�(�False�hxet�bhmho)��ubh^)��}�(hahhK ��h��R�(KK��hh�]�(�pandas._libs.missing��NA���h�et�bhmho)��ubh^)��}�(hahhK ��h��R�(KK��hh�]�(�MONE�h�et�bhmho)��ubh^)��}�(hahhK ��h��R�(KK��hh�]�(�XTKS�h�et�bhmho)��ubh^)��}�(hahhK ��h��R�(KK��hh�]�(�XOFF�h�et�bhmho)��ubh^)��}�(hahhK ��h��R�(KK��hh�]�(�True�h�et�bhmho)��ubhM(�             c�@     ^�@     @�@     @�@�h�f8�����R�(KhRNNNJ����J����K t�bKK��hUt�R�hM(�                      �hQKK��hUt�R�hhK ��h��R�(KKK��h!�]�(�Enfusion�h��
2022-12-06��
2022-12-06��RTS22Transaction�hÌNEWT�h�h�h�h�h��BUYI�h�h�h��JPY�h�h�h��UNIT�hǌAOTC�hȌ2022-12-06T04:41:37.000000Z��2022-12-06T01:00:09.000000Z��218274761205810439��218274761205701772�h�h�]�(}�(�labelId��JP3551500006��path��instrumentDetails.instrument��type��se_elastic_schema.static.market��IdentifierType����OBJECT���R�u}�(hόJP3551500006JPYXTKS�h�h�h�h�u}�(hό6902�h�h�h�h�ue]�(}�(h�h�h�h�h�h�u}�(hόJP3551500006JPYXTKS�h�h�h�h�u}�(h�h�h�h�h�h�ue]�(}�(hόlei:2138002ee8dt72x2qn40�hьparties.executingEntity�h�h�u}�(hόid:emsx tour�hьparties.counterparty�h�h�u}�(hόid:tyson appadoo�hьparties.trader�h�h֌ARRAY���R�u}�(hόid:tyson appadoo�hьparties.executionWithinFirm�h�h�u}�(hόlei:2138002ee8dt72x2qn40�hь
parties.buyer�h�h�u}�(hόid:emsx tour�hьparties.seller�h�h�ue]�(}�(hόlei:2138002ee8dt72x2qn40�h�h�h�h�u}�(hόid:emsx tour�h�h�h�h�u}�(hόid:tyson appadoo�h�h�h�h�u}�(hόid:tyson appadoo�h�h�h�h�u}�(hόlei:2138002ee8dt72x2qn40�h�h�h�h�u}�(hόid:emsx tour�h�h�h�h�ue]�(h�h�h�h�h�h�h�h�h�e]�(h�h�h�h�h�h�j   j  j  e�MntryVal�j  h�h�et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h,at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h9at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h:at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h1h4et�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h)at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h'h(h+h-h.h0h2h5h6h7h8h<h=h>h?h@hAhBet�bhDNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h_�mgr_locs��builtins��slice���KKK��R�u}�(jl  hqjm  jp  KKK��R�u}�(jl  h{jm  jp  K
KK��R�u}�(jl  h�jm  jp  KKK��R�u}�(jl  h�jm  jp  KKK��R�u}�(jl  h�jm  jp  KKK��R�u}�(jl  h�jm  jp  KKK��R�u}�(jl  h�jm  jp  KKK��R�u}�(jl  h�jm  jp  KKK��R�u}�(jl  h�jm  hM(��                                                  	              
                                                                                    �h�i8�����R�(KhRNNNJ����J����K t�bK��hUt�R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.