��Q      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�dataSourceName��date��__meta_model__��reportDetails.reportStatus��sourceIndex��	sourceKey��3tradersAlgosWaiversIndicators.otcPostTradeIndicator��=tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator��3tradersAlgosWaiversIndicators.shortSellingIndicator��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��transactionDetails.netAmount��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��transactionDetails.venue��.transmissionDetails.orderTransmissionIndicator��reportDetails.transactionRefNo��+reportDetails.tradingVenueTransactionIdCode��marketIdentifiers.instrument��marketIdentifiers.parties��marketIdentifiers��__option_strike_price_type__��__asset_class__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(h�numpy.core.numeric��_frombuffer���(�                      �h�i8�����R�(K�<�NNNJ����J����K t�bK���C�t�R�hD�__swarm_raw_index__�u��R�e]�(hhK ��h��R�(KKK��h!�]�(�Enfusion�ha�
2022-12-06��
2022-12-06��RTS22Transaction�hd�NEWT�heet�bhM(�                      �hQKK��hUt�R��pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�js3://puneeth.uat.steeleye.co/flows/tr-feed-enfusion-v2-controller/FifthDelta_Executions_20221207220242.csv�hzet�b�_dtype�hk�StringDtype���)��ubhhK ��h��R�(KKK��h!�]�(�pandas._libs.missing��NA���h�et�bhm)��}�(hphhK ��h��R�(KK��hw�]�(�False�h�et�bh|h~)��ubhhK ��h��R�(KKK��h!�]�(h�h��BUYI�h�et�bhm)��}�(hphhK ��h��R�(KK��hw�]�(h�h�et�bh|h~)��ubhhK ��h��R�(KKK��h!�]�(h�h�et�bhM(�            c�@     ^�@�h�f8�����R�(KhRNNNJ����J����K t�bKK��hUt�R�hhK ��h��R�(KKK��h!�]�(�JPY�h�et�bhm)��}�(hphhK ��h��R�(KK��hw�]�(�MONE�h�et�bh|h~)��ubhM(�            @�@     @�@�h�KK��hUt�R�hhK ��h��R�(KKK��h!�]�(h�h��UNIT�hˌAOTC�ȟ2022-12-06T04:41:37.000000Z��2022-12-06T01:00:09.000000Z�et�bhm)��}�(hphhK ��h��R�(KK��hw�]�(�XTKS�h�et�bh|h~)��ubhm)��}�(hphhK ��h��R�(KK��hw�]�(�XOFF�h�et�bh|h~)��ubhm)��}�(hphhK ��h��R�(KK��hw�]�(�True�h�et�bh|h~)��ubhhK ��h��R�(KKK��h!�]�(�218274761205810439��218274761205701772�h�h�]�(}�(�labelId��JP3551500006��path��instrumentDetails.instrument��type��se_elastic_schema.static.market��IdentifierType����OBJECT���R�u}�(h��JP3551500006JPYXTKS�h�h�h�j  u}�(h��6902�h�h�h�j  ue]�(}�(h�h�h�h�h�j  u}�(h��JP3551500006JPYXTKS�h�h�h�j  u}�(h�j  h�h�h�j  ue]�(}�(h��lei:sample_lei�h��parties.executingEntity�h�j  u}�(h��id:emsx tour�h��parties.counterparty�h�j  u}�(h��id:tyson appadoo�h��parties.trader�h�h��ARRAY���R�u}�(h��id:tyson appadoo�h��parties.executionWithinFirm�h�j  u}�(h��id:tyson appadoo�h��$parties.investmentDecisionWithinFirm�h�j  u}�(h��lei:2138002ee8dt72x2qn40�h��
parties.buyer�h�j  u}�(h��id:emsx tour�h��parties.seller�h�j  u}�(h��lei:sample_lei�h��parties.buyerDecisionMaker�h�j  ue]�(}�(h��lei:sample_lei�h�j  h�j  u}�(h��id:emsx tour�h�j  h�j  u}�(h��id:tyson appadoo�h�j  h�j  u}�(h��id:tyson appadoo�h�j  h�j  u}�(h��id:tyson appadoo�h�j  h�j  u}�(h��lei:2138002ee8dt72x2qn40�h�j   h�j  u}�(h��id:emsx tour�h�j#  h�j  u}�(h��lei:sample_lei�h�j&  h�j  ue]�(h�j  j  j  j  j  j  j  j  j!  j$  e]�(j  j  j
  j(  j*  j,  j.  j0  j2  j4  j6  eet�bhhK ��h��R�(KKK��h!�]�(�MntryVal�j@  et�bhhK ��h��R�(KKK��h!�]�(h�h�et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h'h(et�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h)at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h+at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h,at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h-h.et�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h0at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h1at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h5h6h7h8et�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h9at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h:at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h<h=h>h?h@et�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bhDNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hBat�bhDNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h^�mgr_locs��builtins��slice���K KK��R�u}�(j�  hjj�  j�  KKK��R�u}�(j�  hnj�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KK
K��R�u}�(j�  h�j�  j�  K
KK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KK
K��R�u}�(j�  h�j�  j�  K
KK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  j=  j�  j�  KKK��R�u}�(j�  jD  j�  j�  KKK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.