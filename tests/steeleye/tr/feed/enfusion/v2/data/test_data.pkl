���,      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKB��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�ORDERID��	EVENTTYPE��INSTRUMENTIDENTIFIC<PERSON><PERSON>CODE��DESCRIPTION��	ORDERSIDE��ORDERTOTALQUANTITY��CURRENCY��!EXECUTIONENTITYIDENTIFICATIONCODE��TRADER��BUYERIDENTIFICATIONCODE��SELLERIDENTIFICATIONCODE��ORDEREXECUTIONDESTINATION��LASTPX��
EXECUTIONDATE��
EXECUTIONTIME��EXECID��ISIN��TICKER��EXCHANGEMICCODE��INSTRUMENTCLASSIFICATION��FUTUREEXPIRATIONDATE��NOTIONALCURRENCY2��UNDERLYINGINSTRUMENTCODE��FUTUREBLOOMBERGROOT��TRANSACTIONTYPE��REPORTSTATUS��TRANSACTIONREFERENCENUMBER��BUYERDECISIONMAKERCODE��SELLERDECISIONMAKERCODE��TRANSMISSIONOFORDERINDICATOR��TRADINGDATETIME��TRADINGCAPACITY��QUANTITYTYPE_ALLOCATIONS��QUANTITY��QUANTITYCURRENCY��
PRICE-TYPE��PRICE��
PRICECURRENCY��VENUE��INSTRUMENTFULLNAME��$INSTRUMENTCLASSIFICATION_ALLOCATIONS��UNDERLYINGINDEXNAME��TERMOFUNDERLYINGINDEX-VALUE��
EXPIRYDATE��SHORTSELLINGINDICATOR��COMMODITYDERIVATIVEINDICATOR��'SECURITIESFINANCINGTRANSACTIONINDICATOR��ORDERSIDE_ALLOCATIONS��PORTFOLIOMANAGER�� FUTUREEXPIRATIONDATE_ALLOCATIONS��	CURRENCY2��EXCHANGEMICCODE_ALLOCATIONS��COUNTERPARTY��__TRIGGER_FILE_NAME__��COMPLEXTRADECOMPONENTID��EXECUTIONWITHINFIRM��	NETAMOUNT��OPTIONCONTRACTBLOOMBERGROOTCODE��OPTIONEXPIRATIONDATE��OPTIONSTRIKE��
OPTIONTYPE��OPTIONTYPE_ALLOCATIONS��OTCPOST-TRADEINDICATOR��STRIKEPRICETYPE��)TRADINGVENUETRANSACTIONIDENTIFICATIONCODE��LASTQTY�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C               �t�bhh�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�pandas._libs.missing��NA���h�et�b�_dtype�h~�StringDtype���)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�21827476�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�New�j   et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�JP3551500006�j
  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�	DENSO ORD�j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�cover�j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�JPY�j(  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�2138002EE8DT72X2QN40�j2  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�
Tyson Appadoo�j<  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�2138002EE8DT72X2QN40�jF  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�TOUR�jP  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�EMSX�jZ  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�2022-12-06T04:41:37.000��2022-12-06T01:00:09.000�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�04:41:37.000 +0000��01:00:09.000 +0000�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�
1205810439��
1205701772�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�JP3551500006�j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�6902�j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�XTKS�j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�
BuyToCover�j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�NEWT�j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�	321651062�j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�2138002EE8DT72X2QN40�j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�True�j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�2022-12-07T06:00:00.000000Z�j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�AOTC�j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�UNIT�j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�MONE�j2  et�bh�h�)��ubhhK ��h��R�(KKK��h�f8�����R�(KhvNNNJ����J����K t�b�C@    �F)A    �F)A     c�@     ^�@     C�@     C�@��.�@��.�@�t�bh�)��}�(h�hhK ��h��R�(KK��h��]�(�JPY�jF  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�XOFF�jP  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�False�j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�False�j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�Buy To Cover�j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�Niall O'Keeffe�j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�JPY�j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�XTKS�j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�	EMSX TOUR�j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�js3://puneeth.uat.steeleye.co/flows/tr-feed-enfusion-v2-controller/FifthDelta_Executions_20221207220242.csv�j�  et�bh�h�)��ubhhK ��h��R�(KKK��h�f8�����R�(KhvNNNJ����J����K t�b�C     @�@     @�@�t�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h[at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h\at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h]at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h^at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h_at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h`at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�haat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hbat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hcat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hdat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�heat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h(at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h)at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h+at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h,at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h0at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h9at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h:at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h<at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h=at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h?at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h@at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hBat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hCat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hDat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hEat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hGat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hHat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h*h1hFhIet�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hJat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hKat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hLat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hMat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hNat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hOat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hPat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hQat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hRat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hSat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hTat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hUat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hVat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hWat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hXat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hYat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hZat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hfat�bhhNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs��builtins��slice���K6K7K��R�u}�(j+  h�j,  j/  K7K8K��R�u}�(j+  h�j,  j/  K8K9K��R�u}�(j+  h�j,  j/  K9K:K��R�u}�(j+  h�j,  j/  K:K;K��R�u}�(j+  h�j,  j/  K;K<K��R�u}�(j+  h�j,  j/  K<K=K��R�u}�(j+  h�j,  j/  K=K>K��R�u}�(j+  h�j,  j/  K>K?K��R�u}�(j+  h�j,  j/  K?K@K��R�u}�(j+  h�j,  j/  K@KAK��R�u}�(j+  h�j,  j/  K KK��R�u}�(j+  h�j,  j/  KKK��R�u}�(j+  j  j,  j/  KKK��R�u}�(j+  j
  j,  j/  KKK��R�u}�(j+  j  j,  j/  KKK��R�u}�(j+  j!  j,  j/  KKK��R�u}�(j+  j+  j,  j/  KKK��R�u}�(j+  j5  j,  j/  KK	K��R�u}�(j+  j?  j,  j/  K	K
K��R�u}�(j+  jI  j,  j/  K
KK��R�u}�(j+  jS  j,  j/  KKK��R�u}�(j+  j]  j,  j/  K
KK��R�u}�(j+  jh  j,  j/  KKK��R�u}�(j+  js  j,  j/  KKK��R�u}�(j+  j~  j,  j/  KKK��R�u}�(j+  j�  j,  j/  KKK��R�u}�(j+  j�  j,  j/  KKK��R�u}�(j+  j�  j,  j/  KKK��R�u}�(j+  j�  j,  j/  KKK��R�u}�(j+  j�  j,  j/  KKK��R�u}�(j+  j�  j,  j/  KKK��R�u}�(j+  j�  j,  j/  KKK��R�u}�(j+  j�  j,  j/  KKK��R�u}�(j+  j�  j,  j/  KKK��R�u}�(j+  j�  j,  j/  KKK��R�u}�(j+  j�  j,  j/  KKK��R�u}�(j+  j�  j,  j/  KKK��R�u}�(j+  j�  j,  j/  KKK��R�u}�(j+  j  j,  j/  KKK��R�u}�(j+  j  j,  j/  KK K��R�u}�(j+  j  j,  j/  K K!K��R�u}�(j+  j"  j,  j/  K"K#K��R�u}�(j+  j+  j,  j/  K#K$K��R�u}�(j+  j7  j,  hhK ��h��R�(KK��hu�C               !       $       �t�bu}�(j+  j?  j,  j/  K%K&K��R�u}�(j+  jI  j,  j/  K&K'K��R�u}�(j+  jS  j,  j/  K'K(K��R�u}�(j+  j\  j,  j/  K(K)K��R�u}�(j+  je  j,  j/  K)K*K��R�u}�(j+  jn  j,  j/  K*K+K��R�u}�(j+  jw  j,  j/  K+K,K��R�u}�(j+  j�  j,  j/  K,K-K��R�u}�(j+  j�  j,  j/  K-K.K��R�u}�(j+  j�  j,  j/  K.K/K��R�u}�(j+  j�  j,  j/  K/K0K��R�u}�(j+  j�  j,  j/  K0K1K��R�u}�(j+  j�  j,  j/  K1K2K��R�u}�(j+  j�  j,  j/  K2K3K��R�u}�(j+  j�  j,  j/  K3K4K��R�u}�(j+  j�  j,  j/  K4K5K��R�u}�(j+  j�  j,  j/  K5K6K��R�u}�(j+  j�  j,  j/  KAKBK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.