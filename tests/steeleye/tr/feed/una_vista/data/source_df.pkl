���5      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKY��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�REPORTSTATUS��TRANSACTIONREFERENCENUMBER��VENUETRANSACTIONID��SUBMITTINGENTITYID��EXECUTINGENTITYID��INVESTMENTFIRMINDICATOR��BUYERIDTYPE��BUYERIDSUBTYPE��BUYERID��BUYERCOUNTRYOFBRANCH��BUYERFIRSTNAME��BUYERSURNAME��BUYERDOB��BUYERDECISIONMAKERIDTYPE��BUYERDECISIONMAKERIDSUBTYPE��BUYERDECISIONMAKERID��BUYERDECISIONMAKERFIRSTNAME��BUYERDECISIONMAKERSURNAME��BUYERDECISIONMAKERDOB��SELLERIDTYPE��SELLERIDSUBTYPE��SELLERID��SELLERCOUNTRYOFBRANCH��SELLERFIRSTNAME��
SELLERSURNAME��	SELLERDOB��SELLERDECISIONMAKERIDTYPE��SELLERDECISIONMAKERIDSUBTYPE��SELLERDECISIONMAKERID��SELLERDECISIONMAKERFIRSTNAME��SELLERDECISIONMAKERSURNAME��SELLERDECISIONMAKERDOB��ORDERTRANSMISSIONINDICATOR��BUYERTRANSMITTERID��SELLERTRANSMITTERID��TRADINGDATETIME��TRADINGCAPACITY��QUANTITY��QUANTITYTYPE��QUANTITYCURRENCY��PRICE��	PRICETYPE��	NETAMOUNT��VENUE��COUNTRYOFBRANCH��UP-FRONTPAYMENT��UP-FRONTPAYMENTCURRENCY��INSTRUMENTID��INSTRUMENTIDTYPE��INSTRUMENTNAME��INSTRUMENTCLASSIFICATION��NOTIONALCURRENCY1��PRICEMULTIPLIER��UVINSTRUMENTCLASSIFICATION��UNDERLYINGINSTRUMENTID��
EXPIRYDATE��DELIVERYTYPE��INVESTMENTDECISIONIDTYPE��INVESTMENTDECISIONIDSUBTYPE��INVESTMENTDECISIONID��!INVESTMENTDECISIONCOUNTRYOFBRANCH��FIRMEXECUTIONIDTYPE��FIRMEXECUTIONIDSUBTYPE��FIRMEXECUTIONID��FIRMEXECUTIONCOUNTRYOFBRANCH��SHORTSELLINGINDICATOR��SFTINDICATOR��INTERNALCLIENTIDENTIFICATION��DATACATEGORY��UVINSTRUMENTID��
PRICENOTATION��COMMODITYDERIVATIVEINDICATOR��COMPLEXTRADECOMPONENTID��DERIVATIVENOTIONALCHANGE��MATURITYDATE��NOTIONALCURRENCY2��NOTIONALCURRENCY2TYPE��OTCPOSTTRADEINDICATOR��OPTIONSTYLE��
OPTIONTYPE��
PRICECURRENCY��STRIKEPRICE��STRIKEPRICECURRENCY��STRIKEPRICETYPE��UVINDEXCLASSIFICATION��UNDERLYINGINDEXID��UNDERLYINGINDEXNAME��UNDERLYINGINDEXTERM��WAIVERINDICATOR�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C0                                           �t�bh�__swarm_raw_index__�u��R�e]�(hhK ��h��R�(KKK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�2138006DHU9CTXV86Z88�h�h�h�h�h��TRUE�h�h�h�h�h�G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �N�h�h�h�h�h��NIDN�h�h�h�h�h��GB�h�h�h�h�h�G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      et�b�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h��]�(�pandas._libs.missing��NA���h�h�h�h�h�et�b�_dtype�h��StringDtype���)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�RFPT��	ACTX;RFPT�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�	NLIQ;SIZE��SIZE�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�NEWT�jm  jm  jm  jm  jm  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�263976��263976A1231164��263976A1231167��263976A1231169��263976A1231170��263975A1231163�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�2138006DHU9CTXV86Z88�j�  j�  j�  j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�L��I�j�  j�  j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�G5GSEF7VJP5I7OUK5573��INTC�j�  j�  j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(j�  j�  j�  j�  j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�INTC��549300QEXS73JAJOLE43��5493007BCXEDR17QKB54��5493005IJXEJ0Y6BLD21��549300ISYB0XS18PMD95��549300J0L3G16C3Y3175�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h��GB�j�  j�  j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�TRUE�j�  j�  j�  j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�2023-01-04T17:38:03.783�j�  j�  j�  j�  �2023-01-04T17:38:00.504�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�AOTC�j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�	5,000,000��	4,247,000��244,000��200,000��309,000��500,000�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�NominalValue�j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�USD�j$  j$  j$  j$  j$  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�104.25�j.  j.  j.  j.  j.  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�Pctg�j8  j8  j8  j8  j8  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�
5282447.92��
4486911.26��	257783.46��	211297.92��	326455.28��	528244.79�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�BBSI��XOFF�jR  jR  jR  jQ  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�US900123DF45�jw  jw  jw  jw  jw  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�FinInstrm.Id�j�  j�  j�  j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�GBPB916848A�j�  j�  j�  j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�FALSE�j�  j�  j�  j�  j�  et�bh�h�)��ube]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h(h*h,h/h0h1h2h3h5h6h7h9h<h=h>h?h@hBhChDhFhGh^h_hahbhchehhhiet�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hjat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hkat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hlat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hmat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hnat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hoat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hpat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hqat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hrat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hsat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�htat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�huat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hvat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hwat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hxat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hyat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hzat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h{at�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h|at�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h}at�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h)at�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h+at�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h:at�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hEat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hHat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hIat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hJat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hKat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hLat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hMat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hNat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hOat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hPat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hQat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hRat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hSat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hTat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hUat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hVat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hWat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hXat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hYat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hZat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h[at�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h\at�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h]at�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h`at�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hdat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hfat�bhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hgat�bhNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs�hhK ��h��R�(KK��h��C�                     
                     
                                                                                                  !       "       9       :       <       =       >       @       C       D       �t�bu}�(j  h�j  �builtins��slice���KEKFK��R�u}�(j  h�j  j!  KFKGK��R�u}�(j  h�j  j!  KGKHK��R�u}�(j  h�j  j!  KHKIK��R�u}�(j  h�j  j!  KIKJK��R�u}�(j  h�j  j!  KJKKK��R�u}�(j  h�j  j!  KKKLK��R�u}�(j  h�j  j!  KLKMK��R�u}�(j  h�j  j!  KMKNK��R�u}�(j  j  j  j!  KNKOK��R�u}�(j  j
  j  j!  KOKPK��R�u}�(j  j  j  j!  KPKQK��R�u}�(j  j  j  j!  KQKRK��R�u}�(j  j%  j  j!  KRKSK��R�u}�(j  j.  j  j!  KSKTK��R�u}�(j  j7  j  j!  KTKUK��R�u}�(j  j@  j  j!  KUKVK��R�u}�(j  jI  j  j!  KVKWK��R�u}�(j  jR  j  j!  KWKXK��R�u}�(j  j[  j  j!  KXKYK��R�u}�(j  jf  j  j!  K KK��R�u}�(j  jp  j  j!  KKK��R�u}�(j  j  j  j!  KKK��R�u}�(j  j�  j  j!  KKK��R�u}�(j  j�  j  j!  KKK��R�u}�(j  j�  j  j!  KK	K��R�u}�(j  j�  j  j!  K	K
K��R�u}�(j  j�  j  j!  KKK��R�u}�(j  j�  j  j!  KKK��R�u}�(j  j�  j  j!  KKK��R�u}�(j  j�  j  j!  KKK��R�u}�(j  j�  j  j!  KKK��R�u}�(j  j�  j  j!  K K!K��R�u}�(j  j�  j  j!  K#K$K��R�u}�(j  j�  j  j!  K$K%K��R�u}�(j  j  j  j!  K%K&K��R�u}�(j  j  j  j!  K&K'K��R�u}�(j  j  j  j!  K'K(K��R�u}�(j  j'  j  j!  K(K)K��R�u}�(j  j1  j  j!  K)K*K��R�u}�(j  j;  j  j!  K*K+K��R�u}�(j  jJ  j  j!  K+K,K��R�u}�(j  jU  j  j!  K,K-K��R�u}�(j  j^  j  j!  K-K.K��R�u}�(j  jg  j  j!  K.K/K��R�u}�(j  jp  j  j!  K/K0K��R�u}�(j  jz  j  j!  K0K1K��R�u}�(j  j�  j  j!  K1K2K��R�u}�(j  j�  j  j!  K2K3K��R�u}�(j  j�  j  j!  K3K4K��R�u}�(j  j�  j  j!  K4K5K��R�u}�(j  j�  j  j!  K5K6K��R�u}�(j  j�  j  j!  K6K7K��R�u}�(j  j�  j  j!  K7K8K��R�u}�(j  j�  j  j!  K8K9K��R�u}�(j  j�  j  j!  K;K<K��R�u}�(j  j�  j  j!  K?K@K��R�u}�(j  j�  j  j!  KAKBK��R�u}�(j  j�  j  j!  KBKCK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.