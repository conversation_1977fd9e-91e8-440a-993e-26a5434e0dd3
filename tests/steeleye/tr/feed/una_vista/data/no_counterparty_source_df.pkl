���5      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKW��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�REPORTSTATUS��TRANSACTIONREFERENCENUMBER��VENUETRANSACTIONID��SUBMITTINGENTITYID��EXECUTINGENTITYID��INVESTMENTFIRMINDICATOR��BUYERIDSUBTYPE��BUYERFIRSTNAME��BUYERSURNAME��BUYERDOB��BUYERDECISIONMAKERIDTYPE��BUYERDECISIONMAKERIDSUBTYPE��BUYERDECISIONMAKERFIRSTNAME��BUYERDECISIONMAKERSURNAME��BUYERDECISIONMAKERDOB��SELLERIDSUBTYPE��SELLERFIRSTNAME��
SELLERSURNAME��	SELLERDOB��SELLERDECISIONMAKERIDTYPE��SELLERDECISIONMAKERIDSUBTYPE��SELLERDECISIONMAKERFIRSTNAME��SELLERDECISIONMAKERSURNAME��SELLERDECISIONMAKERDOB��BUYERTRANSMITTERID��SELLERTRANSMITTERID��BUYERCOUNTRYOFBRANCH��BUYERDECISIONMAKERID��BUYERID��BUYERIDTYPE��COMMODITYDERIVATIVEINDICATOR��COMPLEXTRADECOMPONENTID��COUNTRYOFBRANCH��DATACATEGORY��DELIVERYTYPE��DERIVATIVENOTIONALCHANGE��
EXPIRYDATE��FIRMEXECUTIONCOUNTRYOFBRANCH��FIRMEXECUTIONID��FIRMEXECUTIONIDSUBTYPE��FIRMEXECUTIONIDTYPE��INSTRUMENTCLASSIFICATION��INSTRUMENTID��INSTRUMENTIDTYPE��INSTRUMENTNAME��INTERNALCLIENTIDENTIFICATION��!INVESTMENTDECISIONCOUNTRYOFBRANCH��INVESTMENTDECISIONID��INVESTMENTDECISIONIDSUBTYPE��INVESTMENTDECISIONIDTYPE��MATURITYDATE��	NETAMOUNT��NOTIONALCURRENCY1��NOTIONALCURRENCY2��NOTIONALCURRENCY2TYPE��OPTIONSTYLE��
OPTIONTYPE��ORDERTRANSMISSIONINDICATOR��OTCPOSTTRADEINDICATOR��PRICE��
PRICECURRENCY��PRICEMULTIPLIER��	PRICETYPE��QUANTITY��QUANTITYCURRENCY��QUANTITYTYPE��SELLERCOUNTRYOFBRANCH��SELLERDECISIONMAKERID��SELLERID��SELLERIDTYPE��SFTINDICATOR��SHORTSELLINGINDICATOR��STRIKEPRICE��STRIKEPRICECURRENCY��STRIKEPRICETYPE��TRADINGCAPACITY��TRADINGDATETIME��UNDERLYINGINDEXID��UNDERLYINGINDEXNAME��UNDERLYINGINDEXTERM��UNDERLYINGINSTRUMENTID��UP-FRONTPAYMENT��UP-FRONTPAYMENTCURRENCY��UVINDEXCLASSIFICATION��UVINSTRUMENTCLASSIFICATION��VENUE��WAIVERINDICATOR�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C0                                           �t�bh}�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h!�]�(�pandas._libs.missing��NA���h�h�h�h�h�et�b�_dtype�h��StringDtype���)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubhhK ��h��R�(KKK��h�f8�����R�(Kh�NNNJ����J����K t�b�C`      �      �      �      �      �      �      �      �      �      �      �      ��t�bhhK ��h��R�(KKK��h!�]�(�2138006DHU9CTXV86Z88�j�  j�  j�  j�  j�  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      et�bh�)��}�(h�hhK ��h��R�(KK��h!�]�(�CANC�j�  j�  j�  j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�TESTBMTF275554MKT��TESTBMTF275554A1279512ALC��TESTBMTF275554A1279510ALC��TESTTREU275554MKT��TESTTREU275554A1279512ALC��TESTTREU275554A1279510ALC�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�25493001KJTIIGC8Y1R12BBG0000000000B64648FFDB23C0020�h�h��25493001KJTIIGC8Y1R12BBG0000000000B64648FFDB23C0022�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�2138006DHU9CTXV86Z88�j�  j�  j�  j�  j�  et�bh�h�)��ube]�(h
h}�(hhhK ��h��R�(KK��h!�]�h?at�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h@at�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hBat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hCat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hDat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hEat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hFat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hGat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hHat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hIat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hJat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hKat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hLat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hMat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hNat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hOat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hPat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hQat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hRat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hSat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hTat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hUat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hVat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hWat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hXat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hYat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hZat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h[at�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h\at�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h]at�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h^at�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h_at�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�haat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hbat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hcat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hdat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�heat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hfat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hgat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hhat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hiat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hjat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hkat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hlat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hnat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hoat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hpat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hqat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hrat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hsat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�htat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�huat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hvat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hwat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hxat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hyat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hzat�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h{at�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h`hmet�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h(h*h+h,h-h.h/h0h1h2h3h4h5h6h7h8h9h:h;h<h=h>et�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bh}Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h)at�bh}Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs��builtins��slice���KKK��R�u}�(j@  h�jA  jD  KKK��R�u}�(j@  h�jA  jD  KKK��R�u}�(j@  h�jA  jD  KKK��R�u}�(j@  h�jA  jD  KKK��R�u}�(j@  h�jA  jD  KK K��R�u}�(j@  h�jA  jD  K K!K��R�u}�(j@  h�jA  jD  K!K"K��R�u}�(j@  h�jA  jD  K"K#K��R�u}�(j@  h�jA  jD  K#K$K��R�u}�(j@  h�jA  jD  K$K%K��R�u}�(j@  j   jA  jD  K%K&K��R�u}�(j@  j	  jA  jD  K&K'K��R�u}�(j@  j  jA  jD  K'K(K��R�u}�(j@  j  jA  jD  K(K)K��R�u}�(j@  j$  jA  jD  K)K*K��R�u}�(j@  j-  jA  jD  K*K+K��R�u}�(j@  j6  jA  jD  K+K,K��R�u}�(j@  j?  jA  jD  K,K-K��R�u}�(j@  jH  jA  jD  K-K.K��R�u}�(j@  jQ  jA  jD  K.K/K��R�u}�(j@  jZ  jA  jD  K/K0K��R�u}�(j@  jc  jA  jD  K0K1K��R�u}�(j@  jl  jA  jD  K1K2K��R�u}�(j@  ju  jA  jD  K2K3K��R�u}�(j@  j~  jA  jD  K3K4K��R�u}�(j@  j�  jA  jD  K4K5K��R�u}�(j@  j�  jA  jD  K5K6K��R�u}�(j@  j�  jA  jD  K6K7K��R�u}�(j@  j�  jA  jD  K7K8K��R�u}�(j@  j�  jA  jD  K8K9K��R�u}�(j@  j�  jA  jD  K9K:K��R�u}�(j@  j�  jA  jD  K:K;K��R�u}�(j@  j�  jA  jD  K<K=K��R�u}�(j@  j�  jA  jD  K=K>K��R�u}�(j@  j�  jA  jD  K>K?K��R�u}�(j@  j�  jA  jD  K?K@K��R�u}�(j@  j�  jA  jD  K@KAK��R�u}�(j@  j�  jA  jD  KAKBK��R�u}�(j@  j�  jA  jD  KBKCK��R�u}�(j@  j  jA  jD  KCKDK��R�u}�(j@  j  jA  jD  KDKEK��R�u}�(j@  j  jA  jD  KEKFK��R�u}�(j@  j   jA  jD  KFKGK��R�u}�(j@  j)  jA  jD  KGKHK��R�u}�(j@  j2  jA  jD  KIKJK��R�u}�(j@  j;  jA  jD  KJKKK��R�u}�(j@  jD  jA  jD  KKKLK��R�u}�(j@  jM  jA  jD  KLKMK��R�u}�(j@  jV  jA  jD  KMKNK��R�u}�(j@  j_  jA  jD  KNKOK��R�u}�(j@  jh  jA  jD  KOKPK��R�u}�(j@  jq  jA  jD  KPKQK��R�u}�(j@  jz  jA  jD  KQKRK��R�u}�(j@  j�  jA  jD  KRKSK��R�u}�(j@  j�  jA  jD  KSKTK��R�u}�(j@  j�  jA  jD  KTKUK��R�u}�(j@  j�  jA  jD  KUKVK��R�u}�(j@  j�  jA  jD  KVKWK��R�u}�(j@  j�  jA  jD  K;KUK
��R�u}�(j@  j�  jA  hhK ��h��R�(KK��h��C�                                   	       
                     
                                                                                           �t�bu}�(j@  j�  jA  jD  K KK��R�u}�(j@  j�  jA  jD  KKK��R�u}�(j@  j�  jA  jD  KKK��R�u}�(j@  j�  jA  jD  KKK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.