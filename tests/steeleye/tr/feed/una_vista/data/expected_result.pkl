���)      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKE��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�dataSourceName��date��__meta_model__��reportDetails.reportStatus��+reportDetails.tradingVenueTransactionIdCode��sourceIndex��	sourceKey��:tradersAlgosWaiversIndicators.commodityDerivativeIndicator��3tradersAlgosWaiversIndicators.otcPostTradeIndicator��=tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator��3tradersAlgosWaiversIndicators.shortSellingIndicator��-tradersAlgosWaiversIndicators.waiverIndicator��*transactionDetails.branchMembershipCountry��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��+transactionDetails.derivativeNotionalChange��transactionDetails.netAmount��transactionDetails.price�� transactionDetails.priceCurrency��%transactionDetails.priceNotApplicable�� transactionDetails.priceNotation��transactionDetails.pricePending��transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��transactionDetails.recordType��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime��transactionDetails.venue�� transactionDetails.ultimateVenue��!transactionDetails.upFrontPayment��)transactionDetails.upFrontPaymentCurrency��.transmissionDetails.orderTransmissionIndicator��reportDetails.transactionRefNo��marketIdentifiers.instrument��#instrument_classification_attribute��expiry_date_attribute��isin_attribute��underlying_index_name_attribute��%underlying_index_term_value_attribute��venue_attribute��option_strike_price_attribute��notional_currency_1_attribute��notional_currency_2_attribute��option_type_attribute��underlying_isin_attribute��marketIdentifiers.parties��marketIdentifiers��__strike_price_currency__��__notional_currency_1__��__instrument_full_name__��__instrument_classification__��__maturity_date__��__instrument_id_code_type__��__strike_price_type__��__instrument_id_code__��__notional_currency_2__��__delivery_type__��__option_exercise_style__��__strike_price__��__price_multiplier__��__notional_currency_2_type__��__data_category__��__is_created_through_fallback__��1parties.buyerTransmittingFirm.firmIdentifiers.lei��2parties.sellerTransmittingFirm.firmIdentifiers.lei��'transactionDetails.swapDirectionalities��__underlying_index_name__��__underlying_index_term__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C0                                           �t�bhk�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h!�]�(�NEWT�h�h�h�h�h�et�b�_dtype�h��StringDtype���)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�FALSE�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�5000000��4247000��244000��200000��309000��500000�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�AOTC�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�BBSI��XOFF�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�TRUE�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�263976��263976A1231164��263976A1231167��263976A1231169��263976A1231170��263975A1231163�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�pandas._libs.missing��NA���h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(�US900123DF45�j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(j  j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(h�h�h�h�h�h�et�bh�h�)��ubhhK ��h��R�(KKK��h�b1�����R�(Kh"NNNJ����J����K t�b�C�t�bhhK ��h��R�(KKK��h�f8�����R�(KhyNNNJ����J����K t�b�C0�G���&TA
ףпQA�z��wA��(\�	A�Q���AH�z�� A�t�bhhK ��h��R�(KKK��hx�C0                                           �t�bhhK ��h��R�(KK.K��h!�]�(�UnaVista Transaction Report�jl  jl  jl  jl  jl  �
2023-01-04��
2023-01-04��
2023-01-04��
2023-01-04��
2023-01-04��
2023-01-04��RTS22Transaction�js  js  js  js  js  h�h�h�h�h�h�n/Users/<USER>/Desktop/steeleye/se-prefect-dir/swarm-tasks/tests/steeleye/tr/feed/una_vista/data/source_df.pkl�jt  jt  jt  jt  jt  h�h�h�h�h�h�]��RFPT�a]�(�ACTX��RFPT�eh�h�h�h�h�h�h�h�h�h�]�(�NLIQ��SIZE�e]��SIZE�ah�h�h�h�h�h�h�h�h�h�SELL�j  j  j  j  j  h�h�h�h�h�h�h�h�h�h�h�h�104.25�j�  j�  j�  j�  j�  h�h�h�h�h�h�����PERC�j�  j�  j�  j�  j�  �������USD�j�  j�  j�  j�  j�  �NOML�j�  j�  j�  j�  j�  h�h�h�h�h�h�2023-01-04T17:38:03.783000Z��2023-01-04T17:38:03.783000Z��2023-01-04T17:38:03.783000Z��2023-01-04T17:38:03.783000Z��2023-01-04T17:38:03.783000Z��2023-01-04T17:38:00.504000Z�h�h�h�h�h�h�h�h�h�h�h�h�]�}�(�labelId�j  �path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  ua]�}�(j�  j  j�  j�  j�  j�  uah�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�]�(}�(j�  �lei:2138006dhu9ctxv86z88�j�  �parties.executingEntity�j�  j�  u}�(j�  �lei:g5gsef7vjp5i7ouk5573�j�  �parties.counterparty�j�  j�  u}�(j�  �id:gbpb916848a�j�  �parties.executionWithinFirm�j�  j�  u}�(j�  �lei:g5gsef7vjp5i7ouk5573�j�  �
parties.buyer�j�  j�  �ARRAY���R�u}�(j�  �lei:intc�j�  �parties.seller�j�  j�  ue]�(}�(j�  �lei:2138006dhu9ctxv86z88�j�  j�  j�  j�  u}�(j�  �lei:intc�j�  j�  j�  j�  u}�(j�  �id:gbpb916848a�j�  j�  j�  j�  u}�(j�  �lei:intc�j�  j�  j�  j�  u}�(j�  �lei:549300qexs73jajole43�j�  j�  j�  j�  ue]�(}�(j�  �lei:2138006dhu9ctxv86z88�j�  j�  j�  j�  u}�(j�  �lei:intc�j�  j�  j�  j�  u}�(j�  �id:gbpb916848a�j�  j�  j�  j�  u}�(j�  �lei:intc�j�  j�  j�  j�  u}�(j�  �lei:5493007bcxedr17qkb54�j�  j�  j�  j�  ue]�(}�(j�  �lei:2138006dhu9ctxv86z88�j�  j�  j�  j�  u}�(j�  �lei:intc�j�  j�  j�  j�  u}�(j�  �id:gbpb916848a�j�  j�  j�  j�  u}�(j�  �lei:intc�j�  j�  j�  j�  u}�(j�  �lei:5493005ijxej0y6bld21�j�  j�  j�  j�  ue]�(}�(j�  �lei:2138006dhu9ctxv86z88�j�  j�  j�  j�  u}�(j�  �lei:intc�j�  j�  j�  j�  u}�(j�  �id:gbpb916848a�j�  j�  j�  j�  u}�(j�  �lei:intc�j�  j�  j�  j�  u}�(j�  �lei:549300isyb0xs18pmd95�j�  j�  j�  j�  ue]�(}�(j�  �lei:2138006dhu9ctxv86z88�j�  j�  j�  j�  u}�(j�  �lei:g5gsef7vjp5i7ouk5573�j�  j�  j�  j�  u}�(j�  �id:gbpb916848a�j�  j�  j�  j�  u}�(j�  �lei:g5gsef7vjp5i7ouk5573�j�  j�  j�  j�  u}�(j�  �lei:549300j0l3g16c3y3175�j�  j�  j�  j�  ue]�(j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  eG�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      h�h�h�h�h�h�ID�j�  j�  j�  j�  j�  h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�G�      G�      G�      G�      G�      G�      h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�G�      G�      G�      G�      G�      G�      et�bhhK ��h��R�(KKK��h!�]�(G�      G�      G�      G�      G�      G�      et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h(at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h?at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hBat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hEat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hFat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hHat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hIat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hJat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hKat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hLat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hMat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hWat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h^at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h_at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�haat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hbat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hdat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bhkNu��R�h
h}�(hhhK ��h��R�(KK.��h!�]�(h%h&h'h)h+h,h-h/h0h1h2h3h4h6h7h8h9h:h<h=h>h@hChDhGhNhOhPhQhRhShThUhVhXhYhZh[h\h]h`hchehfhghhet�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hiat�bhkNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs��builtins��slice���KKK��R�u}�(j�  h�j�  j�  K	K
K��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  K K!K��R�u}�(j�  h�j�  j�  K!K"K��R�u}�(j�  h�j�  j�  K#K$K��R�u}�(j�  h�j�  j�  K$K%K��R�u}�(j�  h�j�  j�  K%K&K��R�u}�(j�  j  j�  j�  K&K'K��R�u}�(j�  j  j�  j�  K'K(K��R�u}�(j�  j  j�  j�  K(K)K��R�u}�(j�  j   j�  j�  K2K3K��R�u}�(j�  j)  j�  j�  K9K:K��R�u}�(j�  j2  j�  j�  K:K;K��R�u}�(j�  j;  j�  j�  K<K=K��R�u}�(j�  jD  j�  j�  K=K>K��R�u}�(j�  jO  j�  j�  K?K@K��R�u}�(j�  jY  j�  j�  KKK��R�u}�(j�  jc  j�  j�  KKK��R�u}�(j�  ji  j�  hhK ��h��R�(KK.��h�i8�����R�(KhyNNNJ����J����K t�b�Bp                                                    
                     
                                                                                                  "       )       *       +       ,       -       .       /       0       1       3       4       5       6       7       8       ;       >       @       A       B       C       �t�bu}�(j�  j�  j�  j�  KDKEK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.