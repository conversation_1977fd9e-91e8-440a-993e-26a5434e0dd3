���!      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKE��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�dataSourceName��date��__meta_model__��reportDetails.reportStatus��+reportDetails.tradingVenueTransactionIdCode��sourceIndex��	sourceKey��:tradersAlgosWaiversIndicators.commodityDerivativeIndicator��3tradersAlgosWaiversIndicators.otcPostTradeIndicator��=tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator��3tradersAlgosWaiversIndicators.shortSellingIndicator��-tradersAlgosWaiversIndicators.waiverIndicator��*transactionDetails.branchMembershipCountry��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��+transactionDetails.derivativeNotionalChange��transactionDetails.netAmount��transactionDetails.price�� transactionDetails.priceCurrency��%transactionDetails.priceNotApplicable�� transactionDetails.priceNotation��transactionDetails.pricePending��transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��transactionDetails.recordType��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime��transactionDetails.venue�� transactionDetails.ultimateVenue��!transactionDetails.upFrontPayment��)transactionDetails.upFrontPaymentCurrency��.transmissionDetails.orderTransmissionIndicator��reportDetails.transactionRefNo��marketIdentifiers.instrument��#instrument_classification_attribute��expiry_date_attribute��isin_attribute��underlying_index_name_attribute��%underlying_index_term_value_attribute��venue_attribute��option_strike_price_attribute��notional_currency_1_attribute��notional_currency_2_attribute��option_type_attribute��underlying_isin_attribute��marketIdentifiers.parties��marketIdentifiers��__strike_price_currency__��__notional_currency_1__��__instrument_full_name__��__instrument_classification__��__maturity_date__��__instrument_id_code_type__��__strike_price_type__��__instrument_id_code__��__notional_currency_2__��__delivery_type__��__option_exercise_style__��__strike_price__��__price_multiplier__��__notional_currency_2_type__��__data_category__��__is_created_through_fallback__��1parties.buyerTransmittingFirm.firmIdentifiers.lei��2parties.sellerTransmittingFirm.firmIdentifiers.lei��'transactionDetails.swapDirectionalities��__underlying_index_name__��__underlying_index_term__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C0                                           �t�bhk�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h!�]�(�CANC�h�h�h�h�h�et�b�_dtype�h��StringDtype���)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�25493001KJTIIGC8Y1R12BBG0000000000B64648FFDB23C0020��pandas._libs.missing��NA���h��25493001KJTIIGC8Y1R12BBG0000000000B64648FFDB23C0022�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�TESTBMTF275554MKT��TESTBMTF275554A1279512ALC��TESTBMTF275554A1279510ALC��TESTTREU275554MKT��TESTTREU275554A1279512ALC��TESTTREU275554A1279510ALC�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�h�et�bh�h�)��ubhhK ��h��R�(KKK��h�b1�����R�(Kh"NNNJ����J����K t�b�C�t�bhhK ��h��R�(KKK��h�f8�����R�(KhyNNNJ����J����K t�b�C�      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      ��t�bhhK ��h��R�(KKK��hx�C0                                           �t�bhhK ��h��R�(KK/K��h!�]�(�UnaVista Transaction Report�jG  jG  jG  jG  jG  h�h�h�h�h�h��RTS22Transaction�jH  jH  jH  jH  jH  �~/Users/<USER>/Desktop/steeleye/se-prefect-dir/swarm-tasks/tests/steeleye/tr/feed/una_vista/data/no_counterparty_source_df.pkl�jI  jI  jI  jI  jI  h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�G�      G�      G�      G�      G�      G�      h�h�h�h�h�h�������h�h�h�h�h�h�������h�h�h�h�h�h�h�h�h�h�h�h��Market Side��
Allocation�jK  jJ  jK  jK  h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      ]�}�(�labelId��lei:2138006dhu9ctxv86z88��path��parties.executingEntity��type��se_schema.static.market��IdentifierType����OBJECT���R�ua]�}�(jN  �lei:2138006dhu9ctxv86z88�jP  jQ  jR  jX  ua]�}�(jN  �lei:2138006dhu9ctxv86z88�jP  jQ  jR  jX  ua]�}�(jN  �lei:2138006dhu9ctxv86z88�jP  jQ  jR  jX  ua]�}�(jN  �lei:2138006dhu9ctxv86z88�jP  jQ  jR  jX  ua]�}�(jN  �lei:2138006dhu9ctxv86z88�jP  jQ  jR  jX  ua]�jM  a]�jZ  a]�j]  a]�j`  a]�jc  a]�jf  aG�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�G�      G�      G�      G�      G�      G�      et�bhhK ��h��R�(KKK��h!�]�(G�      G�      G�      G�      G�      G�      et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h(at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h)at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hFat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hHat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hIat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hJat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hKat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hLat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hMat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hWat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h^at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h_at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�haat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hbat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hcat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hdat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h6hNh`et�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bhkNu��R�h
h}�(hhhK ��h��R�(KK/��h!�]�(h%h&h'h+h,h-h.h/h0h1h2h3h4h5h7h8h9h:h<h=h>h?h@hAhBhChDhEhGhOhPhQhRhShThUhVhXhYhZh[h\h]hehfhghhet�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hiat�bhkNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs��builtins��slice���KKK��R�u}�(j:  h�j;  j>  KKK��R�u}�(j:  h�j;  j>  KKK��R�u}�(j:  h�j;  j>  K!K"K��R�u}�(j:  h�j;  j>  K#K$K��R�u}�(j:  h�j;  j>  K$K%K��R�u}�(j:  h�j;  j>  K%K&K��R�u}�(j:  h�j;  j>  K&K'K��R�u}�(j:  h�j;  j>  K'K(K��R�u}�(j:  h�j;  j>  K(K)K��R�u}�(j:  h�j;  j>  K2K3K��R�u}�(j:  h�j;  j>  K9K:K��R�u}�(j:  j  j;  j>  K:K;K��R�u}�(j:  j
  j;  j>  K<K=K��R�u}�(j:  j  j;  j>  K=K>K��R�u}�(j:  j  j;  j>  K>K?K��R�u}�(j:  j*  j;  j>  K?K@K��R�u}�(j:  j4  j;  hhK ��h��R�(KK��h�i8�����R�(KhyNNNJ����J����K t�b�C       )       ;       �t�bu}�(j:  j>  j;  j>  KKK��R�u}�(j:  jD  j;  hhK ��h��R�(KK/��jx  �Bx                                             	       
                     
                                                                                                                               "       *       +       ,       -       .       /       0       1       3       4       5       6       7       8       @       A       B       C       �t�bu}�(j:  jq  j;  j>  KDKEK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.