import os
from pathlib import Path

import pandas as pd
import pytest
from prefect import context
from swarm.conf import SettingsCls
from swarm.task.auditor import Auditor

from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI
from swarm_tasks.steeleye.tr.feed.lme.static import TempColumns
from swarm_tasks.steeleye.tr.transformations.rts22_transform_maps import (
    lme_transform_map,
)

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
TEST_FILE_PATH = TEST_FILES_DIR.joinpath(r"test_data.pkl")
EXPECTED_FILE_PATH = TEST_FILES_DIR.joinpath(r"expected_results.pkl")


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


class TestLmeTrTransformations:
    """
    Test LmeTrTransformations
    """

    @staticmethod
    def mock_get_tenant_lei(mocker, test_df):
        mock_get_tenant_lei = mocker.patch.object(GetTenantLEI, "process")
        tenant_lei_df = pd.DataFrame(index=test_df.index)
        tenant_lei_df = tenant_lei_df.assign(
            **{TempColumns.EXECUTING_ENTITY_WITHOUT_PREFIX: "875500PNFZENVO437436"}
        )
        mock_get_tenant_lei.return_value = tenant_lei_df
        return mock_get_tenant_lei

    def test_end_to_end_transformations(self, mocker, auditor):
        os.environ["SWARM_FILE_URL"] = str(TEST_FILE_PATH)

        # mock realm to chose default transformation
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "default.dev.steeleye.co"

        source_frame = pd.read_pickle(TEST_FILE_PATH)
        self.mock_get_tenant_lei(mocker, source_frame)

        task = lme_transform_map.transformation(tenant="foo")(
            source_frame=source_frame, logger=context.get("logger"), auditor=auditor
        )
        result = task.process()
        expected = pd.read_pickle(EXPECTED_FILE_PATH)

        pd.testing.assert_frame_equal(
            result.drop(["sourceKey"], axis=1),
            expected.drop(["sourceKey"], axis=1),
        )
