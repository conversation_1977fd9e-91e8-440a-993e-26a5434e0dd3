import pandas as pd
import pytest
from prefect.engine.signals import SKIP

from swarm_tasks.steeleye.tr.feed.lme.controller.transaction_record_transformer import (
    LmeTransactionFrameTransformer,
)
from swarm_tasks.steeleye.tr.feed.lme.static import SourceColumns


@pytest.fixture()
def sample_source_frame_list() -> dict:
    frame_dict = {
        "eodm": pd.DataFrame(
            {
                SourceColumns.MATCH_SLIP_ID: [1, 2, 3],
                SourceColumns.TRADE_DATE_TIME: [
                    "2022-04-13",
                    "2022-04-14",
                    "2022-04-15",
                ],
                SourceColumns.CLIENT_CODE: ["MC", "CD", "MC"],
                "flag": ["A", "B", "C"],
                SourceColumns.PRICE: ["25,345", "4356", "566"],
            }
        ),
        "additional_details": pd.DataFrame(
            {
                SourceColumns.MATCH_SLIP_ID: [1, 1, 2, 3],
                SourceColumns.TRADE_DATE_TIME: [
                    "2022-04-13",
                    "2022-04-13",
                    "2022-04-14",
                    "2022-04-15",
                ],
                SourceColumns.TRADER_ID: ["700", "700", "100", "600"],
                "flag": ["D", "D", "E", "F"],
            }
        ),
    }
    return frame_dict


@pytest.fixture()
def sample_source_frame_client_code() -> dict:
    frame_dict = {
        "eodm": pd.DataFrame(
            {
                SourceColumns.MATCH_SLIP_ID: [1, 2, 3],
                SourceColumns.TRADE_DATE_TIME: [
                    "2022-04-13",
                    "2022-04-14",
                    "2022-04-15",
                ],
                SourceColumns.CLIENT_CODE: ["MC", pd.NA, "MC"],
                "flag": ["A", "B", "C"],
                SourceColumns.PRICE: ["56,789", "4,009", "567"],
            }
        ),
        "additional_details": pd.DataFrame(
            {
                SourceColumns.MATCH_SLIP_ID: [1, 1, 2, 3],
                SourceColumns.TRADE_DATE_TIME: [
                    "2022-04-13",
                    "2022-04-13",
                    "2022-04-14",
                    "2022-04-15",
                ],
                SourceColumns.TRADER_ID: ["700", "700", "100", "600"],
                "flag": ["D", "D", "E", "F"],
            }
        ),
    }
    return frame_dict


@pytest.fixture()
def sample_s3_file_url_df() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "s3_eodm_file_url": ["/dir/random_eodm_name.csv"],
            "s3_additional_details_file_url": [
                "/dir/random_additional_details_name.csv"
            ],
        }
    )


class TestLmeTransactionFrameTransformer:
    """
    Test LME TR Record Merge Logic
    """

    def test_skip_scenario(self, sample_source_frame_list):
        pre_process_result = {
            "additional_details": sample_source_frame_list["additional_details"]
        }
        with pytest.raises(SKIP):
            task = LmeTransactionFrameTransformer(name="test_task")
            task.execute(pre_process_result=pre_process_result)

    def test_empty_data_scenario(
        self,
    ):
        with pytest.raises(SKIP):
            task = LmeTransactionFrameTransformer(name="test_task")
            task.execute(pre_process_result=dict())

    def test_valid_execution(
        self,
        sample_source_frame_list,
        sample_s3_file_url_df,
    ):
        task = LmeTransactionFrameTransformer(name="test_task")
        result = task.execute(
            pre_process_result=sample_source_frame_list,
            s3_file_url_df=sample_s3_file_url_df,
        )
        result_df = pd.read_csv(result.path).fillna(pd.NA)
        expected_df = pd.DataFrame(
            [
                {
                    "Match Slip ID": 1,
                    "Trade Date Time": "2022-04-13",
                    "flag": "A",
                    "Price": 25345,
                    "Trade Date Time_addl_info": "2022-04-13",
                    "flag_addl_info": "D",
                    "__TRIGGER_FILE_NAME__": "/dir/random_eodm_name.csv",
                    "Client Code": "MC",
                    "Trader ID": 700,
                },
                {
                    "Match Slip ID": 2,
                    "Trade Date Time": "2022-04-14",
                    "flag": "B",
                    "Price": 4356,
                    "Trade Date Time_addl_info": "2022-04-14",
                    "flag_addl_info": "E",
                    "__TRIGGER_FILE_NAME__": "/dir/random_eodm_name.csv",
                    "Client Code": "CD",
                    "Trader ID": 100,
                },
                {
                    "Match Slip ID": 3,
                    "Trade Date Time": "2022-04-15",
                    "flag": "C",
                    "Price": 566,
                    "Trade Date Time_addl_info": "2022-04-15",
                    "flag_addl_info": "F",
                    "__TRIGGER_FILE_NAME__": "/dir/random_eodm_name.csv",
                    "Client Code": "MC",
                    "Trader ID": 600,
                },
            ]
        )

        pd.testing.assert_frame_equal(
            result_df.sort_index(axis=1), expected_df.sort_index(axis=1)
        )

    def test_null_client_code(
        self,
        sample_source_frame_client_code,
        sample_s3_file_url_df,
    ):
        task = LmeTransactionFrameTransformer(name="test_task")

        result = task.execute(
            pre_process_result=sample_source_frame_client_code,
            s3_file_url_df=sample_s3_file_url_df,
        )
        result_df = pd.read_csv(result.path).fillna(pd.NA)

        expected_df = pd.DataFrame(
            [
                {
                    "Match Slip ID": 1,
                    "Trade Date Time": "2022-04-13",
                    "flag": "A",
                    "Trade Date Time_addl_info": "2022-04-13",
                    "flag_addl_info": "D",
                    "__TRIGGER_FILE_NAME__": "/dir/random_eodm_name.csv",
                    "Client Code": "MC",
                    "Trader ID": 700,
                    "Price": 56789,
                },
                {
                    "Match Slip ID": 2,
                    "Trade Date Time": "2022-04-14",
                    "flag": "B",
                    "Trade Date Time_addl_info": "2022-04-14",
                    "flag_addl_info": "E",
                    "__TRIGGER_FILE_NAME__": "/dir/random_eodm_name.csv",
                    "Client Code": "100",
                    "Trader ID": 100,
                    "Price": 4009,
                },
                {
                    "Match Slip ID": 3,
                    "Trade Date Time": "2022-04-15",
                    "flag": "C",
                    "Trade Date Time_addl_info": "2022-04-15",
                    "flag_addl_info": "F",
                    "__TRIGGER_FILE_NAME__": "/dir/random_eodm_name.csv",
                    "Client Code": "MC",
                    "Trader ID": 600,
                    "Price": 567,
                },
            ]
        )

        pd.testing.assert_frame_equal(
            result_df.sort_index(axis=1),
            expected_df.sort_index(axis=1),
        )
