import pytest

from swarm.audit.result.elastic import ElasticResultAggregator


@pytest.fixture()
def write_counts():
    return {
        "created": 3,
        "updated": 13,
        "deleted": 25,
        "errored": 26,
        "duplicate": 11,
        "quarantined": 1,
        "version_conflict": 2,
    }


@pytest.fixture()
def result_write_counts():
    return {
        "created": 3,
        "updated": 13,
        "deleted": 25,
        "errored": 26,
        "duplicate": 11,
        "quarantined": 1,
        "version_conflict": 2,
        "total": 81,
    }


class TestElastic:
    def test_sum_counts(self, write_counts, result_write_counts):
        """Tests the static method sum_counts"""
        # This function call mutates the write_counts dictionary and adds the 'total' key
        ElasticResultAggregator.sum_counts(write_counts)
        assert write_counts == result_write_counts
