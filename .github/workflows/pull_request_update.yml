name: Pull Request Update 

on:
  pull_request:
    types: [opened, edited, reopened, synchronize, ready_for_review]

jobs:
  pr-labeler:
    runs-on: ubuntu-latest
    steps:
      - uses: TimonVS/pr-labeler-action@v3
        env:
          GITHUB_TOKEN: ${{ secrets.GH_SE_BOT_TOKEN }}
  pr-assignee-reviewers:
    # Adds 2 reviewers, and assignee=author for opened and ready_for_review PRs
    runs-on: ubuntu-latest
    if: ${{ github.event.action == 'opened' || github.event.action == 'ready_for_review'}}
    steps:
      - uses: kentaro-m/auto-assign-action@v1.1.2
        with:
          configuration-path: .github/pr-assignee-reviewers.yml
