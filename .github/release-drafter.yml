name-template: 'v$RESOLVED_VERSION 🌈'
tag-template: 'v$RESOLVED_VERSION'

categories:
  - title: '💥 Breaking Changes '
    label: 'breaking-change'

  - title: '🚀 Features '
    labels:
      - 'enhancement'

  - title: '🐛 Bug Fixes '
    labels:
      - 'bug'

  - title: '🧰 Maintenance '
    label: 'maintenance'

  - title: ':green_book: Documentation '
    label: 'documentation'

  - title: ':syringe: Dependencies '
    label: 'dependencies'

change-template: '- $TITLE @$AUTHOR (#$NUMBER)'
change-title-escapes: '\<*_&'

exclude-labels:
  - 'skip-changelog'

replacers:
  - search: '/(?<=[,\[\s])([A-Z]{2,5}-\d+)(?=[,\]])/g'
    replace: '[$1](https://steeleye.atlassian.net/browse/$1)'

template: |
  # What's Changed

  $CHANGES

  # Contributors

  $CONTRIBUTORS
