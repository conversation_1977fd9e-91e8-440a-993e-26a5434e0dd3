[tool.poetry]
name = "swarm"
version = "6.0.2"
description = "This project will hold the Swarm SDK."
authors = [
"<PERSON><PERSON><PERSON> <fabio.ramal<PERSON>@steel-eye.com>",
"<PERSON> <<EMAIL>>",
"<PERSON> <<EMAIL>>",
"<PERSON> <<EMAIL>>",
"Puneeth R <<EMAIL>>"
]
license = "Apache-2.0"
readme = "README.md"
packages = [{ include = "swarm" }]
include = ["swarm/security/resources/*"]

[[tool.poetry.source]]
name = "steeleye-pypi"
url = "https://steeleye.jfrog.io/steeleye/api/pypi/pypi-local/simple/"
priority = "supplemental"


[[tool.poetry.source]]
name = "PyPI"
priority = "primary"

[tool.poetry.dependencies]
python = "^3.9.2"
addict = "^2.4.0"
boto3 = "^1.24"
click = "^8.0"
cryptography = ">=44.0.1"
elasticsearch6 = "^6.8.1"
email-validator = "^1.1.3"
hvac = "^1.1.1"
humanize = "^4.9.0"
pandas = "~=1.1.0"
numpy = "<1.20" # this constraint should happen where pandas 1.1 is a dependency
prefect = "^0.14.16"
pycryptodome = "^3.19.1"
pyotp = "^2.4.0"
pysftp = "^0.2.9"
python-jose = "^3.4.0"
pyyaml = ">=5.3.1"
stringcase = "^1.2.0"
typing_extensions = "^4.0.0"

# steeleye internal
indict = "~1.2.2"
se_boltons = "^0.4"
se-schema = "^5.22.18"
templated-email-generator = "^2024.01.04"
schema-sdk = "^1.6.5"
se-elasticsearch = "^3.1"
se-elastic-schema = ">=0.0.26"
paramiko = "3.4.0"

[tool.poetry.dev-dependencies]
pre-commit = "^2.17.0"
pytest = "^7.1"
pytest-mock = "^3.2.0"
pytest-html = "^3.2.0"
pytest-cov = "^2.8.1"
pytest-asyncio = "^0.18.3"
freezegun = "^1.2.2"

# docs
mkdocs-material = "^8.2.14"
mkdocstrings = "^0.18.1"
mkdocs-mermaid2-plugin = "^0.6.0"
mkdocs-section-index = "^0.3.4"
mkdocs-git-revision-date-localized-plugin = "^1.0.1"

[tool.poetry.scripts]
flow_runner = "swarm.flow.runner:main"
schema_publisher = "swarm.schema.publisher.publisher:main"

[build-system]
requires = ["poetry-core>=1.0.0", "pip>=20.3.3"]
build-backend = "poetry.core.masonry.api"
