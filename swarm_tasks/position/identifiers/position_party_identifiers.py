import pandas as pd
from pydantic import Field
from se_elastic_schema.components.market.identifier import Identifier
from se_elastic_schema.static.market import IdentifierType
from se_elastic_schema.static.mifid2 import PositionLevel
from se_elastic_schema.validators.iso.lei import LEI
from se_trades_tasks.order_and_tr.static import PartyPrefix
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.position.static import PositionColumns


class Params(BaseParams):
    target_attribute: str
    target_file_identifier_attribute: str = Field(PositionColumns.PARTY_FILE_IDENTIFIER)
    level: str
    account_identifier: str = "ACCOUNTID"
    client_identifier: str = "CLIENTID"
    fund_identifier: str = "FUNDID"
    portfolio_manager_identifier: str = "PORTFOLIOMANAGERID"
    risk_entity_identifier: str = "RISKENTITYID"
    trader_identifier: str = "TRADERID"


class PositionPartyIdentifiers(TransformBaseTask):
    """
    This task should create a column with ids which will be used
    by LinkParties to retrieve the documents to embed in the record. This parties
    identifiers task is specific for Position model, since parties for position
    is a different component which always has a single identifier (parties.value)
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources: BaseResources = None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(source_frame=source_frame, params=params)

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame = None,
        params: Params = None,
    ) -> pd.DataFrame:

        target = pd.DataFrame(
            index=source_frame.index, columns=[params.target_attribute]
        )
        if source_frame.empty:
            return target

        cols_used = [
            params.account_identifier,
            params.client_identifier,
            params.fund_identifier,
            params.portfolio_manager_identifier,
            params.risk_entity_identifier,
            params.trader_identifier,
            params.level,
        ]

        cols_mask = source_frame.columns.isin(cols_used)

        df = source_frame.loc[:, cols_mask]
        for col in cols_used:
            if col not in df.columns:
                df[col] = pd.NA
        identifiers_df: pd.DataFrame = pd.DataFrame(index=df.index)

        # The column to be used for identifiers depends on the level. For example,
        # if the level is Client, only the party identifier must be chosen from the
        # CLIENTID col for that row, irrespective of what the othe cols are populated with.
        level_party_type_map = {
            PositionLevel.ACCOUNT.value: params.account_identifier,
            PositionLevel.CLIENT.value: params.client_identifier,
            PositionLevel.FUND.value: params.fund_identifier,
            PositionLevel.PORTFOLIO.value: params.portfolio_manager_identifier,
            PositionLevel.RISK_ENTITY.value: params.risk_entity_identifier,
            PositionLevel.TRADER.value: params.trader_identifier,
        }

        # Mask between each level. This mask is used to generate party identifiers for each level
        # For example, there'd be a mask for client, where for all the rows where level=='Client'
        # and the key would be the source client col.
        masks_dict = {
            source_col: (df.loc[:, params.level] == level_)
            for level_, source_col in level_party_type_map.items()
        }
        for col, mask in masks_dict.items():
            identifiers_df.loc[mask, params.target_attribute] = (
                df.loc[mask, col]
                .dropna()
                .apply(
                    lambda x: [
                        Identifier(
                            labelId=cls._add_party_prefix(value=x),
                            path=PositionColumns.PARTY_VALUE,
                            type=IdentifierType.OBJECT,
                        ).dict()
                    ]
                )
            )
            identifiers_df.loc[mask, params.target_file_identifier_attribute] = (
                df.loc[mask, col].astype("string").str.lower()
            )

        return identifiers_df

    @staticmethod
    def _add_party_prefix(value):
        """
        Adds party prefix based on the the length. If len(party_id) == 20
        the prefix should be 'lei:' , else it should be 'id:'
        :param value: The raw identifier to which prefix needs to be added
        :return: identifier with prefix added.
        """
        if not value or pd.isna(value):
            return value
        return f"{(PartyPrefix.LEI if LEI.validate_lei_code(value).is_valid else PartyPrefix.ID)}{value}".lower()
