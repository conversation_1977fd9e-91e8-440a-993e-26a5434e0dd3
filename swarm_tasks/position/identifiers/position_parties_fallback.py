import pandas as pd
from pydantic import Field
from se_elastic_schema.static.mifid2 import PositionLevel
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.position.static import PositionColumns


class PartyField:
    NAME = "name"


class Params(BaseParams):
    source_attribute: str = Field(
        "__party__",
        description="The source column for raw party identifiers. This is the"
        "value that'll be used for records where no party match"
        "was found with LinkParties",
    )
    level: str = Field(
        PositionColumns.LEVEL, description="Level describing the type of party"
    )


ACCOUNT_PERSON_META_KEY = "AccountPerson:"
MARKET_COUNTERPARTY_META_KEY = "MarketCounterparty:"

# The default party-type for each level
LEVEL_DEFAULT_PARTY_TYPE_MAP = {
    PositionLevel.ACCOUNT: MARKET_COUNTERPARTY_META_KEY,
    PositionLevel.CLIENT: MARKET_COUNTERPARTY_META_KEY,
    PositionLevel.FUND: MARKET_COUNTERPARTY_META_KEY,
    PositionLevel.PORTFOLIO: ACCOUNT_PERSON_META_KEY,
    PositionLevel.RISK_ENTITY: MARKET_COUNTERPARTY_META_KEY,
    PositionLevel.TRADER: ACCOUNT_PERSON_META_KEY,
}


class PositionPartiesFallback(TransformBaseTask):
    """
    populate party fields using data directly from the input file
    if a row has an existing party it will not be updated
    Note: This task just populates the name, meta_key and meta_id of the party.
    The default PartyType is determined based on the level using LEVEL_DEFAULT_PARTY_TYPE_MAP
    """

    params_class = Params

    def execute(
        self, source_frame: pd.DataFrame = None, params: Params = None, **kwargs
    ) -> pd.DataFrame:
        target = pd.DataFrame(
            index=source_frame.index, columns=[PositionColumns.PARTY_VALUE]
        )
        if source_frame.empty:
            return target

        es = Settings.connections.get("tenant-data")
        df = source_frame.copy()

        if PositionColumns.PARTY_VALUE not in df.columns:
            df[PositionColumns.PARTY_VALUE] = pd.NA

        target.loc[:, PositionColumns.PARTY_VALUE] = self._fill_missing_party(
            df=df,
            party=PositionColumns.PARTY_VALUE,
            source_attribute=params.source_attribute,
            meta_key=es.meta.key,
            meta_id=es.meta.id,
            params=params,
        )
        return target

    def _fill_missing_party(
        self,
        df: pd.DataFrame,
        party: str,
        source_attribute: str,
        meta_key: str,
        meta_id: str,
        params: Params,
    ) -> pd.Series:
        """
        :param df: pd.DataFrame
        :param party: str
        :param source_attribute: str
        :param meta_key: str
        :param meta_id: str
        :param params: Params
        :return: pd.Series
        """
        result = df.loc[:, party]
        mask = (df[source_attribute].notnull()) & (df[party].isna())
        result.loc[mask] = self._format_party(
            df.loc[mask],
            name=source_attribute,
            params=params,
            meta_key=meta_key,
            meta_id=meta_id,
        ).apply(
            lambda x: x.dropna().to_dict(),
            axis=1,
        )

        return result

    @staticmethod
    def _format_party(
        source: pd.DataFrame,
        name: str,
        params: Params,
        meta_key: str = None,
        meta_id: str = None,
    ) -> pd.DataFrame:
        """
        :param source: pd.DataFrame
        :param name: str
        :param  params: Params
        :param meta_key: str
        :param meta_id: str
        :return: None
        map columns from source df to fields to be used in party
        fields for Position Model
        """
        df = pd.DataFrame(index=source.index)
        df.loc[:, PartyField.NAME] = source.loc[:, name]
        df.loc[:, PartyField.NAME] = df[PartyField.NAME].replace(
            r"^\s*$", pd.NA, regex=True
        )
        df.loc[:, meta_id] = df.loc[:, PartyField.NAME]
        df.loc[:, meta_key] = source.loc[:, params.level].map(
            LEVEL_DEFAULT_PARTY_TYPE_MAP
        )
        return df
