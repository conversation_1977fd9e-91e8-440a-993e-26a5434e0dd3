from json import loads
from typing import Any
from typing import Dict
from typing import List
from typing import Optional
from typing import Set
from typing import Union

from pydantic import Field
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


class Params(BaseParams):
    arg_name: Union[str, List[str]] = Field(
        ...,
        description="Arg name to check if present in `flow_args`",
    )
    all_present: bool = Field(
        True,
        description="If True, all arg names in `arg_name` must be present in the flow_args",
    )


class FlowArgsController(BaseTask):
    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        flow_args: Optional[str] = None,
        **kwargs,
    ) -> bool:
        """
        Implements control test on values from upstream tasks. Looks into the key
        `flow_args` looking for the param `arg_name` and if it is present in flow_args returns True.
        If we want to check for several values being present we pass `all_present` param as True and
        pass several values in arg_name.

        :param params:
        :param flow_args:
        :return: `<PERSON>ol`
        """

        flow_args_dict: Dict[str, Any] = loads(flow_args) if flow_args else dict()

        if isinstance(params.arg_name, str):
            arg_names = {params.arg_name}
        else:
            arg_names = set(params.arg_name)

        arg_names_present: Set[str] = arg_names.intersection(set(flow_args_dict.keys()))

        if params.all_present:
            return len(arg_names.difference(arg_names_present)) == 0

        return len(arg_names_present) > 0
