import asyncio
import concurrent.futures
import json
from functools import partial
from typing import Any
from typing import Dict
from typing import List
from typing import Optional

import pandas as pd
from indict import Indict
from prefect import context
from prefect.engine import signals
from pydantic import Field
from se_elastic_schema.models import Order
from se_trades_tasks.order.best_execution.plugins.best_ex_fx_rates import (
    run_best_ex_fx_rates_plugin,
)
from se_trades_tasks.order.best_execution.plugins.best_ex_volume import (
    run_best_ex_volume_plugin,
)
from se_trades_tasks.order.universal.order_eod_stats_enricher import (
    Params as OrderEODStatsParams,
)
from se_trades_tasks.order.universal.order_eod_stats_enricher import (
    run_order_eod_stats_enricher,
)
from swarm.conf import Settings
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask


logger = context.get("logger")


class Resources(BaseResources):
    es_client_key: str = Field("reference-data", description="es_client_key")
    es_tenant_key: str = Field("tenant-data", description="es_tenant_key")


class BestExVolumeGetMetrics(BaseTask):
    """
    This task fetches all the records from the given ElasticSearch query in the flow
    args and then converts it into a normal dataframe and runs it through the BestEX
    Volume plugin process.
    """

    resources_class = Resources

    def execute(
        self,
        resources: Optional[Resources] = None,
        flow_args: Optional[str] = None,
        **kwargs,
    ) -> pd.DataFrame:
        return self.process(resources=resources, flow_args=flow_args)

    def process(
        self,
        resources: Optional[Resources] = None,
        flow_args: Optional[str] = None,
    ) -> pd.DataFrame:
        args = json.loads(flow_args) if flow_args else {}
        if not args.get("query"):
            raise signals.FAIL(
                "`query` missing in `SWARM_FLOW_ARGS`. It's required to run the task."
            )
        else:
            query: Dict[Any, Any] = args.get("query")

        batch_results = self.search_batch_elasticsearch(
            resources=resources, query=query
        )

        populate_fields = args.get("populate_fields")
        converted_df = asyncio.run(
            self.convert_elastic_result_to_df(
                results=batch_results,
                populate_fields=populate_fields,
            )
        )

        # This hack because I am converting the dates in returned ES results
        # into ISO format and then the Validation will fail for only this field
        # so convert back to Date.
        # See `normalize_df` method.
        converted_date_series = pd.to_datetime(
            converted_df.loc[:, "date"], format="%Y-%m-%d"
        ).dt.date.astype(str)
        converted_df.loc[:, "date"] = converted_date_series

        best_ex_volume_result = self.execute_best_ex_volume_plugin(
            source_frame=converted_df,
        )

        if args.get("drop_populated_fields") and populate_fields:
            logger.info(f"Dropping extra populated fields: {populate_fields}")
            converted_df = converted_df.drop(list(populate_fields.keys()), axis=1)

        # this because best ex volume does some conversion on the source
        # frame so data isn't consistent
        converted_df.loc[:, "date"] = converted_date_series

        return pd.concat([converted_df, best_ex_volume_result], axis=1)

    @staticmethod
    def search_batch_elasticsearch(
        resources: Resources, query: Dict[Any, Any]
    ) -> List[Dict[Any, Any]]:
        """
        Queries Elasticsearch using the scroll API to fetch the 100k records at once
        and then retuns the batch of it.

        :param resources: Resources
        :param query: The Elasticsearch query to be used to fetch the record
        :return: List of results
        """
        es_client = Settings.connections.get(resources.es_tenant_key)

        # This preserves the scroll in Elasticsearch for 10 minutes too
        # because we don't know how long it can take to retrieve all the
        # 100k records and in case it goes OOM
        response = es_client.client.search(
            index=Order.get_elastic_index_alias(tenant=Settings.tenant),
            body=query,
            scroll="5m",
            size=5000,
        )

        scroll_id = response.get("_scroll_id")
        if not scroll_id:
            raise signals.FAIL("No scroll id found for the scroll search")

        results = []
        hits = response.get("hits", {}).get("hits", [])
        if not hits:
            raise signals.FAIL("Elasticsearch returned no hits.")

        results.extend(hits)

        while 0 < len(results) < 100000:
            response = es_client.client.scroll(scroll_id=scroll_id, scroll="5m")
            hits = response.get("hits", {}).get("hits", [])
            results.extend(hits)

            # scroll didn't get any data for the query, so break the
            # loop because we will still have the data in first search
            if not hits:
                break

        # Ignore 404 because Elasticsearch may have deleted the Scroll
        # before attempted to do
        es_client.client.clear_scroll(
            body={"scroll_id": [scroll_id]},
            ignore=(404,),
        )
        return results

    @staticmethod
    def convert_to_order_dict(value: Dict[Any, Any]) -> Dict[Any, Any]:
        return Order(**value).to_dict(exclude_none=True)

    @staticmethod
    async def convert_elastic_result_to_df(
        results: List[Dict[Any, Any]],
        populate_fields: Optional[Dict[str, str]] = None,
    ) -> pd.DataFrame:
        """
        Converts the Elasticsearch results into a DF which BestEx plugins can understand.
        This drops the meta columns and validation error columns because the result of this task
        will again run through the AssignMeta

        :param results: Elasticsearch results
        :param populate_fields: The extra additional fields to populate
        :return: dataframe result
        """
        # Use some CPU powers to give us some boost :) else
        # going one by one in loop to parse 100k records will be very slowww...
        logger.info("Creating thread pool with 2 threads")
        executor_pool = concurrent.futures.ThreadPoolExecutor(max_workers=2)
        try:
            loop = asyncio.get_running_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        parsed_records = await asyncio.gather(
            *[
                loop.run_in_executor(
                    executor_pool,
                    partial(
                        BestExVolumeGetMetrics.convert_to_order_dict,
                        result["_source"],
                    ),
                )
                for result in results
            ]
        )
        logger.info("Records parsing to dict from OrderState model finish")

        results_df = pd.DataFrame(parsed_records)
        logger.info(
            f"Converted {results_df.shape[0]} records from Elasticsearch to dataframe"
        )

        # These columns need to be normalized because they contain the value
        # which is required by the BestEx volume plugin to run.
        normalized_df = BestExVolumeGetMetrics.normalize_df(
            results_df=results_df,
            columns_to_normalize=[
                "instrumentDetails",
                "priceFormingData",
                "executionDetails",
                "orderIdentifiers",
                "transactionDetails",
                "timestamps",
            ],
        )

        # rename model and parent column to avoid the conflicting columns in the flow
        # as this will go through assign meta, it will have the same cols populated again
        normalized_df = normalized_df.rename(
            columns={
                "&model": "__meta_model__",
                "&parent": "__meta_parent__",
            }
        )

        # Drop Meta columns because the result of this flow will
        # run again through the AssignMeta and they will be populated back. It doesn't
        # filter the & timestamp because it shouldn't be updated.
        normalized_df = normalized_df.drop(
            [
                "&id",
                "&key",
                "&uniqueProps",
                "&user",
                "&version",
                "&hash",
                "&validationErrors",
            ],
            errors="ignore",
            axis=1,
        )

        if not populate_fields:
            return normalized_df
        else:
            return BestExVolumeGetMetrics.populate_fields_in_es_result(
                results_df=normalized_df,
                populate_fields=populate_fields,
            )

    @staticmethod
    def normalize_df(
        results_df: pd.DataFrame,
        columns_to_normalize: List[str],
        drop_parent_columns: bool = True,
    ) -> pd.DataFrame:
        """
        Normalizes the dataframe JSON/DICT columns and returns DF containing the
        results.
        It converts the raw datetime objects to ISO date format so that can be
        used without any problems in the downstream tasks.

        :param results_df: The dataframe on which to normalize should be performed
        :param columns_to_normalize: The column names to normalize
        :param drop_parent_columns: Whether to drop the parent column or not, defaults to TRUE
        :return: Result dataframe
        """
        results = []
        for col in columns_to_normalize:
            # Set max level to 0, so that it only flattens the first layer
            results.append(
                pd.json_normalize(results_df[col], max_level=0).add_prefix(f"{col}.")
            )

        if drop_parent_columns:
            results_df = results_df.drop(columns_to_normalize, axis=1)

        return pd.DataFrame.from_records(
            Indict(
                obj=pd.concat([results_df, *results], axis=1),
                from_dataframe_params={
                    "date_format": "iso",
                    "orient": "records",
                },
            )
            .to_dict()
            .get("data"),
        )

    @staticmethod
    def populate_fields_in_es_result(
        results_df: pd.DataFrame,
        populate_fields: Dict[str, str],
    ) -> pd.DataFrame:
        """
        Populate the extra required columns in the elastic result df as per the
        value provided.

        :param results_df: Elasticsearch records result
        :param populate_fields: The fields to populate
        :return: Elasticsearch result dataframe with the extra populated columns
        """
        for column, value in populate_fields.items():
            results_df[column] = value

        return results_df

    def execute_best_ex_volume_plugin(
        self,
        source_frame: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        Runs the BestEx Volume plugin to populate the BestEx Volumne related fields
        on the record.

        :param source_frame: Source frame where the data should be populated
        :return: BestEx Volume plugin result
        """
        logger.info("Starting BestEx volume plugin run")
        fx_rates_df = self.get_best_ex_volume_fx_rates_df(
            df=source_frame,
        )
        es_client = Settings.connections.get("reference-data")
        try:
            market_eod_data = run_order_eod_stats_enricher(
                source_frame=source_frame,
                es_client=es_client,
                params=OrderEODStatsParams(),
                auditor=self.auditor,
            )

        except Exception:
            market_eod_data = pd.DataFrame()

        return run_best_ex_volume_plugin(
            source_frame=source_frame,
            fx_rates=fx_rates_df,
            market_eod_data=market_eod_data,
            auditor=self.auditor,
        )

    def get_best_ex_volume_fx_rates_df(
        self,
        df: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        Gets the FX Rates DF for the Volume Plugin.
        NOTE: We don't need EOD Stats because we aren't updating
        Orders, it's just OrderState

        :param df: The source frame for which the meta should be fetched
        :return: fx_rates data frame
        """
        try:
            fx_rates = run_best_ex_fx_rates_plugin(
                source_frame=df,
                auditor=self.auditor,
            )
        except Exception as e:
            logger.info(f"Error in Best-Ex: Fx Rates calculation. Error: {e}")
            fx_rates = pd.DataFrame(index=df.index)

        return fx_rates
