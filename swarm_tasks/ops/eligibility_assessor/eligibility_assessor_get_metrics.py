import tempfile
from datetime import datetime
from pathlib import Path
from typing import Dict
from typing import List
from typing import NoReturn
from typing import Op<PERSON>
from typing import Union

import pandas as pd
from indict import Indict
from prefect import context
from prefect.engine import signals
from pydantic import Field
from se_core_tasks.core.core_dataclasses import S3Action
from se_core_tasks.core.core_dataclasses import S3File
from se_elastic_schema.models import RTS22Transaction
from se_elastic_schema.static.mifid2 import RTS22TransactionStatus
from se_elasticsearch.repository.static import MetaPrefix
from se_trades_tasks.tr.eligibility_assessor.eligibility_assessor import (
    run_eligibility_assessor,
)
from se_trades_tasks.tr.eligibility_assessor.eligibility_assessor import WorkflowFields
from swarm.conf import Settings
from swarm.task.auditor import Auditor
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask


class Params(BaseParams):
    return_dataframe: bool = Field(
        False,
        description="If true, returns the dataframe instead of the S3File instance",
    )


class Resources(BaseResources):
    es_client_key: str = Field("reference-data", description="es_client_key")
    es_tenant_key: str = Field("tenant-data", description="es_tenant_key")


class EligibilityAssessorGetMetrics(BaseTask):
    """
    This task fetches all Non-Reportable RTS22 Transaction records from an environment,
    runs those trades against the Eligibility Assessor Task, and creates a CSV with the
    trades that should be reportable.

    With the return_dataframe param as False (default) this task will return a list of
    S3File instances to be passed downstream to be uploaded.
    If return_dataframe is True the task will return a Dataframe with only meta and
    workflow fields to be updated with ElasticBulk
    """

    resources_class = Resources
    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        resources: Optional[Resources] = None,
        **kwargs,
    ) -> Union[List[S3File], pd.DataFrame]:

        return self.process(
            params=params, resources=resources, auditor=self.auditor, logger=self.logger
        )

    @classmethod
    def process(
        cls,
        params: Optional[Params],
        resources: Optional[Resources] = None,
        auditor: Auditor = None,
        logger=context.get("logger"),
    ) -> Union[List[S3File], pd.DataFrame]:
        last_sort = None
        now_reportable_df_list = []
        end_flag = False
        chunk_size = 5000
        batch_number = 1
        metrics = {"fetched": 0, "now_reportable": 0}

        logger.info(
            f"Starting Eligibility Assessor check on Non-Reportable trades present in {Settings.realm}"
        )
        while not end_flag:
            logger.info(f"Searching batch #{batch_number}")
            non_reportable, meta_prefix = cls._get_non_reportable_trades(
                tenant=Settings.tenant,
                resources=resources,
                last_sort=last_sort,
                chunk_size=chunk_size,
            )

            non_reportable_df = cls.get_df_from_es_results(es_results=non_reportable)

            if non_reportable_df.empty:
                break
            elif non_reportable_df.shape[0] < chunk_size:
                end_flag = True

            last_sort = cls.get_last_sort_from_results(es_results=non_reportable)

            eligibility_assessor_result = run_eligibility_assessor(
                source_frame=non_reportable_df,
                tenant=Settings.tenant,
                es_client=Settings.connections.get(resources.es_tenant_key),
                srp_client=Settings.connections.get(resources.es_client_key),
            )

            merged_df = pd.concat(
                [eligibility_assessor_result, non_reportable_df], axis=1
            )
            now_reportable = merged_df[
                merged_df["workflow.status"] == RTS22TransactionStatus.REPORTABLE
            ]

            cls.update_metrics(
                metrics_dict=metrics,
                total_df=merged_df,
                now_reportable_df=now_reportable,
            )

            if not now_reportable.empty:
                if params.return_dataframe:
                    cols_list = [
                        x
                        for x in now_reportable.columns
                        if x.startswith((meta_prefix, "workflow"))
                    ]
                else:
                    cols_list = [
                        "date",
                        "sourceKey",
                        "sourceIndex",
                        "&id",
                        "reportDetails.transactionRefNo",
                    ]

                now_reportable_df_list.append(now_reportable[cols_list])
                logger.info(
                    f"Found {len(now_reportable)} trades in batch #{batch_number} that should be reportable."
                )

            batch_number += 1

        logger.info(
            f"Found a total of {metrics['now_reportable']} trades "
            f"that should be reportable out of {metrics['fetched']} in {Settings.realm}"
        )

        if not now_reportable_df_list:
            raise signals.SKIP("No trades to upload.")

        final_df = pd.concat(now_reportable_df_list).reset_index(drop=True)

        if params.return_dataframe:
            return final_df

        s3_file, s3_key = cls.prepare_s3_upload(final_df)
        logger.info(f"Uploading {s3_key} to {Settings.realm}...")

        return [s3_file]

    @classmethod
    def _get_non_reportable_trades(
        cls,
        tenant: str,
        resources: Resources,
        last_sort: Optional[List[Union[int, str]]],
        chunk_size: int,
    ) -> [Union[Dict, dict], str]:
        """
        Builds the ES query and performs the ES search
        :param tenant: tenant's name
        :param resources: Resources instance
        :param last_sort: last sort from previous search
        :param chunk_size: chunk size of the search
        :return: ES result
        """

        query = cls.generate_non_reportable_trades_query(
            last_sort=last_sort, chunk_size=chunk_size
        )

        es_client = Settings.connections.get(resources.es_tenant_key)
        results = es_client.search(
            query=query,
            index=RTS22Transaction.get_elastic_index_alias(tenant=tenant),
        )

        return results, MetaPrefix.AMPERSAND

    @staticmethod
    def generate_non_reportable_trades_query(
        last_sort: Optional[List[Union[int, str]]], chunk_size: int
    ):
        """
        Builds the query to search ES, considering the presence of the
        last_source variable
        :param last_sort: last sort from the previous search
        :param chunk_size: chunk size of the search
        :return: query
        """
        body = {
            "size": chunk_size,
            "query": {
                "bool": {
                    "must_not": [{"exists": {"field": "&expiry"}}],
                    "must": [
                        {
                            "match": {
                                WorkflowFields.STATUS: RTS22TransactionStatus.NON_REPORTABLE.value
                            }
                        }
                    ],
                }
            },
            "sort": [{"&timestamp": {"order": "asc"}}, {"&id": {"order": "asc"}}],
        }
        if last_sort:
            body.update({"search_after": last_sort})

        return body

    @classmethod
    def get_df_from_es_results(cls, es_results: Union[Dict, dict]) -> pd.DataFrame:
        """
        Extracts the _source from the hits of the ES search into a dataframe,
        and then transforms some fields to some extent to be able to be used in
        the EligibilityAssessor task
        :param es_results: ES results
        :return: ES results converted into dataframe
        """
        hits = es_results.get("hits", {}).get("hits", [])
        if not hits:
            return pd.DataFrame()

        results_df = pd.DataFrame.from_records(
            [cls.flatten_dict(x["_source"]) for x in hits]
        )

        return cls.adapt_records_for_eligibility_assessor(es_result_df=results_df)

    @staticmethod
    def flatten_dict(unflattened_dict: dict) -> dict:
        """
        flattens the dictionaries from ES result
        :param unflattened_dict: unflattened dictionary
        :return: flattened dictionary
        """
        return Indict(obj=unflattened_dict).flatten().to_dict()

    @classmethod
    def adapt_records_for_eligibility_assessor(
        cls, es_result_df: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Transforms instrumentDetails.instrument so that they're usable in EligibilityAssessor,
        drops workflow fields.
        Moves instrumentDetails.instrument fields to a separate dataframe,
            turns the dataframe into dictionaries,
            performs the necessary conversions to the dictionaries
            and then assigns it to the instrumentDetails.instrument column
        :param es_result_df: ES results dataframe
        :return: ES results dataframe with converted fields
        """
        instr_dtls_instr_cols = [
            x for x in es_result_df if x.startswith("instrumentDetails.instrument")
        ]
        workflow_cols = [x for x in es_result_df if x.startswith("workflow")]

        instr_dtls_instr_df = es_result_df.loc[:, instr_dtls_instr_cols]

        columns_to_drop = instr_dtls_instr_cols + workflow_cols
        source_df = es_result_df.drop(columns_to_drop, axis=1)

        instr_dtls_instr_dicts = instr_dtls_instr_df.to_dict("records")

        for i in range(len(instr_dtls_instr_dicts)):
            instr_dtls_instr_dicts[i] = cls.transform_instrument_details(
                instr_details_dict=instr_dtls_instr_dicts[i]
            )

        source_df.loc[:, "instrumentDetails.instrument"] = pd.Series(
            instr_dtls_instr_dicts, index=instr_dtls_instr_df.index
        )

        return source_df

    @classmethod
    def transform_instrument_details(cls, instr_details_dict: dict) -> dict:
        """
        Transforms the instrumentDetails.instrument dictionary to align with
        the expected format within the EligibilityAssessor.
        Ensures the existence of "instrumentIdCode" and unflattens ["derivative", "ext", "venue"].
        :param instr_details_dict:
        :return:
        """
        indict_to_dict = (
            Indict(obj=instr_details_dict)
            .unflatten()
            .to_dict()
            .get("instrumentDetails", {})
            .get("instrument", {})
        )

        # these fields need to be flattened, but the fields inside them don't
        dict_fields = [
            x for x in ["derivative", "ext", "venue"] if x in indict_to_dict.keys()
        ]

        for field in dict_fields:
            field_dict = indict_to_dict.pop(field)

            field_dict_keys = list(field_dict.keys())
            for key in field_dict_keys:
                field_dict[f"{field}.{key}"] = field_dict.pop(key)

            indict_to_dict.update(field_dict)

        return indict_to_dict

    @staticmethod
    def get_last_sort_from_results(
        es_results: Union[Dict, dict]
    ) -> List[Union[int, str]]:
        """
        Extracts the last sort value of the hits list.
        :param es_results: ES results
        :return: Last sort value
        """
        return es_results["hits"]["hits"][-1]["sort"]

    @staticmethod
    def update_metrics(
        metrics_dict: dict, total_df: pd.DataFrame, now_reportable_df: pd.DataFrame
    ) -> NoReturn:
        """
        Updates the metrics dictionary with the present batch's relevant numbers
        :param metrics_dict: dictionary with "fetched" and "now_reportable"
        :param total_df: ES results dataframe
        :param now_reportable_df: Eligibility Assessor dataframe with only reportable trades
        """
        metrics_dict["fetched"] += len(total_df)
        metrics_dict["now_reportable"] += len(now_reportable_df)

    @staticmethod
    def prepare_s3_upload(final_df: pd.DataFrame):
        """
        Saves the final_df to a temp local path and creates the S3File object to
        be uploaded downstream.
        :param final_df: Dataframe to upload
        :return: S3File object and s3 key
        """
        timestamp = datetime.utcnow()
        month = timestamp.strftime("%Y-%m")
        hour = timestamp.strftime("%Y%m%d_%H%M%S")
        filename = f"eligibility_assessor_metrics_{hour}"
        temp_dir = tempfile.gettempdir()
        local_path = Path(temp_dir).joinpath(filename).with_suffix(".csv")
        final_df.to_csv(local_path, index=False)
        s3_key = (
            Path(f"internal/flows/{Settings.bundle}/{month}/{filename}")
            .with_suffix(".csv")
            .as_posix()
        )
        s3_file = S3File(
            file_path=local_path,
            bucket_name=Settings.realm,
            key_name=s3_key,
            action=S3Action.UPLOAD,
        )
        return s3_file, s3_key
