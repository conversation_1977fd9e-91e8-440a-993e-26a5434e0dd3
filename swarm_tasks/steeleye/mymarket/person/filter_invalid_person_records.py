import pandas as pd
from prefect import context
from prefect.engine import signals
from pydantic import Field
from swarm.task.auditor import Auditor
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams):

    source_first_name_column: str = Field(
        ...,
        description="Name of the column which contains the first name",
    )
    source_last_name_column: str = Field(
        ...,
        description="Name of the column which contains the last name",
    )

    source_model_column: str = Field(
        ...,
        description="Name of the column which contains the model (AccountPerson/MarketPerson)",
    )


class FilterInvalidPersonRecords(TransformBaseTask):
    """
    This task drops invalid records, as well as duplicate records based on
    the first name and last name. The operations it does:
    1. Drops records without a first name, last name or &model
    2. Removes duplicate persons (detected by first name, last name and &model)
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources=None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame,
            params=params,
            logger=self.logger,
            auditor=self.auditor,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        logger=context.get("logger"),
        auditor: Auditor = None,
    ) -> pd.DataFrame:
        """Class method which drops invalid and duplicate records."""

        if source_frame.empty:
            message = "Source frame empty, raising SKIP signal"
            auditor.add(message)
            raise signals.SKIP(message)

        # Remove rows without a first name or a last name or type

        name_or_model_null_mask = (
            (source_frame.loc[:, params.source_first_name_column].isnull())
            | (source_frame.loc[:, params.source_last_name_column].isnull())
            | (source_frame.loc[:, params.source_model_column].isnull())
        )
        num_records_without_required_columns = name_or_model_null_mask.sum()
        if num_records_without_required_columns > 0:
            audit_message = (
                f"{num_records_without_required_columns} record(s) in the file don't have"
                f" first name and last name populated."
                f" These records have been dropped."
            )
            auditor.add(message=audit_message)
            logger.info(audit_message)

        df = source_frame.loc[~name_or_model_null_mask].reset_index(drop=True)

        # Drop duplicates
        # Remove duplicate rows in the source frame based on the first name, last name and &model
        duplicated_mask = df.duplicated(
            subset=[
                params.source_model_column,
                params.source_first_name_column,
                params.source_last_name_column,
            ]
        )

        num_duplicated_records = duplicated_mask.sum()
        if num_duplicated_records > 0:
            audit_message = (
                f"{num_duplicated_records} duplicate record(s) in the file"
                f" have been dropped."
            )
            cols_mask = df.columns.isin(
                [
                    params.source_first_name_column,
                    params.source_last_name_column,
                    params.source_model_column,
                ]
            )
            duplicated_names = df.loc[duplicated_mask, cols_mask].values.tolist()
            message_dup_records = (
                f"Duplicate records in input file (based on model"
                f", first name and last name): {duplicated_names}."
            )
            auditor.add(
                message=audit_message,
                ctx={"error": message_dup_records},
            )
            logger.info(f"{audit_message} \n {message_dup_records}")

        df = df.loc[~duplicated_mask]
        # We need to reset index, this will be the input to the rest of the tasks
        df = df.reset_index(drop=True)
        if df.empty:
            skip_message = "All records have invalid first name/last name. Skipping the rest of the flow"
            auditor.add(message=skip_message)
            raise signals.SKIP(skip_message)

        return df
