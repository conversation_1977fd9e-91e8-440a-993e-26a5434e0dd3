from itertools import chain
from typing import List
from typing import Optional

import pandas as pd
from prefect import context
from pydantic import Field
from se_elastic_schema.validators.iso.lei import LEI
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.steeleye.mymarket.firm.utils import UpdateColumns


class Params(BaseParams):
    source_lei_col: str = Field(
        ...,
        description="Name of the source column which contains the LEI",
    )
    source_trade_file_id_col_prefix: str = Field(
        ...,
        description="Column Prefix for trade file identifiers. There"
        "can be multiple columns with this prefix",
    )
    source_name_col: str = Field(
        ...,
        description="Column containing the name. This is only required for auditing"
        "invalid or null LEIs",
    )
    target_lei_col: str = Field(
        default="firmIdentifiers.lei",
        description="Name of target column containing the LEI",
    )
    target_trade_file_ids_col: str = Field(
        default="sinkIdentifiers.tradeFileIdentifiers",
        description="Name of target column containing all trade file identifiers"
        " in the required schema format",
    )
    target_unique_ids_col: str = Field(
        default="uniqueIds", description="Name of target column containing uniqueIds"
    )
    update_with_es_result: bool = Field(
        default=False,
        description="If `True` update the identifiers with existing identifiers from ES."
        "This is done when we want to update some records."
        "tradeFileIdentifiers are expected in column `es_trade_file_identifiers`"
        "trade ids are expected in column `es_trade_ids`",
    )

    def input_columns(self):
        """Returns a list of required source columns. For 'prefix' columns,
        only 1 'required' column is created. So, only 1 TRADEFILEIDENTIFIERS
        column is created.
        """
        return [
            self.source_lei_col,
            self.source_trade_file_id_col_prefix,
            self.source_name_col,
        ]

    def target_columns(self):
        """Returns a list of target columns."""
        return [
            self.target_lei_col,
            self.target_trade_file_ids_col,
            self.target_unique_ids_col,
        ]


class ColumnKeys:
    ID = "id"
    LABEL = "label"


class Prefixes:
    ACCOUNT = "account"
    ID = "id"
    LEI = "lei"


class TempColumns:
    TRADE_FILE_IDS_UNIQUE = "trade_file_ids_unique"


class CreateFirmIdentifiers(TransformBaseTask):
    """
    This task populates a number of firm identifiers from the source columns.
    It populates the LEI if the source is 20 characters long. It populates
    trade file identifiers from the source column, as well as from the LEI column.
    It also populates uniqueIds from the trade file identifiers, including the
    appropriate labels
    If the params.update_with_es_result is True, it updated the obtained results
        with provided identifiers.

    The following list contains all the target columns that this task populates:
    * firmIdentifiers.lei
    * sinkIdentifiers.tradeFileIdentifiers
    * uniqueIds
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources=None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame,
            params=params,
            logger=self.logger,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        logger=context.get("logger"),
    ) -> pd.DataFrame:
        """Process class method which populates all the communication and trade
        file identifiers, as well as uniqueIds"""

        df = source_frame.copy()
        for col in params.target_columns():
            df[col] = pd.NA

        if source_frame.empty:
            logger.warning("Source frame empty, returning empty data frame")
            return df

        # Add source columns if not present
        for col in params.input_columns():
            if col not in df.columns:
                df[col] = pd.NA

        # Populate LEI
        # Records with null/invalid LEI will still be ingested with LEI=pd.NA
        source_lei_not_null_mask = source_frame.loc[:, params.source_lei_col].notnull()
        df.loc[source_lei_not_null_mask, params.target_lei_col] = source_frame.loc[
            source_lei_not_null_mask, params.source_lei_col
        ].apply(lambda lei: lei if LEI.validate_lei_code(value=lei).is_valid else None)

        # Audit firms with invalid/null LEIs
        invalid_or_null_lei_mask = df.loc[:, params.target_lei_col].isnull()
        log_message = (
            f"{invalid_or_null_lei_mask.sum()} records have invalid LEIs,"
            f" they will still be ingested with LEI=null"
        )
        firms_corresponding_to_invalid_leis = df.loc[
            invalid_or_null_lei_mask, params.source_name_col
        ].tolist()
        if len(firms_corresponding_to_invalid_leis) > 0:
            firms_null_invalid_msg = (
                f"Firms with null/invalid LEIs: {firms_corresponding_to_invalid_leis}."
            )
            logger.info(f"{log_message} \n {firms_null_invalid_msg}")

        # Populate Trade file ids column
        trade_file_ids_columns = list(
            filter(
                lambda x: x.startswith(params.source_trade_file_id_col_prefix),
                df.columns,
            )
        )
        # Append LEI column to trade file id columns
        trade_file_ids_and_lei_columns = trade_file_ids_columns + [
            params.target_lei_col
        ]

        if params.update_with_es_result:
            trade_file_ids_and_lei_columns.append(UpdateColumns.ES_TRADE_FILE_IDS)

        trade_file_ids_lei_columns_mask = df.columns.isin(
            trade_file_ids_and_lei_columns
        )
        df.loc[:, params.target_trade_file_ids_col] = df.loc[
            :, trade_file_ids_lei_columns_mask
        ].apply(
            lambda x: cls._populate_trade_file_ids(
                trade_file_ids=x,
                trade_file_id_columns=trade_file_ids_columns,
                params=params,
            ),
            axis=1,
        )

        # Get unique trade file ids (along with prefix) for uniqueIds population
        if params.update_with_es_result:
            trade_file_ids_columns.append(UpdateColumns.ES_TRADE_IDS)
        trade_file_ids_columns_mask = df.columns.isin(trade_file_ids_columns)
        df.loc[:, TempColumns.TRADE_FILE_IDS_UNIQUE] = df.loc[
            :, trade_file_ids_columns_mask
        ].apply(
            lambda x: cls._concatenate_unique_trade_file_ids(
                trade_file_ids=x, params=params
            ),
            axis=1,
        )
        # Populate unique Ids from trade file ids and LEI.
        unique_ids_cols_mask = df.columns.isin(
            [params.target_lei_col, TempColumns.TRADE_FILE_IDS_UNIQUE]
        )
        df.loc[:, params.target_unique_ids_col] = df.loc[:, unique_ids_cols_mask].apply(
            lambda x: cls._populate_unique_ids(identifiers=x, params=params), axis=1
        )
        target_cols_mask = df.columns.isin(params.target_columns())
        return df.loc[:, target_cols_mask].fillna(pd.NA)

    @staticmethod
    def _populate_trade_file_ids(
        trade_file_ids: pd.Series, trade_file_id_columns: List[str], params: Params
    ) -> Optional[List[dict]]:
        """
        :param: trade_file_ids: Series containing the values for all trade file id
                columns for 1 row. The index of the series comes from the column
                names of the trade file id columns (and the LEI column in the calling
                function's dataframe. The non-lei columns are identified by
                the trade_file_id_columns argument
        :param: trade_file_id_columns: Name of the trade file id columns
        :param: params: params instance
        :returns: List of trade file ids in the required schema format
        """
        concat_list = []
        lei = trade_file_ids[params.target_lei_col]
        # Add the LEI first
        if not pd.isna(lei):
            concat_list.append({ColumnKeys.ID: lei, ColumnKeys.LABEL: Prefixes.LEI})
        # Add each of the trade file ids with 2 prefixes: id and account
        for trade_file_id_col in trade_file_id_columns:
            trade_file_id = trade_file_ids[trade_file_id_col]
            if not pd.isna(trade_file_id):
                if (
                    LEI.validate_lei_code(trade_file_id).is_valid
                    and trade_file_id != lei
                ):
                    concat_list.append(
                        {ColumnKeys.ID: trade_file_id, ColumnKeys.LABEL: Prefixes.LEI}
                    )
                else:
                    concat_list.append(
                        {ColumnKeys.ID: trade_file_id, ColumnKeys.LABEL: Prefixes.ID}
                    )
                    concat_list.append(
                        {
                            ColumnKeys.ID: trade_file_id,
                            ColumnKeys.LABEL: Prefixes.ACCOUNT,
                        }
                    )

        if params.update_with_es_result:
            es_existing_ids = trade_file_ids[UpdateColumns.ES_TRADE_FILE_IDS]
            if isinstance(es_existing_ids, List):
                for existing_id in es_existing_ids:
                    if existing_id not in concat_list:
                        concat_list.append(existing_id)

        return None if not concat_list else concat_list

    @staticmethod
    def _concatenate_unique_trade_file_ids(
        trade_file_ids: pd.Series, params: Params
    ) -> List[str]:
        """Returns a list of concatenated trade file ids which are required for
        populating uniqueIds. These IDs include the prefix (id/account). If there
        are no trade file ids in the source, it returns an empty list
        If params.update_with_es_result is True, updates the obtained result with a provided list
            of trade ids
        :param: trade_file_ids: Series containing the values for all trade_file_ids columns for
                1 row. The index of the series comes from the column names of the trade
                file id columns in the original df
        :returns: List of concatenated trade file ids, to be used in uniqueIds population
        """
        if params.update_with_es_result:
            es_trade_file_ids = trade_file_ids[UpdateColumns.ES_TRADE_IDS]
            trade_file_ids = trade_file_ids.drop(UpdateColumns.ES_TRADE_IDS)
        else:
            es_trade_file_ids = None

        concat_list = [
            [f"{Prefixes.ID}:{trade_file_id}", f"{Prefixes.ACCOUNT}:{trade_file_id}"]
            for trade_file_id in trade_file_ids
            if not pd.isna(trade_file_id)
        ]

        if not concat_list:
            return []

        expand_list = list(chain(*concat_list))

        if isinstance(es_trade_file_ids, List):
            expand_list = list(set(expand_list + es_trade_file_ids))

        return expand_list

    @staticmethod
    def _populate_unique_ids(
        identifiers: pd.Series, params: Params
    ) -> Optional[List[str]]:
        """
        Takes a Series containing (i) a list of trade file identifiers and
        (ii) a single LEI. It creates one list containing all identifiers
        after removing duplicates. If both the LEI and trade file identifiers
        are not present, it returns None
        :param: identifiers: Series containing a list of trade file identifiers and
                the LEI for 1 row.
                The index of the series comes from the column names created in the
                calling function
        :param: params: Params instance
        :returns: Concatenated list of unique ids, or None
        """
        concat_list = []
        if not pd.isna(identifiers[params.target_lei_col]):
            concat_list.append(f"{Prefixes.LEI}:{identifiers[params.target_lei_col]}")
        for trade_file_id_with_prefix in identifiers[TempColumns.TRADE_FILE_IDS_UNIQUE]:
            if not pd.isna(trade_file_id_with_prefix):
                concat_list.append(trade_file_id_with_prefix)

        # Remove duplicates
        concat_list = list(set(concat_list))
        # Make all ids lower case
        if concat_list:
            concat_list = [element.lower().strip() for element in concat_list]
        return None if not concat_list else concat_list
