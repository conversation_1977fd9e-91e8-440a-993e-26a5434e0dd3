import logging

import pandas as pd
from prefect import context
from prefect.engine import signals
from pydantic import Field
from swarm.conf import Settings
from swarm.task.auditor import Auditor
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.steeleye.mymarket.firm.helper_functions import (
    audit_duplicate_ids_in_file,
)
from swarm_tasks.steeleye.mymarket.firm.helper_functions import (
    audit_ids_already_in_elastic,
)
from swarm_tasks.steeleye.mymarket.firm.helper_functions import (
    fetch_counterparty_records_from_elastic,
)
from swarm_tasks.steeleye.mymarket.firm.utils import UpdateColumns
from swarm_tasks.utilities.static import MetaModel


class Params(BaseParams):
    name_column: str = Field(
        default="name", description="Source column which contains the name"
    )
    keep_firms_to_update: bool = Field(
        default=False,
        description="If True keep firms that are to be updated downstream."
        "Firms in this scenario should have a column `__to_update__` as True",
    )


class FilterOutFirmsWithDupNames(TransformBaseTask):
    """
    This task receives a dataframe containing MarketCounterparty data enriched
    with names from LeiRecord (in SRP).
    It performs 3 tasks: (1) It removed records with NULL name (even after LeiRecord
    enrichment). (2) It removes any duplicate rows in the source frame
    based on the Name field. (3) It looks up the Names in Elasticsearch (in
    MarketCounterparty) and keeps only those records which are NOT found.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:
        return self.process(
            source_frame=source_frame,
            params=params,
            logger=self.logger,
            auditor=self.auditor,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        logger=context.get("logger"),
        auditor: Auditor = None,
    ) -> pd.DataFrame:
        if source_frame.empty or params.name_column not in source_frame.columns:
            raise signals.SKIP(
                "Source frame empty or required column not present, returning empty DataFrame"
            )
        # Drop rows with null name (even after the LeiRecord lookup).
        name_not_null_mask = source_frame.loc[:, params.name_column].notnull()
        df = source_frame.loc[name_not_null_mask, :]

        es_client = Settings.connections.get("tenant-data")
        duplicated_name_mask = df.duplicated(subset=[params.name_column])

        # Audit duplicate records based on the name column
        audit_duplicate_ids_in_file(
            df=df,
            duplicated_mask=duplicated_name_mask,
            source_col=params.name_column,
            logger=logger,
            auditor=auditor,
        )

        df = df.loc[~duplicated_name_mask].reset_index(drop=True)

        # Filter out firms already in Elastic (based on the name column)
        target = cls._process_counterparty_records(
            df=df,
            es_client=es_client,
            name_column=params.name_column,
            params=params,
            logger=logger,
            auditor=auditor,
        )

        return target.fillna(pd.NA)

    @classmethod
    def _process_counterparty_records(
        cls,
        df: pd.DataFrame,
        es_client,
        name_column: str,
        params: Params,
        logger: logging.Logger,
        auditor: Auditor,
    ) -> pd.DataFrame:
        """Processes counterparty records. It retrieves the records from Elastic
        which have the same LEIs or names as the records in the source data frame.
        It then returns only records in the source frame which DO NOT have the same
        LEI or name as a record from Elastic.
        If params.keep_firms_to_update is True, checks for `__to_update__` column and
            does not discard any with the True
        :param: df: source_df containing records for a client
        :param: es_client: ElasticsearchClient instance
        :param: name_column: name of the field containing the name, values from this
                column are searched in MarketCounterparty
        :param: params: task Params
        :param: logger: Logger for logging messages
        :param: auditor: Auditor to write into SinkFileAudit
        :returns: DataFrame containing records where the records were not found
                  in Elasticsearch
        """

        # Drop names which are already in Elastic
        names_list = list(set(df.loc[:, name_column].dropna().tolist()))
        logger.info(
            f"Fetching {len(names_list)} name(s) from {MetaModel.MARKET_COUNTERPARTY}"
        )
        fetched_name_counterparty_records = fetch_counterparty_records_from_elastic(
            ids=names_list,
            es_client=es_client,
            lookup_column=name_column,
            logger=logger,
        )
        if fetched_name_counterparty_records.empty:
            logger.info(
                f"No records found in Elastic with the following names: {names_list}"
            )
            return df

        name_found_in_elastic_mask = df.loc[:, name_column].isin(
            fetched_name_counterparty_records[name_column]
        )

        if params.keep_firms_to_update:
            name_found_in_elastic_mask = (
                name_found_in_elastic_mask & ~df.loc[:, UpdateColumns.TO_UPDATE]
            )

        # Filter out records which are already in Elastic based on name
        duplicates_name_df = df.loc[name_found_in_elastic_mask]
        # Audit names which are already in Elastic
        audit_ids_already_in_elastic(
            duplicates_df=duplicates_name_df,
            source_col=name_column,
            logger=logger,
            auditor=auditor,
        )
        target = df.loc[~name_found_in_elastic_mask]

        if target.empty:
            all_dup_message = "All records in input file are already in the database. Skipping rest of the flow"
            auditor.add(message=all_dup_message)
            raise signals.SKIP(all_dup_message)
        return target
