import logging
from typing import List
from typing import NoReturn

import pandas as pd
from se_elastic_schema.models import MarketCounterparty
from swarm.conf import Settings
from swarm.task.auditor import Auditor

from swarm_tasks.utilities.es.query_utils import get_terms_query
from swarm_tasks.utilities.static import MetaModel


def audit_duplicate_ids_in_file(
    df: pd.DataFrame,
    duplicated_mask: pd.Series.bool,
    source_col: str,
    logger: logging.Logger,
    auditor: Auditor,
) -> NoReturn:
    """This function audits all duplicate ids in the file (ids can be either
    names or LEIs, based on the source_col param). It also logs the same messages
    that are audited. If no duplicates are present, nothing is logged/audited.

    :param: duplicated_mask: Boolean mask which indicates whether an ID is a
            duplicate or not
    :param: df: source DataFrame on which the duplicated_mask param is applied
    :param: source_col: column based on which duplicates have been detected
    :param: logger: Logger for logging messages
    :param: auditor: Auditor for auditing messages
    :returns: None
    """
    num_dup_records = duplicated_mask.sum()
    if num_dup_records > 0:
        audit_message = (
            f"{num_dup_records} duplicate record(s) in the file"
            f" have been dropped (based on {source_col})."
        )
        duplicate_ids = df.loc[duplicated_mask, source_col].tolist()
        message_dup_records = (
            f"Duplicate {source_col}(s) in input file: {duplicate_ids}."
        )
        auditor.add(
            message=audit_message,
            ctx={"error": message_dup_records},
        )
        logger.info(f"{audit_message} \n {message_dup_records}")


def audit_ids_already_in_elastic(
    duplicates_df: pd.DataFrame,
    source_col: str,
    logger: logging.Logger,
    auditor: Auditor,
) -> NoReturn:
    """This function audits all ids which are already in Elastic (ids can be either
    names or LEIs, based on the source_col param) if any duplicates are present in
    duplicates_df. It also logs the same messages that are audited. If no duplicates
    are present, duplicates_df is empty, and nothing is logged/audited.

    :param: duplicates_df: DataFrame which contains all the duplicate id (name/lei
     based on source_col) values. If no duplicates are present, it is empty
    :param: source_col: column based on which duplicates have been detected
    :param: logger: Logger for logging messages
    :param: auditor: Auditor for auditing messages
    :returns: None
    """
    if not duplicates_df.empty:
        audit_message = (
            f"{len(duplicates_df)} record(s) have been dropped because records with the same"
            f" {source_col} are already present in the DB"
        )
        duplicate_ids = duplicates_df.loc[:, source_col].tolist()
        error_message = (
            f"Foll. {source_col}(s) are already present in the DB: {duplicate_ids}."
        )
        logger.warning(f"{audit_message} \n {error_message}")
        auditor.add(
            message=audit_message,
            ctx={"error": error_message},
        )


# No cover -> result is the result of es_scroll
def fetch_counterparty_records_from_elastic(
    ids: List[str],
    es_client,
    lookup_column: str,
    logger: logging.Logger,
) -> pd.DataFrame:  # pragma: no cover
    """
    This function fetches MarketCounterparty values for all the ids specified in the ids
    argument. ids can be a list of LEIs or names. The lookup_column argument is
    'firmIdentifiers.lei' if the ids are LEIs and 'name' if the ids are names
    :param: ids: list of LEIs/names to be searched in MarketCounterparty
    :param: es_client: es_client_key to access SDP
    :param: lookup_column: name of the MarketCounterparty lookup column
    :param: logger: Logger for logging messages
    :returns: dataframe with the LEIs/names (based on the param lookup_column) that were
              matched against MarketCounterparty
    """

    query = get_terms_query(
        ids=ids,
        es_client=es_client,
        lookup_field=lookup_column,
        model_field=MetaModel.MARKET_COUNTERPARTY,
    )

    alias = MarketCounterparty.get_elastic_index_alias(tenant=Settings.tenant)

    logger.info(f"Scrolling {alias} for {len(ids)} {lookup_column} value(s)")
    df = es_client.scroll(query=query, index=alias)
    return df
