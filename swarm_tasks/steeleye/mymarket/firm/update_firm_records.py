from itertools import chain
from typing import List
from typing import Optional

import pandas as pd
from prefect import context
from se_core_tasks.abstractions.transformations.abstract_market_counterparty_transformations import (
    MarketColumns,
)
from se_core_tasks.map.map_conditional import Case
from se_elastic_schema.static.reference import ClientMandateType
from se_elastic_schema.static.reference import FirmType
from se_elastic_schema.validators.iso.lei import LEI
from se_elasticsearch.repository.static import MetaPrefix
from swarm.conf import Settings
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.steeleye.mymarket.firm.filter_universal_duplicates import (
    TempColumns as FilterTempColumns,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as ParamsMapAttribute
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.transform.map.map_iso_3166_country_code import CountryCodeType
from swarm_tasks.transform.map.map_iso_3166_country_code import MapIso3166CountryCode
from swarm_tasks.transform.map.map_iso_3166_country_code import (
    Params as ParamsMapIso3166CountryCode,
)
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as ParamsMapValue
from swarm_tasks.utilities.static import MetaModel


class Columns:
    @classmethod
    def get_columns(cls):
        return [
            val_
            for key_, val_ in cls.__dict__.items()
            if not key_.startswith("__") and isinstance(val_, str)
        ]


class SourceColumns(Columns):
    CLIENT_MANDATE = "CLIENTMANDATE"
    COUNTRY_OF_BRANCH = "COUNTRYOFBRANCH"
    DECISIONMAKERID = "DECISIONMAKERID"
    FIRMDETAILSINDUSTRY = "FIRMDETAILSINDUSTRY"
    LEI_COL = "LEI"
    MIFID_ENTITY = "MIFIDENTITY"
    NAME_COL = "NAME"
    TRADE_FILE_ID = "TRADEFILEIDENTIFIERS"
    TYPE = "TYPE"


class ColumnKeys:
    ID = "id"
    LABEL = "label"


class Prefixes:
    ACCOUNT = "account"
    ID = "id"
    LEI = "lei"


class TempColumns(Columns):
    BRANCH_COUNTRY = "__branch_country__"
    CLIENT_MANDATE = "__client_mandate__"
    MIFID_REGISTERED = "__mifid_registered__"
    TRADE_FILE_IDS_UNIQUE = "__trade_file_ids_unique__"
    TRADE_FILE_IDS = "__trade_file_ids__"
    TYPE = "__type__"
    UNIQUE_IDS = "__unique_ids__"
    DECISIONMAKERID = "__decision_maker_id__"
    FIRMDETAILSINDUSTRY = "__firm_details_industry__"


class UpdateFirmRecords(TransformBaseTask):
    """
    This task updates a number of firm fields from the source columns according to specific logic.

    The following list contains all the target columns that this task populates:
    * details.firmType
    * details.clientMandate
    * firmIdentifiers.branchCountry
    * details.mifidRegistered
    * firmIdentifiers.lei
    * sinkIdentifiers.tradeFileIdentifiers
    * uniqueIds
    """

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        resources=None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame,
            logger=self.logger,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame = None,
        logger=context.get("logger"),
    ) -> pd.DataFrame:

        df = source_frame.copy()

        if source_frame.empty:
            logger.warning("Source frame empty, returning empty data frame")
            return df

        # Add source columns if not present
        df = pd.concat([df, cls.create_intermediate_fields(df=df)], axis=1)

        df = cls.update_market_counterparty_cols(df=df)

        # Drop meta columns except id and key to link to original record
        es_client = Settings.connections.get("tenant-data")

        selected_columns = [
            col
            for col in df.columns
            if (
                (not col.startswith(MetaPrefix.AMPERSAND))  # remove meta columns
                and (col not in SourceColumns.get_columns())  # remove source columns
                and (
                    not col.startswith(SourceColumns.TRADE_FILE_ID)
                )  # remove possible extra source columns
                and (col not in TempColumns.get_columns())  # remove temp columns
                and (
                    col != FilterTempColumns.ACTION
                )  # remove action column from Filter Universal
            )
        ]
        # add back meta id, meta key and name, they're needed downstream
        selected_columns.extend([es_client.meta.id, es_client.meta.key])

        df = df[selected_columns].fillna(pd.NA)
        df[MarketColumns.META_MODEL] = MetaModel.MARKET_COUNTERPARTY
        return df

    @classmethod
    def create_intermediate_fields(cls, df: pd.DataFrame):
        """
        Creates a dataframe with the flow transformations necessary
        for fields that might be updated downstream.
        :param df: source dataframe
        :return: transformations dataframe
        """
        temp_df = pd.concat(
            [
                MapValue.process(
                    source_frame=df,
                    params=ParamsMapValue(
                        source_attribute=SourceColumns.CLIENT_MANDATE,
                        target_attribute=TempColumns.CLIENT_MANDATE,
                        case_insensitive=True,
                        value_map={
                            "discretionary": ClientMandateType.DISCRETIONARY.value,
                            "non-discretionary": ClientMandateType.NON.value,
                            "nondiscretionary": ClientMandateType.NON.value,
                        },
                    ),
                ),
                MapIso3166CountryCode.process(
                    source_frame=df,
                    params=ParamsMapIso3166CountryCode(
                        source_country_column=SourceColumns.COUNTRY_OF_BRANCH,
                        target_country_column=TempColumns.BRANCH_COUNTRY,
                        target_country_code_type=CountryCodeType.ALPHA_2.value,
                    ),
                ),
            ],
            axis=1,
        )

        return pd.concat(
            [
                MapValue.process(
                    source_frame=df,
                    params=ParamsMapValue(
                        source_attribute=SourceColumns.TYPE,
                        target_attribute=TempColumns.TYPE,
                        case_insensitive=True,
                        value_map={
                            "appointed representative": FirmType.APPOINTED_REPRESENTATIVE.value,
                            "appointedrepresentative": FirmType.APPOINTED_REPRESENTATIVE.value,
                            "client": FirmType.CLIENT.value,
                            "counterparty": FirmType.COUNTERPARTY.value,
                            "liquidity provider": FirmType.LIQUIDITY_PROVIDER.value,
                            "liquidityprovider": FirmType.LIQUIDITY_PROVIDER.value,
                            "other": FirmType.OTHER.value,
                            "tied agent": FirmType.TIED_AGENT.value,
                            "tiedagent": FirmType.TIED_AGENT.value,
                        },
                    ),
                ),
                MapConditional.process(
                    source_frame=pd.concat([df, temp_df], axis=1),
                    params=ParamsMapConditional(
                        target_attribute=TempColumns.BRANCH_COUNTRY,
                        cases=[
                            Case(
                                query=f"`{SourceColumns.TYPE}`.str.fullmatch('client', case=False, na=False)",
                                attribute=TempColumns.BRANCH_COUNTRY,
                            )
                        ],
                    ),
                ),
                MapConditional.process(
                    source_frame=pd.concat([df, temp_df], axis=1),
                    params=ParamsMapConditional(
                        target_attribute=TempColumns.CLIENT_MANDATE,
                        cases=[
                            Case(
                                query=f"`{SourceColumns.TYPE}`.str.fullmatch('client', case=False, na=False)",
                                attribute=TempColumns.CLIENT_MANDATE,
                            )
                        ],
                    ),
                ),
                MapConditional.process(
                    source_frame=df,
                    params=ParamsMapConditional(
                        target_attribute=TempColumns.MIFID_REGISTERED,
                        cases=[
                            Case(
                                query=f"`{SourceColumns.MIFID_ENTITY}`.str.fullmatch('TRUE|T|Y|YES|1', case=False, na=False)",
                                value=True,
                            ),
                            Case(
                                query=f"`{SourceColumns.MIFID_ENTITY}`.str.fullmatch('FALSE|N|F|NO|1', case=False, na=False)",
                                value=False,
                            ),
                        ],
                    ),
                ),
                MapAttribute.process(
                    source_frame=df,
                    params=ParamsMapAttribute(
                        source_attribute=SourceColumns.DECISIONMAKERID,
                        target_attribute=TempColumns.DECISIONMAKERID,
                    ),
                ),
                MapAttribute.process(
                    source_frame=df,
                    params=ParamsMapAttribute(
                        source_attribute=SourceColumns.FIRMDETAILSINDUSTRY,
                        target_attribute=TempColumns.FIRMDETAILSINDUSTRY,
                    ),
                ),
            ],
            axis=1,
        )

    @classmethod
    def update_market_counterparty_cols(cls, df: pd.DataFrame) -> pd.DataFrame:
        """
        Updates each of the fields according to its specific logic.
            Type 1: Straight replacement
            Type 2: Update only if null
            Type 3: tradeFileIdentifiers and trade ids
            Type 4: uniqueIds/uniqueProps
        :param df: market counterparty data dataframe
        :return: updated dataframe
        """
        # Type 1: Replace MarketCounterparty columns with new value if new value is not null
        columns_to_update = {
            TempColumns.TYPE: MarketColumns.DETAILS_FIRM_TYPE,
            TempColumns.CLIENT_MANDATE: MarketColumns.DETAILS_CLIENT_MANDATE,
            TempColumns.BRANCH_COUNTRY: MarketColumns.FIRM_IDENTIFIERS_BRANCH_COUNTRY,
            TempColumns.MIFID_REGISTERED: MarketColumns.DETAILS_MIFID_REGISTERED,
            TempColumns.DECISIONMAKERID: "details.decisionMaker.id",
            TempColumns.FIRMDETAILSINDUSTRY: "details.industry",
        }

        for src_col, target_col in columns_to_update.items():
            src_not_null_mask = df.loc[:, src_col].notnull()
            if src_not_null_mask.any():
                df.loc[src_not_null_mask, target_col] = df.loc[
                    src_not_null_mask, src_col
                ]

        if MarketColumns.FIRM_IDENTIFIERS_LEI not in df.columns:
            df[MarketColumns.FIRM_IDENTIFIERS_LEI] = pd.NA

        # Type 2: Update only if MarketCounterparty column is null
        lei_mask = (
            df[SourceColumns.LEI_COL].notnull()
            & df[MarketColumns.FIRM_IDENTIFIERS_LEI].isnull()
        )
        df.loc[lei_mask, MarketColumns.FIRM_IDENTIFIERS_LEI] = df.loc[
            lei_mask, SourceColumns.LEI_COL
        ]

        # Type 3: Update tradeFileIdentifiers and trade ids
        if MarketColumns.SINK_IDENTIFIERS_TRADE_FILE_IDENTIFIERS not in df.columns:
            df[MarketColumns.SINK_IDENTIFIERS_TRADE_FILE_IDENTIFIERS] = pd.NA

        trade_file_ids_columns = list(
            filter(lambda x: x.startswith(SourceColumns.TRADE_FILE_ID), df.columns)
        )
        trade_file_ids_and_lei_columns = trade_file_ids_columns + [
            MarketColumns.FIRM_IDENTIFIERS_LEI
        ]

        trade_file_ids_lei_columns_mask = df.columns.isin(
            trade_file_ids_and_lei_columns
        )
        df.loc[:, TempColumns.TRADE_FILE_IDS] = df.loc[
            :, trade_file_ids_lei_columns_mask
        ].apply(
            lambda x: cls._populate_trade_file_ids(x, trade_file_ids_columns), axis=1
        )

        is_null_trade_file_ids_mask = df.loc[:, TempColumns.TRADE_FILE_IDS].isnull()
        is_null_target_trade_file_ids_mask = df.loc[
            :, MarketColumns.SINK_IDENTIFIERS_TRADE_FILE_IDENTIFIERS
        ].isnull()
        df.loc[
            is_null_target_trade_file_ids_mask,
            MarketColumns.SINK_IDENTIFIERS_TRADE_FILE_IDENTIFIERS,
        ] = df.loc[is_null_target_trade_file_ids_mask, TempColumns.TRADE_FILE_IDS]

        df.loc[
            ~(is_null_trade_file_ids_mask | is_null_target_trade_file_ids_mask),
            MarketColumns.SINK_IDENTIFIERS_TRADE_FILE_IDENTIFIERS,
        ] = df.loc[
            ~(is_null_trade_file_ids_mask | is_null_target_trade_file_ids_mask),
            [
                TempColumns.TRADE_FILE_IDS,
                MarketColumns.SINK_IDENTIFIERS_TRADE_FILE_IDENTIFIERS,
            ],
        ].apply(
            lambda x: cls._trade_file_ids(
                target_trade_file_ids=x[
                    MarketColumns.SINK_IDENTIFIERS_TRADE_FILE_IDENTIFIERS
                ],
                source_trade_file_ids=x[TempColumns.TRADE_FILE_IDS],
            ),
            axis=1,
        )

        # Type 4: Update uniqueIds & uniqueProps
        if MarketColumns.UNIQUE_IDS not in df.columns:
            df[MarketColumns.UNIQUE_IDS] = pd.NA

        trade_file_ids_columns_mask = df.columns.isin(trade_file_ids_columns)
        df[TempColumns.TRADE_FILE_IDS_UNIQUE] = df.loc[
            :, trade_file_ids_columns_mask
        ].apply(cls._concatenate_unique_trade_file_ids, axis=1)

        # Populate unique Ids from trade file ids and LEI.
        unique_ids_cols_mask = df.columns.isin(
            [MarketColumns.FIRM_IDENTIFIERS_LEI, TempColumns.TRADE_FILE_IDS_UNIQUE]
        )
        df.loc[:, TempColumns.UNIQUE_IDS] = df.loc[:, unique_ids_cols_mask].apply(
            lambda x: cls._populate_unique_ids(identifiers=x), axis=1
        )

        is_null_unique_ids_mask = df.loc[:, TempColumns.UNIQUE_IDS].isnull()
        is_null_target_unique_ids_mask = df.loc[:, MarketColumns.UNIQUE_IDS].isnull()
        df.loc[is_null_target_unique_ids_mask, MarketColumns.UNIQUE_IDS] = df.loc[
            is_null_target_unique_ids_mask, TempColumns.UNIQUE_IDS
        ]
        df.loc[
            ~(is_null_unique_ids_mask | is_null_target_unique_ids_mask),
            MarketColumns.UNIQUE_IDS,
        ] = df.loc[
            ~(is_null_unique_ids_mask | is_null_target_unique_ids_mask),
            [TempColumns.UNIQUE_IDS, MarketColumns.UNIQUE_IDS],
        ].apply(
            lambda x: list(
                set(x[TempColumns.UNIQUE_IDS] + x[MarketColumns.UNIQUE_IDS])
            ),
            axis=1,
        )

        if MarketColumns.UNIQUE_PROPS in df.columns:
            # drop uniqueProps as it will be set to null in AssignMeta & will need to be replaced after AssignMeta
            df = df.drop(columns=MarketColumns.UNIQUE_PROPS)

        return df

    @staticmethod
    def _trade_file_ids(
        target_trade_file_ids: List[dict], source_trade_file_ids: List[dict]
    ) -> Optional[List[dict]]:
        """

        :param target_trade_file_ids:
        :param source_trade_file_ids:
        :return:
        """

        target_dict = {}
        for target_trade_dict in target_trade_file_ids:
            key = (
                target_trade_dict.get("id").lower(),
                target_trade_dict.get("label").lower(),
            )
            target_dict[key] = target_trade_dict

        for source_trade_dict in source_trade_file_ids:
            key = (
                source_trade_dict.get("id").lower(),
                source_trade_dict.get("label").lower(),
            )
            if key not in target_dict:
                target_dict[key] = source_trade_dict

        return list(target_dict.values())

    @staticmethod
    def _populate_trade_file_ids(
        trade_file_ids: pd.Series, trade_file_id_columns: List[str]
    ) -> Optional[List[dict]]:
        """
        Formats the given ids into the schema expected format for sinkIdentifiers.tradeFileIndentifiers

        ['dummylei', 'dummyid'] ->
        [{'id':'dummylei', 'label':'lei'}, {'id':'dummyid', 'label':'id'}, {'id':'dummyid', 'label':'account'}]

        :param: trade_file_ids: Series containing the values for all trade file id
                columns for 1 row. The index of the series comes from the column
                names of the trade file id columns (and the LEI column in the calling
                function's dataframe. The non-lei columns are identified by
                the trade_file_id_columns argument
        :param: trade_file_id_columns: Name of the trade file id columns
        :param: params: params instance
        :returns: List of trade file ids in the required schema format
        """
        concat_list = []
        lei = trade_file_ids[MarketColumns.FIRM_IDENTIFIERS_LEI]
        # Add the LEI first
        if not pd.isna(lei):
            concat_list.append({ColumnKeys.ID: lei, ColumnKeys.LABEL: Prefixes.LEI})
        # Add each of the trade file ids with 2 prefixes: id and account
        for trade_file_id_col in trade_file_id_columns:
            trade_file_id = trade_file_ids[trade_file_id_col]
            if not pd.isna(trade_file_id):
                if (
                    LEI.validate_lei_code(trade_file_id).is_valid
                    and trade_file_id != lei
                ):
                    concat_list.append(
                        {ColumnKeys.ID: trade_file_id, ColumnKeys.LABEL: Prefixes.LEI}
                    )
                else:
                    concat_list.append(
                        {ColumnKeys.ID: trade_file_id, ColumnKeys.LABEL: Prefixes.ID}
                    )
                    concat_list.append(
                        {
                            ColumnKeys.ID: trade_file_id,
                            ColumnKeys.LABEL: Prefixes.ACCOUNT,
                        }
                    )

        return None if not concat_list else concat_list

    @staticmethod
    def _concatenate_unique_trade_file_ids(trade_file_ids: pd.Series) -> List[str]:
        """Returns a list of concatenated trade file ids which are required for
        populating uniqueIds. These IDs include the prefix (id/account). If there
        are no trade file ids in the source, it returns an empty list
        :param: trade_file_ids: Series containing the values for all trade_file_ids columns for
                1 row. The index of the series comes from the column names of the trade
                file id columns in the original df
        :returns: List of concatenated trade file ids, to be used in uniqueIds population
        """
        concat_list = [
            [f"{Prefixes.ID}:{trade_file_id}", f"{Prefixes.ACCOUNT}:{trade_file_id}"]
            for trade_file_id in trade_file_ids
            if not pd.isna(trade_file_id)
        ]
        return [] if not concat_list else list(chain(*concat_list))

    @staticmethod
    def _populate_unique_ids(identifiers: pd.Series) -> Optional[List[str]]:
        """
        Takes a Series containing (i) a list of trade file identifiers and
        (ii) a single LEI. It creates one list containing all identifiers
        after removing duplicates. If both the LEI and trade file identifiers
        are not present, it returns None
        :param: identifiers: Series containing a list of trade file identifiers and
                the LEI for 1 row.
                The index of the series comes from the column names created in the
                calling function
        :param: params: Params instance
        :returns: Concatenated list of unique ids, or None
        """
        concat_list = []
        if not pd.isna(identifiers[MarketColumns.FIRM_IDENTIFIERS_LEI]):
            concat_list.append(
                f"{Prefixes.LEI}:{identifiers[MarketColumns.FIRM_IDENTIFIERS_LEI]}"
            )
        for trade_file_id_with_prefix in identifiers[TempColumns.TRADE_FILE_IDS_UNIQUE]:
            if not pd.isna(trade_file_id_with_prefix):
                concat_list.append(trade_file_id_with_prefix)

        # Remove duplicates
        concat_list = list(set(concat_list))
        # Make all ids lower case
        if concat_list:
            concat_list = [element.lower().strip() for element in concat_list]
        return None if not concat_list else concat_list
