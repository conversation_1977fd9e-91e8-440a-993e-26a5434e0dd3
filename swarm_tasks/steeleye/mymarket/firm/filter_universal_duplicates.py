import logging

import pandas as pd
from prefect import context
from prefect.engine import signals
from pydantic import Field
from swarm.conf import Settings
from swarm.task.auditor import Auditor
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.steeleye.mymarket.firm.helper_functions import (
    audit_duplicate_ids_in_file,
)
from swarm_tasks.steeleye.mymarket.firm.helper_functions import (
    fetch_counterparty_records_from_elastic,
)
from swarm_tasks.utilities.static import MetaModel


class Params(BaseParams):
    source_name_column: str = Field(
        ..., description="Source column which contains the name"
    )
    source_lei_column: str = Field(
        ...,
        description="Source column which contains the LEI",
    )
    elastic_lei_lookup_column: str = Field(
        default="firmIdentifiers.lei",
        description="MarketCounterparty LEI column against which the source_lei_column is matched",
    )
    elastic_name_lookup_column: str = Field(
        default="name",
        description="MarketCounterparty name column against which the source_name_column is matched",
    )


class TempColumns:
    ACTION = "action"


class WriteAction:
    CREATE = "create"
    UPDATE = "update"


class FilterUniversalDuplicates(TransformBaseTask):
    """
    This task receives a dataframe containing MarketCounterparty data. It
    performs 3 tasks:
        (1) It removes rows with null name AND null LEI.
        (2) It removes any duplicate rows in the source frame based on the LEI or Name field.
        (3) It looks up the LEIs and Names in Elasticsearch (in MarketCounterparty):
            An `action` column is created where we flag records that found a match in ES as `update`
            and those without a match as `create` to be updates/created downstream accordingly.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:
        return self.process(
            source_frame=source_frame,
            params=params,
            logger=self.logger,
            auditor=self.auditor,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        logger=context.get("logger"),
        auditor: Auditor = None,
    ) -> pd.DataFrame:
        """Class method which filters out duplicate MarketCounterparty
        records based on the LEI or name, and removes rows without a name.
        """
        if (
            source_frame.empty
            or params.source_name_column not in source_frame.columns
            or params.source_lei_column not in source_frame.columns
        ):
            raise signals.SKIP("Source frame empty or required column not present")

        es_client = Settings.connections.get("tenant-data")

        # Remove rows with neither a name nor an LEI
        name_and_lei_null_mask = (
            source_frame.loc[:, params.source_name_column].isnull()
            & source_frame.loc[:, params.source_lei_column].isnull()
        )
        num_no_name_no_lei_records = name_and_lei_null_mask.sum()

        # Audit the number of records with null names and LEIs
        if num_no_name_no_lei_records > 0:
            no_name_message = (
                f"{num_no_name_no_lei_records} record(s) in the file have neither a name nor an LEI."
                f" They have been dropped."
            )
            auditor.add(message=no_name_message)
            logger.warning(no_name_message)

        df = source_frame.loc[~name_and_lei_null_mask].reset_index(drop=True)

        # Remove duplicate rows based on the LEI
        not_null_lei_mask = df.loc[:, params.source_lei_column].notnull()
        duplicated_lei_mask = df.duplicated(subset=[params.source_lei_column])

        # If there are 2 NAs, they are treated as duplicates, this additional mask
        # prevents that. We don't want to drop null LEIs
        duplicated_lei_mask = not_null_lei_mask & duplicated_lei_mask

        # Audit duplicate records based on the LEI column
        audit_duplicate_ids_in_file(
            df=df,
            duplicated_mask=duplicated_lei_mask,
            source_col=params.source_lei_column,
            logger=logger,
            auditor=auditor,
        )

        df = df.loc[~duplicated_lei_mask].reset_index(drop=True)

        result_df = cls._process_counterparty_records(
            df=df,
            es_client=es_client,
            source_lei_column=params.source_lei_column,
            source_name_column=params.source_name_column,
            lookup_lei_column=params.elastic_lei_lookup_column,
            lookup_name_column=params.elastic_name_lookup_column,
            logger=logger,
            auditor=auditor,
        )
        return result_df.fillna(pd.NA)

    @staticmethod
    def _process_counterparty_records(
        df: pd.DataFrame,
        es_client,
        source_lei_column: str,
        source_name_column: str,
        lookup_lei_column: str,
        lookup_name_column: str,
        logger: logging.Logger,
        auditor: Auditor,
    ) -> pd.DataFrame:
        """
        Merges the source dataframe information with MarketCounterparty records from ES,
            matched according to LEI or NAME.
        Dataframe sources are flagged with a `action` column as to
            whether it should create a records or update an existing one.
        :param df: source frame
        :param es_client: es instance
        :param source_lei_column: lei column in source
        :param source_name_column: name column in source
        :param lookup_lei_column: lei column in es result
        :param lookup_name_column: name column in es result
        :param logger: logger
        :param auditor: auditor
        :return: merged dataframe with es results
        """
        # PART 1: LEI PROCESSING
        leis_list = list(set(df.loc[:, source_lei_column].dropna().tolist()))
        logger.info(
            f"Fetching {len(leis_list)} LEI(s) from {MetaModel.MARKET_COUNTERPARTY}"
        )
        fetched_lei_counterparty_records = fetch_counterparty_records_from_elastic(
            ids=leis_list,
            es_client=es_client,
            lookup_column=lookup_lei_column,
            logger=logger,
        )

        if fetched_lei_counterparty_records.empty:
            unmatched_lei_df = df
            matched_lei_df = pd.DataFrame()

        else:
            join_lei_target = df.reset_index(drop=True).merge(
                fetched_lei_counterparty_records,
                how="left",
                left_on=[source_lei_column],
                right_on=[lookup_lei_column],
                indicator="ind",
            )
            matched_lei_df = join_lei_target.query("ind == 'both'", engine="python")
            unmatched_lei_df = (
                join_lei_target.query("ind == 'left_only'", engine="python")
                .drop(["ind", lookup_lei_column], axis=1)
                .reset_index(drop=True)
            )
        columns = fetched_lei_counterparty_records.columns
        unmatched_lei_df = unmatched_lei_df.drop(columns=columns, errors="ignore")

        # PART 2: NAME PROCESSING
        names_list = list(
            set(unmatched_lei_df.loc[:, source_name_column].dropna().tolist())
        )
        logger.info(
            f"Fetching {len(names_list)} names from {MetaModel.MARKET_COUNTERPARTY}"
        )
        fetched_name_counterparty_records = fetch_counterparty_records_from_elastic(
            ids=names_list,
            es_client=es_client,
            lookup_column=lookup_name_column,
            logger=logger,
        )

        if fetched_name_counterparty_records.empty:
            unmatched_name_df = unmatched_lei_df
            matched_name_df = pd.DataFrame()
        else:
            join_name_target = unmatched_lei_df.reset_index(drop=True).merge(
                fetched_name_counterparty_records,
                how="left",
                left_on=[source_name_column],
                right_on=[lookup_name_column],
                indicator="ind",
            )
            # Handle duplicates
            dup_in_join_name_target_mask = join_name_target.duplicated(
                subset=[source_name_column]
            ) & (join_name_target[source_name_column].notnull())
            join_name_target = join_name_target.loc[~dup_in_join_name_target_mask]

            # Filter out records which are already in Elastic based on name
            matched_name_df = join_name_target.query("ind == 'both'", engine="python")
            unmatched_name_df = (
                join_name_target.query("ind == 'left_only'", engine="python")
                .drop(["ind", lookup_name_column], axis=1)
                .reset_index(drop=True)
            )
            matched_name_df[TempColumns.ACTION] = WriteAction.UPDATE

        unmatched_name_df[TempColumns.ACTION] = WriteAction.CREATE
        matched_lei_df[TempColumns.ACTION] = WriteAction.UPDATE

        result_df = pd.concat(
            [matched_lei_df, matched_name_df, unmatched_name_df], axis=0
        )

        if "ind" in result_df.columns:
            result_df = result_df.drop(columns="ind")

        return result_df
