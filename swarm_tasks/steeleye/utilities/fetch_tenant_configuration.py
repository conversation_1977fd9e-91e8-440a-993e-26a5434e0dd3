from typing import Optional

from addict import addict
from se_core_tasks.io.read.fetch_tenant_configuration import (
    run_fetch_tenant_configuration,
)
from swarm.conf import Settings
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask


class Resources(BaseResources):
    es_client_key: str = "tenant-data"


class FetchTenantConfiguration(BaseTask):
    resources_class = Resources

    def execute(
        self,
        resources: Optional[Resources] = None,
        **kwargs,
    ) -> addict.Dict:

        es_client = Settings.connections.get(resources.es_client_key)

        return run_fetch_tenant_configuration(
            es_client=es_client,
            realm=Settings.realm,
            tenant=Settings.tenant,
            auditor=self.auditor,
            logger=self.logger,
        )
