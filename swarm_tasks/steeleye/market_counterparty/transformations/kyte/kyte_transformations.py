import pandas as pd
from se_core_tasks.abstractions.transformations.abstract_market_counterparty_transformations import (
    AbstractMarketCounterpartyTransformations,
)
from se_elastic_schema.static.reference import ClientMandateType
from se_elastic_schema.static.reference import FirmType
from se_elastic_schema.static.reference import RetailOrProfessional

from swarm_tasks.steeleye.mymarket.firm.utils import UpdateColumns


class SourceColumns:
    COUNTRY_CODE = "countryCode"
    CRM_CLIENT_ID = "crmClientId"
    IS_EU = "isEu"
    ID = "id"
    ID_CHECK = "id_check"
    FIRMWIDE_CLIENT_ID = "firmwideClientId"
    GROUPWIDE_CLIENT_ID = "groupwideClientId"
    LEI = "lei"
    MIFIR_FIRM = "mifidFirm"
    NAME = "name"
    REGION = "region"
    STATUS = "status"


class TempColumns:
    LEI = "__lei__"
    TRADE_FILE_IDENTIFIERS_ID = "__trade_file_identifiers_id__"
    TRADE_FILE_IDENTIFIERS_ID_FIRM_WIDE_CLIENT_ID = (
        "__trade_file_identifiers_id__firm_wide_client_id__"
    )
    TRADE_FILE_IDENTIFIERS_ID_GROUP_WIDE_CLIENT_ID = (
        "__trade_file_identifiers_id__group_wide_client_id__"
    )


class KyteTransformations(AbstractMarketCounterpartyTransformations):
    def process(self):
        """Function which calls all the required functions and thus populates all required
        columns in the target df"""
        self.pre_process()
        self.details_client_mandate()
        self.details_firm_type()
        self.details_in_eea()
        self.details_mifid_registered()
        self.details_retail_or_professional()
        self.firm_identifiers_branch_country()
        self.name()
        self.meta_model()
        self.post_process()
        return self.target_df

    def _pre_process(self):
        """Creates temporary fields which can be used by other methods"""
        self.target_df.loc[:, TempColumns.LEI] = pd.Series(
            data=self.source_frame.loc[:, SourceColumns.LEI].values,
            index=self.source_frame.index,
            name=TempColumns.LEI,
        )

        self.target_df.loc[
            :, TempColumns.TRADE_FILE_IDENTIFIERS_ID_FIRM_WIDE_CLIENT_ID
        ] = self.source_frame.loc[:, SourceColumns.FIRMWIDE_CLIENT_ID]

        self.target_df.loc[
            :, TempColumns.TRADE_FILE_IDENTIFIERS_ID_GROUP_WIDE_CLIENT_ID
        ] = self.source_frame.loc[:, SourceColumns.GROUPWIDE_CLIENT_ID]

    def _post_process(self):
        """Assigns source columns to be passed downstream"""
        self._get_fields_to_update_records()

    def _get_fields_to_update_records(self):
        """
        Gets values needed to update records downstream.
        Only runs if `__to_update__` column is present
        Includes:
            __to_update__
            es_id
            es_trade_file_identifiers
            es_trade_ids
        """
        if UpdateColumns.TO_UPDATE in self.source_frame.columns:
            self.target_df.loc[:, UpdateColumns.TO_UPDATE] = self.source_frame.loc[
                :, UpdateColumns.TO_UPDATE
            ]

            to_update_mask = self.target_df.loc[:, UpdateColumns.TO_UPDATE]

            self.target_df.loc[
                to_update_mask, UpdateColumns.ES_ID
            ] = self.source_frame.loc[:, UpdateColumns.META_ID]

            self.target_df.loc[
                to_update_mask, UpdateColumns.ES_TRADE_FILE_IDS
            ] = self.source_frame.loc[:, UpdateColumns.SINK_IDENTIFIERS_TRADE_IDS]

            self.target_df.loc[
                to_update_mask, UpdateColumns.ES_TRADE_IDS
            ] = self.source_frame.loc[
                to_update_mask, UpdateColumns.SINK_IDENTIFIERS_TRADE_IDS
            ].apply(
                lambda x: [f"{trade_id['label']}:{trade_id['id']}" for trade_id in x]
            )
        else:
            self.target_df.loc[:, UpdateColumns.TO_UPDATE] = False
            self.target_df.loc[
                :,
                [
                    UpdateColumns.ES_ID,
                    UpdateColumns.ES_TRADE_FILE_IDS,
                    UpdateColumns.ES_TRADE_IDS,
                ],
            ] = pd.NA

    def _details_firm_type(self) -> pd.Series:
        """Populates MarketColumns.DETAILS_FIRM_TYPE field"""
        return FirmType.CLIENT

    def _details_mifid_registered(self) -> pd.Series:
        """Populates MarketColumns.DETAILS_MIFID_REGISTERED field"""
        return self.source_frame.loc[:, SourceColumns.MIFIR_FIRM].values

    def _details_retail_or_professional(self) -> pd.Series:
        """Populates MarketColumns.DETAILS_RETAIL_OR_PROFESSIONAL field"""
        return RetailOrProfessional.PROFESSIONAL

    def _details_client_mandate(self) -> pd.Series:
        """Populates MarketColumns.DETAILS_CLIENT_MANDATE field"""
        return ClientMandateType.DISCRETIONARY

    def _name(self) -> pd.Series:
        """Populates MarketColumns.NAME using SourceColumns.NAME"""
        return self.source_frame.loc[:, SourceColumns.NAME].values

    def _meta_model(self) -> pd.Series:
        """Populates aux/temp column MarketColumns.META_MODEL"""
        return "MarketCounterparty"

    def _firm_identifiers_branch_country(self) -> pd.Series:
        """Populates MarketColumns.FIRM_IDENTIFIERS_BRANCH_COUNTRY using COUNTRY_CODE"""
        return self.source_frame.loc[:, SourceColumns.COUNTRY_CODE].values

    def _firm_identifiers_lei(self) -> pd.Series:
        """Not Implemented - Populated Downstream by CreateFirmIdentifiers"""

    def _sink_identifiers_trade_file_identifiers(self) -> pd.Series:
        """Not Implemented - Populated Downstream by CreateFirmIdentifiers"""

    def _details_firm_status(self) -> pd.Series:
        """Not Implemented - Populated Downstream by LinkLeiRecord"""

    def _details_in_eea(self) -> pd.Series:
        """Populates MarketColumns.DETAILS_IN_EEA from SourceColumns.IS_EU"""
        return self.source_frame.loc[:, SourceColumns.IS_EU].values

    def _source_index(self) -> pd.Series:
        """Not Implemented""" ""

    def _source_key(self) -> pd.Series:
        """Not Implemented""" ""

    def _unique_ids(self) -> pd.Series:
        """Not implemented. Populated by create_firm_identifiers"""
