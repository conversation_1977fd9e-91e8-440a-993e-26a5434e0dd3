class ARMReport:
    BUYER_COUNTRY_OF_BRANCH = "Buyer Country of Branch"
    BUYER_DECISION_MAKER_DOB = "Buyer Decision Maker DOB"
    BUYER_DECISION_MAKER_FIRST_NAME = "Buyer Decision Maker First Name"
    BUYER_DECISION_MAKER_ID = "Buyer Decision Maker ID"
    BUYER_DECISION_MAKER_SURNAME = "Buyer Decision Maker Surname"
    BUYER_DOB = "Buyer DOB"
    BUYER_FIRST_NAME = "Buyer First Name"
    BUYER_ID = "Buyer ID"
    BUYER_SURNAME = "Buyer Surname"
    BUYER_TRANSMITTER_ID = "Buyer Transmitter ID"
    COMMODITY_DERIVATIVE_INDICATOR = "Commodity Derivative Indicator"
    COMPLEX_TRADE_COMPONENT_ID = "Complex Trade Component ID"
    COUNTRY_OF_BRANCH = "Country of Branch"
    DELIVERY_TYPE = "Delivery Type"
    DERIVATIVE_NOTIONAL_CHANGE = "Derivative Notional Change"
    EXECUTING_ENTITY_ID = "Executing Entity ID"
    EXPIRY_DATE = "Expiry Date"
    FIRM_EXECUTION_COUNTRY_OF_BRANCH = "Firm Execution Country of Branch"
    FIRM_EXECUTION_ID = "Firm Execution ID"
    FIRM_EXECUTION_ID_TYPE = "Firm Execution ID Type"
    INSTRUMENT_CLASSIFICATION = "Instrument Classification"
    INSTRUMENT_ID = "Instrument ID"
    INSTRUMENT_NAME = "Instrument Name"
    INVESTMENT_DECISION_COUNTRY_OF_BRANCH = "Investment Decision Country of Branch"
    INVESTMENT_DECISION_ID = "Investment Decision ID"
    INVESTMENT_DECISION_ID_TYPE = "Investment Decision ID Type"
    INVESTMENT_FIRM_INDICATOR = "Investment Firm Indicator"
    MATURIY_DATE = "Maturity Date"
    NET_AMOUNT = "Net Amount"
    NOTIONAL_CURRENCY_1 = "Notional Currency 1"
    NOTIONAL_CURRENCY_2 = "Notional Currency 2"
    OPTION_STYLE = "Option Style"
    OPTION_TYPE = "Option Type"
    ORDER_TRANSMISSION_INDICATOR = "Order Transmission Indicator"
    OTC_POST_TRADE_INDICATOR = "OTC Post Trade Indicator"
    PRICE = "Price"
    PRICE_CURRENCY = "Price Currency"
    PRICE_MULTIPLIER = "Price Multiplier"
    PRICE_TYPE = "Price Type"
    QUANTITY = "Quantity"
    QUANTITY_CURRENCY = "Quantity Currency"
    QUANTITY_TYPE = "Quantity Type"
    REPORT_STATUS = "Report Status"
    SELLER_COUNTRY_OF_BRANCH = "Seller Country of Branch"
    SELLER_DECISION_MAKER_DOB = "Seller Decision Maker DOB"
    SELLER_DECISION_MAKER_FIRST_NAME = "Seller Decision Maker First Name"
    SELLER_DECISION_MAKER_ID = "Seller Decision Maker ID"
    SELLER_DECISION_MAKER_SURNAME = "Seller Decision Maker Surname"
    SELLER_DOB = "Seller DOB"
    SELLER_FIRST_NAME = "Seller First Name"
    SELLER_ID = "Seller ID"
    SELLER_SURNAME = "Seller Surname"
    SELLER_TRANSMITTER_ID = "Seller Transmitter ID"
    SFT_INDICATOR = "SFT Indicator"
    SHORT_SELLING_INDICATOR = "Short Selling Indicator"
    STRIKE_PRICE = "Strike Price"
    STRIKE_PRICE_CURRENCY = "Strike Price Currency"
    STRIKE_PRICE_TYPE = "Strike Price Type"
    TRADING_CAPACITY = "Trading Capacity"
    TRADING_DATE_TIME = "Trading Date Time"
    TRANSACTION_REF_NO = "Transaction Reference Number"
    UNDERLYING_INDEX_ID = "Underlying Index ID"
    UNDERLYING_INDEX_NAME = "Underlying Index Name"
    UNDERLYING_INDEX_TERM = "Underlying Index Term"
    UNDERLYING_INSTRUMENT_ID = "Underlying Instrument ID"
    UP_FRONT_PAYMENT = "Up-Front Payment"
    UP_FRONT_PAYMENT_CURRENCY = "Up-Front Payment Currency"
    UV_INDEX_CLASSIFICATION = "UV Index Classification"
    UV_INSTRUMENT_CLASSIFICATION = "UV Instrument Classification"
    VENUE = "Venue"
    VENUE_TRANSACTION_ID = "Venue Transaction ID"
    WAIVER_INDICATOR = "Waiver Indicator"


class NCAReport:
    ADDTLATTRBTS_OTCPSTTRADIND = (
        "Document.FinInstrmRptgTxRpt.Tx.New.AddtlAttrbts.OTCPstTradInd"
    )
    ADDTLATTRBTS_RSKRDCGTX = "Document.FinInstrmRptgTxRpt.Tx.New.AddtlAttrbts.RskRdcgTx"
    ADDTLATTRBTS_SCTIES_FINCGTXIND = (
        "Document.FinInstrmRptgTxRpt.Tx.New.AddtlAttrbts.SctiesFincgTxInd"
    )
    ADDTLATTRBTS_SHRTSELGIND = (
        "Document.FinInstrmRptgTxRpt.Tx.New.AddtlAttrbts.ShrtSellgInd"
    )
    ADDTLATTRBTS_WVRIND = "Document.FinInstrmRptgTxRpt.Tx.New.AddtlAttrbts.WvrInd"
    BUYR_ACCTOWNR_CTRYOFBRNCH = (
        "Document.FinInstrmRptgTxRpt.Tx.New.Buyr.AcctOwnr.CtryOfBrnch"
    )
    BUYR_ACCTOWNR_ID_INTL = "Document.FinInstrmRptgTxRpt.Tx.New.Buyr.AcctOwnr.Id.Intl"
    BUYR_ACCTOWNR_ID_LEI = "Document.FinInstrmRptgTxRpt.Tx.New.Buyr.AcctOwnr.Id.LEI"
    BUYR_ACCTOWNR_ID_PRSN_BIRTHDT = (
        "Document.FinInstrmRptgTxRpt.Tx.New.Buyr.AcctOwnr.Id.Prsn.BirthDt"
    )
    BUYR_ACCTOWNR_ID_PRSN_FRSTNM = (
        "Document.FinInstrmRptgTxRpt.Tx.New.Buyr.AcctOwnr.Id.Prsn.FrstNm"
    )

    BUYR_ACCTOWNR_ID_PRSN_NM = (
        "Document.FinInstrmRptgTxRpt.Tx.New.Buyr.AcctOwnr.Id.Prsn.Nm"
    )
    BUYR_ACCTOWNR_ID_PRSN_OTHR_ID = (
        "Document.FinInstrmRptgTxRpt.Tx.New.Buyr.AcctOwnr.Id.Prsn.Othr.Id"
    )
    BUYR_DCSNMAKR_LEI = "Document.FinInstrmRptgTxRpt.Tx.New.Buyr.DcsnMakr.LEI"
    BUYR_DCSNMAKR_PRSN_BIRTHDT = (
        "Document.FinInstrmRptgTxRpt.Tx.New.Buyr.DcsnMakr.Prsn.BirthDt"
    )
    BUYR_DCSNMAKR_PRSN_FRSTNM = (
        "Document.FinInstrmRptgTxRpt.Tx.New.Buyr.DcsnMakr.Prsn.FrstNm"
    )
    BUYR_DCSNMAKR_PRSN_NM = "Document.FinInstrmRptgTxRpt.Tx.New.Buyr.DcsnMakr.Prsn.Nm"
    BUYR_DCSNMAKR_PRSN_OTHR_ID = (
        "Document.FinInstrmRptgTxRpt.Tx.New.Buyr.DcsnMakr.Prsn.Othr.Id"
    )
    CANC_EXCTGPTY = "Document.FinInstrmRptgTxRpt.Tx.Cxl.ExctgPty"
    CANC_TXID = "Document.FinInstrmRptgTxRpt.Tx.Cxl.TxId"
    EXCTGPRSN_ALGO = "Document.FinInstrmRptgTxRpt.Tx.New.ExctgPrsn.Algo"
    EXCTGPRSN_CLNT = "Document.FinInstrmRptgTxRpt.Tx.New.ExctgPrsn.Clnt"
    EXCTGPRSN_PRSN_CTRYOFBRNCH = (
        "Document.FinInstrmRptgTxRpt.Tx.New.ExctgPrsn.Prsn.CtryOfBrnch"
    )
    EXCTGPRSN_PRSN_OTHR_ID = "Document.FinInstrmRptgTxRpt.Tx.New.ExctgPrsn.Prsn.Othr.Id"
    EXCTGPTY = "Document.FinInstrmRptgTxRpt.Tx.New.ExctgPty"
    FEEDBACK_STS = "Document.FinInstrmRptgTxRpt.Tx.Feedback.Sts"
    FEEDBACK_VLDTNRULE_DESC = "Document.FinInstrmRptgTxRpt.Tx.Feedback.VldtnRule.Desc"
    FEEDBACK_VLDTNRULE_ID = "Document.FinInstrmRptgTxRpt.Tx.Feedback.VldtnRule.Id"
    FININSTRM_ID = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Id"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_ASSTCLSSSPCFCATTRBTS_FX_OTHRNTLCCY = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.AsstClssSpcfcAttrbts.FX.OthrNtnlCcy"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_ASSTCLSSSPCFCATTRBTS_INTRST_OTHRNTNLCCY = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.AsstClssSpcfcAttrbts.Intrst.OthrNtnlCcy"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_DLVRYTP = (
        "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.DlvryTp"
    )
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_MTRTYDT = (
        "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DebtInstrmAttrbts.MtrtyDt"
    )
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_OPTNEXRCSTYLE = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.OptnExrcStyle"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_OPTNTP = (
        "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.OptnTp"
    )
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_PRICMLTPL = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.PricMltplr"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_STRKPRIC_NOPRIC_CCY = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.StrkPric.NoPric.@Ccy"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_STRKPRIC_NOPRIC_PDG = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.StrkPric.NoPric.Pdg"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_STRKPRIC_PRIC_BSISPTS = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.StrkPric.Pric.BsisPts"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_STRKPRIC_PRIC_MNTYRYVAL_AMT = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.StrkPric.Pric.MntryVal.Amt"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_STRKPRIC_PRIC_MNTYRYVAL_AMT_CCY = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.StrkPric.Pric.MntryVal.Amt.@Ccy"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_STRKPRIC_PRIC_PCTG = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.StrkPric.Pric.Pctg"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_STRKPRIC_PRIC_YLD = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.StrkPric.Pric.Yld"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_OTHR_BSKT = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Bskt"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_OTHR_BSKT_INDX_ISIN = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Bskt.Indx.ISIN"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_OTHR_BSKT_INDX_NM_REFRATE_INDX = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Bskt.Indx.Nm.RefRate.Indx"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_OTHR_BSKT_INDX_NM_REFRATE_NM = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Bskt.Indx.Nm.RefRate.Nm"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_OTHR_BSKT_INDX_NM_TERM_UNIT = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Bskt.Indx.Nm.Term.Unit"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_OTHR_BSKT_INDX_NM_TERM_VAL = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Bskt.Indx.Nm.Term.Val"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_OTHR_SNGL = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Sngl"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_OTHR_SNGL_INDX_ISIN = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Sngl.Indx.ISIN"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_OTHR_SNGL_INDX_NM_REFRATE_INDX = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Sngl.Indx.Nm.RefRate.Indx"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_OTHR_SNGL_INDX_NM_REFRATE_NM = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Sngl.Indx.Nm.RefRate.Nm"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_OTHR_SNGL_INDX_NM_TERM_UNIT = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Sngl.Indx.Nm.Term.Unit"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_OTHR_SNGL_INDX_NM_TERM_VAL = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Othr.Sngl.Indx.Nm.Term.Val"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPIN_BSKT = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Bskt"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPIN_BSKT_INDX_ISIN = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Bskt.Indx.ISIN"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPIN_BSKT_INDX_NM_REFRATE_INDX = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Bskt.Indx.Nm.RefRate.Indx"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPIN_BSKT_INDX_NM_REFRATE_NM = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Bskt.Indx.Nm.RefRate.Nm"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPIN_BSKT_INDX_NM_TERM_UNIT = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Bskt.Indx.Nm.Term.Unit"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPIN_BSKT_INDX_NM_TERM_VAL = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Bskt.Indx.Nm.Term.Val"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPIN_SNGL = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Sngl"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPIN_SNGL_INDX_ISIN = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Sngl.Indx.ISIN"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPIN_SNGL_INDX_NM_REFRATE_INDX = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Sngl.Indx.Nm.RefRate.Indx"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPIN_SNGL_INDX_NM_REFRATE_NM = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Sngl.Indx.Nm.RefRate.Nm"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPIN_SNGL_INDX_NM_TERM_UNIT = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Sngl.Indx.Nm.Term.Unit"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPIN_SNGL_INDX_NM_TERM_VAL = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Sngl.Indx.Nm.Term.Val"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPOUT_BSKT = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpOut.Bskt"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPOUT_BSKT_INDX_ISIN = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpOut.Bskt.Indx.ISIN"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPOUT_BSKT_INDX_NM_REFRATE_INDX = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpOut.Bskt.Indx.Nm.RefRate.Indx"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPOUT_BSKT_INDX_NM_REFRATE_NM = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpOut.Bskt.Indx.Nm.RefRate.Nm"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPOUT_BSKT_INDX_NM_TERM_UNIT = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpOut.Bskt.Indx.Nm.Term.Unit"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPOUT_BSKT_INDX_NM_TERM_VAL = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpOut.Bskt.Indx.Nm.Term.Val"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPOUT_SNGL = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpOut.Sngl"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPOUT_SNGL_INDX_ISIN = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpOut.Sngl.Indx.ISIN"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPOUT_SNGL_INDX_NM_REFRATE_INDX = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpOut.Sngl.Indx.Nm.RefRate.Indx"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPOUT_SNGL_INDX_NM_REFRATE_NM = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpOut.Sngl.Indx.Nm.RefRate.Nm"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPOUT_SNGL_INDX_NM_TERM_UNIT = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpOut.Sngl.Indx.Nm.Term.Unit"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_UNDRYGINSTRM_SWP_SWPOUT_SNGL_INDX_NM_TERM_VAL = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpOut.Sngl.Indx.Nm.Term.Val"
    FININSTRM_OTHR_DERIVINSTRMATTRBTS_XPRYDT = (
        "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.XpryDt"
    )
    FININSTRM_OTHR_FININSTRMGNLATTRBTS_CLSSFCTNTP = "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.FinInstrmGnlAttrbts.ClssfctnTp"
    FININSTRM_OTHR_FININSTRMGNLATTRBTS_FULLNM = (
        "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.FinInstrmGnlAttrbts.FullNm"
    )
    FININSTRM_OTHR_FININSTRMGNLATTRBTS_ID = (
        "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.FinInstrmGnlAttrbts.Id"
    )
    FININSTRM_OTHR_FININSTRMGNLATTRBTS_NTNLCCY = (
        "Document.FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.FinInstrmGnlAttrbts.NtnlCcy"
    )

    INVSTMTDCSNPRSN_ALGO = "Document.FinInstrmRptgTxRpt.Tx.New.InvstmtDcsnPrsn.Algo"
    INVSTMTDCSNPRSN_PRSN_CTRYOFBRNCH = (
        "Document.FinInstrmRptgTxRpt.Tx.New.InvstmtDcsnPrsn.Prsn.CtryOfBrnch"
    )
    INVSTMTDCSNPRSN_PRSN_OTHR_ID = (
        "Document.FinInstrmRptgTxRpt.Tx.New.InvstmtDcsnPrsn.Prsn.Othr.Id"
    )
    INVSTMTPTYIND = "Document.FinInstrmRptgTxRpt.Tx.New.InvstmtPtyInd"
    ORDRTRNSMSSN_TRNSMSSNIND = (
        "Document.FinInstrmRptgTxRpt.Tx.New.OrdrTrnsmssn.TrnsmssnInd"
    )
    ORDRTRNSMSSN_TRNSMTTGBUYR = (
        "Document.FinInstrmRptgTxRpt.Tx.New.OrdrTrnsmssn.TrnsmttgBuyr"
    )
    ORDRTRNSMSSN_TRNSMTTGSELLR = (
        "Document.FinInstrmRptgTxRpt.Tx.New.OrdrTrnsmssn.TrnsmttgSellr"
    )
    SELLR_ACCTOWNR_CTRYOFBRNCH = (
        "Document.FinInstrmRptgTxRpt.Tx.New.Sellr.AcctOwnr.CtryOfBrnch"
    )
    SELLR_ACCTOWNR_ID_INTL = "Document.FinInstrmRptgTxRpt.Tx.New.Sellr.AcctOwnr.Id.Intl"
    SELLR_ACCTOWNR_ID_LEI = "Document.FinInstrmRptgTxRpt.Tx.New.Sellr.AcctOwnr.Id.LEI"
    SELLR_ACCTOWNR_ID_PRSN_BIRTHDT = (
        "Document.FinInstrmRptgTxRpt.Tx.New.Sellr.AcctOwnr.Id.Prsn.BirthDt"
    )
    SELLR_ACCTOWNR_ID_PRSN_ID_FRSTNM = (
        "Document.FinInstrmRptgTxRpt.Tx.New.Sellr.AcctOwnr.Id.Prsn.FrstNm"
    )

    SELLR_ACCTOWNR_ID_PRSN_ID_NM = (
        "Document.FinInstrmRptgTxRpt.Tx.New.Sellr.AcctOwnr.Id.Prsn.Nm"
    )
    SELLR_ACCTOWNR_ID_PRSN_OTHR_ID = (
        "Document.FinInstrmRptgTxRpt.Tx.New.Sellr.AcctOwnr.Id.Prsn.Othr.Id"
    )
    SELLR_DCSNMAKR_LEI = "Document.FinInstrmRptgTxRpt.Tx.New.Sellr.DcsnMakr.LEI"
    SELLR_DCSNMAKR_PRSN_BIRTHDT = (
        "Document.FinInstrmRptgTxRpt.Tx.New.Sellr.DcsnMakr.Prsn.BirthDt"
    )
    SELLR_DCSNMAKR_PRSN_FRSTNM = (
        "Document.FinInstrmRptgTxRpt.Tx.New.Sellr.DcsnMakr.Prsn.FrstNm"
    )
    SELLR_DCSNMAKR_PRSN_NM = "Document.FinInstrmRptgTxRpt.Tx.New.Sellr.DcsnMakr.Prsn.Nm"
    SELLR_DCSNMAKR_PRSN_OTHR_ID = (
        "Document.FinInstrmRptgTxRpt.Tx.New.Sellr.DcsnMakr.Prsn.Othr.Id"
    )
    SUBMDT = "Document.FinInstrmRptgTxRpt.Tx.SubmDt"
    SUBMITPGTY = "Document.FinInstrmRptgTxRpt.Tx.New.SubmitgPty"
    TX_CMPLXTRADCMPNTID = "Document.FinInstrmRptgTxRpt.Tx.New.Tx.CmplxTradCmpntId"
    TX_CTRYOFBRNCH = "Document.FinInstrmRptgTxRpt.Tx.New.Tx.CtryOfBrnch"
    TX_DERVNTLCHNG = "Document.FinInstrmRptgTxRpt.Tx.New.Tx.DerivNtnlChng"
    TX_NETAMT = "Document.FinInstrmRptgTxRpt.Tx.New.Tx.NetAmt"
    TX_PRIC_NOPRIC_CCY = "Document.FinInstrmRptgTxRpt.Tx.New.Tx.Pric.NoPric.@Ccy"
    TX_PRIC_NOPRIC_PDG = "Document.FinInstrmRptgTxRpt.Tx.New.Tx.Pric.NoPric.Pdg"
    TX_PRIC_PRIC_BSISPTS = "Document.FinInstrmRptgTxRpt.Tx.New.Tx.Pric.Pric.BsisPts"
    TX_PRIC_PRIC_MNTRYVAL_AMT = (
        "Document.FinInstrmRptgTxRpt.Tx.New.Tx.Pric.Pric.MntryVal.Amt"
    )
    TX_PRIC_PRIC_MNTRYVAL_AMT_CCY = (
        "Document.FinInstrmRptgTxRpt.Tx.New.Tx.Pric.Pric.MntryVal.Amt.@Ccy"
    )
    TX_PRIC_PRIC_MNTRYVAL_SGN = (
        "Document.FinInstrmRptgTxRpt.Tx.New.Tx.Pric.Pric.MntryVal.Sgn"
    )
    TX_PRIC_PRIC_PCTG = "Document.FinInstrmRptgTxRpt.Tx.New.Tx.Pric.Pric.Pctg"
    TX_PRIC_PRIC_YLD = "Document.FinInstrmRptgTxRpt.Tx.New.Tx.Pric.Pric.Yld"
    TX_QTY_MNTRYVAL = "Document.FinInstrmRptgTxRpt.Tx.New.Tx.Qty.MntryVal"
    TX_QTY_MNTRYVAL_CCY = "Document.FinInstrmRptgTxRpt.Tx.New.Tx.Qty.MntryVal.@Ccy"
    TX_QTY_NMNLVAL = "Document.FinInstrmRptgTxRpt.Tx.New.Tx.Qty.NmnlVal"
    TX_QTY_NMNLVAL_CCY = "Document.FinInstrmRptgTxRpt.Tx.New.Tx.Qty.NmnlVal.@Ccy"
    TX_QTY_UNIT = "Document.FinInstrmRptgTxRpt.Tx.New.Tx.Qty.Unit"
    TX_TRADGCPCTY = "Document.FinInstrmRptgTxRpt.Tx.New.Tx.TradgCpcty"
    TX_TRADPLCMTCHGID = "Document.FinInstrmRptgTxRpt.Tx.New.Tx.TradPlcMtchgId"
    TX_TRADT = "Document.FinInstrmRptgTxRpt.Tx.New.Tx.TradDt"
    TX_TRADVN = "Document.FinInstrmRptgTxRpt.Tx.New.Tx.TradVn"
    TX_UPFRNTPMT_AMT = "Document.FinInstrmRptgTxRpt.Tx.New.Tx.UpFrntPmt.Amt"
    TX_UPFRNTPMT_AMT_CCY = "Document.FinInstrmRptgTxRpt.Tx.New.Tx.UpFrntPmt.Amt.@Ccy"
    TXID = "Document.FinInstrmRptgTxRpt.Tx.New.TxId"


class PartyModels:

    ACCOUNT_FIRM = "AccountFirm"
    MARKET_COUNTERPARTY = "MarketCounterparty"


class RecFields:
    FIELD_BREAKS = "reconciliation.fieldBreaks"
    FIELD_BREAKS_FIELD = "field"
    FIELD_BREAKS_VALUE = "value"
    IS_CREATED_THROUGH_RECONCILIATION_FLAG = "isCreatedThroughReconciliation.flag"
    IS_CREATED_THROUGH_RECONCILIATION_SOURCE = "isCreatedThroughReconciliation.source"
    RECONCILIATION_ARM_FIELD_BREAK = "reconciliation.arm.fieldBreak"
    RECONCILIATION_ARM_MATCH = "reconciliation.arm.match"
    RECONCILIATION_ARM_SOURCE_KEY = "reconciliation.arm.sourceKey"
    RECONCILIATION_NCA_FIELD_BREAK = "reconciliation.nca.fieldBreak"
    RECONCILIATION_NCA_MATCH = "reconciliation.nca.match"
    RECONCILIATION_NCA_SOURCE_KEY = "reconciliation.nca.sourceKey"
    RECONCILIATION_SE_MATCH = "reconciliation.se.match"
    RECONCILIATION_SE_SOURCE = "reconciliation.se.source"
    REPORT_DETAILS_REPORT_STATUS = "reportDetails.reportStatus"
    RULE_ID = "ruleId"
    TEMP_RECONCILIATION_ARM_SOURCE_KEY = "__reconciliation.arm.sourceKey"
    TEMP_RECONCILIATION_NCA_SOURCE_KEY = "__reconciliation.nca.sourceKey"
    WORKFLOW_IS_REPORTED = "workflow.isReported"
    WORKFLOW_NCA_CHECKS = "workflow.nca.checks"
    WORKFLOW_NCA_STATUS = "workflow.nca.status"
    WORKFLOW_ARM_STATUS = "workflow.arm.status"


class RecSources:

    ARM = "arm"
    NCA = "nca"
    SE = "se"


class ReportDetailsIdFields:
    CANC = "CANC"
    IS_REPORTED = "workflow.isReported"
    NEWT = "NEWT"
    REPORT_DETAILS_REPORT_STATUS = "reportDetails.reportStatus"
    TEMP_ID = "__Id"
    TEMP_REPORT_STATUS = "__reportDetails.reportStatus"
    TRANSACTION_REF_NO = "reportDetails.transactionRefNo"


class RTS22Transaction:
    DATA_SOURCE_NAME = "dataSourceName"
    DATE = "date"
    DERIVATIVE_UDERLYING_INDEX_NAME = "derivative.underlyingIndexName"
    EXT_UNDERLYING_INDEX_ID = "ext.underlyingIndexId"
    EXT_UNDERLYING_INDEX_NAME = "ext.underlyingIndexName"
    FIRM_IDENTIFIERS_BRANCH_COUNTRY = "firmIdentifiers.branchCountry"
    FIRM_IDENTIFIERS_LEI = "firmIdentifiers.lei"
    INSTRUMENT_DETAILS_INSTRUMENT_BOND_MATURITY_DATE = (
        "instrumentDetails.instrument.bond.maturityDate"
    )
    INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_DELIVERY_TYPE = (
        "instrumentDetails.instrument.derivative.deliveryType"
    )
    INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_EXPIRY_DATE = (
        "instrumentDetails.instrument.derivative.expiryDate"
    )
    INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_OPTION_EXERCISE_STYLE = (
        "instrumentDetails.instrument.derivative.optionExerciseStyle"
    )
    INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_OPTION_TYPE = (
        "instrumentDetails.instrument.derivative.optionType"
    )
    INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_PRICE_MULTIPLIER = (
        "instrumentDetails.instrument.derivative.priceMultiplier"
    )
    INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_STRIKE_PRICE = (
        "instrumentDetails.instrument.derivative.strikePrice"
    )
    INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_STRIKE_PRICE_CURRENCY = (
        "instrumentDetails.instrument.derivative.strikePriceCurrency"
    )
    INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_UL_INDEX_NAME = (
        "instrumentDetails.instrument.derivative.underlyingIndexName"
    )
    INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_UL_INDEX_TERM = (
        "instrumentDetails.instrument.derivative.underlyingIndexTerm"
    )
    INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_UL_INDEX_TERM_VALUE = (
        "instrumentDetails.instrument.derivative.underlyingIndexTermValue"
    )
    INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_UL_INSTRUMENTS = (
        "instrumentDetails.instrument.derivative.underlyingInstruments"
    )
    INSTRUMENT_DETAILS_INSTRUMENT_EXT_STRIKE_PRICE_TYPE = (
        "instrumentDetails.instrument.ext.strikePriceType"
    )
    INSTRUMENT_DETAILS_INSTRUMENT_EXT_UL_INSTRUMENTS = (
        "instrumentDetails.instrument.ext.underlyingInstruments"
    )
    INSTRUMENT_DETAILS_INSTRUMENT_FX_DERIVATIVES_NOTIONAL_CURRENCY2 = (
        "instrumentDetails.instrument.fxDerivatives.notionalCurrency2"
    )
    INSTRUMENT_DETAILS_INSTRUMENT_INSTRUMENT_CLASSIFICATION = (
        "instrumentDetails.instrument.instrumentClassification"
    )
    INSTRUMENT_DETAILS_INSTRUMENT_INSTRUMENT_FULL_NAME = (
        "instrumentDetails.instrument.instrumentFullName"
    )
    INSTRUMENT_DETAILS_INSTRUMENT_INSTRUMENT_ID_CODE = (
        "instrumentDetails.instrument.instrumentIdCode"
    )
    INSTRUMENT_DETAILS_INSTRUMENT_IS_CREATED_THROUGH_FALLBACK = (
        "instrumentDetails.instrument.isCreatedThroughFallback"
    )
    INSTRUMENT_DETAILS_INSTRUMENT_NOTIONAL_CURRENCY_1 = (
        "instrumentDetails.instrument.notionalCurrency1"
    )
    INSTRUMENT_ID_CODE = "instrumentIdCode"
    IS_CREATED_THROUGH_RECONCILIATION = "isCreatedThroughReconciliation"
    MARKET_IDENTIFIERS = "marketIdentifiers"
    MARKET_IDENTIFIERS_INSTRUMENT = "marketIdentifiers.instrument"
    MARKET_IDENTIFIERS_PARTIES_EXECUTION_WITHIN_FIRM = (
        "marketIdentifiers.parties.executionWithinFirm"
    )
    MARKET_IDENTIFIERS_PARTIES = "marketIdentifiers.parties"
    META_MODEL = "__meta_model__"
    OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY = "officialIdentifiers.branchCountry"
    OFFICIAL_IDENTIFIERS_MIFIR_ID = "officialIdentifiers.mifirId"
    PARTIES_BUYER = "parties.buyer"
    PARTIES_BUYER_DECISION_MAKER = "parties.buyerDecisionMaker"
    PARTIES_BUYER_TRANSMITTING_FIRM_FIRM_IDENTIFIERS_LEI = (
        "parties.buyerTransmittingFirm.firmIdentifiers.lei"
    )
    PARTIES_EXEC_WT_FIRM_STRCT_EXEC_WT_FIRM_ID = (
        "parties.executionWithinFirm.structure.executionWithinFirm.id"
    )
    PARTIES_EXEC_WT_FIRM_STRCT_EXEC_WT_FIRM_ID_TYPE = (
        "parties.executionWithinFirm.structure.executionWithinFirm.idType"
    )
    PARTIES_EXECUTING_ENTITY_DETAILS_MIFID_REGISTERED = (
        "parties.executingEntity.details.mifidRegistered"
    )
    PARTIES_EXECUTING_ENTITY_FIRM_IDENTIFIERS_LEI = (
        "parties.executingEntity.firmIdentifiers.lei"
    )

    PARTIES_EXECUTION_WITHIN_FIRM = "parties.executionWithinFirm"
    PARTIES_EXECUTION_WITHIN_FIRM_MIFIR_ID = (
        "parties.executionWithinFirm.officialIdentifiers.mifirId"
    )
    PARTIES_EXECUTION_WITHIN_FIRM_OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY = (
        "parties.executionWithinFirm.officialIdentifiers.branchCountry"
    )
    PARTIES_INV_DEC_WITHIN_FIRM = "parties.investmentDecisionWithinFirm"
    PARTIES_INV_DEC_WITHIN_FIRM_MIFIR_ID = (
        "parties.investmentDecisionWithinFirm.officialIdentifiers.mifirId"
    )
    PARTIES_INV_DEC_WITHIN_FIRM_OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY = (
        "parties.investmentDecisionWithinFirm.officialIdentifiers.branchCountry"
    )
    PARTIES_INV_DEC_WT_FIRM_STRCT_EXEC_WT_FIRM_ID = (
        "parties.investmentDecisionWithinFirm.structure.executionWithinFirm.id"
    )
    PARTIES_INV_DEC_WT_FIRM_STRCT_EXEC_WT_FIRM_ID_TYPE = (
        "parties.investmentDecisionWithinFirm.structure.executionWithinFirm.idType"
    )
    PARTIES_SELLER = "parties.seller"
    PARTIES_SELLER_DECISION_MAKER = "parties.sellerDecisionMaker"
    PARTIES_SELLER_TRANSMITTING_FIRM_FIRM_IDENTIFIERS_LEI = (
        "parties.sellerTransmittingFirm.firmIdentifiers.lei"
    )
    PERSONAL_DETAILS_DOB = "personalDetails.dob"
    PERSONAL_DETAILS_FIRST_NAME = "personalDetails.firstName"
    PERSONAL_DETAILS_LAST_NAME = "personalDetails.lastName"
    REPORT_DETAILS_INVESTMENT_FIRM_COVERED_DIRECTIVE = (
        "reportDetails.investmentFirmCoveredDirective"
    )
    REPORT_DETAILS_REPORT_STATUS = "reportDetails.reportStatus"
    REPORT_DETAILS_TRADING_VENUE_TRANSACTION_ID_CODE = (
        "reportDetails.tradingVenueTransactionIdCode"
    )
    REPORT_DETAILS_TRANSACTION_REF_NO = "reportDetails.transactionRefNo"
    SOURCE_INDEX = "sourceIndex"
    SOURCE_KEY = "sourceKey"
    TRADERS_ALGOS_WAIVERS_INDICATORS_COMMODITY_DRV_IND = (
        "tradersAlgosWaiversIndicators.commodityDerivativeIndicator"
    )
    TRADERS_ALGOS_WAIVERS_INDICATORS_OTC_POST_TRADE_IND = (
        "tradersAlgosWaiversIndicators.otcPostTradeIndicator"
    )
    TRADERS_ALGOS_WAIVERS_INDICATORS_SECRT_FNC_TXN_IND = (
        "tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator"
    )
    TRADERS_ALGOS_WAIVERS_INDICATORS_SHORT_SELLING_INDICATOR = (
        "tradersAlgosWaiversIndicators.shortSellingIndicator"
    )
    TRADERS_ALGOS_WAIVERS_INDICATORS_WAIVER_INDICATOR = (
        "tradersAlgosWaiversIndicators.waiverIndicator"
    )
    TRADERS_ALGOS_WAIVERS_INDICATORS_EXECUTION_WITHIN_FIRM_FILE_IDENTIFIER = (
        "tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier"
    )
    TRADERS_ALGOS_WAIVERS_INDICATORS_INVESTMENT_DECISION_WITHIN_FIRM_FILE_IDENTIFIER = (
        "tradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier"
    )
    TRANSACTION_DETAILS_BRANCH_MEMBERSHIP_COUNTRY = (
        "transactionDetails.branchMembershipCountry"
    )
    TRANSACTION_DETAILS_BUY_SELL_INDICATOR = "transactionDetails.buySellIndicator"
    TRANSACTION_DETAILS_COMPLEX_TRADE_COMPONENT_ID = (
        "transactionDetails.complexTradeComponentId"
    )
    TRANSACTION_DETAILS_CROSS_INDICATOR = "transactionDetails.crossIndicator"
    TRANSACTION_DETAILS_DERIVATIVE_NOTIONAL_CHANGE = (
        "transactionDetails.derivativeNotionalChange"
    )
    TRANSACTION_DETAILS_NET_AMOUNT = "transactionDetails.netAmount"
    TRANSACTION_DETAILS_OUTGOING_ORDER_ADDL_INFO = (
        "transactionDetails.outgoingOrderAddlInfo"
    )
    TRANSACTION_DETAILS_POSITION_EFFECT = "transactionDetails.positionEffect"
    TRANSACTION_DETAILS_PRICE = "transactionDetails.price"
    TRANSACTION_DETAILS_PRICE_CURRENCY = "transactionDetails.priceCurrency"
    TRANSACTION_DETAILS_PRICE_NOT_APPLICABLE = "transactionDetails.priceNotApplicable"
    TRANSACTION_DETAILS_PRICE_NOTATION = "transactionDetails.priceNotation"
    TRANSACTION_DETAILS_PRICE_PENDING = "transactionDetails.pricePending"
    TRANSACTION_DETAILS_QUANTITY = "transactionDetails.quantity"
    TRANSACTION_DETAILS_QUANTITY_CURRENCY = "transactionDetails.quantityCurrency"
    TRANSACTION_DETAILS_QUANTITY_NOTATION = "transactionDetails.quantityNotation"
    TRANSACTION_DETAILS_RECORD_TYPE = "transactionDetails.recordType"
    TRANSACTION_DETAILS_SETTLEMENT_AMOUNT = "transactionDetails.settlementAmount"
    TRANSACTION_DETAILS_SETTLEMENT_AMOUNT_CURRENCY = (
        "transactionDetails.settlementAmountCurrency"
    )
    TRANSACTION_DETAILS_SETTLEMENT_DATE = "transactionDetails.settlementDate"
    TRANSACTION_DETAILS_SWAP_DIRECTIONALITIES = (
        "transactionDetails.swapDirectionalities"
    )
    TRANSACTION_DETAILS_TRADED_QUANTITY = "transactionDetails.tradedQuantity"
    TRANSACTION_DETAILS_TRADING_CAPACITY = "transactionDetails.tradingCapacity"
    TRANSACTION_DETAILS_TRADING_DATE_TIME = "transactionDetails.tradingDateTime"
    TRANSACTION_DETAILS_TRAIL_ID = "transactionDetails.trailId"
    TRANSACTION_DETAILS_UPFRONT_PAYMENT = "transactionDetails.upFrontPayment"
    TRANSACTION_DETAILS_UPFRONT_PAYMENT_CURRENCY = (
        "transactionDetails.upFrontPaymentCurrency"
    )
    TRANSACTION_DETAILS_ULTIMATE_VENUE = "transactionDetails.ultimateVenue"
    TRANSACTION_DETAILS_VENUE = "transactionDetails.venue"

    TRANSMISSION_DETAILS_ORDER_TRANSMISSION_INDICATOR = (
        "transmissionDetails.orderTransmissionIndicator"
    )
    UNDERLYING_INSTRUMENT_CODE = "underlyingInstrumentCode"
    WORKFLOW_IS_REPORTED = "workflow.isReported"
    WORKFLOW_NCA_CHECKS = "workflow.nca.checks"
    WORKFLOW_NCA_STATUS = "workflow.nca.status"


class StaticValues:

    BAPO = "BAPO"
    BSISPTS = "BsisPts"
    CANC = "CANC"
    MNTRYVAL = "MntryVal"
    MNTRYVAL_AMT = "MntryValAmt"
    MONETARY_VALUE = "MONETARYVALUE"
    MONE = "MONE"
    NEWT = "NEWT"
    NOAP = "NOAP"
    NOMINAL_VALUE = "NOMINALVALUE"
    NOML = "NOML"
    OTHER = "Other"
    PCTG = "Pctg"
    PERC = "PERC"
    PNDG = "PNDG"
    PRICE_NOT_AVAILABLE = "Price not available"
    PRICE_PENDING = "Price pending"
    REJECTED = "REJECTED"
    REJECTION_CLEARED = "REJECTION_CLEARED"
    SWAP_IN = "Swap In"
    SWAP_OUT = "Swap Out"
    UNIT = "UNIT"
    YIEL = "YIEL"
    YLD = "Yld"


class TimeFormats:

    ARM_DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S.%f"
    DATETIME = "%Y-%m-%dT%H:%M:%S.%fZ"
