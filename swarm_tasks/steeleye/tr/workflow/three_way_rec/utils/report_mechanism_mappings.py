from swarm_tasks.steeleye.tr.workflow.three_way_rec.arm_unavista_report_mechanism_reconciliation import (
    ArmUnavistaReportMechanismReconciliation,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.nca_fca_report_mechanism_reconciliation import (
    NcaFcaReportMechanismReconciliation,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.enums import ReportTypeEnum

REPORT_MECHANISM_MAP = {
    ReportTypeEnum.NCA_FCA: NcaFcaReportMechanismReconciliation,
    ReportTypeEnum.ARM_UNAVISTA: ArmUnavistaReportMechanismReconciliation,
}
