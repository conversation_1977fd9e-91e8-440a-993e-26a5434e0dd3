from copy import deepcopy
from typing import List
from typing import Optional

import numpy as np
import pandas as pd
from prefect.engine import signals
from pydantic import Field
from se_elastic_schema.models import RTS22Transaction
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.steeleye.tr.static import RecFields
from swarm_tasks.steeleye.tr.static import ReportDetailsIdFields
from swarm_tasks.steeleye.tr.static import StaticValues
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.enums import ReportTypeEnum
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.report_mechanism_mappings import (
    REPORT_MECHANISM_MAP,
)
from swarm_tasks.tr.workflow.generic.static import ElasticSearchQueryTerms
from swarm_tasks.utilities.es_queries import add_terms_to_query_by_max_terms_size


class Params(BaseParams):
    report_type: ReportTypeEnum = Field(
        ...,
        description="ReportTypeEnum enumerator to control the task logic flow for "
        "either ARM_UNAVISTA or NCA_FCA reports",
    )


class Resources(BaseResources):
    es_client_key: str


class LinkReportTransactionsToSteelEye(TransformBaseTask):
    """
    Three-Way-Reconciliation task to link transactions from a NCA_FCA report to the ones in SDP.
    Report transactions found in SDP will be reconciled in downstream tasks.
    Report transactions absent from SDP will be ingested in downstream tasks.
    """

    params_class = Params
    report_mechanism_reconciliation = None
    resources_class = Resources

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Optional[Params] = None,
        resources: Optional[Resources] = None,
        **kwargs,
    ) -> pd.DataFrame:

        self.report_mechanism_reconciliation = REPORT_MECHANISM_MAP[
            params.report_type
        ]()

        if source_frame.empty:
            self.logger.warning("There are no transactions to reconcile")
            raise signals.SKIP(
                "Skipping reconciliation of the report against SteelEye data"
            )

        report_and_steeleye_transactions = deepcopy(source_frame)
        report_and_steeleye_transactions = (
            self.report_mechanism_reconciliation.link_preprocess_data(
                df=report_and_steeleye_transactions
            )
        )

        es_client = self.clients.get(resources.es_client_key)
        tenant = Settings.tenant

        report_and_steeleye_transactions = (
            self._merge_steeleye_transactions_with_report(
                report_transactions=report_and_steeleye_transactions,
                es_client=es_client,
                tenant=tenant,
            )
        )

        return report_and_steeleye_transactions

    def _merge_steeleye_transactions_with_report(
        self,
        report_transactions: pd.DataFrame,
        es_client,
        tenant: str,
    ) -> pd.DataFrame:
        """
        Search for the NCA_FCA report transactions in the tenant's specific data
        in ElasticSearch and merge the datasets.

        :param report_transactions: DataFrame with transactions from the NCA_FCA report
        :param es_client: ElasticSearchClient
        :param tenant: Tenant name (to query the appropriate ES index)
        :return: DataFrame with transactions from the NCA_FCA report
        + the associated SDP transactions, if there is a match
        """

        (
            report_status_newt_mask,
            report_status_cancel_mask,
        ) = self.report_mechanism_reconciliation.get_report_status_masks(
            report_transactions=report_transactions
        )

        (
            new_unique_transaction_ids,
            cancel_unique_transaction_ids,
        ) = self.report_mechanism_reconciliation.get_unique_transaction_ids(
            report_transactions=report_transactions,
            report_status_newt_mask=report_status_newt_mask,
            report_status_cancel_mask=report_status_cancel_mask,
        )

        new_transactions_query = self._fetch_transactions_query(
            report_status=ReportDetailsIdFields.NEWT,
            transaction_ids=list(np.unique(new_unique_transaction_ids)),
            es_client=es_client,
        )

        cancel_transactions_query = self._fetch_transactions_query(
            report_status=ReportDetailsIdFields.CANC,
            transaction_ids=list(np.unique(cancel_unique_transaction_ids)),
            es_client=es_client,
        )

        if len(new_unique_transaction_ids):
            self.logger.info(
                f"scrolling for {len(new_unique_transaction_ids)} new transactions"
            )

        new_transactions = self._fetch_transactions(
            es_client=es_client, query=new_transactions_query, tenant=tenant
        )

        if len(cancel_unique_transaction_ids):
            self.logger.info(
                f"scrolling for {len(cancel_unique_transaction_ids)} cancelled transactions"
            )

        cancel_transactions = self._fetch_transactions(
            es_client=es_client, query=cancel_transactions_query, tenant=tenant
        )

        transactions = pd.concat([new_transactions, cancel_transactions], axis=0)

        if not transactions.empty:

            original_index = report_transactions.index
            for col in [
                ReportDetailsIdFields.TRANSACTION_REF_NO,
                ReportDetailsIdFields.REPORT_DETAILS_REPORT_STATUS,
            ]:
                if col not in transactions.columns:
                    transactions[col] = pd.NA

            report_transactions = (
                self.report_mechanism_reconciliation.merge_transactions(
                    report_transactions=report_transactions,
                    rts22_transactions=transactions,
                )
            )
            report_transactions.index = original_index

            # This is required as REJECTION_CLEARED and REJECTION should be treated the same as per EU-8614
            # REJECTION_CLEARED is an internal status within SteelEye which is assigned to the transaction
            # when the client has reviewed the REJECTED transaction and don't want to view it everytime again.
            if RecFields.WORKFLOW_NCA_STATUS in report_transactions:
                report_transactions[
                    RecFields.WORKFLOW_NCA_STATUS
                ] = report_transactions[RecFields.WORKFLOW_NCA_STATUS].replace(
                    StaticValues.REJECTION_CLEARED, StaticValues.REJECTED
                )

            if RecFields.WORKFLOW_ARM_STATUS in report_transactions:
                report_transactions[
                    RecFields.WORKFLOW_ARM_STATUS
                ] = report_transactions[RecFields.WORKFLOW_ARM_STATUS].replace(
                    StaticValues.REJECTION_CLEARED, StaticValues.REJECTED
                )

        else:
            report_transactions[es_client.meta.id] = pd.NA

        # Downstream on the pipeline, we want to filter out the transactions which have been created
        # previously through 3WayRec. We do not want to reconcile them against the data that generated them,
        # and we do not want to re-ingest them
        if (
            RecFields.IS_CREATED_THROUGH_RECONCILIATION_FLAG
            not in report_transactions.columns
        ):
            report_transactions.loc[
                :, RecFields.IS_CREATED_THROUGH_RECONCILIATION_FLAG
            ] = pd.NA

        report_transactions = report_transactions.fillna(pd.NA)

        return report_transactions

    @staticmethod
    def _fetch_transactions(es_client, query: dict, tenant: str) -> pd.DataFrame:
        """
        Fetch transactions from the tenant's RTS22Transaction ElasticSearch index
        and remove duplicates (keep most recent record)

        :param es_client: ElasticSearchClient
        :param query: Query to execute in ElasticSearch
        :param tenant: Tenant name to define the ES index
        :return: DataFrame with all data of the linked RTS22Transactions
        """
        alias = RTS22Transaction.get_elastic_index_alias(tenant=tenant)
        transactions = es_client.scroll(query=query, index=alias)

        if not transactions.empty:
            # Cast &timestamp field to int
            transactions.loc[:, es_client.meta.timestamp] = transactions.loc[
                :, es_client.meta.timestamp
            ].astype("int")
            transactions = (
                transactions.sort_values(es_client.meta.timestamp, ascending=False)
                .drop_duplicates(
                    subset=[
                        ReportDetailsIdFields.TRANSACTION_REF_NO,
                        ReportDetailsIdFields.REPORT_DETAILS_REPORT_STATUS,
                    ],
                    keep="first",
                )
                .sort_index()
            )

            transactions = transactions.drop(labels=[es_client.meta.timestamp], axis=1)

        return transactions

    def _fetch_transactions_query(
        self,
        report_status: str,
        transaction_ids: List[str],
        es_client,
    ) -> dict:
        """
        Build chunked query to fetch RTS22Transactions from SDP

        :param report_status: ReportStatus to filter between CANCEL and NEWT transactions
        :param transaction_ids: List of TransactionRefNo IDs to filter the reactions from SDP
        :param es_client: ElasticSearchClient
        :return: Dictionary with ES query
        """

        filters = [
            {"term": {es_client.meta.model: ElasticSearchQueryTerms.RTS22_TRANSACTION}},
            {
                "term": {
                    ReportDetailsIdFields.REPORT_DETAILS_REPORT_STATUS: report_status
                }
            },
            {"bool": {"must_not": {"exists": {"field": es_client.meta.expiry}}}},
        ]

        # We are fetching all the fields now to compare for reconciliation
        query = {
            "query": {"bool": {"filter": filters}},
            "size": es_client.MAX_QUERY_SIZE,
        }

        query = add_terms_to_query_by_max_terms_size(
            query=query,
            max_terms_size=es_client.MAX_TERMS_SIZE,
            terms_field=ReportDetailsIdFields.TRANSACTION_REF_NO,
            terms_list=transaction_ids,
        )

        return query
