from typing import List
from typing import Optional

import pandas as pd
from pydantic import Field
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.steeleye.tr.static import RTS22Transaction
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.enums import ReportTypeEnum
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.mappings import BOOL_MAP
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.mappings import (
    REPORT_TYPE_MAPPING,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.report_mechanism_mappings import (
    REPORT_MECHANISM_MAP,
)
from se_core_tasks.utils.encoding import md5_encode


class Params(BaseParams):
    report_type: ReportTypeEnum = Field(
        ...,
        description="ReportTypeEnum enumerator to control the task logic flow for "
        "either ARM_UNAVISTA or NCA_FCA reports",
    )


class Resources(BaseResources):
    es_client_key: str


class BuildTransactionsFromReport(TransformBaseTask):
    """
    Mapping instructions here:
    https://steeleye.atlassian.net/wiki/spaces/IN/pages/1597472769/Mapping+-+RTS22+-+Three-Way-Reconcilliation

    - Three-Way-Reconciliation task that expects a `source_frame` Pandas DataFrame where each row represents
    a transaction from the ARM_UNAVISTA/NCA_FCA report which was NOT found
    in the tenant's SteelEye RTS22Transaction ES index and
    the columns set includes all of the parsed NCA_FCA/ARM_UNAVISTA fields.

    - Report transactions which are not in SteelEye must be ingested, thus,
    this tasks builds the RTS22Transaction records from the ARM_UNAVISTA/NCA_FCA report fields.
    ARM_UNAVISTA vs NCA_FCA is defined by the `report_type` param, thus this task is executed
    from both the `three-way-rec-nca` and `three-way-rec-arm` flows, with a different `source_frame` and params.

    - This algorithm will build the RTS22Transaction fields and output a `target` dataframe
    that includes the necessary fields to create each RTS22Transaction in ElasticSearch
    """

    params_class = Params
    resources_class = Resources
    report_mechanism_reconciliation = None

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Optional[Params] = None,
        resources: Optional[Resources] = None,
        **kwargs,
    ) -> pd.DataFrame:

        target = pd.DataFrame(index=source_frame.index)

        self.report_mechanism_reconciliation = REPORT_MECHANISM_MAP[
            params.report_type
        ]()

        if source_frame.empty:
            self.logger.warning(
                "All transactions in the report have been linked to SteelEye"
            )
            return target

        es = self.clients.get(resources.es_client_key)

        target = self.report_mechanism_reconciliation.preprocess_data(
            source_frame=source_frame, target=target
        )

        target = self._build_rts22transactions_from_report(
            source_data=source_frame, target=target, es=es
        )

        self.logger.info(
            f"Successfully created {target.shape[0]} transactions from the "
            f"{REPORT_TYPE_MAPPING[params.report_type.value]} report"
        )

        return target

    def _build_rts22transactions_from_report(
        self, source_data: pd.DataFrame, target: pd.DataFrame, es
    ) -> pd.DataFrame:
        """
        Trigger all methods that populate multiple RTS22Transaction fields

        :param source_data: Pandas Dataframe with report data
        :param target: Pandas DataFrame with workflow and reconciliation RTS22Transaction fields
        :param es: ElasticSearch client
        :return: Pandas DataFrame with complete RTS22Transaction fields
        """

        self._populate_one_to_one_general_mapping_fields(
            source_data=source_data, target=target
        )

        self._populate_compound_general_mapping_fields(
            source_data=source_data, target=target
        )

        self._populate_compound_party_mapping_fields(
            source_data=source_data, target=target
        )

        self._populate_one_to_one_party_mapping_fields(
            source_data=source_data, target=target, es=es
        )

        self._populate_buyer_seller_and_decision_maker_party_mapping_fields(
            source_data=source_data, target=target, es=es
        )

        self._populate_one_to_one_instrument_mapping_fields(
            source_data=source_data, target=target
        )

        self._populate_compound_instrument_mapping_fields(
            source_data=source_data, target=target
        )

        self._populate_custom_general_mapping_fields(
            source_data=source_data, target=target
        )

        self._populate_underlying_instrument_mapping_fields(
            source_data=source_data, target=target
        )

        return target

    def _populate_one_to_one_general_mapping_fields(
        self, source_data: pd.DataFrame, target: pd.DataFrame
    ):
        """
        Populate General RTS22Transaction fields with a one to one mapping
        Example: FinInstrmRptgTxRpt.Tx.New.Tx.TradPlcMtchgId -> reportDetails.tradingVenueTransactionIdCode

        :param source_data: Pandas DataFrame with Report data
        :param target: Pandas DataFrame with RTS22Transaction fields
        """

        if source_data.empty:
            return

        one_to_one_map = (
            self.report_mechanism_reconciliation.get_general_one_to_one_map()
        )

        self.report_mechanism_reconciliation.generic_populate_one_to_one_mappings(
            one_to_one_map=one_to_one_map,
            source_data=source_data,
            target=target,
        )

    def _populate_compound_general_mapping_fields(
        self, source_data: pd.DataFrame, target: pd.DataFrame
    ):
        if source_data.empty:
            return

        self.report_mechanism_reconciliation.populate_compound_mappings(
            source_data=source_data, target=target
        )

    def _populate_compound_party_mapping_fields(
        self, source_data: pd.DataFrame, target: pd.DataFrame
    ):
        if source_data.empty:
            return

        self.report_mechanism_reconciliation.populate_compound_party_mappings(
            source_data=source_data, target=target
        )

    def _populate_custom_general_mapping_fields(
        self, source_data: pd.DataFrame, target: pd.DataFrame
    ):
        """
        Populate custom RTS22Transaction fields with specific rules, which cannot be expressed through
        generic one-to-one or compound mappings

        :param source_data: Pandas DataFrame with Report data
        :param target: Pandas DataFrame with RTS22Transaction fields
        """

        if source_data.empty:
            return

        # transactionDetails.pricePending & transactionDetails.priceNotApplicable
        price_pending_report_field = (
            self.report_mechanism_reconciliation.get_price_pending_report_field()
        )
        price_pending_report_dictionary = (
            self.report_mechanism_reconciliation.get_price_pending_report_dictionary()
        )

        price_pending = self.report_mechanism_reconciliation.parse_price_pending(
            price_pending_report_field=price_pending_report_field,
            price_pending_report_dictionary=price_pending_report_dictionary,
            source_data=source_data,
        )
        target.loc[:, RTS22Transaction.TRANSACTION_DETAILS_PRICE_PENDING] = (
            price_pending.get(
                RTS22Transaction.TRANSACTION_DETAILS_PRICE_PENDING, "false"
            )
            .loc[:]
            .replace(BOOL_MAP)
        )
        target.loc[:, RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOT_APPLICABLE] = (
            price_pending.get(
                RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOT_APPLICABLE, "false"
            )
            .loc[:]
            .replace(BOOL_MAP)
        )

        # tradersAlgosWaiversIndicators.waiverIndicator[]

        source_waiver_ind_column = (
            self.report_mechanism_reconciliation.get_source_waiver_column()
        )
        waiver_indicator = self.report_mechanism_reconciliation.parse_one_to_list_mapping(
            source_data=source_data,
            source_column=source_waiver_ind_column,
            target_column=RTS22Transaction.TRADERS_ALGOS_WAIVERS_INDICATORS_WAIVER_INDICATOR,
        )

        target.loc[
            :, RTS22Transaction.TRADERS_ALGOS_WAIVERS_INDICATORS_WAIVER_INDICATOR
        ] = waiver_indicator.get(
            RTS22Transaction.TRADERS_ALGOS_WAIVERS_INDICATORS_WAIVER_INDICATOR
        )

        # tradersAlgosWaiversIndicators.otcPostTradeIndicator[]
        otc_indicator_column = (
            self.report_mechanism_reconciliation.get_source_otc_column()
        )

        otc_indicator = self.report_mechanism_reconciliation.parse_one_to_list_mapping(
            source_data=source_data,
            source_column=otc_indicator_column,
            target_column=RTS22Transaction.TRADERS_ALGOS_WAIVERS_INDICATORS_OTC_POST_TRADE_IND,
        )

        target.loc[
            :,
            RTS22Transaction.TRADERS_ALGOS_WAIVERS_INDICATORS_OTC_POST_TRADE_IND,
        ] = otc_indicator.get(
            RTS22Transaction.TRADERS_ALGOS_WAIVERS_INDICATORS_OTC_POST_TRADE_IND
        )

        self.report_mechanism_reconciliation.populate_additional_custom_fields(
            source_data=source_data, target=target
        )

    def _populate_one_to_one_party_mapping_fields(
        self,
        source_data: pd.DataFrame,
        target: pd.DataFrame,
        es,
    ):
        """
        Populate Party RTS22Transaction fields with a one to one mapping
        Example: FinInstrmRptgTxRpt.Tx.New.InvstmtPtyInd -> parties.executingEntity.details.mifidRegistered

        :param source_data: Pandas DataFrame with Report data
        :param target: Pandas DataFrame with RTS22Transaction fields
        :param es: ElasticSearch client
        """
        if source_data.empty:
            return

        (
            one_to_one_map,
            tr_pi_enrichment_map,
        ) = self.report_mechanism_reconciliation.get_party_one_to_one_build_map()

        # no need to check the tenant configuration `trPIEnrichmentEnabled` flag
        # If the flag were False, these fields would not be populated and they would not be part of the
        # RTS22Transaction to be written into ES
        one_to_one_map.update(tr_pi_enrichment_map)

        self.report_mechanism_reconciliation.generic_populate_one_to_one_mappings(
            one_to_one_map=one_to_one_map,
            source_data=source_data,
            target=target,
        )

        # Populate &key of ExecutionWithinFirm and InvestmentDecisionWithinFirm
        rts22party_meta_key_fields = {
            RTS22Transaction.PARTIES_EXECUTION_WITHIN_FIRM: RTS22Transaction.PARTIES_EXECUTION_WITHIN_FIRM_MIFIR_ID,
            RTS22Transaction.PARTIES_INV_DEC_WITHIN_FIRM: RTS22Transaction.PARTIES_INV_DEC_WITHIN_FIRM_MIFIR_ID,
        }

        for rts22party in rts22party_meta_key_fields:

            col_name = f"{rts22party}.{es.meta.key}"
            target.loc[:, col_name] = pd.NA

            rts22party_field = rts22party_meta_key_fields[rts22party]

            if rts22party_field in target.columns:

                not_null_mask = target.loc[:, rts22party_field].notnull()

                if not_null_mask.any():

                    target.loc[not_null_mask, col_name] = target.loc[
                        not_null_mask, rts22party_field
                    ].apply(lambda x: f"AccountPerson:{md5_encode(str(x))}")

    def _populate_buyer_seller_and_decision_maker_party_mapping_fields(
        self, source_data: pd.DataFrame, target: pd.DataFrame, es
    ):
        """
        Populate fields within lists for Party fields, `Buyer`, `Seller`, `Buyer Decision Maker` and
        `Seller Decision Maker`.
        Example:
        FinInstrmRptgTxRpt.Tx.New.Buyr.AcctOwnr.Id.LEI OR FinInstrmRptgTxRpt.Tx.New.Buyr.AcctOwnr.Id.Intl
        -> parties.buyer[].firmIdentifiers.lei
        OR
        FinInstrmRptgTxRpt.Tx.New.Buyr.AcctOwnr.Id.Prsn.Othr.Id -> parties.buyer[].officialIdentifiers.mifirId

        :param source_data: Pandas DataFrame with Report data
        :param target: Pandas DataFrame with RTS22Transaction fields
        :param es: ElasticSearch client
        """

        parties = (
            self.report_mechanism_reconciliation.get_buyer_seller_and_decision_maker_map()
        )
        es_meta_key = es.meta.key

        for party_mapping_data in parties:

            parent_party_field = party_mapping_data.rts22_party_root_field
            target.loc[:, parent_party_field] = source_data.loc[:, :].apply(
                lambda x: self.report_mechanism_reconciliation.populate_nested_party_data(
                    row=x,
                    party_mapping_data=party_mapping_data,
                    es_meta_key=es_meta_key,
                ),
                axis=1,
            )

    def _populate_one_to_one_instrument_mapping_fields(
        self,
        source_data: pd.DataFrame,
        target: pd.DataFrame,
    ):
        """
        Populate Instrument RTS22Transaction fields with a one to one mapping
        Example: FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.FinInstrmGnlAttrbts.FullNm
        -> instrumentDetails.instrument.instrumentFullName

        :param source_data: Pandas DataFrame with Report data
        :param target: Pandas DataFrame with RTS22Transaction fields
        """

        if source_data.empty:
            return

        one_to_one_map = (
            self.report_mechanism_reconciliation.get_instrument_one_to_one_map()
        )

        self.report_mechanism_reconciliation.generic_populate_one_to_one_mappings(
            one_to_one_map=one_to_one_map,
            source_data=source_data,
            target=target,
        )

    def _populate_compound_instrument_mapping_fields(
        self, source_data: pd.DataFrame, target: pd.DataFrame
    ):
        """
        Populate Compound Instrument fields which are mapped by a combination of fields
        Example : instrumentDetails.instrument.fxDerivatives.notionalCurrency2
        May be populated by
        FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.AsstClssSpcfcAttrbts.Intrst.OthrNtnlCcy
        OR
        FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.AsstClssSpcfcAttrbts.FX.OthrNtnlCcy

        :param source_data: Pandas DataFrame with Report data
        :param target: Pandas DataFrame with RTS22Transaction fields
        """

        if source_data.empty:
            return

        self.report_mechanism_reconciliation.populate_compound_instrument_mappings(
            source_data=source_data, target=target
        )

        # All instruments created on this task will be classified as "isCreatedThroughFallback" = True
        instrument_not_null_mask = target.loc[
            :, RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_INSTRUMENT_ID_CODE
        ].notnull()

        target.loc[
            instrument_not_null_mask,
            RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_IS_CREATED_THROUGH_FALLBACK,
        ] = True

    def _populate_underlying_instrument_mapping_fields(
        self, source_data: pd.DataFrame, target: pd.DataFrame
    ):
        """
        Populate Underlying Instrument RTS22Transaction fields. These fields require specific logic
        as it is possible to have multiple underlying instruments, therefore multiple repeating instances of the same
        fields, such as `FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.DerivInstrmAttrbts.UndrlygInstrm.Swp.SwpIn.Bskt.ISIN`

        :param source_data: Pandas DataFrame with Report data
        :param target: Pandas DataFrame with RTS22Transaction fields
        """

        if source_data.empty:
            return

        ext_underlying_instruments = pd.Series(
            data=[pd.NA] * source_data.shape[0], index=source_data.index
        )

        underlying_df = pd.DataFrame(index=source_data.index)

        # Underlying Instrument ID
        ul_instrument_id_report_columns = (
            self.report_mechanism_reconciliation.get_ul_instrument_id_report_columns()
        )

        underlying_df[
            RTS22Transaction.INSTRUMENT_ID_CODE
        ] = self.report_mechanism_reconciliation.parse_ul_instrument_report_series(
            report_columns=ul_instrument_id_report_columns,
            source_data=source_data,
            nested_ordered_dict_field="ISIN",
        )

        # Underlying Index ID
        ul_index_id_report_columns = (
            self.report_mechanism_reconciliation.get_ul_index_id_report_columns()
        )

        underlying_df[
            RTS22Transaction.EXT_UNDERLYING_INDEX_ID
        ] = self.report_mechanism_reconciliation.parse_ul_instrument_report_series(
            report_columns=ul_index_id_report_columns,
            source_data=source_data,
            nested_ordered_dict_field="ISIN",
        )

        # Underlying Index Name
        ul_index_name_report_columns = (
            self.report_mechanism_reconciliation.get_ul_index_name_report_columns()
        )

        ul_index_name_report_series = (
            self.report_mechanism_reconciliation.parse_ul_instrument_report_series(
                report_columns=ul_index_name_report_columns,
                source_data=source_data,
                nested_ordered_dict_field="Nm",
            )
        )

        ul_index_name_report_series_idx = (
            self.report_mechanism_reconciliation.parse_ul_instrument_report_series(
                report_columns=ul_index_name_report_columns,
                source_data=source_data,
                nested_ordered_dict_field="Indx",
            )
        )

        ul_index_name_report_series.update(ul_index_name_report_series_idx)
        underlying_df[
            RTS22Transaction.EXT_UNDERLYING_INDEX_NAME
        ] = ul_index_name_report_series

        df_not_null_mask = underlying_df.loc[:, :].notnull().any(1)
        if df_not_null_mask.any():
            ext_underlying_instruments.loc[df_not_null_mask] = underlying_df.loc[
                df_not_null_mask, :
            ].apply(lambda x: self._assemble_underlying_instruments(row=x), axis=1)

        target.loc[
            :,
            RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_EXT_UL_INSTRUMENTS,
        ] = ext_underlying_instruments

    @staticmethod
    def _assemble_underlying_instruments(row: pd.Series) -> List[dict]:
        """
        Example of input:
        pd.Series(data=[
        ["US4592001014", "US4592001111", "US4592002222", "US4592009999"],
        pd.NA,
        ["index_a", "index_b", "index_c", "index_d"]
        ],
        index=["instrumentIdCode", "ext.name", "ext.underlyingIndexId"])

        ASSUMPTION: If a given underlying field is populated, it always has a consistent size, meaning
        that if there are four underlying instruments with `instrumentIdCode` and `ext.underlyingIndexId` is populated
        we assume that `ext.underlyingIndexId` will also have four values, for each underlying instrument

        :param row: Pandas Series with underlying instrument data
        :return: List of dictionaries, where each dictionary represents an Underlying Instrument and its nested fields
        """
        result = []
        for name_value_pair in list(row.items()):
            col_name = name_value_pair[0]
            data = name_value_pair[1]
            if isinstance(data, list):
                for idx, value in enumerate(data):
                    if len(result) < len(data):
                        result.append({col_name: value})
                    else:
                        result[idx][col_name] = value

        return result
