from copy import deepcopy
from typing import Any
from typing import List
from typing import Optional
from typing import OrderedDict
from typing import Tuple

import numpy as np
import pandas as pd
from addict import Dict
from se_core_tasks.utils.encoding import md5_encode

from swarm_tasks.steeleye.tr.static import RecFields
from swarm_tasks.steeleye.tr.static import RecSources
from swarm_tasks.steeleye.tr.static import RTS22Transaction
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.enums import ReportTypeEnum
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.mappings import BOOL_MAP
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.mappings import (
    REPORT_TYPE_MAPPING,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.twr_dataclasses import (
    ReconcilationNestedPartyList,
)


class ReportMechanismReconciliation:
    def __init__(self, report_type: ReportTypeEnum):

        if report_type not in ReportTypeEnum:
            raise ValueError(f"{report_type} is not supported for 3-Way-Reconciliation")

        self.report_mechanism: ReportTypeEnum = report_type
        self.trading_datetime_field = self.get_trading_datetime_field()
        self.trading_datetime_format = self.get_trading_datetime_format()
        self.field_breaks_history = {}

    def compare_report_and_steeleye_fields(
        self,
        report_series: pd.Series,
        steeleye_series: pd.Series,
        target: pd.DataFrame,
        steeleye_field_names: pd.Series = None,
    ):
        """
        Compare data from the NCA_FCA/ARM_UNAVISTA report with data from SteelEye and update the reconciliation fields

        :param report_series: Series with data from the NCA_FCA/ARM_UNAVISTA report
        :param steeleye_series: Series with the associated data from SteelEye
        :param target: DataFrame which will be updated with the results of the reconciliation
        :param steeleye_field_names: Optional Series with the name of each SteelEye field that provided the data
        for each value in `steeleye_series`
        """

        if isinstance(steeleye_field_names, pd.Series):
            if steeleye_field_names.isnull().all():
                steeleye_field_names = None

        # Convert Bool values cast as string, to lowercase to match XML report data
        # Convert all values to string, except for lists
        not_null_mask = steeleye_series.notnull()
        if not_null_mask.any():
            steeleye_series.loc[not_null_mask] = steeleye_series.loc[not_null_mask].map(
                lambda x: str(x).lower()
                if str(x) in ["True", "False"]
                else str(x)
                if not isinstance(x, list)
                else x
            )

        comparison_frame = report_series.fillna(pd.NA).compare(
            steeleye_series.fillna(pd.NA)
        )

        if comparison_frame.empty:
            # Both Series are exactly the same, thus there is nothing else to map
            return

        # Build fieldBreaks from the result of the comparison
        # if we are reconciling a single RTS22 field, the field name (name of the steeleye_series Series) is enough
        # However, if we are reconciling data from multiple RTS22 fields at once, we need to pass the
        # `steeleye_field_names` parameter, which should be a Series the same size and indexes as steeleye_series
        # containing the name of the field which was reconciled
        # Example: The NCA_FCA field `FinInstrmRptgTxRpt.Tx.New.Sellr.AcctOwnr.Id` can populate
        # `parties.seller[].firmIdentifiers.lei` or `parties.seller[].officialIdentifiers.mifirId` fields for a
        # RTS22Transaction record and we need to know which field was used to populate each row on `steeleye_series`
        field_breaks = comparison_frame.apply(
            lambda x: [
                {
                    RecFields.FIELD_BREAKS_FIELD: steeleye_field_names.loc[x.name]
                    if isinstance(steeleye_field_names, pd.Series)
                    else steeleye_series.name,
                    RecFields.FIELD_BREAKS_VALUE: {
                        REPORT_TYPE_MAPPING[self.report_mechanism]: "NOT_POPULATED"
                        if not isinstance(x.self, list) and pd.isna(x.self)
                        else x.self,
                        RecSources.SE: "NOT_POPULATED"
                        if not isinstance(x.other, list) and pd.isna(x.other)
                        else x.other,
                    },
                }
            ],
            axis=1,
        ).to_frame()

        # Update preexisting (or not) fieldBreaks for each RTS22Transaction
        target.loc[field_breaks.index, RecFields.FIELD_BREAKS] = field_breaks.apply(
            lambda x: self._update_field_breaks(
                new_field_breaks=x, col_name=RecFields.FIELD_BREAKS, target=target
            ),
            axis=1,
        )

    def _update_field_breaks(
        self, new_field_breaks: pd.Series, col_name: str, target: pd.DataFrame
    ):
        """
        self.field_breaks_history will store all of the fields for which have been detected fieldBreaks
        for each transaction. It is necessary to store the fields in a class variable, because
        each execution of this method populates exactly one fieldBreak and tries to "clean"
        resolved fieldBreaks.

        Example: If this method was executed for a given transaction
        to populate fieldBreaks for "transactionDetails.venue" and "transactionDetails.price"
        what happens is that first, when populating the "transactionDetails.venue" fieldBreak, the
        "transactionDetails.price" fieldBreak will have its "NCA_FCA" or "ARM_UNAVISTA"
        value deleted (depends on `report_type` param) because that field is not yet
        in self.field_breaks_history. The second execution of this method for
        transaction 50 will populate the "transactionDetails.price" fieldBreak and it will NOT delete the
        "transactionDetails.venue" fieldBreak, because that one will be contained within the self.field_breaks_history.
        This will go on until all fieldBreaks are processed, thus any previous fieldBreaks for a given transaction,
        which were not identified in this flow's execution are resolved.

        :new_field_breaks: Pandas Series with newly detected field breaks
        :col_name: Name of the column for which we detected field breaks
        :target: Pandas DataFrame with the results of the reconciliation
        """

        target_index = new_field_breaks.name

        if target_index not in self.field_breaks_history:
            self.field_breaks_history[target_index] = []

        previous_field_breaks = target.loc[target_index, col_name]
        new_field_breaks_list = new_field_breaks[0]

        # if report_type == "nca", then only "fieldBreaks[].value.nca" values will be deleted
        # for previous fieldBreaks not identified in the current flow execution and vice-versa
        # TODO if we start supporting additional report types we will need to refactor this logic
        #  of handling other source report field breaks
        source_report = (
            RecSources.NCA
            if self.report_mechanism == ReportTypeEnum.NCA_FCA
            else RecSources.ARM
        )
        other_source_report = (
            RecSources.ARM
            if self.report_mechanism == ReportTypeEnum.NCA_FCA
            else RecSources.NCA
        )

        new_field_breaks_parsed_list = self._drop_case_sensitive_field_breaks(
            field_breaks=new_field_breaks_list
        )

        new_field_breaks_parsed_list = self._drop_datetime_sensitive_field_breaks(
            field_breaks=new_field_breaks_parsed_list
        )

        # No field breaks (neither new or previous)
        if not new_field_breaks_parsed_list and not isinstance(
            previous_field_breaks, list
        ):
            # The fieldBreak field is set to False because when then datetime break is found this is set to True
            # in the method _drop_datetime_sensitive_field_breaks(), the fieldBreak values are removed but
            # the indicator remains true . SO, it is explicitly set to False here
            sources = {
                RecSources.NCA: RecFields.RECONCILIATION_NCA_FIELD_BREAK,
                RecSources.ARM: RecFields.RECONCILIATION_ARM_FIELD_BREAK,
            }
            target.loc[target_index, sources[source_report]] = False
            return pd.NA

        # This transaction has new field breaks but it does not have previous fieldBreaks from reconciliation
        if not isinstance(previous_field_breaks, list):

            for new_field_break in new_field_breaks_list:
                if (
                    new_field_break[RecFields.FIELD_BREAKS_FIELD]
                    not in self.field_breaks_history[target_index]
                ):
                    self.field_breaks_history[target_index].append(
                        new_field_break[RecFields.FIELD_BREAKS_FIELD]
                    )

            return new_field_breaks_parsed_list

        for new_field_break in new_field_breaks_parsed_list:

            new_field = new_field_break[RecFields.FIELD_BREAKS_FIELD]
            new_value = new_field_break[RecFields.FIELD_BREAKS_VALUE]
            found = False

            # compare new fieldBreak with previous fieldBreaks to see if it is related to the same field
            # if it is, we must update the preexisting fieldBreak
            # if it is not, we can add the new fieldBreak
            for old_field_break in previous_field_breaks:

                if old_field_break[RecFields.FIELD_BREAKS_FIELD] == new_field:

                    found = True

                    if new_field not in self.field_breaks_history[target_index]:
                        self.field_breaks_history[target_index].append(new_field)

                    old_field_break[RecFields.FIELD_BREAKS_VALUE].update(new_value)
                    break

            if not found:

                previous_field_breaks.append(new_field_break)
                if new_field not in self.field_breaks_history[target_index]:
                    self.field_breaks_history[target_index].append(new_field)

        # Iterate through all fieldBreaks fields and delete the ones which are not stored in self.field_breaks_history
        # for this specific transaction
        non_valid_field_breaks_indexes = []
        for index, revised_field_break in enumerate(previous_field_breaks):

            if (
                revised_field_break[RecFields.FIELD_BREAKS_FIELD]
                not in self.field_breaks_history[target_index]
            ):

                # example: if "fieldBreaks[].value.nca" has a value for this fieldBreak, delete it
                if source_report in revised_field_break[RecFields.FIELD_BREAKS_VALUE]:
                    revised_field_break[RecFields.FIELD_BREAKS_VALUE].pop(source_report)

                # example: if "fieldBreaks[].value.arm" does not have a value for this fieldBreak,
                # it means that only the "fieldBreaks[].value.se" remains, which only made sense when associated
                # to a "nca" value, thus it must be deleted
                if (
                    other_source_report
                    not in revised_field_break[RecFields.FIELD_BREAKS_VALUE]
                    and RecSources.SE
                    in revised_field_break[RecFields.FIELD_BREAKS_VALUE]
                ):

                    revised_field_break[RecFields.FIELD_BREAKS_VALUE].pop(RecSources.SE)

                # If the fieldBreak does not have any values anymore, the whole fieldBreak must be deleted
                if not revised_field_break[RecFields.FIELD_BREAKS_VALUE]:

                    non_valid_field_breaks_indexes.append(index)

        # iterate the list backwards to delete items without worrying about reindexing
        for idx in sorted(non_valid_field_breaks_indexes, reverse=True):
            del previous_field_breaks[idx]

        return previous_field_breaks

    def run_generic_one_to_one_comparison(
        self,
        one_to_one_map: dict,
        data_to_reconcile: pd.DataFrame,
        target: pd.DataFrame,
    ):
        """
        Generic One to One Comparison Algorithm which performs the reconciliation by iterating over each key
        of the `one_to_one_map`

        :param one_to_one_map: Dictionary with the one to one mappings
        :param data_to_reconcile: Source DataFrame to be reconciled
        :param target: DataFrame which will be updated with the reconciliation results
        """

        for report_field_name in one_to_one_map.keys():

            steeleye_series = (
                data_to_reconcile.loc[:, one_to_one_map[report_field_name]]
                if one_to_one_map[report_field_name] in data_to_reconcile.columns
                else pd.Series(index=data_to_reconcile.index)
            )

            # we need this to be able to populate the field name that is not present in the RTS22Transaction
            if not steeleye_series.name:
                steeleye_series.name = one_to_one_map[report_field_name]

            report_series = (
                data_to_reconcile.loc[:, report_field_name]
                if report_field_name in data_to_reconcile.columns
                else pd.Series(index=data_to_reconcile.index)
            )

            # Since these fields are not to be reconciled if the report has no data for them, rows
            # where report_series is null are changed in steeleye_series to be null as well
            report_series_null_mask = report_series.isnull()
            steeleye_series.loc[report_series_null_mask] = report_series.loc[
                report_series_null_mask
            ]

            steeleye_series = self._remove_non_significant_zero(steeleye_series)

            self._sanitize_report_series(report_series=report_series)

            self.compare_report_and_steeleye_fields(
                report_series=report_series,
                steeleye_series=steeleye_series,
                target=target,
            )

    @staticmethod
    def _remove_non_significant_zero(steeleye_series: pd.Series) -> pd.Series:
        """
        Cast to string and remove non-significant zeros from float values, without converting null values to strings
        Floats are always converted to string, regardless of it having non-significant zeros
        Example: 1000.0 -> "1000"

        :param steeleye_series: Pandas Series with float values
        :return: Pandas Series as string without non-significant zeros
        """
        result = steeleye_series

        not_null_mask = steeleye_series.notnull()
        numeric_mask = steeleye_series.apply(
            lambda x: not isinstance(x, bool)
            and (isinstance(x, float) or isinstance(x, int))
        )

        # np.format_float_positional(x) is needed to avoid formatting the values in scientific notation
        # as values from ARM or NCA reports are never in scientific notation
        result.loc[not_null_mask & numeric_mask] = result.loc[numeric_mask].map(
            lambda x: str(np.format_float_positional(x)).rstrip(".")
        )

        return result

    @staticmethod
    def _sanitize_report_series(report_series: pd.Series):
        raise NotImplementedError

    def run_generic_compound_comparison(
        self,
        compound_map: dict,
        data_to_reconcile: pd.DataFrame,
        target: pd.DataFrame,
    ):
        """
        Generic Compound Comparison Algorithm which performs the reconciliation by iterating over each key
        of the `compound_map` and reiterating over each of the mapping combinations

        :param compound_map: Dictionary with the one to one mappings
        :param data_to_reconcile: Source DataFrame to be reconciled
        :param target: DataFrame which will be updated with the reconciliation results
        """

        for rts22_field_name in compound_map:

            steeleye_series = (
                data_to_reconcile.loc[:, rts22_field_name]
                if rts22_field_name in data_to_reconcile.columns
                else pd.Series(index=data_to_reconcile.index)
            )

            if not steeleye_series.name:
                steeleye_series.name = rts22_field_name

            report_mapped_column = self.populate_data_by_combinations(
                compound_map=compound_map,
                rts22_field_name=rts22_field_name,
                data_to_reconcile=data_to_reconcile,
            )

            # Since these fields are not to be reconciled if the report has no data for them, rows
            # where report_mapped_column is null are changed in steeleye_series to be null as well
            report_series_null_mask = report_mapped_column.isnull()
            steeleye_series.loc[report_series_null_mask] = report_mapped_column.loc[
                report_series_null_mask
            ]

            steeleye_series = self._remove_non_significant_zero(steeleye_series)

            self.compare_report_and_steeleye_fields(
                report_series=report_mapped_column,
                steeleye_series=steeleye_series,
                target=target,
            )

    @staticmethod
    def populate_data_by_combinations(
        compound_map: dict, rts22_field_name: str, data_to_reconcile: pd.DataFrame
    ) -> pd.Series:

        """
        Populate a Pandas Series from the combination of multiple Series. Each element of the Series
        (indexes match source data), will be populated from any of the associated list of report fields.

        :param compound_map: {RTS22_FIELD_NAME:[(REPORT_FIELD_NAME_1, Optional[STATIC_VALUE]_1), ...]}
        :param rts22_field_name: RTS22Transaction field to reconcile
        :param data_to_reconcile: Source DataFrame to be reconciled
        :return: Pandas Series with values from the possible combinations of `compound_map`
        """

        combinations = compound_map[rts22_field_name]
        report_mapped_column = pd.Series(data=pd.NA, index=data_to_reconcile.index)
        report_mapped_column.name = rts22_field_name

        for comb_tuple in combinations:

            match_col_name = comb_tuple[0]
            match_static_value = comb_tuple[1]

            if match_col_name in data_to_reconcile.columns:

                match_not_null_mask = data_to_reconcile.loc[:, match_col_name].notnull()

                if match_not_null_mask.any():
                    report_mapped_column[match_not_null_mask] = (
                        pd.Series(
                            data=match_static_value, index=match_not_null_mask.index
                        )
                        if match_static_value
                        else data_to_reconcile.loc[match_not_null_mask, match_col_name]
                    )

        return report_mapped_column

    def reconcile_list_party_fields(
        self, data_to_reconcile: pd.DataFrame, target: pd.DataFrame
    ):

        """
        Reconcile Party mapping fields which will populate nested sub-fields of list fields
        Example:
        The list parties.seller[] may contain multiple instances of the field "personalDetails.firstName"
        and this will populate `FinInstrmRptgTxRpt.Tx.New.Sellr.AcctOwnr.Id.Prsn.FrstNm`

        :param data_to_reconcile: Source Frame which will be reconciled
        :param target: DataFrame with reconciliation results
        """
        if data_to_reconcile.empty:
            return

        list_map = self.get_party_list_map()

        for report_field_name in list_map:

            rts22_col_name = list_map[report_field_name][0]
            rts22_nested_field = list_map[report_field_name][1]
            steeleye_mapped_column = pd.Series(
                data=pd.NA, index=data_to_reconcile.index
            )
            steeleye_field_names = pd.Series(
                data=f"{rts22_col_name}[].{rts22_nested_field}",
                index=data_to_reconcile.index,
            )

            # If the Party identifier list contains multiple instances,
            # such as multiple buyers, we will use the instance where we first find the field
            if rts22_col_name in data_to_reconcile.columns:

                not_null_mask = data_to_reconcile.loc[:, rts22_col_name].notnull()

                if not_null_mask.any():

                    steeleye_mapped_column[not_null_mask] = (
                        data_to_reconcile.loc[not_null_mask, rts22_col_name]
                        .to_frame()
                        .apply(
                            lambda x: self._get_party_data_from_model(
                                party_record=x,
                                nested_field=rts22_nested_field,
                                steeleye_field_names=steeleye_field_names,
                            ),
                            axis=1,
                        )
                    )

            report_series = (
                data_to_reconcile.loc[:, report_field_name]
                if report_field_name in data_to_reconcile.columns
                else pd.Series(index=data_to_reconcile.index)
            )

            steeleye_mapped_column = self._remove_non_significant_zero(
                steeleye_mapped_column
            )

            self._sanitize_report_series(report_series=report_series)

            self._add_buyer_and_sell_intl_alternative(
                report_field_name=report_field_name,
                report_series=report_series,
                data_to_reconcile=data_to_reconcile,
            )

            self.compare_report_and_steeleye_fields(
                report_series=report_series,
                steeleye_series=steeleye_mapped_column,
                target=target,
                steeleye_field_names=steeleye_field_names,
            )

    def get_party_list_map(self) -> List:
        return []

    @staticmethod
    def _get_party_data_from_model(
        party_record: pd.Series, nested_field: str, steeleye_field_names: pd.Series
    ) -> dict:
        """
        Iterate through all entries in a party instance (i.e parties.buyer) and retrieve the `nested_field` value,
        pd.NA if non-existent

        :param party_record: Pandas Series with List of party instances
        :param nested_field: Nested dot separated field to search in the party record (i.e `personalDetails.firstName`)
        :return: Value of the nested field if it exists, pd.NA otherwise
        """
        party_entity_list = party_record.iloc[0]

        for index, party_entity in enumerate(party_entity_list):

            nested_field_list = nested_field.split(".")
            data = deepcopy(party_entity)

            for nested_field in nested_field_list:

                data = data.get(nested_field, {})

            if data:
                steeleye_field_names.loc[party_record.name] = steeleye_field_names.loc[
                    party_record.name
                ].replace("[]", f"[{index}]")
                return data

        return pd.NA

    def _add_buyer_and_sell_intl_alternative(
        self, report_field_name, report_series, data_to_reconcile
    ):
        raise NotImplementedError

    def reconcile_list_party_fields_with_model_variations(
        self,
        data_to_reconcile: pd.DataFrame,
        es,
        target: pd.DataFrame,
    ):
        """
        Reconcile Party mapping fields which will populate nested sub-fields of list fields
        but the actual field depends on the Model of the Party
        Example:
        The list parties.seller[] may contain multiple instances of the field "firmIdentifiers.branchCountry"
        if the parties.Seller is a MarketCounterparty or AccountFirm, ""officialIdentifiers.branchCountry" otherwise
        and this will populate `FinInstrmRptgTxRpt.Tx.New.Sellr.AcctOwnr.CtryOfBrnch`

        :param data_to_reconcile: Source Frame which will be reconciled
        :param target: DataFrame with reconciliation results
        :param es: ElasticSearchClient
        """
        # Data Structure #
        # {NCA_REPORT_FIELD_NAME:
        #   {RTS22_FIELD_NAME_WHICH_CONTAINS_A_LIST:
        #       (
        #           {
        #               PARTY_MODEL_1: ASSOCIATED_RTS22_NESTED_FIELD_1,
        #               PARTY_MODEL_X: ASSOCIATED_RTS22_NESTED_FIELD_X
        #           },
        #           DEFAULT_RTS22_NESTED_FIELD_IF_PARTIES_DO_NOT_MATCH_ANY_OF_`PARTY_MODEL`
        #       )
        #   }
        # }
        list_conditional_map = self.get_party_conditional_list_map()
        es_meta_key = es.meta.key

        for report_field_name in list_conditional_map:

            steeleye_mapped_column = pd.Series(
                data=pd.NA, index=data_to_reconcile.index
            )
            rts22_col_name = list(list_conditional_map[report_field_name].keys())[0]

            # Ideally we want the `field` of the field break to be the exact field
            # but that is not always possible, example:
            # "Document.FinInstrmRptgTxRpt.Tx.New.Sellr.AcctOwnr.CtryOfBrnch" can populate
            # either "parties.buyerDecisionMaker.firmIdentifiers.lei" OR
            # parties.buyer.buyerDecisionMaker.officialIdentifiers.mifirId
            # However, if "parties.buyer" is not populated we have to use this parent field in the fieldBreak
            steeleye_field_names = pd.Series(
                data=f"{rts22_col_name}|{report_field_name}",
                index=data_to_reconcile.index,
            )

            if rts22_col_name in data_to_reconcile.columns:

                not_null_mask = data_to_reconcile.loc[:, rts22_col_name].notnull()

                if not_null_mask.any():
                    fields_by_model = list(
                        list_conditional_map[report_field_name].values()
                    )[0][0]
                    value_to_match_if_model_does_not_match = list(
                        list_conditional_map[report_field_name].values()
                    )[0][1]

                    steeleye_mapped_column[not_null_mask] = (
                        data_to_reconcile.loc[not_null_mask, rts22_col_name]
                        .to_frame()
                        .apply(
                            lambda x: self._get_conditional_party_data_from_model(
                                party_record=x,
                                nested_fields_by_model=fields_by_model,
                                value_to_match_if_model_does_not_match=value_to_match_if_model_does_not_match,
                                meta_key=es_meta_key,
                                steeleye_field_names=steeleye_field_names,
                                rts22_col_name=rts22_col_name,
                            ),
                            axis=1,
                        )
                    )

            report_series = (
                data_to_reconcile.loc[:, report_field_name]
                if report_field_name in data_to_reconcile.columns
                else pd.Series(index=data_to_reconcile.index)
            )

            steeleye_mapped_column = self._remove_non_significant_zero(
                steeleye_series=steeleye_mapped_column
            )

            self._sanitize_report_series(report_series=report_series)

            self.compare_report_and_steeleye_fields(
                report_series=report_series,
                steeleye_series=steeleye_mapped_column,
                target=target,
                steeleye_field_names=steeleye_field_names,
            )

    @staticmethod
    def get_party_conditional_list_map() -> List:
        raise NotImplementedError

    @staticmethod
    def _get_conditional_party_data_from_model(
        party_record: pd.Series,
        nested_fields_by_model: dict,
        value_to_match_if_model_does_not_match: str,
        meta_key: str,
        steeleye_field_names: pd.Series,
        rts22_col_name: str,
    ) -> Any:
        """
        Iterate through all entries in a party instance (i.e parties.buyer), check the &model of the Party instance,
        and retrieve the associated nested field value as provided by `nested_fields_by_model`, if there are no
        compatible &model Party instances, use default `value_to_match_if_model_does_not_match` to name
        the field which has a fieldBreak. If no value was retrieved from the nested fields, pd.NA is returned

        :param party_record: Series with a list of party instances
        :param nested_fields_by_model: i.e {"AccountFirm": "personalDetails.dateOfBirth"}
        :param value_to_match_if_model_does_not_match: Default field name to avoid
        null or misleading "field" values on fieldBreaks
        :param meta_key: Meta key of this specific tenant (differs between ES5 and ES7)
        :param steeleye_field_names: Pandas Series which contains the name of the field that was searched,
        to populate the fieldBreaks "field" values
        (which specify which RTS22Transaction field actually has the fieldBreak)
        :param rts22_col_name: Name of the RTS22Transaction field
        :return: Value of the nested field if it exists, pd.NA otherwise
        """

        field_to_match = value_to_match_if_model_does_not_match
        for index, party_entity in enumerate(party_record.iloc[0]):

            party_meta_key = party_entity.get(meta_key)
            model = party_meta_key.split(":")[0]

            field_to_match = (
                nested_fields_by_model[model]
                if model in nested_fields_by_model
                else field_to_match
            )
            nested_field_list = field_to_match.split(".")
            data = deepcopy(party_entity)

            for nested_field in nested_field_list:

                data = data.get(nested_field, {})

            if data:
                steeleye_field_names.loc[party_record.name] = (
                    f"{rts22_col_name}[{index}]." + field_to_match
                )
                return data

        # if the the expected RTS22 field is not in this record, we must populate a field name
        steeleye_field_names.loc[party_record.name] = (
            f"{rts22_col_name}[]." + field_to_match
        )

        return pd.NA

    def reconcile_custom_general_fields(
        self,
        data_to_reconcile: pd.DataFrame,
        target: pd.DataFrame,
        tenant_configuration: Dict,
    ):
        """
        Reconcile very specific mappings which require a more tailored approach:

        1) NCA_FCA and ARM_UNAVISTA
        FinInstrmRptgTxRpt.Tx.New.Tx.Pric.NoPric.Pdg - (PNDG|NOAP)
        If the XML path is NOT populated, then this should map to OUR schema values in BOTH
            transactionDetails.pricePending
            transactionDetails.priceNotApplicable
        as “false” (i.e., this is the boolean value equivalent to a NULL xml Path).
        Similarly;
            - if the FinInstrmRptgTxRpt.Tx.New.Tx.Pric.NoPric.Pdg = "PNDG”, then this should map to our
            schema value in transactionDetails.pricePending of “true”
            - if the FinInstrmRptgTxRpt.Tx.New.Tx.Pric.NoPric.Pdg = "NOAP”, then this should map to our
             schema value in transactionDetails.priceNotApplicable of “true”

        2) NCA_FCA and ARM_UNAVISTA
        tradersAlgosWaiversIndicators.waiverIndicator[] - RTS22
        FinInstrmRptgTxRpt.Tx.New.AddtlAttrbts.WvrInd - This XML field may or may not contain a list of values
        and it must always populate a list of values on the RTS22 field

        3) NCA_FCA and ARM_UNAVISTA
        tradersAlgosWaiversIndicators.otcPostTradeIndicator[] - RTS22
        FinInstrmRptgTxRpt.Tx.New.AddtlAttrbts.OTCPstTradInd - This XML field may or may not contain a list of values
        and it must always populate a list of values on the RTS22 field

        4) ARM_UNAVISTA
        transactionDetails.tradingDateTime
        UnaVista `Trading Date Time` column is formatted as "%Y-%m-%d %H:%M:%S.%f" which must be converted
        to "%Y-%m-%dT%H:%M:%S.%fZ" to reconcile with SteelEye transactions

        5) ARM_UNAVISTA
        transactionDetails.priceNotation
        UnaVista "Price Type" column may be populated with "MntryValAmt", "Pctg", "Yld" or "BsisPts".
        These values must be mapped to match SteelEye enums ['MONE', 'PERC', 'YIEL', 'BAPO']

        6) ARM_UNAVISTA
        UnaVista `Underlying Index Term` will be used to populate the following columns:
        - instrumentDetails.instrument.derivative.underlyingIndexTermValue - Keep numeric characters only
        - instrumentDetails.instrument.derivative.underlyingIndexTerm - Keep alphabetical letters only

        7) ARM_UNAVISTA
        Unavista `Strike Price Type` value is transformed to match the value in SteelEye platform

        :param data_to_reconcile: Source Frame which will be reconciled
        :param target: DataFrame with reconciliation results
        :param tenant_configuration: Tenant Configuration dict
        """

        custom_mapping_result = {}

        # transactionDetails.pricePending
        # transactionDetails.priceNotApplicable
        price_pending_report_field = self.get_price_pending_report_field()
        price_pending_report_dictionary = self.get_price_pending_report_dictionary()

        custom_mapping_result.update(
            self.parse_price_pending(
                price_pending_report_field=price_pending_report_field,
                price_pending_report_dictionary=price_pending_report_dictionary,
                source_data=data_to_reconcile,
            )
        )

        # tradersAlgosWaiversIndicators.waiverIndicator[]
        source_waiver_column = self.get_source_waiver_column()
        target_waiver_column = (
            RTS22Transaction.TRADERS_ALGOS_WAIVERS_INDICATORS_WAIVER_INDICATOR
        )

        custom_mapping_result.update(
            self.parse_one_to_list_mapping(
                source_data=data_to_reconcile,
                source_column=source_waiver_column,
                target_column=target_waiver_column,
            )
        )

        # tradersAlgosWaiversIndicators.otcPostTradeIndicator[]
        source_otc_column = self.get_source_otc_column()
        target_otc_column = (
            RTS22Transaction.TRADERS_ALGOS_WAIVERS_INDICATORS_OTC_POST_TRADE_IND
        )

        custom_mapping_result.update(
            self.parse_one_to_list_mapping(
                source_data=data_to_reconcile,
                source_column=source_otc_column,
                target_column=target_otc_column,
            )
        )

        custom_mapping_result[
            RTS22Transaction.PARTIES_EXECUTION_WITHIN_FIRM_OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY
        ] = self.parse_execution_within_firm_country_branch(
            source_data=data_to_reconcile, tenant_configuration=tenant_configuration
        )

        custom_mapping_result[
            RTS22Transaction.PARTIES_INV_DEC_WITHIN_FIRM_OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY
        ] = self.parse_investment_decision_within_firm_country_branch(
            source_data=data_to_reconcile, tenant_configuration=tenant_configuration
        )

        self.parse_additional_custom_fields(
            source_data=data_to_reconcile, custom_mapping_result=custom_mapping_result
        )

        for rts22_field_name in custom_mapping_result:

            if (
                rts22_field_name
                in [
                    RTS22Transaction.PARTIES_EXECUTION_WITHIN_FIRM_OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY,
                    RTS22Transaction.PARTIES_INV_DEC_WITHIN_FIRM_OFFICIAL_IDENTIFIERS_BRANCH_COUNTRY,
                ]
                and tenant_configuration.trPIEnrichmentEnabled is True
            ):
                continue

            steeleye_series = (
                data_to_reconcile.loc[:, rts22_field_name]
                if rts22_field_name in data_to_reconcile.columns
                else pd.Series(index=data_to_reconcile.index)
            )

            # Specific for "transactionDetails.pricePending|priceNotApplicable"
            # These values being populated with False, or not being populated should be considered equivalent
            if rts22_field_name in [
                RTS22Transaction.TRANSACTION_DETAILS_PRICE_PENDING,
                RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOT_APPLICABLE,
            ]:
                steeleye_series = steeleye_series.fillna("false")

            if not steeleye_series.name:
                steeleye_series.name = rts22_field_name

            self._sanitize_report_series(custom_mapping_result[rts22_field_name])

            # [EU-9364] If instrumentDetails.instrument.ext.strikePriceType is null in ARM extract then we should not
            # reconcile this field i.e. steeleye_series and custom_mapping_result[rts22_field_name] should be treated
            # as the same
            if (
                rts22_field_name
                in RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_EXT_STRIKE_PRICE_TYPE
            ):
                self.validate_strike_price_type(
                    custom_mapping_result_series=custom_mapping_result[
                        rts22_field_name
                    ],
                    steeleye_series=steeleye_series,
                )

            self.compare_report_and_steeleye_fields(
                report_series=custom_mapping_result[rts22_field_name],
                steeleye_series=steeleye_series,
                target=target,
            )

    @staticmethod
    def validate_strike_price_type(
        custom_mapping_result_series: pd.Series, steeleye_series: pd.Series
    ):
        raise NotImplementedError

    @staticmethod
    def get_price_pending_report_field():
        raise NotImplementedError

    @staticmethod
    def get_price_pending_report_dictionary():
        raise NotImplementedError

    @staticmethod
    def parse_price_pending(
        price_pending_report_field: str,
        price_pending_report_dictionary: dict,
        source_data: pd.DataFrame,
    ) -> dict:
        """
        Return a dictionary with the Pandas Series with data for populating
        `transactionDetails.pricePending` and `transactionDetails.priceNotApplicable`

        :param price_pending_report_field: Name of the report field which will contain data to determine
            `transactionDetails.pricePending` and `transactionDetails.priceNotApplicable`
        :param price_pending_report_dictionary: Dictionary containing the mapping for each report field
        :param source_data: Pandas DataFrame with data to be reconciled
        :return: Dictionary containing a Pandas Series with report data for both mapping fields
        """
        result = {}

        for rts22_field_name in price_pending_report_dictionary:

            price_pending_status = price_pending_report_dictionary[rts22_field_name]
            # must be "false" and not the False bool as all report values are read as string
            mapped_report_column = pd.Series(data="false", index=source_data.index)

            if price_pending_report_field in source_data.columns:
                price_pending_mask = (
                    source_data.loc[:, price_pending_report_field]
                    == price_pending_status
                )
                mapped_report_column.loc[price_pending_mask] = "true"

            result[rts22_field_name] = mapped_report_column

        return result

    @staticmethod
    def get_source_waiver_column() -> str:
        return ""

    @staticmethod
    def parse_one_to_list_mapping(
        source_data: pd.DataFrame,
        source_column: str,
        target_column: str,
        delimiter=",",
    ) -> dict:

        return {}

    @staticmethod
    def get_source_otc_column():
        return ""

    def parse_additional_custom_fields(
        self, source_data: pd.DataFrame, custom_mapping_result: dict
    ):
        return NotImplementedError

    @staticmethod
    def _parse_ordered_dict_nested_fields(
        row: pd.Series, nested_ordered_dict_field: str
    ) -> List[str]:
        """
        Build List of unique Underlying Instrument fields to perform reconciliation

        :param row: Pandas Series with data from a NCA_FCA XML report underlying instrument field
        :param nested_ordered_dict_field: Name of the field that contains the relevant data
            within the nested ordered dicts
        :return: Sorted List of unique underlying instrument fields
        """

        result = []

        for value in list(row.values):

            if pd.isna(value):
                continue

            # Only one u/l instrument, value example -> "US4592001111"
            if not value.startswith("[") and not value.endswith("]"):
                result.append(value)
                continue

            # Multiple u/l instruments, value example ->
            # "[OrderedDict([('ISIN', 'US4592001111')]), OrderedDict([('ISIN', 'US4592002222')])]"
            ordered_dicts: Tuple[OrderedDict] = eval(
                value[1:-1]
            )  # ignore square brackets

            for ord_dict in ordered_dicts:
                nested_value = ord_dict.get(nested_ordered_dict_field)
                if nested_value:
                    result.append(nested_value)

        return result

    def parse_underlying_instrument_steeleye_series(
        self,
        steeleye_root_instrument_field: str,
        steeleye_nested_instrument_field: Optional[str],
        source_data: pd.DataFrame,
    ) -> pd.Series:
        """
        Build SE Series with underlying instrument data to perform reconciliation

        :param steeleye_root_instrument_field: Root instrument field name (i.e `instrumentDetails.instrument.ext`)
        :param steeleye_nested_instrument_field: Nested instrument field name (i.e `priceNotation`)
        :param source_data: Pandas DataFrame with data to be reconciled
        :return: Pandas Series with data from SE instruments
        """

        steeleye_series = pd.Series(data=pd.NA, index=source_data.index)
        steeleye_series.name = (
            f"{steeleye_root_instrument_field}[].{steeleye_nested_instrument_field}"
            if steeleye_nested_instrument_field
            else steeleye_root_instrument_field
        )

        if steeleye_root_instrument_field in source_data.columns:

            steeleye_data = source_data.loc[:, steeleye_root_instrument_field]
            steeleye_data_not_null_mask = steeleye_data.loc[:].notnull()

            if steeleye_data_not_null_mask.any():

                if (
                    steeleye_nested_instrument_field
                    and "." in steeleye_nested_instrument_field
                ):

                    ext_or_derivative = steeleye_nested_instrument_field.split(".")[0]
                    nested_ext_or_derivative_field = (
                        steeleye_nested_instrument_field.split(".")[1]
                    )

                    steeleye_series.loc[
                        steeleye_data_not_null_mask
                    ] = steeleye_data.loc[steeleye_data_not_null_mask].apply(
                        lambda x: self.convert_empty_set_to_pd_na(
                            sorted(
                                set(
                                    [
                                        ul_instrument_dict.get(
                                            ext_or_derivative, {}
                                        ).get(nested_ext_or_derivative_field)
                                        for ul_instrument_dict in x
                                        if ext_or_derivative in ul_instrument_dict
                                        and ul_instrument_dict.get(
                                            ext_or_derivative, {}
                                        ).get(nested_ext_or_derivative_field)
                                        is not None
                                    ]
                                )
                            )
                        )
                    )

                elif steeleye_nested_instrument_field:
                    steeleye_series.loc[
                        steeleye_data_not_null_mask
                    ] = steeleye_data.loc[steeleye_data_not_null_mask].apply(
                        lambda x: self.convert_empty_set_to_pd_na(
                            sorted(
                                set(
                                    [
                                        ul_instrument_dict.get(
                                            steeleye_nested_instrument_field
                                        )
                                        for ul_instrument_dict in x
                                        if steeleye_nested_instrument_field
                                        in ul_instrument_dict
                                        and ul_instrument_dict.get(
                                            steeleye_nested_instrument_field
                                        )
                                        is not None
                                    ]
                                )
                            )
                        )
                    )

                else:

                    steeleye_series.loc[
                        steeleye_data_not_null_mask
                    ] = steeleye_data.loc[steeleye_data_not_null_mask].apply(
                        lambda x: self.convert_empty_set_to_pd_na(sorted(set(x)))
                    )

        return steeleye_series

    @classmethod
    def parse_underlying_instrument_with_static_values_report_series(
        cls,
        report_columns_mappings: dict,
        source_data: pd.DataFrame,
    ) -> pd.Series:
        """
        Build Pandas Series with static values inferred from report data for underlying instruments

        :param report_columns_mappings: {REPORT_FIELD_NAME: STATIC_VALUE_TO_USE_IF_VALUE_IS_POPULATED, ...}
        :param source_data: Pandas DataFrame to be reconciled
        :return: Pandas Series with static values inferred from report data
        """
        report_cols_mask = source_data.columns.isin(
            list(report_columns_mappings.keys())
        )
        report_df = source_data.loc[:, report_cols_mask]
        report_series = pd.Series(data=pd.NA, index=source_data.index)

        # select only rows which have at least one column populated
        report_df_not_null_mask = report_df.loc[:, :].notnull().any(1)

        if report_df_not_null_mask.any():

            report_series.loc[report_df_not_null_mask] = report_df.loc[
                report_df_not_null_mask, :
            ].apply(
                lambda x: cls.convert_empty_set_to_pd_na(
                    sorted(
                        set(
                            [
                                report_columns_mappings[report_field_name]
                                for report_field_name, report_value in list(x.items())
                                if not pd.isna(report_value)
                            ]
                        )
                    )
                ),
                axis=1,
            )

        return report_series

    def _drop_datetime_sensitive_field_breaks(self, field_breaks: List) -> List:
        """
        Returns a list of field breaks without datetime sensitive breaks
        Example: "2021-06-30T00:02:01Z" and "2021-06-30T00:02:01.000000Z" represent the same datetime value
        but are different strings. These should not be included as field breaks

        :param field_breaks: List of field breaks
        :return: List of field breaks without datetime sensitive breaks
        """

        field_breaks_datetime_insensitive_list = []

        for fb in field_breaks:

            value = fb[RecFields.FIELD_BREAKS_VALUE]

            # Convert the report and SteelEye values to datetime and compare them
            try:
                report_value = value.get(REPORT_TYPE_MAPPING[self.report_mechanism])
                se_value = value.get(RecSources.SE)
                report_value_as_datetime = pd.to_datetime(report_value)
                se_value_as_datetime = pd.to_datetime(se_value)

                # If the string values as datetime represent the same datetime, they do not count as a field break
                if report_value_as_datetime.replace(
                    tzinfo=None
                ) != se_value_as_datetime.replace(tzinfo=None):
                    field_breaks_datetime_insensitive_list.append(fb)
            # If the strings cannot be converted to datetime we do not perform datetime comparison and maintain
            # the field break
            except ValueError:
                field_breaks_datetime_insensitive_list.append(fb)

        return field_breaks_datetime_insensitive_list

    def _drop_case_sensitive_field_breaks(self, field_breaks: List) -> List:
        """
        Returns a list of field breaks without case sensitive breaks

        :param field_breaks: List of field breaks
        :return: List of field breaks without case sensitive breaks
        """

        field_breaks_case_insensitive_list = []

        for fb in field_breaks:

            new_value = fb[RecFields.FIELD_BREAKS_VALUE]

            # Field breaks which differ only due to case sensitivity should not be included
            if not (
                not isinstance(
                    new_value.get(REPORT_TYPE_MAPPING[self.report_mechanism]), list
                )
                and not isinstance(new_value.get(RecSources.SE), list)
                and new_value.get(
                    REPORT_TYPE_MAPPING[self.report_mechanism], ""
                ).lower()
                == new_value.get(RecSources.SE, "").lower()
            ):
                field_breaks_case_insensitive_list.append(fb)

        return field_breaks_case_insensitive_list

    def preprocess_data(
        self, source_frame: pd.DataFrame, target: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Populate workflow and reconciliation RTS22Transaction fields

        :param source_frame: Pandas DataFrame with report data
        :param target: Pandas DataFrame with RTS22Transaction fields
        :return: Pandas DataFrame with workflow and reconciliation results
        """

        # these fields will contain the Reconciliation results
        target.loc[:, RecFields.RECONCILIATION_SE_MATCH] = False
        target.loc[:, RecFields.RECONCILIATION_SE_SOURCE] = pd.Series(
            data=[[REPORT_TYPE_MAPPING[self.report_mechanism.value]]] * target.shape[0],
            index=target.index,
        )

        target.loc[:, RecFields.WORKFLOW_IS_REPORTED] = True
        target.loc[:, RecFields.IS_CREATED_THROUGH_RECONCILIATION_FLAG] = True
        target.loc[
            :, RecFields.IS_CREATED_THROUGH_RECONCILIATION_SOURCE
        ] = REPORT_TYPE_MAPPING[self.report_mechanism.value]
        target.loc[
            :,
            RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_IS_CREATED_THROUGH_FALLBACK,
        ] = pd.NA

        self.build_preprocess_data(source_frame=source_frame, target=target)

        return target

    def build_preprocess_data(self, source_frame: pd.DataFrame, target: pd.DataFrame):

        raise NotImplementedError

    @staticmethod
    def generic_populate_one_to_one_mappings(
        one_to_one_map: dict,
        source_data: pd.DataFrame,
        target: pd.DataFrame,
    ):
        """
        Generic method to populate RTS22Transaction fields from one to one mappings
        Assumptions:
        - All source_data columns are strings
        - Strings that represent booleans are in lowercase (i.e "false")

        :param one_to_one_map: One to One mappings
        :param source_data: Pandas DataFrame with Report data
        :param target: Pandas DataFrame with RTS22Transaction fields
        """

        for report_field_name in one_to_one_map.keys():

            data = pd.Series(index=source_data.index)

            if report_field_name in source_data.columns:

                not_null_mask = source_data.loc[:, report_field_name].notnull()
                if not_null_mask.any():
                    data.loc[not_null_mask] = source_data.loc[
                        not_null_mask, report_field_name
                    ].replace(BOOL_MAP)

                target.loc[:, one_to_one_map[report_field_name]] = data

    def _generic_populate_compound_mappings(
        self,
        compound_map: dict,
        source_data: pd.DataFrame,
        target: pd.DataFrame,
    ):
        """
        Generic method to populate RTS22Transaction fields from compound mappings

        :param compound_map: Compound mappings
        :param source_data: Pandas DataFrame with Report data
        :param target: Pandas DataFrame with RTS22Transaction fields
        """

        for rts22_field_name in compound_map:

            report_mapped_column = self.populate_data_by_combinations(
                compound_map=compound_map,
                rts22_field_name=rts22_field_name,
                data_to_reconcile=source_data,
            )

            not_null_mask = report_mapped_column.notnull()
            if not_null_mask.any():
                report_mapped_column.loc[not_null_mask] = report_mapped_column.loc[
                    not_null_mask
                ].replace(BOOL_MAP)

            target.loc[:, rts22_field_name] = report_mapped_column

    def populate_nested_party_data(
        self,
        row: pd.Series,
        party_mapping_data: ReconcilationNestedPartyList,
        es_meta_key: str,
    ):
        """
        Populate `parties.buyer`, `parties.seller`, `parties.buyerDecisionMaker` or `parties.sellerDecisionMaker`
        nested fields

        :param row: Pandas Series with all report fields
        :param party_mapping_data: ReconcilationNestedPartyList dataclass with mappings
        :param es_meta_key: ElasticSearch meta key string
        """

        result = {}
        reconciliation_fields_dataclasses = party_mapping_data.reconciliation_fields

        for rec_field_data in reconciliation_fields_dataclasses:
            result[rec_field_data.rts22_party_nested_field] = (
                row.loc[rec_field_data.report_field]
                if rec_field_data.report_field in row.index
                else pd.NA
            )

        # If `firmIdentifiers.lei` was not populated from the XML "AcctOwnr.Id.LEI" field,
        # use the `AcctOwner.Id.Intl` field instead (if populated)
        self._populate_firm_identifiers_lei(
            row=row, party_mapping_data=party_mapping_data, mapping_result=result
        )

        branch_country = (
            row.loc[party_mapping_data.report_country_branch_field]
            if party_mapping_data.report_country_branch_field
            and party_mapping_data.report_country_branch_field in row.index
            else pd.NA
        )

        valid_lei = self._populate_branch_country(
            mapping_result=result, branch_country=branch_country
        )

        result_non_null_values = [
            val for val in list(result.values()) if not pd.isna(val)
        ]
        content_concat = "".join(sorted(result_non_null_values))

        model_prefix = self._populate_model_prefix(
            mapping_result=result, valid_lei=valid_lei
        )

        result[es_meta_key] = (
            f"{model_prefix}:{md5_encode(content_concat)}" if content_concat else pd.NA
        )
        result_any_non_null_values = any(
            set([not pd.isna(val) for val in result.values()])
        )
        if result_any_non_null_values:
            return [result]
        return pd.NA

    def _populate_firm_identifiers_lei(
        self,
        row: pd.Series,
        party_mapping_data: ReconcilationNestedPartyList,
        mapping_result: dict,
    ):
        raise NotImplementedError

    def _populate_branch_country(
        self, mapping_result: dict, branch_country: Optional[str]
    ) -> bool:
        return False

    @staticmethod
    def _populate_model_prefix(mapping_result: dict, valid_lei: bool) -> str:
        raise NotImplementedError

    @staticmethod
    def get_trading_datetime_field() -> str:
        raise NotImplementedError

    @staticmethod
    def get_trading_datetime_format() -> str:
        raise NotImplementedError

    @staticmethod
    def convert_empty_set_to_pd_na(sett: Any):
        return sett if len(sett) > 0 else pd.NA

    def parse_execution_within_firm_country_branch(
        self,
        source_data: pd.DataFrame,
        tenant_configuration: Dict,
    ) -> pd.Series:
        raise NotImplementedError

    def parse_investment_decision_within_firm_country_branch(
        self,
        source_data: pd.DataFrame,
        tenant_configuration: Dict,
    ) -> pd.Series:
        raise NotImplementedError
