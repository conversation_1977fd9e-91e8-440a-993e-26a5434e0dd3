from typing import Any
from typing import Dict as typing_dict
from typing import List
from typing import Op<PERSON>
from typing import Union

import pandas as pd
import prefect
from addict import Dict
from pandas._libs.missing import NAType
from pydantic import Field
from se_elastic_schema.static.mifid2 import PriceNotation
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.steeleye.tr.static import RecFields
from swarm_tasks.steeleye.tr.static import RecSources
from swarm_tasks.steeleye.tr.static import RTS22Transaction
from swarm_tasks.steeleye.tr.static import StaticValues
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.enums import ReportTypeEnum
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.mappings import (
    REPORT_TYPE_MAPPING,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.report_mechanism_mappings import (
    REPORT_MECHANISM_MAP,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.twr_dataclasses import (
    UnderlyingInstrumentComparison,
)
from swarm_tasks.utilities.decimal import enforce_significant_figures


class Params(BaseParams):
    report_type: ReportTypeEnum = Field(
        ...,
        description="ReportTypeEnum enumerator to control the task logic flow "
        "for either ARM_UNAVISTA or NCA_FCA reports",
    )


class Resources(BaseResources):
    es_client_key: str


class ReconcileReportTransactions(TransformBaseTask):
    """
    Mapping instructions here:
    https://steeleye.atlassian.net/wiki/spaces/IN/pages/1597472769/Mapping+-+RTS22+-+Three-Way-Reconcilliation

    - Three-Way-Reconciliation task that expects a `source_frame` Pandas DataFrame where each row represents
    a transaction from the ARM_UNAVISTA/NCA_FCA report which was found
    in the tenant's SteelEye RTS22Transaction ES index and
    the columns set includes all of the parsed NCA_FCA/ARM_UNAVISTA fields
     + all of the relevant RTS22Transaction fields.

    - This tasks performs the reconciliation of the ARM_UNAVISTA/NCA_FCA
    report fields against the RTS22Transaction fields for
    each row of the `source_frame`. ARM_UNAVISTA vs NCA_FCA is defined
    by the `report_type` param, thus this task is executed
    from both the `three-way-rec-nca` and `three-way-rec-arm` flows, with a different `source_frame` and params.

    - This algorithm will perform the reconciliation and output a `target` dataframe which will include the necessary
    fields to update each RTS22Transaction in ElasticSearch, such as &id and &key, as well as all of the reconciliation
    fields populated throughout the task.

    - Note that this algorithm will take into consideration if reconciliation was already executed to the input
    transactions, and it will remove every preexisting fieldBreak which the task does not manage to replicate.
    The rationale behind this design is that a user may run reconciliation against a NCA_FCA/ARM_UNAVISTA report,
    thus identifying a set of fieldBreaks, then resolve those fieldBreaks by updating the preexisting RTS22Transactions,
    and then proceed to run reconciliation a second time to confirm that there are no fieldBreaks anymore.
    """

    params_class = Params
    report_mechanism_reconciliation = None
    resources_class = Resources

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Optional[Params] = None,
        resources: Optional[Resources] = None,
        **kwargs,
    ) -> pd.DataFrame:

        target = pd.DataFrame(index=source_frame.index)
        self.report_mechanism_reconciliation = REPORT_MECHANISM_MAP[
            params.report_type
        ]()

        if source_frame.empty:
            self.logger.warning("There are no transactions to reconcile")
            return target

        es = self.clients.get(resources.es_client_key)

        target = self._preprocess_data(source_frame=source_frame, target=target, es=es)

        target = self._reconcile_data(
            data_to_reconcile=source_frame, target=target, es=es
        )

        target = self.validate_bool_report_type_field_break(
            target=target, report_type=params.report_type
        )

        self.logger.info(
            f"Successfully reconciled {target.shape[0]} transactions from the "
            f"{REPORT_TYPE_MAPPING[params.report_type.value]} report"
        )
        # Setting the pd.NA values to empty list so that it can over-write
        # existing field-breaks from previous runs.
        target.loc[:, RecFields.FIELD_BREAKS] = target.loc[
            :, RecFields.FIELD_BREAKS
        ].apply(lambda x: [] if not (isinstance(x, list)) else x)

        return target

    def _preprocess_data(
        self, source_frame: pd.DataFrame, target: pd.DataFrame, es
    ) -> pd.DataFrame:

        """
        Initialize default 3WayRec values

        :param source_frame: Source data to be reconciled which will provide this method with data for default values
        :param target: Dataframe with default results of reconciliation
        :param es: ElasticSearchClient
        :return:  DataFrame with 3WayRec default values ready to begin reconciliation
        """

        # these fields are necessary to update the RTS22Transactions already ingested to SteelEye
        target[es.meta.id] = source_frame.loc[:, es.meta.id]
        target[es.meta.model] = source_frame.loc[:, es.meta.model]
        target[es.meta.hash] = source_frame.loc[:, es.meta.hash]

        # these fields will contain the Reconciliation results
        target[RecFields.RECONCILIATION_SE_MATCH] = True

        # use report_type fieldBreaks if preexisting, otherwise initialize them as null
        target[RecFields.FIELD_BREAKS] = (
            source_frame.loc[:, RecFields.FIELD_BREAKS]
            if RecFields.FIELD_BREAKS in source_frame.columns
            else pd.NA
        )

        target[RecFields.FIELD_BREAKS] = target[RecFields.FIELD_BREAKS].apply(
            lambda fieldbreaks: self.remove_same_report_field_breaks(
                fieldbreaks=fieldbreaks, report_type=self.params.report_type
            )
        )

        empty_previous_field_break_mask = (
            target[RecFields.FIELD_BREAKS].str.len() == 0
        ) & pd.notnull(target[RecFields.FIELD_BREAKS])

        target.loc[empty_previous_field_break_mask, RecFields.FIELD_BREAKS] = pd.NA

        self.report_mechanism_reconciliation.reconcile_preprocess_data(
            source_df=source_frame, target_df=target
        )

        # Sanitize the `transactionDetails.price` based on `transactionDetails.priceNotation`
        # as we do precision on outbound report.
        # See: https://steeleye.atlassian.net/browse/EU-7384
        if RTS22Transaction.TRANSACTION_DETAILS_PRICE in source_frame.columns:
            source_frame.loc[
                :, RTS22Transaction.TRANSACTION_DETAILS_PRICE
            ] = self.sanitize_price_based_on_price_notation(source_data=source_frame)

        return target

    @staticmethod
    def remove_same_report_field_breaks(
        fieldbreaks: Union[List[Optional[typing_dict[str, Any]]], NAType],
        report_type: str,
    ) -> List[Optional[typing_dict[str, Any]]]:
        """
        Remove fieldBreaks that will again be calculated using the report_type
        :param fieldbreaks: List of fieldBreaks
        :param report_type: ReportTypeEnum
        :return: List of fieldBreaks
        """
        if fieldbreaks is pd.NA or len(fieldbreaks) == 0:
            return []

        source_report = (
            RecSources.NCA if report_type == ReportTypeEnum.NCA_FCA else RecSources.ARM
        )
        other_source_report = (
            RecSources.ARM if report_type == ReportTypeEnum.NCA_FCA else RecSources.NCA
        )

        new_fieldbreaks = []

        for fieldbreak in fieldbreaks:
            if {source_report, other_source_report}.issubset(
                fieldbreak[RecFields.FIELD_BREAKS_VALUE].keys()
            ):
                fieldbreak[RecFields.FIELD_BREAKS_VALUE].pop(source_report)
                new_fieldbreaks.append(fieldbreak)

            elif other_source_report in fieldbreak[RecFields.FIELD_BREAKS_VALUE].keys():
                new_fieldbreaks.append(fieldbreak)

            elif source_report in fieldbreak[RecFields.FIELD_BREAKS_VALUE].keys():
                continue

        return new_fieldbreaks

    @staticmethod
    def sanitize_price_based_on_price_notation(source_data: pd.DataFrame) -> pd.Series:
        """
        Parse the `transactionDetails.price` as it's done in the outbound report

        :param source_data: Transactions dataframe
        :return: Pandas series of parsed price values
        """

        def parse_price_value(price_type: str, price_value: str) -> float:
            if price_type == PriceNotation.MONE.value:
                price_value = enforce_significant_figures(
                    value=price_value, precision=18, scale=13
                )
            elif price_type in [PriceNotation.PERC.value, PriceNotation.YIEL.value]:
                price_value = enforce_significant_figures(
                    value=price_value, precision=18, scale=10
                )
            elif price_type == PriceNotation.BAPO.value:
                price_value = enforce_significant_figures(
                    value=price_value, precision=18, scale=17
                )
            return float(price_value)

        price_column = RTS22Transaction.TRANSACTION_DETAILS_PRICE
        mapped_steeleye_column = pd.Series(data=pd.NA, index=source_data.index)

        if price_column in source_data.columns:
            price_not_null_mask = (source_data.loc[:, price_column]).notnull()
            mapped_steeleye_column.loc[price_not_null_mask] = source_data.loc[
                price_not_null_mask,
                [price_column, RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOTATION],
            ].apply(
                lambda x: parse_price_value(
                    price_type=x[RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOTATION],
                    price_value=str(x[RTS22Transaction.TRANSACTION_DETAILS_PRICE]),
                ),
                axis=1,
            )

        return mapped_steeleye_column

    @staticmethod
    def validate_bool_report_type_field_break(
        target: pd.DataFrame, report_type: str
    ) -> pd.DataFrame:
        """
        This function takes target dataframe and then on the basis of the reconciliation.fieldbreaks
        list it determines whether arm/nca.fieldbreak value should be set to True

        :param target: Target dataframe
        :param report_type: Indicates arm/nca as report_type
        :return: Pandas dataframe of correctly populated arm/nca.fieldbreaks bool value
        """

        def specified_report_in_list(
            source_report: str, fieldbreaks_list: List[typing_dict[str, Any]]
        ):
            for fieldbreak in fieldbreaks_list:
                if source_report in fieldbreak[RecFields.FIELD_BREAKS_VALUE]:
                    return True
            return False

        source_report = (
            RecSources.NCA if report_type == ReportTypeEnum.NCA_FCA else RecSources.ARM
        )

        sources = {
            RecSources.NCA: RecFields.RECONCILIATION_NCA_FIELD_BREAK,
            RecSources.ARM: RecFields.RECONCILIATION_ARM_FIELD_BREAK,
        }

        target[sources[source_report]] = target[RecFields.FIELD_BREAKS].apply(
            lambda x: True
            if (
                (not (x is pd.NA))
                and (
                    specified_report_in_list(
                        source_report=source_report, fieldbreaks_list=x
                    )
                )
            )
            else False
        )

        return target

    def _reconcile_data(
        self,
        data_to_reconcile: pd.DataFrame,
        target: pd.DataFrame,
        es,
    ) -> pd.DataFrame:

        """
        This method invokes multiple methods which will perform the reconciliation for a subset of fields.
        These subsets are organized by data representation (i.e specific methods for Parties and Instruments)
        and most importantly, by data structure (i.e one to one mappings must be processed differently than
        compound mappings where a RTS22Transaction field may populate one of multiple NCA_FCA report fields)

        :param data_to_reconcile: Source DataFrame with ARM_UNAVISTA/NCA_FCA report data
         + RTS22Transaction data to be reconciled
        :param target: DataFrame with reconciliation results
        :param es: ElasticSearchClient
        :return: `target` dataframe with all reconciliation results updated
        """

        tenant_configuration = prefect.context.swarm.tenant_configuration

        new_transactions_mask = (
            data_to_reconcile.loc[:, RecFields.REPORT_DETAILS_REPORT_STATUS]
            == StaticValues.NEWT
        )
        cancelled_transactions_mask = (
            data_to_reconcile.loc[:, RecFields.REPORT_DETAILS_REPORT_STATUS]
            == StaticValues.CANC
        )

        self._reconcile_workflow_mapping_fields(
            data_to_reconcile=data_to_reconcile, target=target
        )

        self._reconcile_cancelled_mapping_fields(
            data_to_reconcile=data_to_reconcile.loc[cancelled_transactions_mask, :],
            target=target,
        )

        self._reconcile_one_to_one_general_mapping_fields(
            data_to_reconcile=data_to_reconcile.loc[new_transactions_mask, :],
            target=target,
        )

        self._reconcile_one_to_one_party_mapping_fields(
            data_to_reconcile=data_to_reconcile.loc[new_transactions_mask, :],
            target=target,
        )

        self._reconcile_one_to_one_instrument_mapping_fields(
            data_to_reconcile=data_to_reconcile.loc[new_transactions_mask, :],
            target=target,
        )

        self._reconcile_compound_general_mapping_fields(
            data_to_reconcile=data_to_reconcile.loc[new_transactions_mask, :],
            target=target,
        )

        self._reconcile_compound_party_mapping_fields(
            data_to_reconcile=data_to_reconcile.loc[new_transactions_mask, :],
            target=target,
            tenant_configuration=tenant_configuration,
        )

        self._reconcile_compound_instrument_mapping_fields(
            data_to_reconcile=data_to_reconcile.loc[new_transactions_mask, :],
            target=target,
        )

        self._reconcile_list_party_mapping_fields(
            data_to_reconcile=data_to_reconcile.loc[new_transactions_mask, :],
            target=target,
        )

        self._reconcile_list_party_with_model_variations_mapping_fields(
            data_to_reconcile=data_to_reconcile.loc[new_transactions_mask, :],
            target=target,
            es=es,
        )

        self._reconcile_custom_general_mapping_fields(
            data_to_reconcile=data_to_reconcile.loc[new_transactions_mask, :],
            target=target,
            tenant_configuration=tenant_configuration,
        )

        self._reconcile_underlying_instruments_mapping_fields(
            data_to_reconcile=data_to_reconcile.loc[new_transactions_mask, :],
            target=target,
        )

        return target

    def _reconcile_workflow_mapping_fields(
        self, data_to_reconcile: pd.DataFrame, target: pd.DataFrame
    ):
        """
        Reconcile `workflow.nca.status`, `workflow.nca.checks[.ruleId]` for NCA_FCA,
        and `workflow.isReported` for both ARM_UNAVISTA and NCA_FCA.

        :param data_to_reconcile: Source Frame that will be reconciled
        :param target: DataFrame with reconciliation results
        """
        if data_to_reconcile.empty:
            return

        workflow_mapping_result = {
            RTS22Transaction.WORKFLOW_IS_REPORTED: pd.Series(
                data=["true"] * data_to_reconcile.shape[0],
                index=data_to_reconcile.index,
            )
        }

        self.report_mechanism_reconciliation.reconcile_workflow_fields(
            workflow_mapping_result=workflow_mapping_result,
            data_to_reconcile=data_to_reconcile,
            target_df=target,
        )

        for rts22_field_name in workflow_mapping_result:

            steeleye_series = (
                data_to_reconcile.loc[:, rts22_field_name]
                if rts22_field_name in data_to_reconcile.columns
                else pd.Series(index=data_to_reconcile.index)
            )

            if not steeleye_series.name:
                steeleye_series.name = rts22_field_name

            self.report_mechanism_reconciliation.compare_report_and_steeleye_fields(
                report_series=workflow_mapping_result[rts22_field_name],
                steeleye_series=steeleye_series,
                target=target,
            )

    def _reconcile_cancelled_mapping_fields(
        self, data_to_reconcile: pd.DataFrame, target: pd.DataFrame
    ):
        """
        Reconcile Cancelled transactions which portray a very small set of data

        :param data_to_reconcile: Source Frame which will be reconciled
        :param target: DataFrame with reconciliation results
        """

        if data_to_reconcile.empty:
            return

        one_to_one_map = (
            self.report_mechanism_reconciliation.get_cancelled_one_to_one_map()
        )

        self.report_mechanism_reconciliation.run_generic_one_to_one_comparison(
            one_to_one_map=one_to_one_map,
            data_to_reconcile=data_to_reconcile,
            target=target,
        )

    def _reconcile_one_to_one_general_mapping_fields(
        self, data_to_reconcile: pd.DataFrame, target: pd.DataFrame
    ):
        """
        Reconcile General fields which have a one to one mapping
        Example: reportDetails.tradingVenueTransactionIdCode -> FinInstrmRptgTxRpt.Tx.New.Tx.TradPlcMtchgId

        :param data_to_reconcile: Source Frame which will be reconciled
        :param target: DataFrame with reconciliation results
        """

        if data_to_reconcile.empty:
            return

        one_to_one_map = (
            self.report_mechanism_reconciliation.get_general_one_to_one_map()
        )

        self.report_mechanism_reconciliation.run_generic_one_to_one_comparison(
            one_to_one_map=one_to_one_map,
            data_to_reconcile=data_to_reconcile,
            target=target,
        )

    def _reconcile_one_to_one_party_mapping_fields(
        self, data_to_reconcile: pd.DataFrame, target: pd.DataFrame
    ):
        """
        Reconcile Party fields which have a one to one mapping
        Example: parties.executingEntity.details.mifidRegistered -> FinInstrmRptgTxRpt.Tx.New.InvstmtPtyInd

        :param data_to_reconcile: Source Frame which will be reconciled
        :param target: DataFrame with reconciliation results
        """

        if data_to_reconcile.empty:
            return

        one_to_one_map = (
            self.report_mechanism_reconciliation.get_party_one_to_one_reconcile_map()
        )

        self.report_mechanism_reconciliation.run_generic_one_to_one_comparison(
            one_to_one_map=one_to_one_map,
            data_to_reconcile=data_to_reconcile,
            target=target,
        )

    def _reconcile_one_to_one_instrument_mapping_fields(
        self, data_to_reconcile: pd.DataFrame, target: pd.DataFrame
    ):
        """
        Reconcile Instrument fields which have a one to one mapping
        Example: instrumentDetails.instrument.instrumentFullName ->
        FinInstrmRptgTxRpt.Tx.New.FinInstrm.Othr.FinInstrmGnlAttrbts.FullNm

        :param data_to_reconcile: Source Frame which will be reconciled
        :param target: DataFrame with reconciliation results
        """

        # These fields should only be compared if they are present in both SE and the NCA_FCA report
        # If we only have the fields in SE they should not count as field breaks from being absent from NCA_FCA
        # Thus the `skip_if_no_data_on_report = True` flag

        if data_to_reconcile.empty:
            return

        one_to_one_map = (
            self.report_mechanism_reconciliation.get_instrument_one_to_one_map()
        )

        self.report_mechanism_reconciliation.run_generic_one_to_one_comparison(
            one_to_one_map=one_to_one_map,
            data_to_reconcile=data_to_reconcile,
            target=target,
        )

    def _reconcile_compound_general_mapping_fields(
        self, data_to_reconcile: pd.DataFrame, target: pd.DataFrame
    ):

        self.report_mechanism_reconciliation.reconcile_compound_general_fields(
            data_to_reconcile=data_to_reconcile, target=target
        )

    def _reconcile_compound_party_mapping_fields(
        self,
        data_to_reconcile: pd.DataFrame,
        target: pd.DataFrame,
        tenant_configuration: Dict,
    ):

        self.report_mechanism_reconciliation.reconcile_compound_party_fields(
            data_to_reconcile=data_to_reconcile,
            target=target,
            tenant_configuration=tenant_configuration,
        )

    def _reconcile_compound_instrument_mapping_fields(
        self, data_to_reconcile: pd.DataFrame, target: pd.DataFrame
    ):

        self.report_mechanism_reconciliation.reconcile_compound_instrument_fields(
            data_to_reconcile=data_to_reconcile, target=target
        )

    def _reconcile_list_party_mapping_fields(
        self, data_to_reconcile: pd.DataFrame, target: pd.DataFrame
    ):
        self.report_mechanism_reconciliation.reconcile_list_party_fields(
            data_to_reconcile=data_to_reconcile, target=target
        )

    def _reconcile_list_party_with_model_variations_mapping_fields(
        self,
        data_to_reconcile: pd.DataFrame,
        target: pd.DataFrame,
        es,
    ):
        if data_to_reconcile.empty:
            return

        self.report_mechanism_reconciliation.reconcile_list_party_fields_with_model_variations(
            data_to_reconcile=data_to_reconcile, es=es, target=target
        )

    def _reconcile_custom_general_mapping_fields(
        self,
        data_to_reconcile: pd.DataFrame,
        target: pd.DataFrame,
        tenant_configuration: Dict,
    ):

        if data_to_reconcile.empty:
            return

        self.report_mechanism_reconciliation.reconcile_custom_general_fields(
            data_to_reconcile=data_to_reconcile,
            target=target,
            tenant_configuration=tenant_configuration,
        )

    def _reconcile_underlying_instruments_mapping_fields(
        self, data_to_reconcile: pd.DataFrame, target: pd.DataFrame
    ):
        """
        Perform reconciliation of the Underlying Instruments report and SE fields.

        :param data_to_reconcile: Pandas DataFrame with Data to be reconciled
        :param target: Pandas DataFrame with the reconciliation results
        """
        underlying_comparison_data = []

        # Underlying Instrument ID
        ul_instrument_id_report_columns = (
            self.report_mechanism_reconciliation.get_ul_instrument_id_report_columns()
        )

        ul_instrument_id_report_series = (
            self.report_mechanism_reconciliation.parse_ul_instrument_report_series(
                report_columns=ul_instrument_id_report_columns,
                source_data=data_to_reconcile,
                nested_ordered_dict_field="ISIN",
            )
        )

        steeleye_series_ext = self.report_mechanism_reconciliation.parse_underlying_instrument_steeleye_series(
            steeleye_root_instrument_field=RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_EXT_UL_INSTRUMENTS,
            steeleye_nested_instrument_field=RTS22Transaction.INSTRUMENT_ID_CODE,
            source_data=data_to_reconcile,
        )

        steeleye_series_derivative = self.report_mechanism_reconciliation.parse_underlying_instrument_steeleye_series(
            steeleye_root_instrument_field=RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_UL_INSTRUMENTS,
            steeleye_nested_instrument_field=RTS22Transaction.UNDERLYING_INSTRUMENT_CODE,
            source_data=data_to_reconcile,
        )

        # All values from `steeleye_series_derivative` will be replaced by the
        # same-index non-null value of `steeleye_series_ext`. It is not expected, but
        # in the chance that both series have a value populated at the same index,
        # `steeleye_series_ext` is the value that will be used, as well as its field name
        # (we do not want to flag the `steeleye_series_derivative` field name for the field break
        # as it is deprecated)
        steeleye_series_derivative.update(steeleye_series_ext)
        steeleye_series_derivative.name = steeleye_series_ext.name

        # As we don't want to generate false positive field breaks
        # when the data is not in Report but is in SteelEye database
        # so convert the SteelEye data to NULL according to the received
        # Report data
        steeleye_series_derivative = steeleye_series_derivative.mask(
            ul_instrument_id_report_series.isnull()
        )

        underlying_comparison_data.append(
            UnderlyingInstrumentComparison(
                report_series=ul_instrument_id_report_series,
                steeleye_series=steeleye_series_derivative,
            )
        )

        # Underlying Index ID
        ul_index_id_report_columns = (
            self.report_mechanism_reconciliation.get_ul_index_id_report_columns()
        )

        ul_index_id_report_series = (
            self.report_mechanism_reconciliation.parse_ul_instrument_report_series(
                report_columns=ul_index_id_report_columns,
                source_data=data_to_reconcile,
                nested_ordered_dict_field="ISIN",
            )
        )

        ul_index_id_se_series = self.report_mechanism_reconciliation.parse_underlying_instrument_steeleye_series(
            steeleye_root_instrument_field=RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_EXT_UL_INSTRUMENTS,
            steeleye_nested_instrument_field=RTS22Transaction.EXT_UNDERLYING_INDEX_ID,
            source_data=data_to_reconcile,
        )
        underlying_comparison_data.append(
            UnderlyingInstrumentComparison(
                report_series=ul_index_id_report_series,
                steeleye_series=ul_index_id_se_series,
            )
        )

        # Underlying Index Name
        ul_index_name_report_columns = (
            self.report_mechanism_reconciliation.get_ul_index_name_report_columns()
        )

        ul_index_name_report_series = (
            self.report_mechanism_reconciliation.parse_ul_instrument_report_series(
                report_columns=ul_index_name_report_columns,
                source_data=data_to_reconcile,
                nested_ordered_dict_field="Nm",
            )
        )

        ul_idx_name_report_series_idx = (
            self.report_mechanism_reconciliation.parse_ul_instrument_report_series(
                report_columns=ul_index_name_report_columns,
                source_data=data_to_reconcile,
                nested_ordered_dict_field="Indx",
            )
        )

        ul_index_name_report_series.update(ul_idx_name_report_series_idx)

        steeleye_series_field_names = pd.Series(
            data=RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_UL_INDEX_NAME,
            index=data_to_reconcile.index,
        )

        ul_index_name_se_series = self.report_mechanism_reconciliation.parse_underlying_instrument_steeleye_series(
            steeleye_root_instrument_field=RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_EXT_UL_INSTRUMENTS,
            steeleye_nested_instrument_field=RTS22Transaction.EXT_UNDERLYING_INDEX_NAME,
            source_data=data_to_reconcile,
        )

        ul_index_name_se_series_drv = self.report_mechanism_reconciliation.parse_underlying_instrument_steeleye_series(
            steeleye_root_instrument_field=RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_EXT_UL_INSTRUMENTS,
            steeleye_nested_instrument_field=RTS22Transaction.DERIVATIVE_UDERLYING_INDEX_NAME,
            source_data=data_to_reconcile,
        )
        inst_details_ul_index_name_series = (
            pd.Series(
                data=[
                    [x] if isinstance(x, str) else pd.NA
                    for x in data_to_reconcile[
                        RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_UL_INDEX_NAME
                    ].values.tolist()
                ],
                index=data_to_reconcile.index,
                name=RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_UL_INDEX_NAME,
            )
            if RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_UL_INDEX_NAME
            in data_to_reconcile.columns
            else pd.Series(
                data=pd.NA,
                index=data_to_reconcile.index,
                name=RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_UL_INDEX_NAME,
            )
        )

        # As we don't want to generate false positive field breaks
        # when the data is not in Report but is in SteelEye database
        # so convert the SteelEye data to NULL according to the received
        # Report data
        inst_details_ul_index_name_series = inst_details_ul_index_name_series.mask(
            ul_index_name_report_series.isnull()
        )

        for series in [ul_index_name_se_series, ul_index_name_se_series_drv]:
            null_mask = inst_details_ul_index_name_series.isnull()
            null_mask_and_series_not_null = null_mask & series.notnull()
            inst_details_ul_index_name_series[null_mask_and_series_not_null] = series[
                null_mask_and_series_not_null
            ]
            steeleye_series_field_names.loc[null_mask_and_series_not_null] = series.name

        underlying_comparison_data.append(
            UnderlyingInstrumentComparison(
                report_series=ul_index_name_report_series,
                steeleye_series=inst_details_ul_index_name_series,
                names_series=steeleye_series_field_names,
            )
        )

        for ul_instrument_comparison in underlying_comparison_data:

            self.report_mechanism_reconciliation.compare_report_and_steeleye_fields(
                report_series=ul_instrument_comparison.report_series,
                steeleye_series=ul_instrument_comparison.steeleye_series,
                target=target,
                steeleye_field_names=ul_instrument_comparison.names_series,
            )
