from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.reference import StrikePriceType


class TempColumns:
    """These columns are to be used by downstream tasks and dropped before ingestion"""

    ASSET_CLASS = "__asset_class__"
    DATA_CATEGORY = "__data_category__"
    DELIVERY_TYPE = "__delivery_type__"
    IS_CREATED_THROUGH_FALLBACK = "__is_created_through_fallback__"
    INSTRUMENT_CLASSIFICATION = "__instrument_classification__"
    INSTRUMENT_FULL_NAME = "__instrument_full_name__"
    INSTRUMENT_ID_CODE = "__instrument_id_code__"
    INSTRUMENT_ID_CODE_TYPE = "__instrument_id_code_type__"
    NOTIONAL_CURRENCY_1 = "__notional_currency_1__"
    NOTIONAL_CURRENCY_2 = "__notional_currency_2__"
    NOTIONAL_CURRENCY_2_TYPE = "__notional_currency_2_type__"
    MATURITY_DATE = "__maturity_date__"
    OPTION_EXERCISE_STYLE = "__option_exercise_style__"
    OPTION_TYPE = "__option_type__"
    PRICE = "__price__"
    PRICE_CURRENCY = "__price_currency__"
    PRICE_MULTIPLIER = "__price_multiplier__"
    PRICE_NOTATION = "__price_notation__"
    QUANTITY_TYPE = "__quantity_type__"
    QUANTITY_NOTATION = "__quantity_notation__"
    STRIKE_PRICE = "__strike_price__"
    STRIKE_PRICE_CURRENCY = "__strike_price_currency__"
    STRIKE_PRICE_TYPE = "__strike_price_type__"
    TRADING_DATE_TIME = "__trading_date_time__"
    UNDERLYING_INDEX_NAME = "__underlying_index_name__"
    UNDERLYING_INDEX_TERM = "__underlying_index_term__"
    UNDERLYING_ISIN = "__underlying_isin__"
    UNDER_INSTR_UNDERLYING_INDEX_NAME = "__under_instr_underlying_index_name__"
    UNDER_INSTR_UNDERLYING_INDEX_TERM = "__under_instr_underlying_index_term__"
    UV_INDEX_CLASSIFICATION = "__uv_index_classification__"
    UV_INSTRUMENT_CLASSIFICATION = "__uv_instrument_classification__"


PRICE_NOTATION_MAP = {
    "Pctg": PriceNotation.PERC.value,
    "Yld": PriceNotation.YIEL.value,
    "MntryValAmt": PriceNotation.MONE.value,
    "Bsis": PriceNotation.BAPO.value,
    "BsisPts": PriceNotation.BAPO.value,
}

STRIKE_PRICE_TYPE_MAP = {
    "Pctg": StrikePriceType.PCTG.value,
    "Yld": StrikePriceType.YLD.value,
    "MntryValAmt": StrikePriceType.MNTRYVAL.value,
    "BsisPts": StrikePriceType.BSISPTS.value,
    "PNDG": StrikePriceType.PNDG.value,
}

QUANTITY_NOTATION_MAP = {
    "Unit": QuantityNotation.UNIT.value,
    "NominalValue": QuantityNotation.NOML.value,
    "MonetaryValue": QuantityNotation.MONE.value,
}

INSTRUMENT_ID_MAP = {"FinInstrm.Id": "ID", "FinInstrm.Othr": "OTHR"}
