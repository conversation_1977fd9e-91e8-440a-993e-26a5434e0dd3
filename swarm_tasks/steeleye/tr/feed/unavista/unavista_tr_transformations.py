import os

import pandas as pd
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.feeds.tr.unavista.static import TargetColumns as SourceColumns
from se_core_tasks.map.map_attribute import CastTo
from se_core_tasks.map.map_conditional import Case
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import OptionType
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_trades_tasks.abstractions.abstract_rts22_transaction_transformations import (
    AbstractRTS22TransactionsTransformations,
)
from se_trades_tasks.order_and_tr.party.utils import add_id_or_lei
from se_trades_tasks.order_and_tr.static import AssetClass
from se_trades_tasks.order_and_tr.static import (
    ISIN_REGEX_ASSET_CLASS_MAP,
)

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ConvertMinorToMajorParams,
)
from swarm_tasks.steeleye.tr.feed.unavista.static import INSTRUMENT_ID_MAP
from swarm_tasks.steeleye.tr.feed.unavista.static import PRICE_NOTATION_MAP
from swarm_tasks.steeleye.tr.feed.unavista.static import QUANTITY_NOTATION_MAP
from swarm_tasks.steeleye.tr.feed.unavista.static import STRIKE_PRICE_TYPE_MAP
from swarm_tasks.steeleye.tr.feed.unavista.static import TempColumns
from swarm_tasks.steeleye.tr.generic.map_swap_directionalities import (
    MapSwapDirectionalities,
)
from swarm_tasks.steeleye.tr.generic.map_swap_directionalities import (
    Params as MapSwapDirectionalitiesParams,
)
from swarm_tasks.steeleye.tr.static import RTS22Transaction
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ConcatAttributesParams,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ConvertDatetimeParams,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as MapAttributeParams
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as MapConditionalParams
from swarm_tasks.transform.map.map_static import MapStatic
from swarm_tasks.transform.map.map_static import Params as MapStaticParams
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as MapValueParams
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as MergeMarketIdentifiersParams,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    Params as InstrumentIdentifiersParams,
)
from swarm_tasks.transform.steeleye.tr.identifiers.party_identifiers import (
    Params as PartyIdentifiersParams,
)
from swarm_tasks.transform.steeleye.tr.identifiers.party_identifiers import (
    PartyIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.iso20022.map_instrument import In
from swarm_tasks.utilities.static import Delimiters


class UnaVistaTransformations(AbstractRTS22TransactionsTransformations):
    """Primary Transformations for Unavista RTS22Transactions flow"""

    COUNTERPARTY_COLUMN = "__counterparty__"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.file_url = os.getenv("SWARM_FILE_URL")

    def _pre_process(self):

        self.pre_process_df.loc[
            :, TempColumns.UV_INDEX_CLASSIFICATION
        ] = MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=SourceColumns.UV_INDEX_CLASSIFICATION,
                cast_to=CastTo.STRING_LIST,
                list_delimiter=[";"],
                target_attribute=TempColumns.UV_INDEX_CLASSIFICATION,
            ),
        )

        self.pre_process_df.loc[
            :, TempColumns.UV_INSTRUMENT_CLASSIFICATION
        ] = MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=SourceColumns.UV_INSTRUMENT_CLASSIFICATION,
                cast_to=CastTo.STRING_LIST,
                list_delimiter=[";"],
                target_attribute=TempColumns.UV_INSTRUMENT_CLASSIFICATION,
            ),
        )

        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._temp_trading_date_time(),
                self._temp_currencies_and_prices(),
                self._temp_instrument_full_name(),
                self._temp_instrument_classification(),
                self._temp_option_type(),
            ],
            axis=1,
        )

    def _post_process(self):
        self.target_df = pd.concat(
            [
                self.target_df,
                self.pre_process_df.loc[
                    :,
                    [
                        TempColumns.STRIKE_PRICE_CURRENCY,
                        TempColumns.NOTIONAL_CURRENCY_1,
                        TempColumns.INSTRUMENT_FULL_NAME,
                        TempColumns.INSTRUMENT_CLASSIFICATION,
                    ],
                ],
                ConvertDatetime.process(
                    source_frame=self.source_frame,
                    params=ConvertDatetimeParams(
                        source_attribute=SourceColumns.MATURITY_DATE,
                        target_attribute=TempColumns.MATURITY_DATE,
                        convert_to=ConvertTo.DATE,
                    ),
                ),
                MapValue.process(
                    source_frame=self.source_frame,
                    params=MapValueParams(
                        source_attribute=SourceColumns.INSTRUMENT_ID_TYPE,
                        target_attribute=TempColumns.INSTRUMENT_ID_CODE_TYPE,
                        value_map=INSTRUMENT_ID_MAP,
                    ),
                ),
                MapValue.process(
                    source_frame=self.source_frame,
                    params=MapValueParams(
                        source_attribute=SourceColumns.STRIKE_PRICE_TYPE,
                        target_attribute=TempColumns.STRIKE_PRICE_TYPE,
                        value_map=STRIKE_PRICE_TYPE_MAP,
                    ),
                ),
                # Concatenate values from both columns and remove [,], ', " characters so
                # that we have ISINs separated by commas (if there are multiple
                # ISINs in one of the source columns) and semi-colons (if both source
                # columns are non-null)
                ConcatAttributes.process(
                    source_frame=self.source_frame,
                    params=ConcatAttributesParams(
                        source_attributes=[
                            SourceColumns.UNDERLYING_INSTRUMENT_ID,
                            SourceColumns.UNDERLYING_INDEX_ID,
                        ],
                        target_attribute=TempColumns.INSTRUMENT_ID_CODE,
                        delimiter=Delimiters.SEMI_COLON,
                    ),
                )[TempColumns.INSTRUMENT_ID_CODE]
                .astype("string")
                .str.replace(r"[\'\"\[\]]", "", regex=True),
                MapConditional.process(
                    source_frame=pd.concat(
                        [
                            self.source_frame.loc[
                                :, SourceColumns.NOTIONAL_CURRENCY_2_TYPE
                            ],
                            self.pre_process_df.loc[:, TempColumns.NOTIONAL_CURRENCY_2],
                        ],
                        axis=1,
                    ).astype("string"),
                    params=MapConditionalParams(
                        target_attribute=TempColumns.NOTIONAL_CURRENCY_2,
                        cases=[
                            Case(
                                query=f"`{SourceColumns.NOTIONAL_CURRENCY_2_TYPE}`.str.fullmatch('FX', case=False, na=False)",
                                attribute=TempColumns.NOTIONAL_CURRENCY_2,
                            )
                        ],
                    ),
                ),
                pd.Series(
                    data=self.source_frame.loc[:, SourceColumns.DELIVERY_TYPE].values,
                    index=self.source_frame.index,
                    name=TempColumns.DELIVERY_TYPE,
                ),
                pd.Series(
                    data=self.source_frame.loc[:, SourceColumns.OPTION_STYLE].values,
                    index=self.source_frame.index,
                    name=TempColumns.OPTION_EXERCISE_STYLE,
                ),
                pd.Series(
                    data=self.pre_process_df.loc[:, TempColumns.STRIKE_PRICE].values,
                    index=self.source_frame.index,
                    name=TempColumns.STRIKE_PRICE,
                ),
                pd.Series(
                    data=self.source_frame.loc[
                        :, SourceColumns.PRICE_MULTIPLIER
                    ].values,
                    index=self.source_frame.index,
                    name=TempColumns.PRICE_MULTIPLIER,
                ),
                pd.Series(
                    data=self.source_frame.loc[
                        :, SourceColumns.NOTIONAL_CURRENCY_2_TYPE
                    ].values,
                    index=self.source_frame.index,
                    name=TempColumns.NOTIONAL_CURRENCY_2_TYPE,
                ),
                pd.Series(
                    data=self.source_frame.loc[:, SourceColumns.DATA_CATEGORY].values,
                    index=self.source_frame.index,
                    name=TempColumns.DATA_CATEGORY,
                ),
                pd.Series(
                    data=True,
                    index=self.source_frame.index,
                    name=TempColumns.IS_CREATED_THROUGH_FALLBACK,
                ),
                pd.Series(
                    data=self.source_frame.loc[
                        :, SourceColumns.BUYER_TRANSMITTER_ID
                    ].values,
                    index=self.source_frame.index,
                    name=RTS22Transaction.PARTIES_BUYER_TRANSMITTING_FIRM_FIRM_IDENTIFIERS_LEI,
                ).fillna(pd.NA),
                pd.Series(
                    data=self.source_frame.loc[
                        :, SourceColumns.SELLER_TRANSMITTER_ID
                    ].values,
                    index=self.source_frame.index,
                    name=RTS22Transaction.PARTIES_SELLER_TRANSMITTING_FIRM_FIRM_IDENTIFIERS_LEI,
                ).fillna(pd.NA),
                self._populate_transaction_details_swap_directionalities(),
            ],
            axis=1,
        )

        under_instr_not_array_mask = ~self.source_frame.loc[
            :, SourceColumns.UNDERLYING_INSTRUMENT_ID
        ].str.match(r"[.*]", na=False)

        under_index_name_not_array_mask = ~self.source_frame.loc[
            :, SourceColumns.UNDERLYING_INDEX_NAME
        ].str.match(r"[.*]", na=False)

        # Combine underlying index name and underlying instrument id as per the
        # foll. logic:
        #
        temp_index_name_series = (
            self.source_frame.loc[
                under_instr_not_array_mask & under_index_name_not_array_mask,
                SourceColumns.UNDERLYING_INDEX_NAME,
            ]
            .fillna("")
            .str.replace(r"\s+", "", regex=True)
            .str[:25]
        )

        temp_index_id_series = self.source_frame.loc[
            under_instr_not_array_mask & under_index_name_not_array_mask,
            SourceColumns.UNDERLYING_INSTRUMENT_ID,
        ].fillna("")

        combined_length_series = (
            temp_index_name_series.str.len() + temp_index_id_series.str.len()
        )

        # Create a Series that concatenates only where combined length <= 25
        # If combined length is > 25, it just keeps the Index name value
        concatenated_series = (
            (temp_index_name_series + Delimiters.SEMI_COLON + temp_index_id_series)
            .where(combined_length_series <= 25, temp_index_name_series)
            .str.strip(Delimiters.SEMI_COLON)
        )

        self.target_df.loc[
            under_instr_not_array_mask & under_index_name_not_array_mask,
            TempColumns.UNDERLYING_INDEX_NAME,
        ] = concatenated_series.replace("", pd.NA)

        under_index_term_not_array_mask = (
            ~self.source_frame[SourceColumns.UNDERLYING_INDEX_TERM]
            .astype("string")
            .str.match(r"[.*]", na=False)
        )

        # It is used to populate `instrument.derivative.underlyingIndexTerm`
        # during InstrumentFallback
        self.target_df.loc[
            under_index_term_not_array_mask,
            TempColumns.UNDERLYING_INDEX_TERM,
        ] = self.source_frame.loc[
            under_index_term_not_array_mask,
            SourceColumns.UNDERLYING_INDEX_TERM,
        ]

    def _date(self) -> pd.DataFrame:
        """
        Populates using UnaVistaColumns.TRADING_DATE_TIME
        """
        return ConvertDatetime.process(
            source_frame=self.pre_process_df,
            params=ConvertDatetimeParams(
                source_attribute=TempColumns.TRADING_DATE_TIME,
                target_attribute=RTS22Transaction.DATE,
                convert_to=ConvertTo.DATE,
            ),
        )

    def _data_source_name(self) -> pd.DataFrame:
        """
        Static value: UnaVista Transaction Report
        """
        return MapStatic.process(
            source_frame=self.source_frame,
            params=MapStaticParams(
                target_attribute=RTS22Transaction.DATA_SOURCE_NAME,
                target_value="UnaVista Transaction Report",
            ),
        )

    def _meta_model(self) -> pd.DataFrame:
        """
        Static value: RTS22Transaction
        """
        return MapStatic.process(
            source_frame=self.source_frame,
            params=MapStaticParams(
                target_attribute=RTS22Transaction.META_MODEL,
                target_value="RTS22Transaction",
            ),
        )

    def _report_details_investment_firm_covered_directive(self) -> pd.DataFrame:
        """Not implemented"""

    def _report_details_report_status(self) -> pd.DataFrame:
        """Populates with UnaVistaColumns.REPORT_STATUS"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=SourceColumns.REPORT_STATUS,
                target_attribute=RTS22Transaction.REPORT_DETAILS_REPORT_STATUS,
            ),
            auditor=self.auditor,
        )

    def _report_details_trading_venue_transaction_id_code(self):
        """Populates with UnaVistaColumns.VENUE_TRANSACTION_ID"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=SourceColumns.VENUE_TRANSACTION_ID,
                target_attribute=RTS22Transaction.REPORT_DETAILS_TRADING_VENUE_TRANSACTION_ID_CODE,
            ),
            auditor=self.auditor,
        )

    def _source_index(self) -> pd.DataFrame:
        """
        Static value: index from the source_frame
        """
        return MapStatic.process(
            source_frame=self.source_frame,
            params=MapStaticParams(
                target_attribute=RTS22Transaction.SOURCE_INDEX,
                from_index=True,
            ),
        )

    def _source_key(self) -> pd.DataFrame:
        """
        Static value: File URL
        """
        return MapStatic.process(
            source_frame=self.source_frame,
            params=MapStaticParams(
                target_attribute=RTS22Transaction.SOURCE_KEY, target_value=self.file_url
            ),
        )

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(self):
        """Populated from UnaVistaColumns.COMMODITY_DERIVATIVE_INDICATOR"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=MapValueParams(
                source_attribute=SourceColumns.COMMODITY_DERIVATIVE_INDICATOR,
                target_attribute=RTS22Transaction.TRADERS_ALGOS_WAIVERS_INDICATORS_COMMODITY_DRV_IND,
                case_insensitive=True,
                value_map={"TRUE": True, "FALSE": False},
            ),
        )

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(self):
        """Populates from UnaVistaColumns.OTC_POST_TRADE_INDICATOR"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=SourceColumns.OTC_POST_TRADE_INDICATOR,
                target_attribute=RTS22Transaction.TRADERS_ALGOS_WAIVERS_INDICATORS_OTC_POST_TRADE_IND,
                list_delimiter=";",
                cast_to=CastTo.STRING_LIST,
            ),
            auditor=self.auditor,
        )

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(self):
        """
        Populates from UnaVistaColumns.SFT_INDICATOR
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=SourceColumns.SFT_INDICATOR,
                target_attribute=RTS22Transaction.TRADERS_ALGOS_WAIVERS_INDICATORS_SECRT_FNC_TXN_IND,
            ),
            auditor=self.auditor,
        )

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        """Populates from UnaVistaColumns.SHORT_SELLING_INDICATOR"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=SourceColumns.SHORT_SELLING_INDICATOR,
                target_attribute=RTS22Transaction.TRADERS_ALGOS_WAIVERS_INDICATORS_SHORT_SELLING_INDICATOR,
            ),
            auditor=self.auditor,
        )

    def _traders_algos_waivers_indicators_waiver_indicator(self):
        """Populates from UnaVistaColumns.WAIVER_INDICATOR"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=SourceColumns.WAIVER_INDICATOR,
                target_attribute=RTS22Transaction.TRADERS_ALGOS_WAIVERS_INDICATORS_WAIVER_INDICATOR,
                list_delimiter=";",
                cast_to=CastTo.STRING_LIST,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """
        Populates from Short Selling Indicator
        """
        deal_records_cases = [
            Case(
                query=f"`{SourceColumns.TRADING_CAPACITY}`.str.fullmatch('{TradingCapacity.DEAL.value}', case=False, na=False) & "
                f"`{SourceColumns.BUYER_ID_TYPE}`.str.fullmatch('L', case=False, na=False) & "
                f"`{SourceColumns.EXECUTING_ENTITY_ID}` == `{SourceColumns.BUYER_ID}` & "
                f"~(`{SourceColumns.SELLER_ID_TYPE}`.str.fullmatch('L', case=False, na=True)) | "
                f"`{SourceColumns.BUYER_ID}` != `{SourceColumns.SELLER_ID}`",
                value=BuySellIndicator.BUYI.value,
            ),
            Case(
                query=f"`{SourceColumns.TRADING_CAPACITY}`.str.fullmatch('{TradingCapacity.DEAL.value}', case=False, na=False) & "
                f"`{SourceColumns.SELLER_ID_TYPE}`.str.fullmatch('L', case=False, na=False) & "
                f"`{SourceColumns.EXECUTING_ENTITY_ID}` == `{SourceColumns.SELLER_ID}` & "
                f"~(`{SourceColumns.BUYER_ID_TYPE}`.str.fullmatch('L', case=False, na=True)) | "
                f"`{SourceColumns.BUYER_ID}` != `{SourceColumns.SELLER_ID}`",
                value=BuySellIndicator.SELL.value,
            ),
        ]

        non_deal_records_cases = [
            Case(
                query=f"~(`{SourceColumns.TRADING_CAPACITY}`.str.fullmatch('{TradingCapacity.DEAL.value}', case=False, na=True)) & "
                f"`{SourceColumns.BUYER_COUNTRY_OF_BRANCH}`.notnull() & `{SourceColumns.SELLER_COUNTRY_OF_BRANCH}`.isnull()",
                value=BuySellIndicator.BUYI.value,
            ),
            Case(
                query=f"~(`{SourceColumns.TRADING_CAPACITY}`.str.fullmatch('{TradingCapacity.DEAL.value}', case=False, na=True)) & "
                f"`{SourceColumns.SELLER_COUNTRY_OF_BRANCH}`.notnull() & `{SourceColumns.BUYER_COUNTRY_OF_BRANCH}`.isnull()",
                value=BuySellIndicator.SELL.value,
            ),
            Case(
                query=f"~(`{SourceColumns.TRADING_CAPACITY}`.str.fullmatch('{TradingCapacity.DEAL.value}', case=False, na=True)) & "
                f"`{SourceColumns.SELLER_COUNTRY_OF_BRANCH}`.isnull() & "
                f"`{SourceColumns.BUYER_COUNTRY_OF_BRANCH}`.isnull() & "
                f"`{SourceColumns.BUYER_ID}`.str.fullmatch('INTC', case=False, na=False) & "
                f"~(`{SourceColumns.SELLER_ID}`.str.fullmatch('INTC', case=False, na=True))",
                value=BuySellIndicator.BUYI.value,
            ),
            Case(
                query=f"~(`{SourceColumns.TRADING_CAPACITY}`.str.fullmatch('{TradingCapacity.DEAL.value}', case=False, na=True)) & "
                f"`{SourceColumns.SELLER_COUNTRY_OF_BRANCH}`.isnull() & "
                f"`{SourceColumns.BUYER_COUNTRY_OF_BRANCH}`.isnull() & "
                f"`{SourceColumns.SELLER_ID}`.str.fullmatch('INTC', case=False, na=False) & "
                f"~(`{SourceColumns.BUYER_ID}`.str.fullmatch('INTC', case=False, na=True))",
                value=BuySellIndicator.SELL.value,
            ),
        ]

        return MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=RTS22Transaction.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
                cases=deal_records_cases + non_deal_records_cases,
            ),
        )

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        """Populates with UnaVistaColumns.COMPLEX_TRADE_COMPONENT_ID"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=SourceColumns.COMPLEX_TRADE_COMPONENT_ID,
                target_attribute=RTS22Transaction.TRANSACTION_DETAILS_COMPLEX_TRADE_COMPONENT_ID,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        """Populates with UnaVistaColumns.DERIVATIVE_NOTIONAL_CHANGE"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=SourceColumns.DERIVATIVE_NOTIONAL_CHANGE,
                target_attribute=RTS22Transaction.TRANSACTION_DETAILS_DERIVATIVE_NOTIONAL_CHANGE,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_branch_membership_country(self):
        """Populates from UnaVistaColumns.COUNTRY_OF_BRANCH"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=SourceColumns.COUNTRY_OF_BRANCH,
                target_attribute=RTS22Transaction.TRANSACTION_DETAILS_BRANCH_MEMBERSHIP_COUNTRY,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_cross_indicator(self):
        """Not Implemented"""

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        """
        Populates from UnaVistaColumns.NET_AMOUNT.
        """
        mask = self.source_frame[SourceColumns.NET_AMOUNT].notnull()
        return MapAttribute.process(
            source_frame=self.source_frame.loc[mask, [SourceColumns.NET_AMOUNT]].astype(
                float
            ),
            params=MapAttributeParams(
                source_attribute=SourceColumns.NET_AMOUNT,
                target_attribute=RTS22Transaction.TRANSACTION_DETAILS_NET_AMOUNT,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_outgoing_order_addl_info(self):
        """Not Implemented"""

    def _transaction_details_position_effect(self):
        """Not Implemented"""

    def _transaction_details_position_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_price(self) -> pd.DataFrame:
        """Populates from SourceColumns.PRICE"""
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ConvertMinorToMajorParams(
                source_ccy_attribute=SourceColumns.PRICE_CURRENCY,
                source_price_attribute=SourceColumns.PRICE,
                target_price_attribute=RTS22Transaction.TRANSACTION_DETAILS_PRICE,
            ),
        )

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """Populates from SourceColumns.PRICE_CURRENCY"""
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ConvertMinorToMajorParams(
                source_ccy_attribute=SourceColumns.PRICE_CURRENCY,
                target_ccy_attribute=RTS22Transaction.TRANSACTION_DETAILS_PRICE_CURRENCY,
            ),
        )

    def _transaction_details_price_not_applicable(self):
        """Populates from UnaVistaColumns.PRICE_TYPE"""
        return MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOT_APPLICABLE,
                cases=[
                    Case(
                        query=f"~(`{SourceColumns.PRICE_TYPE}`.str.fullmatch('NOAP', case=False, na=False))",
                        value=False,
                    ),
                    Case(
                        query=f"`{SourceColumns.PRICE_TYPE}`.str.fullmatch('NOAP', case=False, na=False)",
                        value=True,
                    ),
                ],
            ),
        )

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        return MapValue.process(
            source_frame=self.source_frame,
            params=MapValueParams(
                source_attribute=SourceColumns.PRICE_TYPE,
                target_attribute=RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOTATION,
                case_insensitive=True,
                value_map=PRICE_NOTATION_MAP,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_price_pending(self):
        """Populates from UnaVistaColumns.PRICE_TYPE"""
        return MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=RTS22Transaction.TRANSACTION_DETAILS_PRICE_PENDING,
                cases=[
                    Case(
                        query=f"~(`{SourceColumns.PRICE_TYPE}`.str.fullmatch('PNDG', case=False, na=False))",
                        value=False,
                    ),
                    Case(
                        query=f"`{SourceColumns.PRICE_TYPE}`.str.fullmatch('PNDG', case=False, na=False)",
                        value=True,
                    ),
                ],
            ),
        )

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """Populates with UnaVistaColumns.QUANTITY"""
        return MapStatic.process(
            source_frame=self.source_frame,
            params=MapStaticParams(
                target_attribute=RTS22Transaction.TRANSACTION_DETAILS_QUANTITY,
                target_value=self.source_frame[SourceColumns.QUANTITY]
                .astype("string")
                .str.replace(",", "")
                .values,
            ),
        )

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        """Populates with UnaVistaColumns.QUANTITY_CURRENCY"""
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ConvertMinorToMajorParams(
                source_ccy_attribute=SourceColumns.QUANTITY_CURRENCY,
                target_ccy_attribute=RTS22Transaction.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
            ),
        )

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """Populates from UnaVistaColumns.QUANTITY_TYPE"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=MapValueParams(
                source_attribute=SourceColumns.QUANTITY_TYPE,
                target_attribute=RTS22Transaction.TRANSACTION_DETAILS_QUANTITY_NOTATION,
                case_insensitive=True,
                value_map=QUANTITY_NOTATION_MAP,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_record_type(self):
        return MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=RTS22Transaction.TRANSACTION_DETAILS_RECORD_TYPE,
                cases=[
                    Case(
                        query=f"`{SourceColumns.TRANSACTION_REFERENCE_NUMBER}`.str.upper().str.endswith('ALC', na=False)",
                        value="Allocation",
                    ),
                    Case(
                        query=f"`{SourceColumns.TRANSACTION_REFERENCE_NUMBER}`.str.upper().str.endswith('MKT', na=False)",
                        value="Market Side",
                    ),
                ],
            ),
        )

    def _transaction_details_settlement_amount(self):
        """Not Implemented"""

    def _transaction_details_settlement_amount_currency(self):
        """Not Implemented"""

    def _transaction_details_settlement_date(self):
        """Not Implemented"""

    def _transaction_details_swap_directionalities(self):
        """Not Implemented"""

    def _transaction_details_traded_quantity(self):
        """Not Implemented"""

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        """Populates with UnaVistaColumns.TRADING_CAPACITY"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=SourceColumns.TRADING_CAPACITY,
                target_attribute=RTS22Transaction.TRANSACTION_DETAILS_TRADING_CAPACITY,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """
        Populates using UnaVistaColumns.TRADING_DATE_TIME
        """
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=MapAttributeParams(
                source_attribute=TempColumns.TRADING_DATE_TIME,
                target_attribute=RTS22Transaction.TRANSACTION_DETAILS_TRADING_DATE_TIME,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_trail_id(self):
        """Not Implemented"""

    def _transaction_details_ultimate_venue(self):
        """Populates with UnaVistaColumns.VENUE"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=SourceColumns.VENUE,
                target_attribute=RTS22Transaction.TRANSACTION_DETAILS_ULTIMATE_VENUE,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_venue(self) -> pd.DataFrame:
        """Populates with UnaVistaColumns.VENUE"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=SourceColumns.VENUE,
                target_attribute=RTS22Transaction.TRANSACTION_DETAILS_VENUE,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        """Populates with UnaVistaColumns.UPFRONT_PAYMENT"""
        upfront_payments = pd.DataFrame(
            self.source_frame[SourceColumns.UP_FRONT_PAYMENT]
            .astype("string")
            .str.replace(",", "")
        )
        return MapAttribute.process(
            source_frame=upfront_payments,
            params=MapAttributeParams(
                source_attribute=SourceColumns.UP_FRONT_PAYMENT,
                target_attribute=RTS22Transaction.TRANSACTION_DETAILS_UPFRONT_PAYMENT,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        """Populates with UnaVistaColumns.UPFRONT_PAYMENT_CURRENCY"""
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ConvertMinorToMajorParams(
                source_ccy_attribute=SourceColumns.UP_FRONT_PAYMENT_CURRENCY,
                target_ccy_attribute=RTS22Transaction.TRANSACTION_DETAILS_UPFRONT_PAYMENT_CURRENCY,
            ),
        )

    def _transmission_details_order_transmission_indicator(self) -> pd.DataFrame:
        """Populates with UnaVistaColumns.ORDER_TRANSMISSION_INDICATOR"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=SourceColumns.ORDER_TRANSMISSION_INDICATOR,
                target_attribute=RTS22Transaction.TRANSMISSION_DETAILS_ORDER_TRANSMISSION_INDICATOR,
            ),
            auditor=self.auditor,
        )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=SourceColumns.TRANSACTION_REFERENCE_NUMBER,
                target_attribute=RTS22Transaction.REPORT_DETAILS_TRANSACTION_REF_NO,
            ),
            auditor=self.auditor,
        )

    def _market_identifiers_instrument(self):
        """Creates InstrumentIdentifiers results"""
        market_identifier_df = pd.concat(
            [
                self.source_frame.loc[
                    :,
                    [
                        SourceColumns.INSTRUMENT_CLASSIFICATION,
                        SourceColumns.EXPIRY_DATE,
                        SourceColumns.INSTRUMENT_ID,
                        SourceColumns.UNDERLYING_INDEX_NAME,
                        SourceColumns.UNDERLYING_INDEX_TERM,
                        SourceColumns.UNDERLYING_INSTRUMENT_ID,
                        SourceColumns.UNDERLYING_INDEX_ID,
                        SourceColumns.VENUE,
                    ],
                ],
                self.pre_process_df.loc[
                    :,
                    [
                        TempColumns.STRIKE_PRICE,
                        TempColumns.NOTIONAL_CURRENCY_1,
                        TempColumns.NOTIONAL_CURRENCY_2,
                        TempColumns.OPTION_TYPE,
                    ],
                ],
            ],
            axis=1,
        )
        market_identifier_df[TempColumns.UNDERLYING_ISIN] = ConcatAttributes.process(
            source_frame=market_identifier_df,
            params=ConcatAttributesParams(
                target_attribute=TempColumns.UNDERLYING_ISIN,
                source_attributes=[
                    SourceColumns.UNDERLYING_INSTRUMENT_ID,
                    SourceColumns.UNDERLYING_INDEX_ID,
                ],
                delimiter=Delimiters.SEMI_COLON,
            ),
        )

        asset_class_map = {
            k: v
            for k, v in ISIN_REGEX_ASSET_CLASS_MAP.items()
            if v != AssetClass.CDS_SINGLE_STOCK
        }

        return InstrumentIdentifiers.process(
            source_frame=market_identifier_df,
            params=InstrumentIdentifiersParams(
                instrument_classification_attribute=SourceColumns.INSTRUMENT_CLASSIFICATION,
                alternative_asset_class_regex_map=asset_class_map,
                currency_attribute=TempColumns.NOTIONAL_CURRENCY_1,
                notional_currency_1_attribute=TempColumns.NOTIONAL_CURRENCY_1,
                expiry_date_attribute=SourceColumns.EXPIRY_DATE,
                isin_attribute=SourceColumns.INSTRUMENT_ID,
                notional_currency_2_attribute=TempColumns.NOTIONAL_CURRENCY_2,
                option_strike_price_attribute=TempColumns.STRIKE_PRICE,
                option_type_attribute=TempColumns.OPTION_TYPE,
                underlying_index_name_attribute=SourceColumns.UNDERLYING_INDEX_NAME,
                underlying_index_term_attribute=SourceColumns.UNDERLYING_INDEX_TERM,
                underlying_index_term_value_attribute=SourceColumns.UNDERLYING_INDEX_TERM,
                underlying_isin_attribute=TempColumns.UNDERLYING_ISIN,
                venue_attribute=SourceColumns.VENUE,
                retain_task_inputs=True,
            ),
        )

    def _market_identifiers_parties(self):
        """
        Creates PartyIdentifiers results
        """
        executing_entity = add_id_or_lei(
            self.source_frame, SourceColumns.EXECUTING_ENTITY_ID
        )

        investment_decision_within_firm = add_id_or_lei(
            self.source_frame, SourceColumns.INVESTMENT_DECISION_ID
        )

        execution_within_firm = self._add_id_or_clnt_nore(
            column=SourceColumns.FIRM_EXECUTION_ID
        )

        buyer = add_id_or_lei(self.source_frame, SourceColumns.BUYER_ID)

        buyer_decision_maker = add_id_or_lei(
            self.source_frame, SourceColumns.BUYER_DECISION_MAKER_ID
        )

        seller = add_id_or_lei(self.source_frame, SourceColumns.SELLER_ID)

        seller_decision_maker = add_id_or_lei(
            self.source_frame, SourceColumns.SELLER_DECISION_MAKER_ID
        )

        counterparty = self._counterparty()

        buy_sell_side_attribute = self.target_df[
            RTS22Transaction.TRANSACTION_DETAILS_BUY_SELL_INDICATOR
        ]

        return PartyIdentifiers.process(
            pd.concat(
                [
                    executing_entity,
                    investment_decision_within_firm,
                    execution_within_firm,
                    buyer,
                    buyer_decision_maker,
                    seller,
                    seller_decision_maker,
                    counterparty,
                    buy_sell_side_attribute,
                ],
                axis=1,
            ),
            params=PartyIdentifiersParams(
                target_attribute=RTS22Transaction.MARKET_IDENTIFIERS_PARTIES,
                executing_entity_identifier=SourceColumns.EXECUTING_ENTITY_ID,
                investment_decision_within_firm_identifier=SourceColumns.INVESTMENT_DECISION_ID,
                execution_within_firm_identifier=SourceColumns.FIRM_EXECUTION_ID,
                buyer_identifier=SourceColumns.BUYER_ID,
                seller_identifier=SourceColumns.SELLER_ID,
                buyer_decision_maker_identifier=SourceColumns.BUYER_DECISION_MAKER_ID,
                seller_decision_maker_identifier=SourceColumns.SELLER_DECISION_MAKER_ID,
                counterparty_identifier=self.COUNTERPARTY_COLUMN,
                buy_sell_side_attribute=RTS22Transaction.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
            ),
        )

    def _counterparty(self):
        """Implement logic to derive counterparty field for PartyIdentifiers task"""
        deal_seller_id_mask = (
            self.source_frame[SourceColumns.TRADING_CAPACITY].str.fullmatch(
                TradingCapacity.DEAL.value, case=False, na=False
            )
            & self.source_frame[SourceColumns.BUYER_ID_TYPE].str.fullmatch(
                "L", case=False, na=False
            )
            & (
                self.source_frame[SourceColumns.EXECUTING_ENTITY_ID]
                == self.source_frame[SourceColumns.BUYER_ID]
            )
            & (
                ~self.source_frame[SourceColumns.SELLER_ID_TYPE].str.fullmatch(
                    "L", case=False, na=False
                )
                | (
                    self.source_frame[SourceColumns.BUYER_ID]
                    != self.source_frame[SourceColumns.SELLER_ID]
                )
            )
        )

        deal_buyer_id_mask = (
            self.source_frame[SourceColumns.TRADING_CAPACITY].str.fullmatch(
                TradingCapacity.DEAL.value, case=False, na=False
            )
            & self.source_frame[SourceColumns.SELLER_ID_TYPE].str.fullmatch(
                "L", case=False, na=False
            )
            & (
                self.source_frame[SourceColumns.EXECUTING_ENTITY_ID]
                == self.source_frame[SourceColumns.SELLER_ID]
            )
            & (
                ~self.source_frame[SourceColumns.BUYER_ID_TYPE].str.fullmatch(
                    "L", case=False, na=False
                )
                | (
                    self.source_frame[SourceColumns.BUYER_ID]
                    != self.source_frame[SourceColumns.SELLER_ID]
                )
            )
        )

        non_deal_seller_id_mask_1 = (
            ~self.source_frame[SourceColumns.TRADING_CAPACITY].str.fullmatch(
                TradingCapacity.DEAL.value, case=False, na=False
            )
            & self.source_frame[SourceColumns.BUYER_COUNTRY_OF_BRANCH].notnull()
            & self.source_frame[SourceColumns.SELLER_COUNTRY_OF_BRANCH].isnull()
        )
        non_deal_seller_id_mask_2 = (
            ~self.source_frame[SourceColumns.TRADING_CAPACITY].str.fullmatch(
                TradingCapacity.DEAL.value, case=False, na=False
            )
            & self.source_frame[SourceColumns.BUYER_COUNTRY_OF_BRANCH].isnull()
            & self.source_frame[SourceColumns.SELLER_COUNTRY_OF_BRANCH].isnull()
            & self.source_frame[SourceColumns.BUYER_ID].str.fullmatch(
                "INTC", case=False, na=False
            )
            & ~self.source_frame[SourceColumns.SELLER_ID].str.fullmatch(
                "INTC", case=False, na=False
            )
        )
        non_deal_buyer_id_mask_1 = (
            ~self.source_frame[SourceColumns.TRADING_CAPACITY].str.fullmatch(
                TradingCapacity.DEAL.value, case=False, na=False
            )
            & self.source_frame[SourceColumns.SELLER_COUNTRY_OF_BRANCH].notnull()
            & self.source_frame[SourceColumns.BUYER_COUNTRY_OF_BRANCH].isnull()
        )
        non_deal_buyer_id_mask_2 = (
            ~self.source_frame[SourceColumns.TRADING_CAPACITY].str.fullmatch(
                TradingCapacity.DEAL.value, case=False, na=False
            )
            & self.source_frame[SourceColumns.BUYER_COUNTRY_OF_BRANCH].isnull()
            & self.source_frame[SourceColumns.SELLER_COUNTRY_OF_BRANCH].isnull()
            & self.source_frame[SourceColumns.SELLER_ID].str.fullmatch(
                "INTC", case=False, na=False
            )
            & ~self.source_frame[SourceColumns.BUYER_ID].str.fullmatch(
                "INTC", case=False, na=False
            )
        )
        seller_mask = (
            deal_seller_id_mask | non_deal_seller_id_mask_1 | non_deal_seller_id_mask_2
        )
        seller_index = self.source_frame[seller_mask].index

        buyer_mask = (
            deal_buyer_id_mask | non_deal_buyer_id_mask_1 | non_deal_buyer_id_mask_2
        )
        buyer_index = self.source_frame[buyer_mask].index

        counterparties = pd.concat(
            [
                pd.Series(
                    index=seller_index,
                    data=self.source_frame[seller_mask][SourceColumns.SELLER_ID],
                    name=self.COUNTERPARTY_COLUMN,
                ),
                pd.Series(
                    index=buyer_index,
                    data=self.source_frame[buyer_mask][SourceColumns.BUYER_ID],
                    name=self.COUNTERPARTY_COLUMN,
                ),
            ]
        )

        if counterparties.empty:
            return pd.Series(
                data=pd.NA, name=self.COUNTERPARTY_COLUMN, index=self.source_frame.index
            )

        return add_id_or_lei(pd.DataFrame(counterparties), self.COUNTERPARTY_COLUMN)

    def _market_identifiers(self) -> pd.DataFrame:
        """
        Merges InstrumentIdentifiers and PartyIdentifiers results

        Assumes _market_identifiers_instrument() and _market_identifiers_parties()
        have run successfully
        """

        market_identifier_columns = pd.DataFrame(index=self.source_frame.index)

        market_identifier_columns.loc[
            :, RTS22Transaction.MARKET_IDENTIFIERS_INSTRUMENT
        ] = self.target_df.loc[:, RTS22Transaction.MARKET_IDENTIFIERS_INSTRUMENT]
        market_identifier_columns.loc[
            :, RTS22Transaction.MARKET_IDENTIFIERS_PARTIES
        ] = self.target_df.loc[:, RTS22Transaction.MARKET_IDENTIFIERS_PARTIES]

        return MergeMarketIdentifiers.process(
            market_identifier_columns,
            params=MergeMarketIdentifiersParams(
                identifiers_path=RTS22Transaction.MARKET_IDENTIFIERS,
                instrument_path=RTS22Transaction.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=RTS22Transaction.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _add_id_or_clnt_nore(self, column: str) -> pd.Series:
        """
        Aux function to generate the execution within firm values.
        Checks if provided column matches with `NORE`:
            if so replaces that column value with `clnt:nore`
            else adds `id` as prefix
        :return: pd.Series with updated column
        """
        execution_within_firm = pd.Series(
            data=pd.NA, index=self.source_frame.index, name=column
        )
        nore_firm_execution_id = self.source_frame.loc[:, column].str.fullmatch(
            "NORE", case=False, na=False
        )
        execution_within_firm.loc[~nore_firm_execution_id] = (
            "id:" + self.source_frame.loc[~nore_firm_execution_id, column].values
        )
        execution_within_firm.loc[nore_firm_execution_id] = "clnt:nore"
        return execution_within_firm

    def _populate_transaction_details_swap_directionalities(self) -> pd.DataFrame:
        """
        https://steeleye.atlassian.net/browse/ON-3407
        """
        return MapSwapDirectionalities.process(
            source_frame=self.pre_process_df,
            params=MapSwapDirectionalitiesParams(
                source_index_classification_attribute=TempColumns.UV_INDEX_CLASSIFICATION,
                source_instrument_classification_attribute=TempColumns.UV_INSTRUMENT_CLASSIFICATION,
                swapin_pattern="swapin",
                swapout_pattern="swapout",
                target_attribute=In.SWAP_DIRECTIONALITIES,
            ),
        )

    def _temp_trading_date_time(self) -> pd.DataFrame:
        """
        Populates using UnaVistaColumns.TRADING_DATE_TIME
        """
        extended_format_result = ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.TRADING_DATE_TIME,
                target_attribute=TempColumns.TRADING_DATE_TIME,
                convert_to=ConvertTo.DATETIME,
                source_attribute_format="%Y-%m-%dT%H:%M:%S.%fZ",
            ),
        )
        shorter_format_result = ConvertDatetime.process(
            source_frame=self.source_frame[extended_format_result.isnull().values],
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.TRADING_DATE_TIME,
                target_attribute=TempColumns.TRADING_DATE_TIME,
                convert_to=ConvertTo.DATETIME,
                source_attribute_format="%Y-%m-%dT%H:%M:%S",
            ),
        )
        return pd.concat(
            [
                extended_format_result.dropna(axis=0),
                shorter_format_result.dropna(axis=0),
            ]
        )

    def _temp_currencies_and_prices(self) -> pd.DataFrame:
        """
        Concatenation of several currency and price converted fields to use downstream
        """
        return pd.concat(
            [
                ConvertMinorToMajor.process(
                    source_frame=self.source_frame,
                    params=ConvertMinorToMajorParams(
                        source_ccy_attribute=SourceColumns.NOTIONAL_CURRENCY_1,
                        target_ccy_attribute=TempColumns.NOTIONAL_CURRENCY_1,
                    ),
                ),
                ConvertMinorToMajor.process(
                    source_frame=self.source_frame,
                    params=ConvertMinorToMajorParams(
                        source_ccy_attribute=SourceColumns.NOTIONAL_CURRENCY_2,
                        target_ccy_attribute=TempColumns.NOTIONAL_CURRENCY_2,
                    ),
                ),
                ConvertMinorToMajor.process(
                    source_frame=self.source_frame,
                    params=ConvertMinorToMajorParams(
                        source_ccy_attribute=SourceColumns.STRIKE_PRICE_CURRENCY,
                        target_ccy_attribute=TempColumns.STRIKE_PRICE_CURRENCY,
                    ),
                ),
                ConvertMinorToMajor.process(
                    source_frame=self.source_frame,
                    params=ConvertMinorToMajorParams(
                        source_ccy_attribute=SourceColumns.STRIKE_PRICE_CURRENCY,
                        source_price_attribute=SourceColumns.STRIKE_PRICE,
                        target_price_attribute=TempColumns.STRIKE_PRICE,
                    ),
                ),
            ],
            axis=1,
        )

    def _temp_instrument_full_name(self) -> pd.DataFrame:
        """
        Instrument Name for Instrument Fallback
        Uses SourceColumns.INSTRUMENT_NAME when present and SourceColumns.INSTRUMENT_ID when not
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=TempColumns.INSTRUMENT_FULL_NAME,
                cases=[
                    Case(
                        query=f"`{SourceColumns.INSTRUMENT_NAME}`.notnull()",
                        attribute=SourceColumns.INSTRUMENT_NAME,
                    ),
                    Case(
                        query=f"`{SourceColumns.INSTRUMENT_NAME}`.isnull()",
                        attribute=SourceColumns.INSTRUMENT_ID,
                    ),
                ],
            ),
        )

    def _temp_instrument_classification(self) -> pd.DataFrame:
        """
        Derives Instrument Classification for Instrument Fallback.
        Specs: https://steeleye.atlassian.net/wiki/spaces/IN/pages/2703294465/RTS22+UnaVista+Handler#Instrument-Fallback
        :return:
        """
        has_classification = f"(`{SourceColumns.INSTRUMENT_CLASSIFICATION}`.notnull())"
        bond = f"(`{SourceColumns.INTERNAL_CLIENT_IDENTIFICATION}`.str.match('.*bond.*', case=False, na=False))"
        cds = f"(`{SourceColumns.INTERNAL_CLIENT_IDENTIFICATION}`.str.match('.*cds.*', case=False, na=False))"
        interest_rate_swap = f"(`{SourceColumns.INTERNAL_CLIENT_IDENTIFICATION}`.str.match('.*interest rate swap.*', case=False, na=False))"
        fx_forward = f"(`{SourceColumns.INTERNAL_CLIENT_IDENTIFICATION}`.str.match('.*fx forward.*', case=False, na=False))"
        fx = f"(`{SourceColumns.INTERNAL_CLIENT_IDENTIFICATION}`.str.match('.*fx.*', case=False, na=False))"
        fwd = f"(`{SourceColumns.INTERNAL_CLIENT_IDENTIFICATION}`.str.match('.*fwd.*', case=False, na=False))"

        return MapConditional.process(
            source_frame=self.source_frame.loc[
                :,
                [
                    SourceColumns.INSTRUMENT_CLASSIFICATION,
                    SourceColumns.INTERNAL_CLIENT_IDENTIFICATION,
                ],
            ].astype("string"),
            params=MapConditionalParams(
                target_attribute=TempColumns.INSTRUMENT_CLASSIFICATION,
                cases=[
                    Case(
                        query=f"{has_classification}",
                        attribute=SourceColumns.INSTRUMENT_CLASSIFICATION,
                    ),
                    Case(
                        query=f"~{has_classification} & {bond}",
                        value="DBXXXX",
                    ),
                    Case(
                        query=f"~{has_classification} & {cds}",
                        value="SCUXXX",
                    ),
                    Case(
                        query=f"~{has_classification} & {interest_rate_swap}",
                        value="SRXXXX",
                    ),
                    Case(
                        query=f"~{has_classification} & ({fx_forward} | ({fx} & {fwd}))",
                        value="SRXXXX",
                    ),
                ],
            ),
        )

    def _temp_option_type(self) -> pd.DataFrame:
        """
        Populates a temporary Option Type from SourceColumns.OPTION_TYPE
        """
        return MapValue.process(
            source_frame=self.source_frame,
            params=MapValueParams(
                source_attribute=SourceColumns.OPTION_TYPE,
                target_attribute=TempColumns.OPTION_TYPE,
                value_map={
                    "CALL": OptionType.CALL.value,
                    "PUTO": OptionType.PUTO.value,
                    "PUT": OptionType.PUTO.value,
                    "OTHR": OptionType.OTHR.value,
                },
                case_insensitive=True,
            ),
        )
