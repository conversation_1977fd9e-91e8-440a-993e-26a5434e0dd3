import pandas as pd
from se_core_tasks.map.map_conditional import Case
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI
from swarm_tasks.steeleye.generic.get_tenant_lei import Params as GetTenantLEIParams
from swarm_tasks.steeleye.tr.feed.enfusion.v2.enfusion_v2_tr_transformations import (
    EnfusionV2Transformations,
)
from swarm_tasks.steeleye.tr.feed.enfusion.v2.static import ExecutionSourceColumns
from swarm_tasks.steeleye.tr.feed.enfusion.v2.static import TempColumns
from swarm_tasks.steeleye.tr.static import RTS22Transaction
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as MapConditionalParams
from swarm_tasks.transform.steeleye.tr.identifiers.party_identifiers import (
    Params as PartyIdentifiersParams,
)
from swarm_tasks.transform.steeleye.tr.identifiers.party_identifiers import (
    PartyIdentifiers,
)


class ThornbridgeEnfusionV2Transformations(EnfusionV2Transformations):
    """
    Thornbridge Override for Enfusion V2
    """

    def _temp_executing_entity(self) -> pd.DataFrame:
        """
        Maps to Tenant LEI from Account Firm
        """

        tenant_lei = GetTenantLEI.process(
            source_frame=self.source_frame,
            params=GetTenantLEIParams(
                target_lei_column=TempColumns.EXECUTING_ENTITY_WITHOUT_PREFIX
            ),
        )

        return pd.concat(
            objs=[
                tenant_lei,
                pd.DataFrame(
                    data=PartyPrefix.LEI
                    + tenant_lei.loc[
                        :, TempColumns.EXECUTING_ENTITY_WITHOUT_PREFIX
                    ].values,
                    index=self.source_frame.index,
                    columns=[TempColumns.EXECUTING_ENTITY],
                ),
            ],
            axis=1,
        )

    @staticmethod
    def get_buyer_seller_first_case_column():
        """
        Overriding Executing Entity with EXECUTION_ENTITY_IDENTIFICATION_CODE for some buyer seller conditions
        """
        return ExecutionSourceColumns.EXECUTION_ENTITY_IDENTIFICATION_CODE

    def _temp_party_ids_buyer_decision_maker(self) -> pd.DataFrame:
        """
        Maps to Tenant LEI from Account Firm when BUY
        """

        return MapConditional.process(
            source_frame=self.pre_process_df,
            params=MapConditionalParams(
                target_attribute=TempColumns.BUYER_DECISION_MAKER,
                cases=[
                    Case(
                        query=f"`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.BUYI.value}'",
                        attribute=TempColumns.EXECUTING_ENTITY,
                    )
                ],
            ),
        )

    def _temp_party_ids_seller_decision_maker(self) -> pd.DataFrame:
        """
        Maps to Tenant LEI from Account Firm when SELL
        """

        return MapConditional.process(
            source_frame=self.pre_process_df,
            params=MapConditionalParams(
                target_attribute=TempColumns.SELLER_DECISION_MAKER,
                cases=[
                    Case(
                        query=f"`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.SELL.value}'",
                        attribute=TempColumns.EXECUTING_ENTITY,
                    )
                ],
            ),
        )

    def _market_identifiers_parties(self):
        """
        Generates PartyIdentifiers to be used in linking parties downstream

        Maps Trader to Investment Decision Maker
        """

        return PartyIdentifiers.process(
            source_frame=self.pre_process_df,
            params=PartyIdentifiersParams(
                target_attribute=RTS22Transaction.MARKET_IDENTIFIERS_PARTIES,
                executing_entity_identifier=TempColumns.EXECUTING_ENTITY,
                investment_decision_within_firm_identifier=TempColumns.TRADER,
                execution_within_firm_identifier=TempColumns.TRADER,
                counterparty_identifier=TempColumns.COUNTERPARTY,
                buy_sell_side_attribute=TempColumns.BUY_SELL_INDICATOR,
                buyer_identifier=TempColumns.BUYER,
                seller_identifier=TempColumns.SELLER,
                buyer_decision_maker_identifier=TempColumns.BUYER_DECISION_MAKER,
                seller_decision_maker_identifier=TempColumns.SELLER_DECISION_MAKER,
                trader_identifier=TempColumns.TRADER,
            ),
        )
