import pandas as pd
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.reference import CrossIndicator
from se_elastic_schema.static.reference import InstrumentIdCodeType
from se_elastic_schema.static.reference import PersonStructureInstrumentAssetClass

from swarm_tasks.abstractions.transformations.reference.instrument.steeleye.abstract_steeleye_instrument_transformations import (
    AbstractSteelEyeInstrumentTransformations,
)
from swarm_tasks.abstractions.transformations.reference.instrument.steeleye.abstract_steeleye_instrument_transformations import (
    SEInstrumentColumns,
)
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ConcatAttributesParams,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as MapAttributeParams
from swarm_tasks.transform.map.map_static import MapStatic
from swarm_tasks.transform.map.map_static import Params as MapStaticParams


class Prefix:
    CFD = "__cfd."
    SB = "__sb."


class Suffix:
    SPACE_CFD = " CFD"
    SPACE_SB = " SB"
    UNDERSCORE_CFD = "_CFD"
    UNDERSCORE_LOWER_CFD = "_cfd"
    UNDERSCORE_LOWER_SB = "_sb"
    UNDERSCORE_SB = "_SB"


class TempColumns:
    CONCAT_INSTR_ID_CODE_NOTIONAL_CCY = "__concat_instr_id_code_and_notional_ccy__"


class CommodityStaticValues:
    CASH = "Cash"
    CD = "CD"
    CFD = "CFD"
    COMMODITY = "CO"
    CONTRACT_FOR_DIFFERENCE = "Contract for difference"
    EQUITY = "Equity"
    EQ = "EQ"
    EQUITY_FORWARD = "EquityForward"
    FORWARDS = "Forwards"
    FUTURES = "Futures"
    JEFXCC = "JEFXCC"
    JEFXSC = "JEFXSC"
    NOT_APPLICABLE_UNDEFINED = "Not Applicable/Undefined"
    ONE = 1
    OTHER_DERIVATIVES = "Swaps and other equity derivatives"
    SB = "SB"
    SPREAD_BET = "Spread-bet"
    STEELEYE_INSTRUMENT = "SteelEyeInstrument"
    XXXX_VENUE = "XXXX"


class CFDSBTransformerIndexTransformations(AbstractSteelEyeInstrumentTransformations):
    def pre_process(self):
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._concat_instr_id_code_and_notional_ccy(),
            ],
            axis=1,
        )

    def _cfi_attribute_1(self):
        return MapStatic.process(
            source_frame=self.source_frame,
            params=MapStaticParams(
                target_attribute=SEInstrumentColumns.CFI_ATTRIBUTE_1,
                target_value=CommodityStaticValues.FUTURES,
            ),
        )

    def _cfi_attribute_2(self):
        return MapStatic.process(
            source_frame=self.source_frame,
            params=MapStaticParams(
                target_attribute=SEInstrumentColumns.CFI_ATTRIBUTE_2,
                target_value=CommodityStaticValues.NOT_APPLICABLE_UNDEFINED,
            ),
        )

    def _cfi_attribute_3(self):
        return pd.concat(
            [
                MapStatic.process(
                    source_frame=self.source_frame,
                    params=MapStaticParams(
                        target_attribute=Prefix.CFD
                        + SEInstrumentColumns.CFI_ATTRIBUTE_3,
                        target_value=CommodityStaticValues.CONTRACT_FOR_DIFFERENCE,
                    ),
                ),
                MapStatic.process(
                    source_frame=self.source_frame,
                    params=MapStaticParams(
                        target_attribute=Prefix.SB
                        + SEInstrumentColumns.CFI_ATTRIBUTE_3,
                        target_value=CommodityStaticValues.SPREAD_BET,
                    ),
                ),
            ],
            axis=1,
        )

    def _cfi_attribute_4(self):
        return MapStatic.process(
            source_frame=self.source_frame,
            params=MapStaticParams(
                target_attribute=SEInstrumentColumns.CFI_ATTRIBUTE_4,
                target_value=CommodityStaticValues.CASH,
            ),
        )

    def _cfi_category(self):
        return MapStatic.process(
            source_frame=self.source_frame,
            params=MapStaticParams(
                target_attribute=SEInstrumentColumns.CFI_CATEGORY,
                target_value=CommodityStaticValues.FORWARDS,
            ),
        )

    def _cfi_group(self):
        return MapStatic.process(
            source_frame=self.source_frame,
            params=MapStaticParams(
                target_attribute=SEInstrumentColumns.CFI_GROUP,
                target_value=CommodityStaticValues.EQUITY,
            ),
        )

    def _commodities_or_emission_allowance_derivative_ind(self):
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                target_attribute=SEInstrumentColumns.COMMODITIES_OR_EMISSION_ALLOWANCE_DERIVATIVE_IND,
                source_attribute=SEInstrumentColumns.COMMODITIES_OR_EMISSION_ALLOWANCE_DERIVATIVE_IND,
            ),
            auditor=self.auditor,
        )

    def _derivative_delivery_type(self):
        return MapStatic.process(
            source_frame=self.source_frame,
            params=MapStaticParams(
                target_attribute=Prefix.CFD
                + SEInstrumentColumns.DERIVATIVE_DELIVERY_TYPE,
                target_value=CrossIndicator.CASH.value,
            ),
        )

    def _derivative_price_multiplier(self):
        return MapStatic.process(
            source_frame=self.source_frame,
            params=MapStaticParams(
                target_attribute=SEInstrumentColumns.DERIVATIVE_PRICE_MULTIPLIER,
                target_value=CommodityStaticValues.ONE,
            ),
        )

    def _derivative_underlying_instruments_underlying_instrument_classification(self):
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                target_attribute=SEInstrumentColumns.DERIVATIVE_UNDERLYING_INSTRUMENTS_UNDERLYING_INSTRUMENT_CLASSIFICATION,
                source_attribute=SEInstrumentColumns.INSTRUMENT_CLASSIFICATION,
            ),
            auditor=self.auditor,
        )

    def _derivative_underlying_instruments_underlying_instrument_code(self):
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                target_attribute=SEInstrumentColumns.DERIVATIVE_UNDERLYING_INSTRUMENTS_UNDERLYING_INSTRUMENT_CODE,
                source_attribute=SEInstrumentColumns.INSTRUMENT_ID_CODE,
            ),
            auditor=self.auditor,
        )

    def _ext_alternative_instrument_identifier(self):
        return pd.concat(
            [
                MapAttribute.process(
                    source_frame=self.source_frame,
                    params=MapAttributeParams(
                        target_attribute=Prefix.CFD
                        + SEInstrumentColumns.EXT_ALTERNATIVE_INSTRUMENT_IDENTIFIER,
                        source_attribute=SEInstrumentColumns.INSTRUMENT_ID_CODE,
                        prefix=CommodityStaticValues.XXXX_VENUE,
                        suffix=CommodityStaticValues.CFD,
                    ),
                    auditor=self.auditor,
                ),
                MapAttribute.process(
                    source_frame=self.source_frame,
                    params=MapAttributeParams(
                        target_attribute=Prefix.SB
                        + SEInstrumentColumns.EXT_ALTERNATIVE_INSTRUMENT_IDENTIFIER,
                        source_attribute=SEInstrumentColumns.INSTRUMENT_ID_CODE,
                        prefix=CommodityStaticValues.XXXX_VENUE,
                        suffix=CommodityStaticValues.SB,
                    ),
                    auditor=self.auditor,
                ),
            ],
            axis=1,
        )

    def _ext_best_ex_asset_class_main(self):
        return MapStatic.process(
            source_frame=self.source_frame,
            params=MapStaticParams(
                target_attribute=SEInstrumentColumns.EXT_BEST_EX_ASSET_CLASS_MAIN,
                target_value=PersonStructureInstrumentAssetClass.EQUITY_DERIVATIVES.value,
            ),
        )

    def _ext_best_ex_asset_class_sub(self):
        return MapStatic.process(
            source_frame=self.source_frame,
            params=MapStaticParams(
                target_attribute=SEInstrumentColumns.EXT_BEST_EX_ASSET_CLASS_SUB,
                target_value=CommodityStaticValues.OTHER_DERIVATIVES,
            ),
        )

    def _ext_emir_eligible(self):
        return MapStatic.process(
            source_frame=self.source_frame,
            params=MapStaticParams(
                target_attribute=SEInstrumentColumns.EXT_EMIR_ELIGIBLE,
                target_value=True,
            ),
        )

    def _ext_instrument_id_code_type(self):
        return MapStatic.process(
            source_frame=self.source_frame,
            params=MapStaticParams(
                target_attribute=SEInstrumentColumns.EXT_INSTRUMENT_ID_CODE_TYPE,
                target_value=InstrumentIdCodeType.OTHR.value,
            ),
        )

    def _ext_instrument_unique_identifier(self):
        return pd.concat(
            [
                MapAttribute.process(
                    source_frame=self.pre_process_df,
                    params=MapAttributeParams(
                        target_attribute=Prefix.CFD
                        + SEInstrumentColumns.EXT_INSTRUMENT_UNIQUE_IDENTIFIER,
                        source_attribute=TempColumns.CONCAT_INSTR_ID_CODE_NOTIONAL_CCY,
                        prefix=CommodityStaticValues.XXXX_VENUE,
                        suffix=CommodityStaticValues.CFD,
                    ),
                    auditor=self.auditor,
                ),
                MapAttribute.process(
                    source_frame=self.pre_process_df,
                    params=MapAttributeParams(
                        target_attribute=Prefix.SB
                        + SEInstrumentColumns.EXT_INSTRUMENT_UNIQUE_IDENTIFIER,
                        source_attribute=TempColumns.CONCAT_INSTR_ID_CODE_NOTIONAL_CCY,
                        prefix=CommodityStaticValues.XXXX_VENUE,
                        suffix=CommodityStaticValues.SB,
                    ),
                    auditor=self.auditor,
                ),
            ],
            axis=1,
        )

    def _ext_price_notation(self):
        return MapStatic.process(
            source_frame=self.source_frame,
            params=MapStaticParams(
                target_attribute=SEInstrumentColumns.EXT_PRICE_NOTATION,
                target_value=PriceNotation.MONE.value,
            ),
        )

    def _ext_pricing_references_ric(self):
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                target_attribute=SEInstrumentColumns.EXT_PRICING_REFERENCES_RIC,
                source_attribute=SEInstrumentColumns.EXT_PRICING_REFERENCES_RIC,
            ),
            auditor=self.auditor,
        )

    def _ext_quantity_notation(self):
        return MapStatic.process(
            source_frame=self.source_frame,
            params=MapStaticParams(
                target_attribute=SEInstrumentColumns.EXT_QUANTITY_NOTATION,
                target_value=QuantityNotation.UNIT.value,
            ),
        )

    def _ext_underlying_instruments_derivative_expiry_date(self):
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                target_attribute=SEInstrumentColumns.EXT_UNDERLYING_INSTRUMENTS_DERIVATIVE_EXPIRY_DATE,
                source_attribute=SEInstrumentColumns.DERIVATIVE_EXPIRY_DATE,
            ),
            auditor=self.auditor,
        )

    def _ext_underlying_instruments_instrument_id_code(self):
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                target_attribute=SEInstrumentColumns.EXT_UNDERLYING_INSTRUMENTS_INSTRUMENT_ID_CODE,
                source_attribute=SEInstrumentColumns.INSTRUMENT_ID_CODE,
            ),
            auditor=self.auditor,
        )

    def _ext_underlying_instruments_venue_trading_venue(self):
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                target_attribute=SEInstrumentColumns.EXT_UNDERLYING_INSTRUMENTS_VENUE_TRADING_VENUE,
                source_attribute=SEInstrumentColumns.VENUE_TRADING_VENUE,
            ),
            auditor=self.auditor,
        )

    def _instrument_classification(self):
        return pd.concat(
            [
                MapStatic.process(
                    source_frame=self.source_frame,
                    params=MapStaticParams(
                        target_attribute=Prefix.CFD
                        + SEInstrumentColumns.INSTRUMENT_CLASSIFICATION,
                        target_value=CommodityStaticValues.JEFXCC,
                    ),
                ),
                MapStatic.process(
                    source_frame=self.source_frame,
                    params=MapStaticParams(
                        target_attribute=Prefix.SB
                        + SEInstrumentColumns.INSTRUMENT_CLASSIFICATION,
                        target_value=CommodityStaticValues.JEFXSC,
                    ),
                ),
            ],
            axis=1,
        )

    def _instrument_classification_emir_asset_class(self):
        return MapStatic.process(
            source_frame=self.source_frame,
            params=MapStaticParams(
                target_attribute=SEInstrumentColumns.INSTRUMENT_CLASSIFICATION_EMIR_ASSET_CLASS,
                target_value=CommodityStaticValues.EQ,
            ),
        )

    def _instrument_classification_emir_contract_type(self):
        return pd.concat(
            [
                MapStatic.process(
                    source_frame=self.source_frame,
                    params=MapStaticParams(
                        target_attribute=Prefix.CFD
                        + SEInstrumentColumns.INSTRUMENT_CLASSIFICATION_EMIR_CONTRACT_TYPE,
                        target_value=CommodityStaticValues.CD,
                    ),
                ),
                MapStatic.process(
                    source_frame=self.source_frame,
                    params=MapStaticParams(
                        target_attribute=Prefix.SB
                        + SEInstrumentColumns.INSTRUMENT_CLASSIFICATION_EMIR_CONTRACT_TYPE,
                        target_value=CommodityStaticValues.SB,
                    ),
                ),
            ],
            axis=1,
        )

    def _instrument_classification_emir_product_type(self):
        return pd.concat(
            [
                MapStatic.process(
                    source_frame=self.source_frame,
                    params=MapStaticParams(
                        target_attribute=Prefix.CFD
                        + SEInstrumentColumns.INSTRUMENT_CLASSIFICATION_EMIR_PRODUCT_YPE,
                        target_value=CommodityStaticValues.CFD,
                    ),
                ),
                MapStatic.process(
                    source_frame=self.source_frame,
                    params=MapStaticParams(
                        target_attribute=Prefix.SB
                        + SEInstrumentColumns.INSTRUMENT_CLASSIFICATION_EMIR_PRODUCT_YPE,
                        target_value=CommodityStaticValues.EQUITY_FORWARD,
                    ),
                ),
            ],
            axis=1,
        )

    def _instrument_full_name(self):
        return pd.concat(
            [
                MapAttribute.process(
                    source_frame=self.source_frame,
                    params=MapAttributeParams(
                        target_attribute=Prefix.CFD
                        + SEInstrumentColumns.INSTRUMENT_FULL_NAME,
                        source_attribute=SEInstrumentColumns.INSTRUMENT_FULL_NAME,
                        suffix=Suffix.SPACE_CFD,
                    ),
                    auditor=self.auditor,
                ),
                MapAttribute.process(
                    source_frame=self.source_frame,
                    params=MapAttributeParams(
                        target_attribute=Prefix.SB
                        + SEInstrumentColumns.INSTRUMENT_FULL_NAME,
                        source_attribute=SEInstrumentColumns.INSTRUMENT_FULL_NAME,
                        suffix=Suffix.SPACE_SB,
                    ),
                    auditor=self.auditor,
                ),
            ],
            axis=1,
        )

    def _meta_model(self):
        return MapStatic.process(
            source_frame=self.source_frame,
            params=MapStaticParams(
                target_attribute=SEInstrumentColumns.META_MODEL,
                target_value=CommodityStaticValues.STEELEYE_INSTRUMENT,
            ),
        )

    def _notional_currency_1(self):
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                target_attribute=SEInstrumentColumns.NOTIONAL_CURRENCY_1,
                source_attribute=SEInstrumentColumns.NOTIONAL_CURRENCY_1,
            ),
            auditor=self.auditor,
        )

    def _source_key(self):
        return pd.concat(
            [
                MapAttribute.process(
                    source_frame=self.source_frame,
                    params=MapAttributeParams(
                        target_attribute=Prefix.CFD + SEInstrumentColumns.SOURCE_KEY,
                        source_attribute=SEInstrumentColumns.SOURCE_KEY,
                        suffix=Suffix.UNDERSCORE_LOWER_CFD,
                    ),
                    auditor=self.auditor,
                ),
                MapAttribute.process(
                    source_frame=self.source_frame,
                    params=MapAttributeParams(
                        target_attribute=Prefix.SB + SEInstrumentColumns.SOURCE_KEY,
                        source_attribute=SEInstrumentColumns.SOURCE_KEY,
                        suffix=Suffix.UNDERSCORE_LOWER_SB,
                    ),
                    auditor=self.auditor,
                ),
            ],
            axis=1,
        )

    def _venue_financial_instrument_short_name(self):
        return pd.concat(
            [
                MapAttribute.process(
                    source_frame=self.source_frame,
                    params=MapAttributeParams(
                        target_attribute=Prefix.CFD
                        + SEInstrumentColumns.VENUE_FINANCIAL_INSTRUMENT_SHORT_NAME,
                        source_attribute=SEInstrumentColumns.VENUE_FINANCIAL_INSTRUMENT_SHORT_NAME,
                        suffix=Suffix.SPACE_CFD,
                    ),
                    auditor=self.auditor,
                ),
                MapAttribute.process(
                    source_frame=self.source_frame,
                    params=MapAttributeParams(
                        target_attribute=Prefix.SB
                        + SEInstrumentColumns.VENUE_FINANCIAL_INSTRUMENT_SHORT_NAME,
                        source_attribute=SEInstrumentColumns.VENUE_FINANCIAL_INSTRUMENT_SHORT_NAME,
                        suffix=Suffix.SPACE_SB,
                    ),
                    auditor=self.auditor,
                ),
            ],
            axis=1,
        )

    def _venue_trading_venue(self):
        return MapStatic.process(
            source_frame=self.source_frame,
            params=MapStaticParams(
                target_attribute=SEInstrumentColumns.VENUE_TRADING_VENUE,
                target_value=CommodityStaticValues.XXXX_VENUE,
            ),
        )

    # Auxiliary methods

    def _concat_instr_id_code_and_notional_ccy(self):
        return ConcatAttributes.process(
            source_frame=self.source_frame,
            params=ConcatAttributesParams(
                target_attribute=TempColumns.CONCAT_INSTR_ID_CODE_NOTIONAL_CCY,
                source_attributes=[
                    SEInstrumentColumns.INSTRUMENT_ID_CODE,
                    SEInstrumentColumns.NOTIONAL_CURRENCY_1,
                ],
            ),
        )
