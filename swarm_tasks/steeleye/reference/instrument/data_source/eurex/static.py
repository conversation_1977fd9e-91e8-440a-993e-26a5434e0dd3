from swarm_tasks.utilities.task_utils import BaseColumns


class EurexXMLColumns(BaseColumns):
    AID = "AID"
    ALT_ID = "@AltID"
    ALT_ID_SRC = "@AltIDSrc"
    CFI = "@CFI"
    CNTRCT_DT = "@CntrctDt"
    DESC = "@Desc"
    EXERSTYLE = "@ExerStyle"
    ID = "@ID"
    INSTRMT = "Instrmt"
    INSTRMTEXT = "InstrmtExt"
    INSTRATTRIB = "InstrAttrib"
    MULT = "@Mult"
    PUT_CALL = "@PutCall"
    SECTYP = "@SecTyp"
    SETTLMETH = "@SettlMeth"
    STRKPX = "@StrkPx"
    TYP = "@Typ"
    VAL = "@Val"
    MKT_SEG_GRP = "MktSegGrp"
    MKT_SEG_ID = "@MktSegID"


class SecurityTypeRegex:
    OPTION = "^OPT$"
    FUTURE = "^FUT$"


EUR = "EUR"
XEUR = "XEUR"
