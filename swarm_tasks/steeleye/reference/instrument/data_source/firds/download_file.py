import re
from enum import Enum
from pathlib import Path
from typing import Optional

from prefect import context
from se_core_tasks.core.core_dataclasses import S3Action
from se_core_tasks.core.core_dataclasses import S3File
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask

from swarm_tasks.utilities import requests_wrapper
from swarm_tasks.utilities import s3


logger = context.get("logger")


class FileType(str, Enum):
    FCA = "fca"
    ESMA = "esma"


class S3Meta:
    bucket_name = "scrapes.srp.steeleye.co"
    key_name_fmt = "feeds/task/firds/{type}/{file_date}/"
    s3_prefix = "s3://"


class Params(BaseParams):
    type: FileType


class DownloadFile(BaseTask):
    """
    For given URL check in s3 if it's downloaded else download the file

    Params:
        type: esma/fca - type of firds file
        url: file url
    """

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        resources: Optional[BaseResources] = None,
        url: Optional[str] = None,
        **kwargs,
    ) -> Optional[S3File]:

        global logger
        logger = context.get("logger")

        if not url:
            error = "missing file URL"
            logger.error(error)
            raise AttributeError(error)

        logger.info(f"URL is {url}")
        file_name = url.split("/")[-1]
        file_date = self._parse_file_date(file_name=file_name)

        s3_key = (
            S3Meta.key_name_fmt.format(type=params.type, file_date=file_date)
            + file_name
        )

        logger.info(f"checking if s3 key `{s3_key}` exists for url `{url}`")

        if s3.does_key_exists(bucket=S3Meta.bucket_name, key=s3_key):
            message = (
                f"`{file_name}` is downloaded and available in s3 location: "
                f"{S3Meta.s3_prefix}{S3Meta.bucket_name}/{s3_key}"
            )
            logger.info(message)
            return

        target_dir: Path = context.swarm.get("targets_dir")
        local_file_path = target_dir.joinpath(file_name)
        local_file_path.parent.mkdir(exist_ok=True, parents=True)

        logger.info(f"downloading file `{file_name}`")

        response = requests_wrapper.get(url, stream=True)
        response.raise_for_status()

        with open(local_file_path.as_posix(), "wb") as fh:
            for content_ in response.iter_content(1000 * 1000):
                fh.write(content_)

        logger.info(f"successfully downloaded file `{file_name}`")

        s3_file = S3File(
            file_path=local_file_path,
            action=S3Action.UPLOAD,
            bucket_name=S3Meta.bucket_name,
            key_name=s3_key,
            bytes=local_file_path.stat().st_size,
        )

        return s3_file

    @staticmethod
    def _parse_file_date(file_name: str) -> str:
        """
        parse firds file name to get file date

        args:
            file_name: name of the firds file
                       example: DLTINS_20210106_01of01.zip
                                FULINS_D_20210102_01of02.zip
        returns:
            file date
        """
        return re.match(r"^.*_(?P<date>\d{8})_.*.zip$", file_name).group("date")
