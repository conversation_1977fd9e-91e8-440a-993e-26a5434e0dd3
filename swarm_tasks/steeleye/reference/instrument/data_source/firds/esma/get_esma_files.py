import json
from datetime import datetime
from typing import List
from typing import Optional

from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask

from swarm_tasks.utilities import requests_wrapper


class Params(BaseParams):
    download_date: Optional[str] = None
    url: str = "https://registers.esma.europa.eu/solr/esma_registers_firds_files/select?q=*&fq=publication_date:%5B{download_date}T00:00:00Z+TO+{download_date}T23:59:59Z%5D&wt=json&indent=true&start=0&rows=100"


class GetESMAFiles(BaseTask):
    """
    Query ESMA and get list of files published for given `download_date`.
    `download_date` can be passed in `flow_args` or `params` and defaults to run_date

    Params:
        download_date: file publication date
        url: ESMA query URL for list of files published on given date
    """

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        resources: Optional[BaseResources] = None,
        flow_args: Optional[str] = None,
        **kwargs,
    ) -> List[str]:

        args = json.loads(flow_args) if flow_args else dict()

        # priority for download date: flow_args -> params -> run date
        download_date = args.get(
            "download_date", params.download_date or datetime.now().strftime("%Y-%m-%d")
        )
        self.logger.info(f"querying ESMA for files published on `{download_date}`")

        url = params.url.format(download_date=download_date)

        response = requests_wrapper.get(url).json()

        file_urls = [i["download_link"] for i in response["response"]["docs"]]

        self.logger.info(f"list of files published on `{download_date}`: \n{file_urls}")

        return file_urls
