import logging
import math
from collections import Iterable
from datetime import datetime
from decimal import Decimal
from typing import Any
from typing import List
from typing import Optional

import pandas as pd
import pendulum
import pycountry
import pytz
from pendulum.parsing.exceptions import ParserError
from prefect import context
from se_elastic_schema.elastic_schema.core.base import BaseStrEnum
from xlrd import xldate_as_tuple

str_to_bool_dict = {
    "true": True,
    "y": True,
    "yes": True,
    "t": True,
    "on": True,
    "false": False,
    "n": False,
    "no": False,
    "f": False,
    "off": False,
}


DATE_FORMAT = "%Y-%m-%d"
UTC_TIMEZONE = pytz.timezone("UTC")

UTF_8 = "utf-8"

logger = logging.getLogger(__name__)


def double(inp_val: Any) -> Optional[float]:
    """
    Cast value to float
    :param inp_val: value to be converted to float
    :return: float value or None if not inp_val
    """
    if not inp_val and inp_val != 0:
        return None
    if isinstance(inp_val, float) and math.isnan(inp_val):
        return None
    if isinstance(inp_val, str):
        if inp_val.isspace() or (inp_val.strip() == ""):
            return None
        inp_val = inp_val.replace(",", "")
        inp_val = inp_val.strip()
        inp_val = Decimal(inp_val)
        inp_val = float(inp_val)
    return inp_val


def integer(inp_val: Any) -> Optional[int]:
    """
    Cast value to integer
    :param inp_val: value to be converted to integer
    :return: integer value or None if not inp_val
    """
    inp_val = double(inp_val)
    if inp_val is None:
        return None
    return int(inp_val)


# noinspection PyTypeChecker
def is_empty(val: Any) -> bool:
    """
    Check if value is the equivalent of None/nan/Empty for iterables
    :param val: value to check
    :return: True if object meets the above condition, False otherwise
    """
    return (
        val is None
        or (isinstance(val, str) and len(val.strip()) == 0)
        or (isinstance(val, Iterable) and len(val) == 0)
        or (isinstance(val, str) and val.lower() == "nan")
    )


def abs_or_none(value: Any) -> Optional[float]:
    """
    Cast value to float
    :param value: value to be converted to float
    :return: value converted to float; 0.0 if double(value) returns None; None if value
    is None
    """
    if not is_empty(value):
        out = double(value) or 0.0
        return abs(out)


def date(dt_str: str, dt_fmt="%d/%m/%Y", is_xls=False) -> Optional[str]:
    """
    Convert date str to input date format
    :param dt_str: Date string to be converted
    :param dt_fmt: Date format
    :param is_xls: bool
    :return: Converted date string
    """
    ts = process_dtm(dt_str=dt_str, dt_fmt=dt_fmt, is_xls=is_xls)
    if isinstance(ts, datetime):
        return ts.strftime(DATE_FORMAT)
    else:
        return ts


def formatted_date_time_string(
    dt_str: str, dt_fmt="%d/%m/%Y", target_fmt=DATE_FORMAT, is_xls=False
) -> Optional[str]:
    """
    Convert date str to input date format
    :param dt_str: Date string to be converted
    :param dt_fmt: Date format
    :param target_fmt: Format to which the timestamp should be converted to
    :param is_xls: bool
    :return: Converted date string
    """
    ts = process_dtm(dt_str=dt_str, dt_fmt=dt_fmt, is_xls=is_xls)
    if isinstance(ts, datetime):
        return ts.strftime(target_fmt)
    else:
        return ts


def process_dtm(
    dt_str=None,
    dt_fmt="%d/%m/%Y %H:%M:%S",
    epoch_ms=None,
    tm_sep=":",
    is_xls=False,
    timezone="UTC",
) -> Optional[datetime]:
    """
    Process date time
    :param dt_str: Date string
    :param dt_fmt: Date format
    :param epoch_ms: UNIX epoch milliseconds
    :param tm_sep: time separator
    :param is_xls: bool
    :return: datetime object
    """
    if is_empty(dt_str) and epoch_ms is None:
        return None
    elif is_xls:
        return datetime(*xldate_as_tuple(double(dt_str), 0))
    elif isinstance(dt_str, datetime):
        return dt_str

    val = None
    if epoch_ms is not None:
        val = datetime.utcfromtimestamp(float(epoch_ms) / 1000.0)
    else:
        if not isinstance(dt_fmt, list):
            dt_fmt = [dt_fmt]

        for fmt in dt_fmt:
            try:
                val = parse_dtm(dt_str, fmt, tm_sep)
            except ValueError:
                try:
                    dtm_str_strip_z = dt_str[:-1] if dt_str.endswith("Z") else dt_str
                    val = pendulum.parse(dtm_str_strip_z, tz=timezone)
                except ParserError:
                    continue
            if val is not None:
                break
    if val is None and dt_str.split(".")[0] != dt_str:
        try:
            val = process_dtm(dt_str.split(".")[0], dt_fmt, epoch_ms, tm_sep, is_xls)
        except ValueError:
            raise ValueError(f"Invalid Format. dtm{dt_str}, fmt{dt_fmt}")

    return val


def parse_dtm(dt_str=None, fmt="%d/%m/%Y %H:%M:%S", tm_sep=":") -> Optional[datetime]:
    """
    Parse datetime
    :param dt_str: Date string
    :param fmt: Datetime format
    :param tm_sep: Time separator
    :return: Parsed Datetime
    """
    for sep in [".%f", tm_sep + "%S", tm_sep + "%M", "%H"]:
        try:
            return datetime.strptime(dt_str.strip(), fmt.strip())
        except ValueError:
            if fmt.endswith(sep):
                fmt = fmt.replace(sep, "")
    return datetime.strptime(dt_str.strip(), fmt.strip())


def dtm(
    dt_str: str,
    target_format: str,
    dt_fmt="%d/%m/%Y",
    is_xls=False,
    timezone: datetime.tzinfo = UTC_TIMEZONE,
) -> Optional[str]:
    ts = process_dtm(dt_str=dt_str, dt_fmt=dt_fmt, is_xls=is_xls, timezone=timezone)
    if isinstance(ts, datetime):
        utc_timestamp = _to_utc(ts, timezone)
        return utc_timestamp.strftime(target_format)
    else:
        return ts


def _to_utc(datetime_obj: datetime, timezone: datetime.tzinfo) -> datetime:
    if datetime_obj.tzinfo is not None:
        return datetime_obj.astimezone(UTC_TIMEZONE)
    elif timezone is None or timezone == UTC_TIMEZONE:
        return datetime_obj

    return timezone.localize(datetime_obj).astimezone(UTC_TIMEZONE)


def string_to_bool(source_series: pd.Series) -> pd.Series:
    """
    :param source_series: pd.Series
    :return: pd.Series
    Converts source string values to bool
    """
    result = source_series.str.lower().map(str_to_bool_dict).fillna(False)
    return result


def frame_categorical_sort(
    df: pd.DataFrame,
    sort_column: str,
    sort_order: List[str],
    logger=context.get("logger"),
) -> pd.DataFrame:
    """
    Sorts given dataframe on `sort_column` in the order as per in
    `sort_order` provided. On exception returns source df.
    :param df: pd.DataFrame
    :param sort_column: str
    :param sort_order: List[str]
    :param logger: logger
    :return: pd.DataFrame
    """
    temp_categorical_column = "TEMP_CATEGORICAL_COLUMN"
    try:
        df.loc[:, sort_column] = df.loc[:, sort_column].str.upper()

        # create a temporary categorical column using
        # `sort_order` based on `sort_order`
        df.loc[:, temp_categorical_column] = pd.Categorical(
            df.loc[:, sort_column],
            categories=sort_order,
            ordered=True,
        )
        # sort the dataframe using the categorical column
        df = df.sort_values(temp_categorical_column)
        # drop temporary column and return sorted dataframe
        cols_mask = df.columns.isin([temp_categorical_column])
        return df.loc[:, ~cols_mask]

    except Exception as e:
        logger.warning(f"Error sorting dataframe in required_order.Error:{e}")
        cols_mask = df.columns.isin([temp_categorical_column])
        return df.loc[:, ~cols_mask]


def fill_unpopulated_required_columns_with_na(
    df: pd.DataFrame,
    required_column_list: list,
) -> pd.DataFrame:
    """
    Create columns in dataframe with NA values

    :param df: Input Dataframe
    :param required_column_list: List of required columns which must be present in the dataframe,
    if not create and fill them with NA
    """
    columns_to_be_filled = set(required_column_list) - set(df.columns)

    if columns_to_be_filled:
        logger.warning(
            f"{columns_to_be_filled} columns were not populated and were filled with NA."
        )

    # Not using pd.assign as we want to fill the values in-place
    for col in columns_to_be_filled:
        df.loc[:, col] = pd.NA

    return df


class ValidCurrencyCodesNotInISO(BaseStrEnum):
    CNH = "CNH"
    RMB = "RMB"


def check_currency(currency: str) -> str:
    """
    Function which checks if it's a valid currency
    according to ISO databases also has a static fallback
    to currencies which are not present in ISO
    :param currency:
    :return: Validated currency
    """
    try:
        pycountry.currencies.lookup(currency)
        return currency
    except LookupError:
        if currency.upper() in ValidCurrencyCodesNotInISO.list():
            return currency
        else:
            return pd.NA
