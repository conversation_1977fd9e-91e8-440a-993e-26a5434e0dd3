from typing import Any
from typing import Callable

from se_trades_tasks.order_and_tr.fix.dataclasses.fix_parser_dataclasses import (
    FixParserResult,
)

from swarm_tasks.utilities.data_util import is_empty
from swarm_tasks.utilities.dict_util import cast_to_type


def fix_getter(fix: FixParserResult) -> Callable:
    """
    Auxiliary function that returns a function to get fix field values for a given tag
    :param fix: FixTag dataclass
    :return: get_value function to access fix field values for a given tag
    """

    def get_value(tag: int, pos=1, default=None, typ="str", all_vals=False) -> Any:
        return get_tag_val(
            fix=fix, tag=tag, pos=pos, default=default, typ=typ, all_vals=all_vals
        )

    return get_value


def get_tag_val(
    fix: FixParserResult, tag: int, pos=1, default=None, typ="str", all_vals=False
) -> Any:
    """
    Access the fix field value of a given tag, cast it accordingly and return it
    :param fix: FixTag dataclass
    :param tag: int - fix field tag
    :param pos: int - index
    :param default: default value to return
    :param typ: default data type
    :param all_vals: return all values for a given tag if True
    :return: fix field value for a given tag
    """
    data_with_this_tag = fix.tags.get(tag, list())
    if all_vals:
        return [
            get_tag_val(fix, tag, p, default, typ)
            for p, _ in enumerate(data_with_this_tag, 1)
        ]

    val = default
    if pos <= len(data_with_this_tag):
        specific_tag_data = data_with_this_tag[pos - 1]
        val = specific_tag_data.value if specific_tag_data.value else default

    if val == default or is_empty(val):
        return default

    return cast_to_type(val, typ)
