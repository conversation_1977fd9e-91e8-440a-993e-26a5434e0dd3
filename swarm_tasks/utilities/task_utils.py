from typing import Any
from typing import List
from typing import Union

import pandas as pd
from swarm.task.io.read import FrameProducerResult
from swarm.task.transform.result import TransformResult


def match_enum_value(item_to_match: str, enum_class: Any) -> Any:
    """
    Utility function to take in a string and an Enum class and check if anu value in enum class matches
    (substring match) the string
    :param: item_to_match: Input String
    :param: enum_class: Input Enum Class whose values are to be matched
    :returns: Enum class value which matches the given string else returns None
    """
    matched_enum_dict = dict(
        filter(
            lambda x: x[1].value.lower() in item_to_match.lower(),
            enum_class._member_map_.items(),
        )
    )
    # Return value of the enum
    return next(iter(matched_enum_dict.values())) if matched_enum_dict else None


def extract_source_frame(
    source_frame: Union[pd.DataFrame, FrameProducerResult, TransformResult]
) -> pd.DataFrame:
    """
    Parse DataClasses from upstream tasks to obtain source DataFrame
    :param source_frame: Can be a Pandas DataFrame or a dataclass holding one
    :return: Pandas DataFrame
    """
    if isinstance(source_frame, FrameProducerResult):
        source_frame = source_frame.frame
    elif isinstance(source_frame, TransformResult):
        source_frame = source_frame.target
    return source_frame


class BaseColumns:
    def get_columns(self) -> List[str]:
        columns = [
            self.__class__.__dict__.get(k)
            for k in self.__class__.__dict__.keys()
            if not k.startswith("__") and not k.endswith("__")
        ]

        return columns
