from typing import Any
from typing import Optional

import pandas as pd
from se_core_tasks.feeds.tr.unavista.apollo_parties_unavista import (
    run_apollo_parties_fallback,
)
from swarm.conf import Settings
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class Resources(BaseResources):
    es_client_key: str = "tenant-data"


class ApolloPartiesFallback(TransformBaseTask):
    """
    This task is a bespoke Parties Fallback task for Apollo.
    It checks each of:
        buyer
        seller
        buyerDecisionMaker
        sellerDecisionMaker
        executionWithinFirm
        investmentDecisionMakerWithinFirm
    If any of these fields are missing they are populated according to the spece here:
    https://steeleye.atlassian.net/wiki/spaces/IN/pages/2703294465/RTS22+UnaVista+Handler#Party-Fallback
    """

    resources_class = Resources

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        resources: Optional[Resources] = None,
        **kwargs,
    ) -> Any:

        es_client = Settings.connections.get(resources.es_client_key)

        result = run_apollo_parties_fallback(
            source_frame=source_frame,
            es_client=es_client,
            auditor=self.auditor,
            logger=self.logger,
        )

        return result
