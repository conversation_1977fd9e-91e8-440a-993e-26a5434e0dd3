from typing import List

from se_core_tasks.core.core_dataclasses import FileSplitterResult
from se_core_tasks.core.core_dataclasses import S3File
from se_core_tasks.feeds.tr.unavista.xml_to_unavista_converter import (
    Params as GenericParams,
)
from se_core_tasks.feeds.tr.unavista.xml_to_unavista_converter import (
    run_xml_to_unavista_converter,
)
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


class Params(BaseParams, GenericParams):
    pass


class XmlToUnavistaConverter(BaseTask):
    """
    Shenkman are changing technology provider to Charles River (CRD).
    CRD provide a report for MiFIR which is an XML in the UV format.
    A handler for this format is nearly finished for Apollo, but this handler uses CSV format.

    This task converts a xml into a csv, ready for the Apollo handler.
    https://steeleye.atlassian.net/wiki/spaces/IN/pages/**********/RTS22Transaction%3A+UnaVista+Handler
    """

    params_class = Params

    def execute(
        self,
        params: Params = None,
        file_url: str = None,
        header_file_splitter_result: List[FileSplitterResult] = None,
        file_splitter_result: FileSplitterResult = None,
        **kwargs,
    ) -> S3File:

        result = run_xml_to_unavista_converter(
            params=params,
            realm=Settings.realm,
            file_url=file_url,
            header_file_splitter_result=header_file_splitter_result,
            file_splitter_result=file_splitter_result,
            auditor=self.auditor,
            logger=self.logger,
            **kwargs,
        )

        return result
