from copy import deepcopy
from typing import Optional

import pandas as pd
from pydantic import Field
from se_elastic_schema.static.mifid2 import BuySellIndicator
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams):
    source_price_attribute: str = Field(
        ...,
        description="Source price column from which the price has to be populated"
        " in the client leg",
    )
    source_buyer_field: str = Field(
        ...,
        description="Source column from which the buyer has to be populated"
        " in the client leg",
    )
    source_seller_field: str = Field(
        ...,
        description="Source column from which the seller has to be populated"
        " in the client leg",
    )
    source_transaction_ref_no_suffix: str = Field(
        default="LP",
        description="Suffix which has already been added to the "
        "reportDetails.transactionRefNo column for the liquidity provider leg",
    )
    buyer_column: str = Field(
        ...,
        description="Target column in which the buyer is to be populated."
        " This column should've been populated in the LP leg already",
    )
    seller_column: str = Field(
        ...,
        description="Target column in which the seller is to be populated."
        " This column should've been populated in the LP leg already",
    )
    target_transaction_ref_no_suffix: str = Field(
        default="CL",
        description="Suffix to be added to the reportDetails.transactionRefNo column"
        "for the client leg",
    )


class MappingAttributes:
    REPORT_DETAILS_TRANSACTION_NO = "reportDetails.transactionRefNo"
    TRANSACTION_DETAILS_BUYSELL_INDICATOR = "transactionDetails.buySellIndicator"
    TRANSACTION_DETAILS_PRICE = "transactionDetails.price"


class ClientLegGenerator(TransformBaseTask):
    """
    This task creates duplicate records with different values for certain fields.
    These duplicate records form the client leg, while the 'normal' records
    generated (which are in the source frame) form the liquidity provider leg.

    Some background from the Confluence page:
    (Note: In the following description, EBC is an example of a firm using the feed)
    The given trade data represents the overall transaction from client through to
    Liquidity Provider. EBC stand as the intermediary, so if their client executes
    a buy, then EBC will report two trades:
    EBC sell to the client
    EBC buy from the LP

    NOTE: in the bundle, the results of this task (the client leg) have to be vertically
    concatenated with the results of the liquidity provider leg.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Optional[Params] = None,
        **kwargs,
    ) -> pd.DataFrame:

        # Create a deepcopy of the source_frame. The target will have the
        # same columns as the source frame, but different values for some
        # columns
        target = deepcopy(source_frame)
        if source_frame.empty:
            self.logger.warning("Source frame empty, returned an empty dataframe")
            return target

        target = self._overwrite_attributes(df=target, params=params)
        target = self._replace_suffix_transaction_ref_no(df=target, params=params)
        target = self._flip_transaction_side(df=target)
        return target

    @staticmethod
    def _flip_transaction_side(df: pd.DataFrame) -> pd.DataFrame:
        """
        Flip the transactionDetails.buySellIndicator attribute.
        If it is BUYI -> change to SELL
        If it is SELL -> change to BUYI
        :param df: dataframe containing the transactionDetails.buySellIndicator
                   column
        :type: df: pd.DataFrame
        :returns: dataframe with the vakyes in transactionDetails.buySellIndicator
                  flipped
        :rtype: pd.DataFrame
        """

        df.loc[:, MappingAttributes.TRANSACTION_DETAILS_BUYSELL_INDICATOR] = df.loc[
            :, MappingAttributes.TRANSACTION_DETAILS_BUYSELL_INDICATOR
        ].map(
            {
                BuySellIndicator.BUYI.value: BuySellIndicator.SELL.value,
                BuySellIndicator.SELL.value: BuySellIndicator.BUYI.value,
            }
        )
        return df

    @staticmethod
    def _replace_suffix_transaction_ref_no(
        df: pd.DataFrame, params: Params
    ) -> pd.DataFrame:
        """This function changes the suffix of the transaction reference number
        from LP (used to indicate Liquidity provider leg records) to CL (used to
        indicate Client Leg records)
        :param: df: data frame in which the transac ref no. suffix has to be replaced
        :type: df: pd.DataFrame
        :param: params: Params instance
        :returns: DataFrame with transac ref no. suffix replaced
        :rtype: pd.DataFrame
        """
        existing_suffix_len = len(params.source_transaction_ref_no_suffix)
        transaction_ref_no_not_null_mask = df.loc[
            :, MappingAttributes.REPORT_DETAILS_TRANSACTION_NO
        ].notnull()
        df.loc[
            transaction_ref_no_not_null_mask,
            MappingAttributes.REPORT_DETAILS_TRANSACTION_NO,
        ] = df.loc[
            transaction_ref_no_not_null_mask,
            MappingAttributes.REPORT_DETAILS_TRANSACTION_NO,
        ].apply(
            lambda trefno: trefno[:-existing_suffix_len]
            + params.target_transaction_ref_no_suffix
        )
        return df

    @staticmethod
    def _overwrite_attributes(df: pd.DataFrame, params: Params) -> pd.DataFrame:
        """Remaps attributes for the client leg.
        :param: df: data frame in which the attributes need to be overwritten
        :type: df: pd.DataFrame
        :param: params: Params instance
        :returns: DataFrame with attributes overwritten
        :rtype: pd.DataFrame
        """

        # Set columns which we need to overwrite to pd.NA
        cols_to_overwrite = [
            MappingAttributes.TRANSACTION_DETAILS_PRICE,
            params.buyer_column,
            params.seller_column,
        ]

        for col in cols_to_overwrite:
            df.loc[:, col] = pd.NA

        # Map new value for transactionDetails.price
        source_price_not_null_mask = df.loc[:, params.source_price_attribute].notnull()
        df.loc[
            source_price_not_null_mask, MappingAttributes.TRANSACTION_DETAILS_PRICE
        ] = df.loc[source_price_not_null_mask, params.source_price_attribute]
        # Map new value for buyer and seller
        source_buyer_not_null_mask = df.loc[:, params.source_buyer_field].notnull()
        df.loc[source_buyer_not_null_mask, params.buyer_column] = df.loc[
            source_buyer_not_null_mask, params.source_buyer_field
        ]
        source_seller_not_null_mask = df.loc[:, params.source_seller_field].notnull()
        df.loc[source_seller_not_null_mask, params.seller_column] = df.loc[
            source_seller_not_null_mask, params.source_seller_field
        ]
        return df
