import pandas as pd
from se_trades_tasks.order_and_tr.instrument.link.link_instrument import (
    Params as GenericParams,
)
from se_trades_tasks.order_and_tr.instrument.link.link_instrument import (
    run_link_instruments,
)
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class Resources(BaseResources):
    ref_data_key: str
    tenant_data_key: str


class LinkInstrument(TransformBaseTask):
    params_class = Params
    resources_class = Resources

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources: Resources = None,
        **kwargs,
    ) -> pd.DataFrame:
        es_client_srp = Settings.connections.get(resources.ref_data_key)
        es_client_tenant = Settings.connections.get(resources.tenant_data_key)

        tenant = Settings.tenant
        return run_link_instruments(
            source_frame=source_frame,
            params=params,
            tenant=tenant,
            es_client_tenant=es_client_tenant,
            es_client_srp=es_client_srp,
        )
