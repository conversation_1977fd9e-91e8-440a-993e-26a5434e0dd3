from typing import Optional

import pandas as pd
from pydantic import Field
from se_elastic_schema.components.market.identifier import Identifier
from se_elastic_schema.static.market import IdentifierType
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams):
    source_attribute: str
    path: str = Field(
        ...,
        description="Path to be stored on the identifier"
        " e.g. instrumentDetails.instrument",
    )
    target_column: str = Field(
        ...,
        description="Name of the target column"
        "e.g. marketIdentifiers.instrument or marketIdentifiers.parties",
    )
    identifier_type: IdentifierType = Field(
        ...,
        description="OBJECT or ARRAY based on whether the target schema field is a"
        " single object or an array",
    )


class CreateIdentifier(TransformBaseTask):
    """
    Generic task to convert a column into identifiers
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Optional[BaseParams] = None,
        resources: Optional[BaseResources] = None,
        **kwargs,
    ) -> pd.DataFrame:
        target = pd.DataFrame(index=source_frame.index, columns=[params.target_column])
        target[params.target_column] = source_frame[params.source_attribute]

        mask = target[params.target_column].notnull()
        if mask.any():
            target.loc[mask, params.target_column] = target.loc[
                mask, params.target_column
            ].apply(
                lambda x: [
                    Identifier(
                        labelId=x,
                        path=params.path,
                        type=params.identifier_type,
                    ).dict(by_alias=True)
                ]
            )

        return target
