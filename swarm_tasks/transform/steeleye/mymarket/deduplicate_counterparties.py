from typing import List

import pandas as pd
from prefect.engine import signals
from se_elastic_schema.models import MarketCounterparty
from se_elastic_schema.static.reference import FirmType
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class MarketCounterpartyFields:
    MODEL = "MarketCounterparty"
    TRADE_FILE_IDENTIFIERS = "sinkIdentifiers.tradeFileIdentifiers"
    LEI = "lei"
    UNIQUE_PROPS = "&uniqueProps"
    UNIQUE_IDS = "uniqueIds"
    SOURCE_INDEX = "sourceIndex"
    FIRM_TYPE = "details.firmType"


class Params(BaseParams):
    identifiers_path: str


class Resources(BaseResources):
    es_client_key: str


class DeduplicateCounterParties(TransformBaseTask):
    """
    This task receives a dataframe containing counterparties data and deduplicates it
    by removing counterparties already ingested to SDP
    """

    params_class = Params
    resources_class = Resources

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources: Resources = None,
        **kwargs,
    ) -> pd.DataFrame:
        """
        Fetches counterparties already in SDP and deduplicates source_frame
        :param source_frame: pre-processed counterparties dataframe
        :param params: params.identifiers_path is the name of the column with the LEI value
        :param resources: es_client_key to access SDP
        :return: dataframe with counterparties absent from SDP (empty if 100% duplicates)
        """
        if source_frame.empty:
            raise signals.SKIP("Empty source frame")
        elif params.identifiers_path not in source_frame.columns:
            raise signals.SKIP("LEI column not present in source frame")

        df = source_frame.dropna(subset=[params.identifiers_path])

        # Filter out rows where details.firmType is not a valid enum
        firm_type_values = [e.value for e in FirmType]
        valid_firm_type_mask = source_frame.loc[
            :, MarketCounterpartyFields.FIRM_TYPE
        ].isin(firm_type_values)
        df = df.loc[valid_firm_type_mask, :]

        ids_list = df.loc[:, params.identifiers_path].dropna().unique()
        ids = [MarketCounterpartyFields.LEI + ":" + ident.lower() for ident in ids_list]
        self.logger.info(f"fetch {len(ids)} parties")

        party_leis_fetched = self._fetch_party_leis(
            ids=ids, resources=resources, identifiers_path=params.identifiers_path
        )

        if party_leis_fetched.empty:
            self.logger.info(f"No parties found in tenant data for :{ids}")
            df = df.drop_duplicates(subset=[params.identifiers_path])
            self._transform_market_counterparty(
                df=df,
                identifiers_path=params.identifiers_path,
            )
            return df

        self.logger.info("start transforming counterparties")
        # left join where right column is null (no intersection) to find non-duplicates
        # keep original indexes to track duplicate rows in input file
        temp_target = (
            df.reset_index()
            .merge(
                party_leis_fetched,
                how="left",
                on=[params.identifiers_path],
                indicator="ind",
            )
            .set_index(df.index.names)
            .drop_duplicates(subset=[params.identifiers_path])
        )

        duplicate_unique_props_indexes = temp_target.query(
            "ind == 'both'", engine="python"
        ).index.tolist()
        if duplicate_unique_props_indexes:
            self.auditor.add(
                "Duplicate records",
                ctx={
                    "error": f"{len(duplicate_unique_props_indexes)} "
                    f"rows with duplicate LEIs in the following indexes: "
                    f"{duplicate_unique_props_indexes}",
                },
            )

        target = temp_target.query("ind == 'left_only'", engine="python").drop(
            "ind", axis=1
        )
        if target.empty:
            raise signals.SKIP("All input records are duplicate")

        self._transform_market_counterparty(
            df=target, identifiers_path=params.identifiers_path
        )
        return target

    def _fetch_party_leis(
        self, ids: List[str], resources: Resources, identifiers_path: str
    ) -> pd.DataFrame:
        """
        Access SDP to fetch MarketCounterparty entries using the LEI as a primary key
        :param ids: list of LEIs to be searched in SDP
        :param resources: es_client_key to access SDP
        :param identifiers_path: name of the field containing the LEI, to be searched in MarketCounterparty
        :return: dataframe with the LEIs that were matched against SDP
        """
        es_client = self.clients.get(resources.es_client_key)

        query = self._parties_unique_props_terms_query(
            label_ids=ids, es_client=es_client, identifiers_path=identifiers_path
        )

        self.logger.info(f"scrolling parties for {len(ids)} ids")

        df = es_client.scroll(
            query=query,
            index=MarketCounterparty.get_elastic_index_alias(tenant=Settings.tenant),
        )

        return df

    @staticmethod
    def _transform_market_counterparty(df: pd.DataFrame, identifiers_path: str) -> None:
        """
        Add new columns to MarketCounterparty dataframe based on required mapping and schema
        :param df: dataframe with marketCounterparty data
        :param identifiers_path: name of the field containing the LEI value
        """
        df.loc[:, MarketCounterpartyFields.TRADE_FILE_IDENTIFIERS] = df.loc[
            :, identifiers_path
        ].apply(lambda x: [{"id": x, "label": MarketCounterpartyFields.LEI}])

        df.loc[:, MarketCounterpartyFields.UNIQUE_PROPS] = df.loc[
            :, identifiers_path
        ].apply(lambda x: [MarketCounterpartyFields.LEI + ":" + x.lower()])

        df.loc[:, MarketCounterpartyFields.UNIQUE_IDS] = df.loc[
            :, identifiers_path
        ].apply(lambda x: [MarketCounterpartyFields.LEI + ":" + x.lower()])

        df.loc[:, MarketCounterpartyFields.SOURCE_INDEX] = df.loc[
            :, MarketCounterpartyFields.SOURCE_INDEX
        ].apply(lambda x: str(x))

    @staticmethod
    def _parties_unique_props_terms_query(
        label_ids: List[str], es_client, identifiers_path: str
    ) -> dict:
        """
        Build elastic query to find a list of LEIs in MarketCounterparty
        :param es_client: ElasticsearchClient
        :param identifiers_path: name of the field containing the LEI value
        :param label_ids: list of LEIs to be searched
        :return: terms query for MarketCounterparty records using &uniqueProps
        """
        query = {
            "size": es_client.MAX_QUERY_SIZE,
            "_source": {"includes": [identifiers_path]},
            "query": {
                "bool": {
                    "filter": [
                        {"term": {es_client.meta.model: MarketCounterpartyFields.MODEL}}
                    ]
                }
            },
        }

        # TODO move this block to an utility function
        if len(label_ids) > es_client.MAX_TERMS_SIZE:
            label_ids_chunks = [
                label_ids[ix : ix + es_client.MAX_TERMS_SIZE]
                for ix in range(0, len(label_ids), es_client.MAX_TERMS_SIZE)
            ]
            should = [
                {"terms": {es_client.meta.unique_props: chunk}}
                for chunk in label_ids_chunks
            ]

            query["query"]["bool"]["should"] = should
            query["query"]["bool"]["minimum_should_match"] = 1
        else:
            query["query"]["bool"]["filter"].append(
                {"terms": {es_client.meta.unique_props: label_ids}}
            )

        return query
