from se_core_tasks.currency.convert_minor_to_major import JSON_FILE_PATH


class PartiesFields:
    PARTIES_BUYER = "buyer"
    PARTIES_BUYER_FILE_ID = "buyerFileIdentifier"
    PARTIES_BUYER_DECISION_MAKER = "buyerDecisionMaker"
    PARTIES_BUYER_DECISION_MAKER_FILE_ID = "buyerDecisionMakerFileIdentifier"
    PARTIES_CLIENT = "clientIdentifiers.client"
    PARTIES_CLIENT_FILE_ID = "clientFileIdentifier"
    PARTIES_COUNTERPARTY = "counterparty"
    PARTIES_CP_FILE_ID = "counterpartyFileIdentifier"
    PARTIES_EXECUTING_ENTITY = "reportDetails.executingEntity"
    PARTIES_EXEC_ENTITY_FILE_ID = "reportDetails.executingEntity.fileIdentifier"
    PARTIES_EXECUTION_WITHIN_FIRM = "tradersAlgosWaiversIndicators.executionWithinFirm"
    PARTIES_EXEC_WITHIN_FIRM_FILE_ID = (
        "tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier"
    )
    PARTIES_INVEST_DEC_WITHIN_FIRM = (
        "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm"
    )
    PARTIES_INVEST_DEC_WITHIN_FIRM_FILE_ID = (
        "tradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier"
    )
    PARTIES_SELLER = "seller"
    PARTIES_SELLER_FILE_ID = "sellerFileIdentifier"
    PARTIES_SELLER_DECISION_MAKER = "sellerDecisionMaker"
    PARTIES_SELLER_DECISION_MAKER_FILE_ID = "sellerDecisionMakerFileIdentifier"
    PARTIES_TRADER = "trader"
    PARTIES_TRADER_FILE_ID = "traderFileIdentifier"
    TRX_DTL_BUY_SELL_INDICATOR = "transactionDetails.buySellIndicator"
    MARKET_IDENTIFIERS_PARTIES = "marketIdentifiers.parties"


MINOR_CURRENCIES_FILE_PATH = JSON_FILE_PATH
