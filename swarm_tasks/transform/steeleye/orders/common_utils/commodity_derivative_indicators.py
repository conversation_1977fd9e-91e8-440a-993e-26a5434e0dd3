import pandas as pd
from se_trades_tasks.order_and_tr.commodity_derivative_indicators import (
    Params as GenericParams,
)
from se_trades_tasks.order_and_tr.commodity_derivative_indicators import (
    run_commodity_derivative_indicators,
)
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class CommodityDerivativeIndicators(TransformBaseTask):
    """
    This task should transform Commodity Derivative Indicator based on the
    instrumentDetails.instrument.commoditiesOrEmissionAllowanceDerivativeInd field

        Examples:
        map: Populate commodityDerivativeIndicator based on commoditiesOrEmissionAllowanceDerivativeInd in
        instrumentDetails.instrument
        ```
        - path: swarm_tasks.transform.steeleye.orders.common_utils.commodity_derivative_indicators:CommodityDerivativeIndicators
          name: MapStatic
          paramsList:
          - target_attribute: tradersAlgosWaiversIndicators.commodityDerivativeIndicator
            instrument_nested_path: commoditiesOrEmissionAllowanceDerivativeInd
            instrument_attribute: instrumentDetails.instrument
        ```
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources: BaseResources = None,
        **kwargs,
    ) -> pd.DataFrame:
        return run_commodity_derivative_indicators(
            source_frame=source_frame, params=params, **kwargs
        )
