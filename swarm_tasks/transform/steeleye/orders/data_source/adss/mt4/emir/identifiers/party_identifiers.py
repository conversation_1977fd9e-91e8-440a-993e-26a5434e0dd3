from typing import Optional

import pandas as pd
from se_elastic_schema.components.market.identifier import Identifier
from se_elastic_schema.static.market import IdentifierType
from se_elastic_schema.static.mifid2 import BuySellIndicator
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.orders.data_source.adss.mt4.emir.identifiers.static import (
    ADSSEmirMT4Columns,
)


class PartiesFields:
    PARTIES_BUYER = "parties.buyer"
    PARTIIES_BUYER_FILE_ID = "buyerFileIdentifier"
    PARTIES_BUYER_DECISION_MAKER = "parties.buyerDecisionMaker"
    PARTIES_COUNTERPARTY = "parties.counterparty"
    PARTIIES_CP_FILE_ID = "counterpartyFileIdentifier"
    PARTIES_EXECUTING_ENTITY = "parties.executingEntity"
    PARTIIES_EXEC_ENTITY_FILE_ID = "executingEntityFileIdentifier"
    PARTIES_EXECUTION_WITHIN_FIRM = "parties.executionWithinFirm"
    PARTIES_EXEC_WITHIN_FIRM_FILE_ID = (
        "tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier"
    )
    PARTIES_INVEST_DEC_WITHIN_FIRM = "parties.investmentDecisionWithinFirm"
    PARTIES_INVEST_DEC_WITHIN_FIRM_FILE_ID = (
        "tradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier"
    )
    PARTIES_SELLER = "parties.seller"
    PARTIIES_SELLER_FILE_ID = "sellerFileIdentifier"
    PARTIES_SELLER_DECISION_MAKER = "parties.sellerDecisionMaker"
    PARTIES_TRADER = "parties.trader"
    PARTIES_TRADER_FILE_ID = "traderFileIdentifier"
    TRX_DTL_BUY_SELL_INDICATOR = "transactionDetails.buySellIndicator"
    MARKET_IDENTIFIERS_PARTIES = "marketIdentifiers.parties"


class PartyIdentifiers(TransformBaseTask):
    """
    This task should create all the columns with ids which will be used
    by LinkParties to retrieve the documents to embed in the record.
    """

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Optional[BaseParams] = None,
        resources: Optional[BaseResources] = None,
        **kwargs,
    ) -> pd.DataFrame:

        cols_used = [
            ADSSEmirMT4Columns.BENEFICIARY_ID,
            ADSSEmirMT4Columns.OTHER_COUNTERPARTY_ID,
            PartiesFields.TRX_DTL_BUY_SELL_INDICATOR,
        ]

        for col in cols_used:
            if col not in source_frame.columns:
                source_frame[col] = pd.NA

        cols_mask = source_frame.columns.isin(cols_used)

        df = source_frame.loc[:, cols_mask]

        result = self._build_market_identifiers_parties(df=df)

        return result

    @staticmethod
    def _add_prefix_to_ids(df: pd.DataFrame) -> pd.DataFrame:
        lei_mask = df[ADSSEmirMT4Columns.BENEFICIARY_ID].notnull()
        if lei_mask.any():
            df.loc[lei_mask, ADSSEmirMT4Columns.BENEFICIARY_ID] = (
                df.loc[lei_mask, ADSSEmirMT4Columns.BENEFICIARY_ID]
                .dropna()
                .apply(lambda x: f"lei:{x}")
            )
        id_mask = df[ADSSEmirMT4Columns.OTHER_COUNTERPARTY_ID].notnull()
        if id_mask.any():
            df.loc[id_mask, ADSSEmirMT4Columns.OTHER_COUNTERPARTY_ID] = (
                df.loc[id_mask, ADSSEmirMT4Columns.OTHER_COUNTERPARTY_ID]
                .dropna()
                .apply(lambda x: f"id:{x}")
            )

        return df

    def _build_market_identifiers_parties(self, df: pd.DataFrame) -> pd.DataFrame:
        """
            paths:
            parties:
                buyer
                counterparty
                executing_entity
                execution_within_firm
                investment_decision_within_firm
                seller
                trader
        :param df:
        :return:
        """
        df = self._add_prefix_to_ids(df=df)

        buy_mask = df[PartiesFields.TRX_DTL_BUY_SELL_INDICATOR] == BuySellIndicator.BUYI

        sell_mask = (
            df[PartiesFields.TRX_DTL_BUY_SELL_INDICATOR] == BuySellIndicator.SELL
        )

        identifiers_df: pd.DataFrame = pd.DataFrame(index=df.index)

        # buyer
        identifiers_df[PartiesFields.PARTIES_BUYER] = self._make_buyer_identifier(
            df=df, buy_mask=buy_mask
        )

        # buyerFileIdentifier
        identifiers_df[
            PartiesFields.PARTIIES_BUYER_FILE_ID
        ] = self._make_buyer_file_identifier(df=df, buy_mask=buy_mask)

        # #counterparty
        identifiers_df[
            PartiesFields.PARTIES_COUNTERPARTY
        ] = self._make_counterparty_identifier(df=df)

        # counterpartyFileIdentifier
        identifiers_df[
            PartiesFields.PARTIIES_CP_FILE_ID
        ] = self._make_counterparty_file_identifier(df=df)

        # executing entity
        identifiers_df[
            PartiesFields.PARTIES_EXECUTING_ENTITY
        ] = self._make_executing_entity_identifier(df=df)

        # executingEntityFileIdentifier
        identifiers_df[
            PartiesFields.PARTIIES_EXEC_ENTITY_FILE_ID
        ] = self._make_executing_entity_file_identifier(df=df)

        # trader
        identifiers_df[PartiesFields.PARTIES_TRADER] = self._make_trader_identifier(
            df=df
        )

        # traderFileIdentifier
        identifiers_df[
            PartiesFields.PARTIES_TRADER_FILE_ID
        ] = self._make_trader_file_identifier(df=df)

        # seller
        identifiers_df[PartiesFields.PARTIES_SELLER] = self._make_seller_identifier(
            df=df, sell_mask=sell_mask
        )

        # sellerFileIdentifier
        identifiers_df[
            PartiesFields.PARTIIES_SELLER_FILE_ID
        ] = self._make_seller_file_identifier(df=df, sell_mask=sell_mask)

        cols_to_merge = [
            PartiesFields.PARTIES_BUYER,
            PartiesFields.PARTIES_EXECUTING_ENTITY,
            PartiesFields.PARTIES_SELLER,
            PartiesFields.PARTIES_TRADER,
            PartiesFields.PARTIES_COUNTERPARTY,
        ]

        identifiers_df.loc[
            :, PartiesFields.MARKET_IDENTIFIERS_PARTIES
        ] = identifiers_df[cols_to_merge].apply(lambda x: x.dropna().tolist(), axis=1)

        target_col_list = [
            PartiesFields.MARKET_IDENTIFIERS_PARTIES,
            PartiesFields.PARTIIES_EXEC_ENTITY_FILE_ID,
            PartiesFields.PARTIIES_BUYER_FILE_ID,
            PartiesFields.PARTIIES_SELLER_FILE_ID,
            PartiesFields.PARTIES_TRADER_FILE_ID,
            PartiesFields.PARTIIES_CP_FILE_ID,
        ]

        return identifiers_df[target_col_list]

    @staticmethod
    def _make_buyer_identifier(df: pd.DataFrame, buy_mask: pd.Series) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)

        parties_buyer_buy_mask = (
            buy_mask & df[ADSSEmirMT4Columns.BENEFICIARY_ID].notnull()
        )

        if parties_buyer_buy_mask.any():
            result.loc[parties_buyer_buy_mask] = df.loc[
                parties_buyer_buy_mask, ADSSEmirMT4Columns.BENEFICIARY_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=PartiesFields.PARTIES_BUYER,
                    type=IdentifierType.ARRAY,
                ).dict(by_alias=True)
            )

        parties_buyer_not_buy_mask = (
            ~buy_mask & df[ADSSEmirMT4Columns.OTHER_COUNTERPARTY_ID].notnull()
        )

        if parties_buyer_not_buy_mask.any():
            result.loc[parties_buyer_not_buy_mask] = df.loc[
                parties_buyer_not_buy_mask, ADSSEmirMT4Columns.OTHER_COUNTERPARTY_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=PartiesFields.PARTIES_BUYER,
                    type=IdentifierType.ARRAY,
                ).dict(by_alias=True)
            )

        return result

    @staticmethod
    def _make_buyer_file_identifier(df: pd.DataFrame, buy_mask: pd.Series) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)

        parties_buyer_buy_mask = (
            buy_mask & df[ADSSEmirMT4Columns.BENEFICIARY_ID].notnull()
        )

        if parties_buyer_buy_mask.any():
            result.loc[parties_buyer_buy_mask] = (
                df.loc[parties_buyer_buy_mask, ADSSEmirMT4Columns.BENEFICIARY_ID]
                .dropna()
                .str.lower()
            )

        parties_buyer_not_buy_mask = (
            ~buy_mask & df[ADSSEmirMT4Columns.OTHER_COUNTERPARTY_ID].notnull()
        )

        if parties_buyer_not_buy_mask.any():
            result.loc[parties_buyer_not_buy_mask] = (
                df.loc[
                    parties_buyer_not_buy_mask, ADSSEmirMT4Columns.OTHER_COUNTERPARTY_ID
                ]
                .dropna()
                .str.lower()
            )

        return result

    @staticmethod
    def _make_counterparty_identifier(df: pd.DataFrame) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)

        parties_cp_not_null_mask = df[
            ADSSEmirMT4Columns.OTHER_COUNTERPARTY_ID
        ].notnull()

        if parties_cp_not_null_mask.any():
            result.loc[parties_cp_not_null_mask] = df.loc[
                parties_cp_not_null_mask, ADSSEmirMT4Columns.OTHER_COUNTERPARTY_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=PartiesFields.PARTIES_COUNTERPARTY,
                    type=IdentifierType.OBJECT,
                ).dict(by_alias=True)
            )

        return result

    @staticmethod
    def _make_counterparty_file_identifier(df: pd.DataFrame) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)

        parties_cp_not_null_mask = df[
            ADSSEmirMT4Columns.OTHER_COUNTERPARTY_ID
        ].notnull()

        if parties_cp_not_null_mask.any():
            result.loc[parties_cp_not_null_mask] = df.loc[
                parties_cp_not_null_mask, ADSSEmirMT4Columns.OTHER_COUNTERPARTY_ID
            ].str.lower()

        return result

    @staticmethod
    def _make_executing_entity_identifier(df: pd.DataFrame) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)

        parties_exec_entity_not_null_mask = df[
            ADSSEmirMT4Columns.BENEFICIARY_ID
        ].notnull()

        if parties_exec_entity_not_null_mask.any():
            result.loc[parties_exec_entity_not_null_mask] = df.loc[
                parties_exec_entity_not_null_mask, ADSSEmirMT4Columns.BENEFICIARY_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=PartiesFields.PARTIES_EXECUTING_ENTITY,
                    type=IdentifierType.OBJECT,
                ).dict(by_alias=True)
            )

        return result

    @staticmethod
    def _make_executing_entity_file_identifier(df: pd.DataFrame) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)

        parties_exec_entity_not_null_mask = df[
            ADSSEmirMT4Columns.BENEFICIARY_ID
        ].notnull()

        if parties_exec_entity_not_null_mask.any():
            result.loc[parties_exec_entity_not_null_mask] = df.loc[
                parties_exec_entity_not_null_mask, ADSSEmirMT4Columns.BENEFICIARY_ID
            ].str.lower()

        return result

    @staticmethod
    def _make_trader_identifier(df: pd.DataFrame) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)

        parties_trader_not_null_mask = df[
            ADSSEmirMT4Columns.OTHER_COUNTERPARTY_ID
        ].notnull()

        if parties_trader_not_null_mask.any():
            result.loc[parties_trader_not_null_mask] = df.loc[
                parties_trader_not_null_mask, ADSSEmirMT4Columns.OTHER_COUNTERPARTY_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=PartiesFields.PARTIES_TRADER,
                    type=IdentifierType.ARRAY,
                ).dict(by_alias=True)
            )

        return result

    @staticmethod
    def _make_trader_file_identifier(df: pd.DataFrame) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)

        parties_trader_not_null_mask = df[
            ADSSEmirMT4Columns.OTHER_COUNTERPARTY_ID
        ].notnull()

        if parties_trader_not_null_mask.any():
            result.loc[parties_trader_not_null_mask] = df.loc[
                parties_trader_not_null_mask, ADSSEmirMT4Columns.OTHER_COUNTERPARTY_ID
            ].str.lower()

        return result

    @staticmethod
    def _make_seller_identifier(df: pd.DataFrame, sell_mask: pd.Series) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)

        parties_seller_sell_mask = (
            sell_mask & df[ADSSEmirMT4Columns.BENEFICIARY_ID].notnull()
        )

        if parties_seller_sell_mask.any():
            result.loc[parties_seller_sell_mask] = df.loc[
                parties_seller_sell_mask, ADSSEmirMT4Columns.BENEFICIARY_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=PartiesFields.PARTIES_SELLER,
                    type=IdentifierType.ARRAY,
                ).dict(by_alias=True)
            )

        parties_seller_not_sell_mask = (
            ~sell_mask & df[ADSSEmirMT4Columns.OTHER_COUNTERPARTY_ID].notnull()
        )

        if parties_seller_not_sell_mask.any():
            result.loc[parties_seller_not_sell_mask] = df.loc[
                parties_seller_not_sell_mask, ADSSEmirMT4Columns.OTHER_COUNTERPARTY_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=PartiesFields.PARTIES_SELLER,
                    type=IdentifierType.ARRAY,
                ).dict(by_alias=True)
            )

        return result

    @staticmethod
    def _make_seller_file_identifier(
        df: pd.DataFrame, sell_mask: pd.Series
    ) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)

        parties_seller_sell_mask = (
            sell_mask & df[ADSSEmirMT4Columns.BENEFICIARY_ID].notnull()
        )

        if parties_seller_sell_mask.any():
            result.loc[parties_seller_sell_mask] = df.loc[
                parties_seller_sell_mask, ADSSEmirMT4Columns.BENEFICIARY_ID
            ].str.lower()

        parties_seller_not_sell_mask = (
            ~sell_mask & df[ADSSEmirMT4Columns.OTHER_COUNTERPARTY_ID].notnull()
        )

        if parties_seller_not_sell_mask.any():
            result.loc[parties_seller_not_sell_mask] = df.loc[
                parties_seller_not_sell_mask, ADSSEmirMT4Columns.OTHER_COUNTERPARTY_ID
            ].str.lower()

        return result
