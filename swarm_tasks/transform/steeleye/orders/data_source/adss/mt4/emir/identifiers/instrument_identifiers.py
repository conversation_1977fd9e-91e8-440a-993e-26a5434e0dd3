from typing import Optional

import pandas as pd
from se_elastic_schema.components.market.identifier import Identifier
from se_elastic_schema.static.market import IdentifierType
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.orders.data_source.adss.mt4.emir.identifiers.static import (
    ADSSEmirMT4Columns,
)
from swarm_tasks.transform.steeleye.orders.data_source.adss.mt4.emir.identifiers.static import (
    StaticIdentifiers,
)

INSTRUMENT_PATH = "instrumentDetails.instrument"
# Target Column
MARKET_IDENTIFIERS_INSTRUMENT = "marketIdentifiers.instrument"


class DfColumns:
    CURRENCY_COMBINATION = "CURRENCY_COMBINATION"


class InstrumentId:
    COMMODITIES = "CO"
    CURRENCY = "CU"


class InstrumentIdentifiers(TransformBaseTask):
    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Optional[BaseParams] = None,
        resources: Optional[BaseResources] = None,
        **kwargs
    ) -> pd.DataFrame:

        cols_used = [
            ADSSEmirMT4Columns.INSTRUMENT_CLASSIFICATION,
            ADSSEmirMT4Columns.NOTIONAL_CURRENCY_1,
            ADSSEmirMT4Columns.NOTIONAL_CURRENCY_2,
            ADSSEmirMT4Columns.INSTRUMENT,
            ADSSEmirMT4Columns.INSTRUMENT_ID,
            MARKET_IDENTIFIERS_INSTRUMENT,
        ]
        target = pd.DataFrame(
            index=source_frame.index, columns=[MARKET_IDENTIFIERS_INSTRUMENT]
        )
        if source_frame.empty:
            return target

        for col in cols_used:
            if col not in source_frame.columns:
                source_frame[col] = pd.NA

        cols_mask = source_frame.columns.isin(cols_used)

        df = source_frame.loc[:, cols_mask]
        # create currency combination column default
        df[DfColumns.CURRENCY_COMBINATION] = (
            df[ADSSEmirMT4Columns.NOTIONAL_CURRENCY_2]
            + df[ADSSEmirMT4Columns.NOTIONAL_CURRENCY_1]
        )
        # create currency combination column for currency/commodities instruments
        currency_commodity_mask = (
            df[ADSSEmirMT4Columns.INSTRUMENT_ID]
            .str.upper()
            .isin([InstrumentId.CURRENCY, InstrumentId.COMMODITIES])
        )
        if currency_commodity_mask.any():
            df.loc[currency_commodity_mask, DfColumns.CURRENCY_COMBINATION] = df.loc[
                currency_commodity_mask, ADSSEmirMT4Columns.INSTRUMENT
            ].apply(lambda x: x.split(".")[0] if not pd.isna(x) else pd.NA)

        sb_mask = (
            df[ADSSEmirMT4Columns.INSTRUMENT_CLASSIFICATION].str.upper()
            == StaticIdentifiers.SB
        )
        cfd_mask = (
            df[ADSSEmirMT4Columns.INSTRUMENT_CLASSIFICATION].str.upper()
            == StaticIdentifiers.CD
        )

        if sb_mask.any():
            df.loc[sb_mask, MARKET_IDENTIFIERS_INSTRUMENT] = (
                StaticIdentifiers.VENUE_XXXX
                + df.loc[sb_mask, DfColumns.CURRENCY_COMBINATION]
                + StaticIdentifiers.FX
                + StaticIdentifiers.SB
            )

        if cfd_mask.any():
            df.loc[cfd_mask, MARKET_IDENTIFIERS_INSTRUMENT] = (
                StaticIdentifiers.VENUE_XXXX
                + df.loc[cfd_mask, DfColumns.CURRENCY_COMBINATION]
                + StaticIdentifiers.FX
                + StaticIdentifiers.CFD
            )

        target[MARKET_IDENTIFIERS_INSTRUMENT] = (
            df[MARKET_IDENTIFIERS_INSTRUMENT]
            .dropna()
            .apply(
                lambda x: [
                    Identifier(
                        labelId=x,
                        path=INSTRUMENT_PATH,
                        type=IdentifierType.OBJECT,
                    ).dict(by_alias=True)
                ]
            )
        )
        return target
