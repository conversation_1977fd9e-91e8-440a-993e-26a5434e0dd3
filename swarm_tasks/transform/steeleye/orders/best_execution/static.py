from se_elastic_schema.elastic_schema.core.base import BaseStrEnum


class BestExecutionFields:
    BEST_EX_ASSET_CLASS_MAIN = "bestExAssetClassMain"
    BEST_EXECUTION_DATA_LARGE_IN_SCALE = "bestExecutionData.largeInScale"
    BEST_EXECUTION_DATA_PENDING_DISCLOSURE = "bestExecutionData.pendingDisclosure"
    BEST_EXECUTION_DATA_RTS_27_UTC_TIME_BAND = "bestExecutionData.rts27UtcTimeBand"
    BEST_EXECUTION_DATA_RTS_27_VALUE_BAND = "bestExecutionData.rts27ValueBand"
    BEST_EXECUTION_DATA_TIME_ACCEPT_EXECUTE_PLACED = (
        "bestExecutionData.timeAcceptExecutePlaced"
    )
    BEST_EXECUTION_DATA_TIME_ACCEPT_EXECUTE_RECEIVED = (
        "bestExecutionData.timeAcceptExecuteReceived"
    )
    BEST_EXECUTION_DATA_TIME_RFQ_RESPONSE_PLACED = (
        "bestExecutionData.timeRfqResponsePlaced"
    )
    BEST_EXECUTION_DATA_TIME_RFQ_RESPONSE_RECEIVED = (
        "bestExecutionData.timeRfqResponseReceived"
    )
    BEST_EXECUTION_DATA_TIME_TO_FILL = "bestExecutionData.timeToFill"
    BOND_NOMINAL_UNIT_OR_MIN_TRADED_VALUE = "bond.nominalUnitOrMinTradedValue"
    BOND_NOMINAL_VALUE_CURRENCY = "bond.nominalValueCurrency"
    COMMODITY_AND_EMISSION_ALLOWANCES_SUB_PRODUCT = (
        "commodityAndEmissionAllowances.subProduct"
    )
    DATE = "date"
    DERIVATIVE_PRICE_MULTIPLIER = "derivative.priceMultiplier"
    DERIVATIVE_STRIKE_PRICE = "derivative.strikePrice"
    ECB_REF_RATE = "ecbRefRate"
    EXECUTION_DETAILS_ORDER_TYPE = "executionDetails.orderType"
    EXECUTION_DETAILS_ORDERSTATUS = "executionDetails.orderStatus"
    EXT_BEST_EX_ASSET_CLASS_MAIN = "ext.bestExAssetClassMain"
    EXT_PRICE_NOTATION = "ext.priceNotation"
    EXT_QUANTITY_NOTATION = "ext.quantityNotation"
    FLAGS_COMPUTE_VALUE_AGGREGATION = "flags.computeValueAggregation"
    INSTRUMENT_CLASSIFICATION = "instrumentClassification"
    INSTRUMENT_DETAILS_INSTRUMENT = "instrumentDetails.instrument"
    INSTRUMENT_ID = "&id"
    NATIVE = "native"
    NATIVE_CURRENCY = "nativeCurrency"
    NOMINAL_UNIT_OR_MIN_TRADED_VALUE = "nominalUnitOrMinTradedValue"
    NOTIONAL_CURRENCY_1 = "notionalCurrency1"
    ORDER_ID_CODE = "orderIdentifiers.orderIdCode"
    ORDER_VOLUME = "bestExecutionData.orderVolume"
    PRICE_FORMING_DATA_ASK = "priceFormingData.ask"
    PRICE_FORMING_DATA_BID = "priceFormingData.bid"
    PRICE_FORMING_DATA_PRICE = "priceFormingData.price"
    PRICE_FORMING_DATA_TRADED_QUANTITY = "priceFormingData.tradedQuantity"
    PRICE_FORMING_DATA_INITIAL_QUANTITY = "priceFormingData.initialQuantity"
    PRICE_MULTIPLIER = "priceMultiplier"
    PRICE_NOTATION = "priceNotation"
    QUANTITY_NOTATION = "quantityNotation"
    TIMESTAMPS_ORDER_RECEIVED = "timestamps.orderReceived"
    TIMESTAMPS_ORDER_SUBMITTED = "timestamps.orderSubmitted"
    TRADING_DATETIME = "timestamps.tradingDateTime"
    TRANSACTION_DETAILS_PRICE_CURRENCY = "transactionDetails.priceCurrency"
    TRANSACTION_DETAILS_PRICE_NOTATION = "transactionDetails.priceNotation"
    TRANSACTION_DETAILS_QUANTITY_CURRENCY = "transactionDetails.quantityCurrency"
    TRANSACTION_DETAILS_QUANTITY_NOTATION = "transactionDetails.quantityNotation"
    TRX_VALUE = "bestExecutionData.transactionValue"
    TRX_VOLUME = "bestExecutionData.transactionVolume"


class EODDataFields:
    DATE = "Date"
    CURRENCY = "Currency"
    PRICE = "Close Price"


class TempCols:
    EOD_CURRENCY = "__eod_currency__"
    EOD_DATA = "__eod_data__"
    EOD_PRICE = "__eod_price__"
    INSTRUMENT_CODE = "__inst_code__"
    NATIVE_CURRENCY_ORDER = "__native_currency_order__"
    NATIVE_CURRENCY_ORDER_STATE = "__native_currency_order_state__"
    NOMINAL_UNIT = "__nominal_unit__"
    ORDER_FLAG = "__order_flag__"
    ORDER_STATE_FLAG = "__order_state_flag__"
    ORDER_PRICE = "__order_price__"
    PRICE = "__price__"
    PRICE_MULTIPLIER = "__price_multiplier__"
    PRICE_NOTATION = "__price_notation__"
    QUANTITY = "__quantity__"
    STRIKE_PRICE = "__strike_price__"
    VOLUME = "__volume__"


class RequiredColumnsForBestEx(BaseStrEnum):
    DATE = "date"
    EXECUTION_DETAILS_ORDER_TYPE = "executionDetails.orderType"
    EXECUTION_DETAILS_ORDERSTATUS = "executionDetails.orderStatus"
    INSTRUMENT_DETAILS_INSTRUMENT = "instrumentDetails.instrument"
    ORDER_ID_CODE = "orderIdentifiers.orderIdCode"
    PRICE_FORMING_DATA_INITIAL_QUANTITY = "priceFormingData.initialQuantity"
    PRICE_FORMING_DATA_PRICE = "priceFormingData.price"
    PRICE_FORMING_DATA_TRADED_QUANTITY = "priceFormingData.tradedQuantity"
    TIMESTAMPS_ORDER_RECEIVED = "timestamps.orderReceived"
    TIMESTAMPS_ORDER_SUBMITTED = "timestamps.orderSubmitted"
    TRADING_DATETIME = "timestamps.tradingDateTime"
    TRANSACTION_DETAILS_PRICE_CURRENCY = "transactionDetails.priceCurrency"
    TRANSACTION_DETAILS_PRICE_NOTATION = "transactionDetails.priceNotation"
    TRANSACTION_DETAILS_QUANTITY_CURRENCY = "transactionDetails.quantityCurrency"
    TRANSACTION_DETAILS_QUANTITY_NOTATION = "transactionDetails.quantityNotation"


DATE_FORMAT = "%Y-%m-%d"
