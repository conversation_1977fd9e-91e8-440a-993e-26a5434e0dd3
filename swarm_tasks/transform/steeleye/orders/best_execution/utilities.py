import datetime
import logging
from collections import Iterable
from typing import List
from typing import Optional
from typing import Union

import pandas as pd
from se_elastic_schema.static.mifid2 import QuantityNotation

logger_ = logging.getLogger(__name__)

MAX_INT = 2147483000

PRICE_TYPE_MONE_NML = [QuantityNotation.MONE.value, QuantityNotation.NOML.value]

currency_list = [
    "USD",
    "JPY",
    "BGN",
    "CZK",
    "DKK",
    "GBP",
    "HUF",
    "PLN",
    "RON",
    "SEK",
    "CHF",
    "NOK",
    "HRK",
    "RUB",
    "TRY",
    "AUD",
    "BRL",
    "CAD",
    "CNY",
    "HKD",
    "IDR",
    "ILS",
    "INR",
    "KRW",
    "MXN",
    "MYR",
    "NZD",
    "PHP",
    "SGD",
    "THB",
    "ZAR",
]


class AssetClass:
    OTHER = "Other Instruments"
    CFD = "Contracts for Difference"
    CRED_DER = "Credit Derivatives"
    CURR_DER = "Currency Derivatives"
    BOND = "BOND"
    DEBT = "Debt Instruments"
    EQUITY = "Equity"
    EQUI_DER = "Equity Derivatives"
    ETP = "Exchange Traded Products"
    INTR_DER = "Interest Rate Derivatives"
    SECU_DER = "Securitised Derivatives"
    SFI = "Structured Finance Instruments"
    COMM_DER = "Commodities derivatives and emission allowances Derivatives"


class InstrumentSubLevel:
    EXT = "ext"
    DERIVATIVE = "derivative"


class CommodityEmissionAllowanceSubPrd:
    EMIS = "EMIS"
    CRBR = "CRBR"


def fx_conversion(
    native: float,
    currency: str,
    date: str,
    fx: pd.DataFrame,
    target_currency: Optional[str] = None,
) -> Union[dict, str]:
    if empty([native]) or empty([currency]):
        return pd.NA
    conversion = {
        "native": native,
        "nativeCurrency": currency,
        "ecbRefRate": calc_ecb_ref_rate(native, currency, date, fx),
    }
    if target_currency:
        converted = conversion["ecbRefRate"].get(target_currency)
        if converted is None:
            return pd.NA
        return converted
    return conversion


def calc_ecb_ref_rate(
    native: float, currency: str, date: str, fx: pd.DataFrame
) -> dict:
    """
    calculates ecb reference rates based on currency
    """
    if pd.isna(date) or not date:
        return {}

    try:
        ecb = previous_valid_date(fx, date)
        if currency in ["XAG", "XAU"]:
            currency = "USD"
        elif currency == "CNH":
            currency = "CNY"
        if currency != "EUR" and pd.isna(ecb.get(currency)):
            return pd.NA
        euro = native if currency == "EUR" else 1 / ecb.get(currency) * native
        converted = dict(refRateDate=ecb.get("timestamp"), EUR=euro)
        for cur in currency_list:
            if cur in ecb:
                if cur != currency:
                    converted[cur] = euro * ecb.get(cur)
                else:
                    converted[cur] = native
        return converted

    except Exception as e:
        logger_.error(
            f"Error in calc_ecb_ref_rate for native={native}, currency={currency} and date={date}: {e}"
        )
        return {}


def previous_valid_date(fx: pd.DataFrame, date: str) -> pd.Series:
    """
    returns previous valid date ref rate as a series.
    checks for the input date and upto  five previous business dates
    and returns one of those dates if found in the fx dataframe.
    """
    date_list = fx.index.to_list()

    if date[:10] in date_list:
        return fx.loc[date[:10]]

    target = datetime.datetime.strptime(date[:10], "%Y-%m-%d")
    for i in range(0, 5):
        target = target - datetime.timedelta(days=1)
        if target.strftime("%Y-%m-%d") in date_list:
            return fx.loc[target.strftime("%Y-%m-%d")]

    return pd.Series(index=fx.index)


def is_empty(val: Optional[Union[str, Iterable]]) -> bool:
    return (
        val is None
        or pd.isna(val)
        or (isinstance(val, str) and len(val.strip()) == 0)
        or (isinstance(val, Iterable) and len(val) == 0)
        or (isinstance(val, str) and val.lower() == "nan")
    )


def empty(val_list: List) -> bool:
    return all(is_empty(x) for x in val_list)
