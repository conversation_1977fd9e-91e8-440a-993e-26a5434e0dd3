import base64
import json
from datetime import datetime
from io import Bytes<PERSON>
from pathlib import Path
from typing import Any
from typing import Optional
from uuid import uuid4

import filetype
import pydub
from boto3 import Session
from dateutil.relativedelta import relativedelta

from swarm_tasks.transform.steeleye.comms.voice.transcription.static import (
    TranscriptionRules,
)


class TranscriptionJobNameEncoderDecoder:
    @classmethod
    def encode_job_name(cls, amp_id: str) -> str:
        """
        Encodes name to match requirements of AWS transcription job name
        https://docs.aws.amazon.com/transcribe/latest/dg/API_StartTranscriptionJob.html#transcribe-StartTranscriptionJob-request-TranscriptionJobName

        transcription jobs must keep a reference to the input call for the job.
        AWS transcribe does not currently support adding info to a job so the only way to reference the
        call in the job is via the job name.

        aws transcribe allows a limited set of characters for job names and call &id's may not conform to this
        if an invalid job name is used, the job will not commence and an error will be raised

        valid job names must be used while retaining a reference to the call &id

        base64 encoded version of name (call &id) with few b64 characters replaced to conform
        to aws transcription job name regex [0-9a-zA-Z._-] vs b64 regex [A-Za-z0-9+/=]
        using a base64 encoded string ensures we can derive the corresponding call for the transcription job
        """

        b64: bytes = base64.b64encode(amp_id.encode("utf8"))
        # replace b64 characters that don't conform to aws transcribe job name regex [0-9a-zA-Z._-]
        encoded_name: str = (
            b64.decode().replace("=", "_").replace("+", ".").replace("/", "-")
        )
        return encoded_name

    @classmethod
    def _decode_job_name(cls, name: str) -> str:
        """
        :param name: AWS transcribe job name

        reverses the operation of encode_job_name() to get the &id of the call for the transcribe job
        this method must always be the inverse of encode_job_name()
        """
        b64: str = name.replace("_", "=").replace(".", "+").replace("-", "/")
        decoded: str = base64.b64decode(b64).decode()
        return decoded

    @classmethod
    def call_meta_id_from_output_key(cls, key: str) -> str:
        """
        :param key: S3 key name for output transcription file

        given the output S3 key for an AWS transcribe job, return the &id for the call used
        filename of S3 key is a (base64 with some characters changed) encoded version of the call &id
        """

        # stem used to strip off extension from filename e.g. foo.auto -> foo
        # just the filename stem is b64 encoded so including the extension will produce incorrect output
        name = Path(key).stem
        return cls._decode_job_name(name)


def s3_upload(
    file: bytes, bucket: str, file_path: Optional[str], session: Optional[Session]
):
    """
    uploads the bytes file to the destination bucket and file path
    Multipart, retry, streaming and chunck for s3 ( ftp, sftp, ...)
    https://github.com/RaRe-Technologies/smart_open
    :param file:
    :param bucket:
    :param file_path:
    :param session:
    :return:
    """

    if not session:
        session = Session()

    path = get_path(bucket, file_path)

    if not isinstance(file, bytes):
        file = str(file).encode()

    with open(path, "wb", transport_params={"session": session}) as savefile:
        savefile.write(file)
        s3_meta = savefile.to_boto3(resource=session.resource("s3"))

    return s3_meta


def get_path(bucket: str, file_path: str):
    if not file_path:
        file_path = f"{uuid4().hex}"
    if file_path.startswith("/"):
        file_path = file_path.lstrip("/")
    return f"s3://{Path(bucket).joinpath(Path(file_path)).as_posix()}"


def s3_read(
    bucket: str,
    path: str,
    force_string: Optional[bool] = True,
    session: Optional[Session] = None,
) -> Any:
    """
    returns the string contents of a file but if force_string is False.
    Warning this can use a lot of memmory given the file size

    Multipart, retry, streaming and chunck for s3 ( ftp, sftp, ...)
    https://github.com/RaRe-Technologies/smart_open
    :param bucket: str bucket name
    :param path: path and key of the file
    :param force_string: bool forces the result to be a string
    :param session: boto3 Session
    :return: Any but normally string
    """

    if not session:
        session = Session()

    try:
        with open(
            f"s3://{bucket}/{path}", "rb", transport_params={"session": session}
        ) as last:
            data = last.read()

    except OSError:
        data = ""

    if force_string:
        try:
            return data.decode()
        except AttributeError:
            return str(data)

    return data


def get_last_execution(
    bucket: str,
    path: Optional[str] = "config/last_audio.txt",
    session: Optional[Session] = None,
) -> Optional[datetime]:
    """
    retrieves the last file created
    :param bucket: butcket name
    :param path: extra prefix for the execution
    :param session: boto3 Session
    :return: datetime
    """

    data = s3_read(bucket, f"{path}", session=session)

    if not data:
        return

    return data


def set_last_execution(
    bucket: str,
    last: Optional[str],
    path: Optional[str] = "config/last_audio.txt",
    session: Optional[Session] = None,
):
    """
    sets the last file created
    :param bucket: butcket name
    :param last: datetime for the last execution
    :param path: extra prefix for the execution
    :param session: boto3 Session
    :return:
    """

    if not last:
        last = datetime.now().isoformat().split(".")[0]

    s3_upload(last.encode(), bucket, path, session=session)


def guess_filetype(file: bytes, filename: Optional[str] = None) -> Optional[str]:
    """
    returns the filetype first from the file content and from filename if file fails
    :param file: bytes file
    :param filename: str filename with extension
    :return: str or None
    """
    kind = filetype.guess(file)
    if not kind:
        if filename:
            tmp = filename.split(".")
            if len(tmp) > 1:
                return tmp[-1]
        return

    return kind.extension


def to_mp3(file: bytes, name: Optional[str] = None) -> dict:
    """
    converts the given audio file to mp3, avoids work if the file is already a mp3 audio
    :param file: bytes file
    :param name: options filename for the file
    :return: bytes as mp3
    """
    format_ = guess_filetype(file)

    if format_ == "mp3":
        return file, f"{name}.mp3" if name else f"{uuid4().hex}.mp3"

    tmp = pydub.AudioSegment.from_file(BytesIO(file), format_)

    return tmp.export(format="mp3"), f"{name}.mp3" if name else f"{uuid4().hex}.mp3"


def date_range(
    start_date: datetime, end_date: datetime, increment: int, period: str
) -> list:
    """
    returns a date range
    """
    result = []
    nxt = start_date
    delta = relativedelta(**{period: increment})
    while nxt <= end_date:
        result.append(nxt)
        nxt += delta
    return result


def get_voice_config(bucket: str, config_path: str) -> TranscriptionRules:
    """
    donload and process the configuration
    """
    config_data = s3_read(bucket, config_path)
    if config_data:
        return TranscriptionRules(**json.loads(config_data))

    return TranscriptionRules()
