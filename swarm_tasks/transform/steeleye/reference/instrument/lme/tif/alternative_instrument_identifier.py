from datetime import datetime

import pandas as pd
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.reference.instrument.lme.tif import static


class Column:
    VENUE = "venue.tradingVenue"
    CONTRACT_CODE = static.TIFColumns.CONTRACT_CODE
    NOTIONAL_CURRENCY = "notionalCurrency1"
    CFI = static.TIFColumns.CFI
    MATURITY = static.TIFColumns.MATURITY
    CONTRACT_TYPE = static.TIFColumns.CONTRACT_TYPE
    STRIKE_PRICE = static.TIFColumns.STRIKE_PRICE
    ALL_COLUMNS = [
        VENUE,
        CONTRACT_CODE,
        NOTIONAL_CURRENCY,
        CFI,
        MATURITY,
        CONTRACT_TYPE,
        STRIKE_PRICE,
    ]


TARGET_ATTRIBUTE = "ext.alternativeInstrumentIdentifier"


class AlternativeInstrumentIdentifier(TransformBaseTask):
    """
    This task creates the alternative instrument identifier
    """

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: BaseParams = None,
        resources: BaseResources = None,
        **kwargs,
    ) -> pd.DataFrame:

        for col in Column.ALL_COLUMNS:
            if col not in source_frame.columns:
                source_frame[col] = pd.NA

        data = source_frame[Column.ALL_COLUMNS]

        data.loc[:, Column.MATURITY] = (
            pd.to_datetime(data[Column.MATURITY].fillna(pd.NaT))
            .dropna()
            .dt.date.apply(self.format_date)
        )

        data[[Column.CONTRACT_CODE, Column.CFI]] = (
            data[[Column.CONTRACT_CODE, Column.CFI]].dropna().apply(lambda x: x.str[:2])
        )

        data[Column.CFI] = data[Column.CFI].str.replace("^F.", "FF")

        data[Column.STRIKE_PRICE] = (
            data[Column.STRIKE_PRICE].dropna().apply(lambda x: f"{x:.8f}")
        )

        data.loc[data[Column.CONTRACT_TYPE] == "OPTN", TARGET_ATTRIBUTE] = (
            data.loc[
                data[Column.CONTRACT_TYPE] == "OPTN",
                [
                    Column.VENUE,
                    Column.CONTRACT_CODE,
                    Column.NOTIONAL_CURRENCY,
                    Column.CFI,
                    Column.MATURITY,
                    Column.STRIKE_PRICE,
                ],
            ]
            .dropna()
            .apply(lambda x: "".join(x.tolist()), axis=1)
        )

        data.loc[data[Column.CONTRACT_TYPE] != "OPTN", TARGET_ATTRIBUTE] = (
            data.loc[
                data[Column.CONTRACT_TYPE] != "OPTN",
                [
                    Column.VENUE,
                    Column.CONTRACT_CODE,
                    Column.NOTIONAL_CURRENCY,
                    Column.CFI,
                    Column.MATURITY,
                ],
            ]
            .dropna()
            .apply(lambda x: "".join(x.tolist()), axis=1)
        )

        result = data[TARGET_ATTRIBUTE].to_frame()

        return result

    @staticmethod
    def format_date(dtm: datetime) -> str:
        date_string = dtm.isoformat()
        return "-".join(date_string.split("-")) + " 00:00:00"
