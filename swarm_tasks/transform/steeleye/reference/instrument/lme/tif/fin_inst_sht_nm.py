import pandas as pd
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.reference.instrument.lme.tif import static


class Column:
    CONTRACT_CODE = static.TIFColumns.CONTRACT_CODE
    CONTRACT_TYPE = static.TIFColumns.CONTRACT_TYPE
    MATURITY = static.TIFColumns.MATURITY
    ALL_COLUMNS = [
        CONTRACT_CODE,
        MATURITY,
        CONTRACT_TYPE,
    ]


class Static:
    LME = "LME/"


TARGET_ATTRIBUTE = "venue.financialInstrumentShortName"


class FinancialInstrumentShortName(TransformBaseTask):
    """
    This task creates the financial instrument short name
    """

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: BaseParams = None,
        resources: BaseResources = None,
    ) -> pd.DataFrame:

        for col in Column.ALL_COLUMNS:
            if col not in source_frame.columns:
                source_frame[col] = pd.np.nan

        data = source_frame[Column.ALL_COLUMNS]

        data.loc[:, Column.MATURITY] = (
            pd.to_datetime(data[Column.MATURITY])
            .dropna()
            .dt.date.apply(lambda x: x.isoformat())
        )

        data[Column.CONTRACT_CODE] = data[Column.CONTRACT_CODE].dropna().str[:2]

        data[TARGET_ATTRIBUTE] = data.dropna().apply(
            lambda x: Static.LME + " ".join(x.tolist()),
            axis=1,
        )

        result = data[TARGET_ATTRIBUTE].to_frame()

        return result
