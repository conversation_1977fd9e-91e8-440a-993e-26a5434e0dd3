import pandas as pd
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.static import (
    SteelEyeTradeBlotterColumns,
)

TRADING_VENUE = "transactionDetails.venue"
ULTIMATE_TRADING_VENUE = "transactionDetails.ultimateVenue"
PARTIES_COUNTERPARTY = "parties.counterparty"
FIRMIDENTIFIERS_MIC = "firmIdentifiers.mic"

ULTIMATE_VENUE_MAP = {
    "CITI VELOCITY": "CBNL",
    "UBS NEO": "UBSY",
    "HSBC EVOLVE": "HSBC",
    "BNP CORTEX": "BNPS",
    "MS FUSION": "MSSI",
}


class Venues:

    INTC = "INTC"
    XOFF = "XOFF"
    FXCONNECT = "FXCONNECT"


class TradingVenue(TransformBaseTask):
    """
    This task creates the transactionDetails.venue and transactionDetails.ultimateVenue columns;
    business logic is specific to "mg" client
    """

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: BaseParams = None,
        resources: BaseResources = None,
        **kwargs
    ) -> pd.DataFrame:

        df = pd.DataFrame(index=source_frame.index)
        df[TRADING_VENUE] = pd.NA

        if source_frame.empty:
            return df

        counter_party_mask = (
            ultimate_venue_map_mask
        ) = ultimate_venue_fx_connect_mask = pd.Series(False, index=source_frame.index)

        if SteelEyeTradeBlotterColumns.COUNTERPARTY_ID in source_frame.columns:
            counter_party_mask = counter_party_mask | (
                source_frame.loc[
                    :, SteelEyeTradeBlotterColumns.COUNTERPARTY_ID
                ].str.upper()
                == Venues.INTC
            )
            df.loc[counter_party_mask, TRADING_VENUE] = Venues.XOFF

        if SteelEyeTradeBlotterColumns.ULTIMATE_VENUE in source_frame.columns:
            ultimate_venue_fx_connect_mask = ultimate_venue_fx_connect_mask | (
                (~counter_party_mask)
                & (
                    source_frame.loc[
                        :, SteelEyeTradeBlotterColumns.ULTIMATE_VENUE
                    ].str.upper()
                    == Venues.FXCONNECT
                )
            )
            df.loc[ultimate_venue_fx_connect_mask, TRADING_VENUE] = source_frame.loc[
                ultimate_venue_fx_connect_mask, SteelEyeTradeBlotterColumns.EXCHANGE_MIC
            ]

            ultimate_venue_map_mask = (
                ~(counter_party_mask | ultimate_venue_fx_connect_mask)
            ) & (
                source_frame.loc[:, SteelEyeTradeBlotterColumns.ULTIMATE_VENUE]
                .str.upper()
                .isin(ULTIMATE_VENUE_MAP.keys())
            )
            df[TRADING_VENUE][ultimate_venue_map_mask] = (
                source_frame.loc[:, SteelEyeTradeBlotterColumns.ULTIMATE_VENUE]
                .str.upper()
                .map(ULTIMATE_VENUE_MAP)
            )

        default_mask = ~(
            counter_party_mask
            | ultimate_venue_fx_connect_mask
            | ultimate_venue_map_mask
        )

        default_mask_counterparties = source_frame.loc[
            default_mask, PARTIES_COUNTERPARTY
        ]

        if (
            PARTIES_COUNTERPARTY in source_frame.columns
            and default_mask_counterparties.any()
        ):
            df.loc[default_mask, TRADING_VENUE] = default_mask_counterparties.loc[
                ~default_mask_counterparties.isnull()
            ].apply(lambda x: x.get(FIRMIDENTIFIERS_MIC))

        # Requested on ON-2820: if [Instrument Asset Class] contains “future” then use [Exchange MIC]
        if (
            SteelEyeTradeBlotterColumns.INSTRUMENT_ASSET_CLASS in source_frame.columns
            and SteelEyeTradeBlotterColumns.EXCHANGE_MIC in source_frame.columns
        ):
            future_asset_class_mask = source_frame.loc[
                :, SteelEyeTradeBlotterColumns.INSTRUMENT_ASSET_CLASS
            ].str.match("future", na=False, case=False)
            df.loc[future_asset_class_mask, TRADING_VENUE] = source_frame.loc[
                future_asset_class_mask, SteelEyeTradeBlotterColumns.EXCHANGE_MIC
            ]

        # trading venue logic is the same for ultimate trading venue
        df.loc[:, ULTIMATE_TRADING_VENUE] = df.loc[:, TRADING_VENUE]

        return df
