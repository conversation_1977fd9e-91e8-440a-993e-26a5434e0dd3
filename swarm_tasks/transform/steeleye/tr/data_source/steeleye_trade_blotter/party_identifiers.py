from typing import Any
from typing import List
from typing import Optional

import pandas as pd
from pydantic import Field
from se_core_tasks.feeds.generic.get_tenant_lei import Params as ParamsGetTenantLEI
from se_core_tasks.feeds.generic.get_tenant_lei import run_get_tenant_lei
from se_elastic_schema.components.market.identifier import Identifier
from se_elastic_schema.models import AccountFirm
from se_elastic_schema.models import Account<PERSON>erson
from se_elastic_schema.models import MarketCounterparty
from se_elastic_schema.models import MarketPerson
from se_elastic_schema.static.market import IdentifierType
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_elastic_schema.static.reference import ClientMandateType
from se_elastic_schema.validators.iso.lei import LEI
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.static import (
    SteelEyeTradeBlotterColumns,
)


class Params(BaseParams):
    firm_id: Optional[str] = Field(
        None, description="If exists, hard-codes `Executing Entity ID` values"
    )
    override_discretionary: bool = False
    override_non_discretionary: bool = False
    override_non_lei_prefix: Optional[str] = Field(
        None,
        description="If exists, it hard-codes the 'account' prefix for non lei party identifiers",
    )


class Rts22TransactionModelField:
    TRX_DTL_BUY_SELL_INDICATOR = "transactionDetails.buySellIndicator"
    TRX_DTL_TRADING_CAPACITY = "transactionDetails.tradingCapacity"
    PARTIES_BUYER = "parties.buyer"
    PARTIES_BUYER_DECISION_MAKER = "parties.buyerDecisionMaker"
    PARTIES_COUNTERPARTY = "parties.counterparty"
    PARTIES_EXECUTING_ENTITY = "parties.executingEntity"
    PARTIES_EXECUTION_WITHIN_FIRM = "parties.executionWithinFirm"
    PARTIES_INVESTMENT_DECISION_WITHIN_FIRM = "parties.investmentDecisionWithinFirm"
    PARTIES_SELLER = "parties.seller"
    PARTIES_SELLER_DECISION_MAKER = "parties.sellerDecisionMaker"
    PARTIES_TRADER = "parties.trader"


class DFColumns:
    CLIENT = "CLIENT"
    CLIENT_MANDATE = "CLIENT_MANDATE"
    DECISION_MAKER_WITHIN_FIRM = "DECISION_MAKER_WITHIN_FIRM"
    FALLBACK_MANDATE = "FALLBACK_MANDATE"
    MARKET_IDENTIFIERS_PARTIES = "marketIdentifiers.parties"
    TENANT_LEI = "__tenant_lei__"
    TRADING_CAPACITY = "transactionDetails.tradingCapacity"


class Identifiers:
    TRADE_FILE_IDENTIFIERS = "sinkIdentifiers.tradeFileIdentifiers"
    DETAILS_CLIENT_MANDATE = "details.clientMandate"
    ID = "id"
    INTC = "intc"
    CLNT_NORE = "clnt:nore"


class PartyIDPrefix:
    LEI = "lei"
    ACCOUNT = "account"


class PartyIdentifiers(TransformBaseTask):
    """
    This task should create all the columns with ids which will be used
    by LinkParties to retrieve the documents to embed in the record.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:

        target = pd.DataFrame(
            data=pd.NA,
            index=source_frame.index,
            columns=[DFColumns.MARKET_IDENTIFIERS_PARTIES],
        )
        if source_frame.empty:
            return target

        es_client = Settings.connections.get("tenant-data")

        cols_used = [
            SteelEyeTradeBlotterColumns.CLIENT_ID,
            SteelEyeTradeBlotterColumns.COUNTERPARTY_ID,
            SteelEyeTradeBlotterColumns.EXECUTING_ENTITY_ID,
            SteelEyeTradeBlotterColumns.INVESTMENT_DECISION_MAKER,
            SteelEyeTradeBlotterColumns.TRADER_ID,
            Rts22TransactionModelField.TRX_DTL_BUY_SELL_INDICATOR,
            DFColumns.TRADING_CAPACITY,
            SteelEyeTradeBlotterColumns.EXECUTION_WITHIN_FIRM,
        ]
        df = source_frame.loc[:, source_frame.columns.intersection(cols_used)]
        for col in cols_used:
            if col not in df.columns:
                df.loc[:, col] = pd.NA

        # use the tenant lei in account firm if none is provided
        account_firm_tenant_lei_df = run_get_tenant_lei(
            source_frame=source_frame,
            params=ParamsGetTenantLEI(target_lei_column=DFColumns.TENANT_LEI),
            tenant=Settings.tenant,
            es_client=es_client,
        )

        null_executing_entity = df[
            SteelEyeTradeBlotterColumns.EXECUTING_ENTITY_ID
        ].isnull()

        df.loc[
            null_executing_entity, SteelEyeTradeBlotterColumns.EXECUTING_ENTITY_ID
        ] = account_firm_tenant_lei_df.loc[null_executing_entity, DFColumns.TENANT_LEI]

        # override EXECUTING_ENTITY_ID if specified in params
        if params.firm_id is not None:
            df[SteelEyeTradeBlotterColumns.EXECUTING_ENTITY_ID] = params.firm_id

        # Assign Client Mandate
        client_map = self._get_client_map(df=df, es_client=es_client)

        intc_mask = df[SteelEyeTradeBlotterColumns.CLIENT_ID].str.fullmatch(
            Identifiers.INTC, case=False, na=False
        )
        deal_mask = df[DFColumns.TRADING_CAPACITY].str.fullmatch(
            TradingCapacity.DEAL, case=False, na=False
        )
        match_mask = df[DFColumns.TRADING_CAPACITY].str.fullmatch(
            TradingCapacity.MTCH, case=False, na=False
        )

        match_or_intc_mask = match_mask | intc_mask

        # process df with required transformations
        df = self._process_data(
            df=df,
            client_map=client_map,
            params=params,
            match_or_intc_mask=match_or_intc_mask,
            deal_mask=deal_mask,
        )

        # required masks
        buy_mask = (
            df[Rts22TransactionModelField.TRX_DTL_BUY_SELL_INDICATOR]
            == BuySellIndicator.BUYI
        )

        sell_mask = (
            df[Rts22TransactionModelField.TRX_DTL_BUY_SELL_INDICATOR]
            == BuySellIndicator.SELL
        )

        identifiers_df: pd.DataFrame = pd.DataFrame(index=df.index)
        # buyer
        identifiers_df[
            Rts22TransactionModelField.PARTIES_BUYER
        ] = self._make_buyer_identifier(df=df, buy_mask=buy_mask)

        # buyer decision maker
        identifiers_df[
            Rts22TransactionModelField.PARTIES_BUYER_DECISION_MAKER
        ] = self._make_buyer_decision_maker_identifier(
            df=df,
            buy_mask=buy_mask,
            match_or_intc_mask=match_or_intc_mask,
            deal_mask=deal_mask,
        )

        # counterparty
        identifiers_df[
            Rts22TransactionModelField.PARTIES_COUNTERPARTY
        ] = self._make_counterparty_identifier(df=df)

        # executing entity
        identifiers_df[
            Rts22TransactionModelField.PARTIES_EXECUTING_ENTITY
        ] = self._make_executing_entity_identifier(df=df)

        # execution within firm
        identifiers_df[
            Rts22TransactionModelField.PARTIES_EXECUTION_WITHIN_FIRM
        ] = self._make_execution_within_firm_identifier(df=df)

        # investment decision within firm
        identifiers_df[
            Rts22TransactionModelField.PARTIES_INVESTMENT_DECISION_WITHIN_FIRM
        ] = self._make_investment_decision_within_firm_identifier(df=df)

        # seller
        identifiers_df[
            Rts22TransactionModelField.PARTIES_SELLER
        ] = self._make_seller_identifier(df=df, sell_mask=sell_mask)

        # seller decision maker
        identifiers_df[
            Rts22TransactionModelField.PARTIES_SELLER_DECISION_MAKER
        ] = self._make_seller_decision_maker(
            df=df,
            sell_mask=sell_mask,
            match_or_intc_mask=match_or_intc_mask,
            deal_mask=deal_mask,
        )

        # trader
        identifiers_df[
            Rts22TransactionModelField.PARTIES_TRADER
        ] = self._make_trader_identifier(df=df)

        cols_to_merge = [
            Rts22TransactionModelField.PARTIES_BUYER,
            Rts22TransactionModelField.PARTIES_BUYER_DECISION_MAKER,
            Rts22TransactionModelField.PARTIES_EXECUTING_ENTITY,
            Rts22TransactionModelField.PARTIES_SELLER,
            Rts22TransactionModelField.PARTIES_SELLER_DECISION_MAKER,
            Rts22TransactionModelField.PARTIES_COUNTERPARTY,
            Rts22TransactionModelField.PARTIES_INVESTMENT_DECISION_WITHIN_FIRM,
            Rts22TransactionModelField.PARTIES_EXECUTION_WITHIN_FIRM,
            Rts22TransactionModelField.PARTIES_TRADER,
        ]

        # Merge columns to create PartiesFields.MARKET_IDENTIFIERS_PARTIES
        identifiers_df.loc[:, DFColumns.MARKET_IDENTIFIERS_PARTIES] = identifiers_df[
            cols_to_merge
        ].apply(lambda x: x.dropna().tolist(), axis=1)

        return identifiers_df[DFColumns.MARKET_IDENTIFIERS_PARTIES].to_frame()

    def _get_client_map(self, df: pd.DataFrame, es_client: Any) -> dict:
        cols_mask = df.columns.isin(
            [
                SteelEyeTradeBlotterColumns.CLIENT_ID,
                SteelEyeTradeBlotterColumns.INVESTMENT_DECISION_MAKER,
            ]
        )

        ids = list(df.loc[:, cols_mask].stack().dropna().unique())

        if not ids:
            return {}

        query = self._trade_file_identifiers_query(ids=ids, es_client=es_client)

        index = ",".join(
            [
                AccountPerson.get_elastic_index_alias(tenant=Settings.tenant),
                MarketPerson.get_elastic_index_alias(tenant=Settings.tenant),
                AccountFirm.get_elastic_index_alias(tenant=Settings.tenant),
                MarketCounterparty.get_elastic_index_alias(tenant=Settings.tenant),
            ]
        )
        result = es_client.scroll(query=query, index=index)

        if result.empty:
            return {}

        result = result.explode(Identifiers.TRADE_FILE_IDENTIFIERS)
        result[Identifiers.ID] = result[Identifiers.TRADE_FILE_IDENTIFIERS].apply(
            lambda x: x.get(Identifiers.ID)
        )

        if Identifiers.DETAILS_CLIENT_MANDATE not in result.columns:
            result[Identifiers.DETAILS_CLIENT_MANDATE] = pd.NA

        map_ = result.set_index(Identifiers.ID)[
            Identifiers.DETAILS_CLIENT_MANDATE
        ].to_dict()

        return map_

    @staticmethod
    def _trade_file_identifiers_query(ids: List[str], es_client) -> dict:
        max_terms_size = es_client.MAX_TERMS_SIZE
        size = es_client.SIZE
        meta = es_client.meta
        nested_path = Identifiers.TRADE_FILE_IDENTIFIERS
        ids = [x.lower() for x in ids]
        if len(ids) > max_terms_size:
            ids_chunks = [
                ids[ix : ix + max_terms_size]
                for ix in range(0, len(ids), max_terms_size)
            ]
            shoulds = [{"terms": {f"{nested_path}.id": chunk}} for chunk in ids_chunks]

            condition = {"should": shoulds, "minimum_should_match": 1}
        else:
            condition = {"filter": {"terms": {f"{nested_path}.id": ids}}}

        identifier_query = {
            "nested": {"path": nested_path, "query": {"bool": condition}}
        }

        query = {
            "size": size,
            "_source": {
                "includes": [
                    "details.clientMandate",
                    "sinkIdentifiers.tradeFileIdentifiers",
                ]
            },
            "query": {
                "bool": {
                    "filter": [
                        {
                            "terms": {
                                meta.model: [
                                    "AccountPerson",
                                    "MarketPerson",
                                    "AccountFirm",
                                    "MarketCounterparty",
                                ]
                            }
                        },
                        identifier_query,
                    ]
                }
            },
        }

        return query

    @staticmethod
    def _get_decision_maker_within_firm(
        df: pd.DataFrame, match_or_intc_mask: pd.Series.bool, deal_mask: pd.Series.bool
    ) -> pd.Series:
        """
        If [Client ID] == INTC or TradingCapacity == MATCH:
            DECISION_MAKER_WITHIN_FIRM = EMPTY
        elif TradingCapacity == DEAL:
            DECISION_MAKER_WITHIN_FIRM = [Investment Decision Maker]
        elif Client_Mandate == DISCRETIONARY:
            DECISION_MAKER_WITHIN_FIRM = [Investment Decision Maker] or [Trader ID]
        else:
            DECISION_MAKER_WITHIN_FIRM = [Investment Decision Maker]
        """
        df.loc[:, DFColumns.DECISION_MAKER_WITHIN_FIRM] = pd.NA
        df.loc[~match_or_intc_mask, DFColumns.DECISION_MAKER_WITHIN_FIRM] = df.loc[
            ~match_or_intc_mask, SteelEyeTradeBlotterColumns.INVESTMENT_DECISION_MAKER
        ]
        deal_not_mtch_or_intc_mask = deal_mask & ~match_or_intc_mask
        if deal_not_mtch_or_intc_mask.any():
            df.loc[
                deal_not_mtch_or_intc_mask, DFColumns.DECISION_MAKER_WITHIN_FIRM
            ] = df.loc[
                deal_not_mtch_or_intc_mask,
                SteelEyeTradeBlotterColumns.INVESTMENT_DECISION_MAKER,
            ]

        discretionary_not_mtch_or_intc = ~match_or_intc_mask & (
            df[DFColumns.CLIENT_MANDATE] == ClientMandateType.DISCRETIONARY
        )
        # when client mandate is discretionary add fallback as [TRADER ID]
        if discretionary_not_mtch_or_intc.any():
            use_trader_mask = (
                discretionary_not_mtch_or_intc
                & df[SteelEyeTradeBlotterColumns.INVESTMENT_DECISION_MAKER].isnull()
                & df[SteelEyeTradeBlotterColumns.TRADER_ID].notnull()
            )
            df.loc[use_trader_mask, DFColumns.DECISION_MAKER_WITHIN_FIRM] = df.loc[
                use_trader_mask,
                SteelEyeTradeBlotterColumns.TRADER_ID,
            ]

        return df[DFColumns.DECISION_MAKER_WITHIN_FIRM]

    @staticmethod
    def _get_execution_within_firm(
        df: pd.DataFrame, deal_mask: pd.Series.bool
    ) -> pd.Series:
        """
           Default EXECUTION_WITHIN_FIRM = clnt:nore
        if Client_Mandate == DISCRETIONARY or TradingCapacity == DEAL or [Investment
        Decision Maker] populated:
            EXECUTION_WITHIN_FIRM = [Trader ID] or [Investment Decision Maker]
        """
        null_execution_within_firm_mask = df.loc[
            :, SteelEyeTradeBlotterColumns.EXECUTION_WITHIN_FIRM
        ].isnull()
        df.loc[
            null_execution_within_firm_mask,
            SteelEyeTradeBlotterColumns.EXECUTION_WITHIN_FIRM,
        ] = Identifiers.CLNT_NORE
        discretionary_mask = (
            df[DFColumns.CLIENT_MANDATE] == ClientMandateType.DISCRETIONARY
        )
        # Client mandate discretionary or TradingCapacity==DEAL
        deal_or_discretionary_mask = (
            discretionary_mask
            | deal_mask
            | (df[SteelEyeTradeBlotterColumns.INVESTMENT_DECISION_MAKER].notnull())
        ) & (null_execution_within_firm_mask)
        if deal_or_discretionary_mask.any():
            df.loc[
                deal_or_discretionary_mask,
                SteelEyeTradeBlotterColumns.EXECUTION_WITHIN_FIRM,
            ] = df.loc[
                deal_or_discretionary_mask, SteelEyeTradeBlotterColumns.TRADER_ID
            ].fillna(
                df.loc[
                    deal_or_discretionary_mask,
                    SteelEyeTradeBlotterColumns.INVESTMENT_DECISION_MAKER,
                ]
            )

        df.loc[
            null_execution_within_firm_mask,
            SteelEyeTradeBlotterColumns.EXECUTION_WITHIN_FIRM,
        ] = df.loc[
            null_execution_within_firm_mask,
            SteelEyeTradeBlotterColumns.EXECUTION_WITHIN_FIRM,
        ].fillna(
            Identifiers.CLNT_NORE
        )

        return df[SteelEyeTradeBlotterColumns.EXECUTION_WITHIN_FIRM]

    @staticmethod
    def _get_client(df: pd.DataFrame) -> pd.Series:
        discretionary_mask = (
            df[DFColumns.CLIENT_MANDATE] == ClientMandateType.DISCRETIONARY
        )
        trading_capacity_deal_mask = (
            df[DFColumns.TRADING_CAPACITY] == TradingCapacity.DEAL
        )

        # default Client_ID
        df.loc[:, DFColumns.CLIENT] = df[SteelEyeTradeBlotterColumns.CLIENT_ID]

        # Client mandate discretionary and trading_capacity=Deal
        mask = discretionary_mask & trading_capacity_deal_mask
        df.loc[mask, DFColumns.CLIENT] = df.loc[
            mask, SteelEyeTradeBlotterColumns.EXECUTING_ENTITY_ID
        ]
        return df[DFColumns.CLIENT]

    @staticmethod
    def _get_client_mandate(df: pd.DataFrame, client_map: dict) -> pd.Series:
        # FALLBACK_MANDATE
        df.loc[:, DFColumns.FALLBACK_MANDATE] = ClientMandateType.NON.value

        # Trading Capacity != TradingCapacity.DEAL
        trading_capacity_non_deal_mask = (
            df[DFColumns.TRADING_CAPACITY] != TradingCapacity.DEAL
        )

        non_deal_df = df.loc[trading_capacity_non_deal_mask]

        if non_deal_df.empty:
            return df[DFColumns.FALLBACK_MANDATE]

        df.loc[trading_capacity_non_deal_mask, DFColumns.CLIENT_MANDATE] = df.loc[
            trading_capacity_non_deal_mask, SteelEyeTradeBlotterColumns.CLIENT_ID
        ].map(client_map)

        df.loc[:, DFColumns.CLIENT_MANDATE] = df[DFColumns.CLIENT_MANDATE].fillna(
            df[DFColumns.FALLBACK_MANDATE]
        )

        return df[DFColumns.CLIENT_MANDATE]

    def _process_data(
        self,
        df: pd.DataFrame,
        client_map: dict,
        params: Params,
        match_or_intc_mask: bool,
        deal_mask: bool,
    ) -> pd.DataFrame:

        # Assign client mandate
        if params.override_discretionary:
            df.loc[:, DFColumns.CLIENT_MANDATE] = ClientMandateType.DISCRETIONARY.value
        elif params.override_non_discretionary:
            df.loc[:, DFColumns.CLIENT_MANDATE] = ClientMandateType.NON.value
        else:
            df.loc[:, DFColumns.CLIENT_MANDATE] = self._get_client_mandate(
                df=df, client_map=client_map
            )

        # add prefixes
        cols = [
            SteelEyeTradeBlotterColumns.CLIENT_ID,
            SteelEyeTradeBlotterColumns.COUNTERPARTY_ID,
            SteelEyeTradeBlotterColumns.EXECUTING_ENTITY_ID,
            SteelEyeTradeBlotterColumns.TRADER_ID,
            SteelEyeTradeBlotterColumns.INVESTMENT_DECISION_MAKER,
        ]

        if params.override_non_lei_prefix:
            non_lei_prefix = params.override_non_lei_prefix
        else:
            non_lei_prefix = PartyIDPrefix.ACCOUNT

        for col in cols:
            not_null_mask = df[col].notnull()

            if not not_null_mask.any():
                continue

            df.loc[:, col] = (
                df.loc[:, col]
                .dropna()
                .apply(
                    lambda x: f"{PartyIDPrefix.LEI}:{x}"
                    if LEI.validate_lei_code(x.upper()).is_valid
                    and x.lower() != Identifiers.INTC
                    else f"{non_lei_prefix}:{x}"
                )
            )
        # Make Decision Maker Within Firm Column
        df.loc[
            :, DFColumns.DECISION_MAKER_WITHIN_FIRM
        ] = self._get_decision_maker_within_firm(
            df=df, match_or_intc_mask=match_or_intc_mask, deal_mask=deal_mask
        )

        # Make Execution Within Firm Column
        df.loc[
            :, SteelEyeTradeBlotterColumns.EXECUTION_WITHIN_FIRM
        ] = self._get_execution_within_firm(df=df, deal_mask=deal_mask)

        # Make Client Column
        df.loc[:, DFColumns.CLIENT] = self._get_client(df=df)

        return df

    @staticmethod
    def _make_buyer_identifier(df: pd.DataFrame, buy_mask: pd.Series) -> pd.Series:

        result = pd.Series(pd.NA, index=df.index)

        # buyer
        parties_buyer_buy_mask = (
            buy_mask & df[SteelEyeTradeBlotterColumns.CLIENT_ID].notnull()
        )

        if parties_buyer_buy_mask.any():
            result.loc[parties_buyer_buy_mask] = df.loc[
                parties_buyer_buy_mask, SteelEyeTradeBlotterColumns.CLIENT_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=Rts22TransactionModelField.PARTIES_BUYER,
                    type=IdentifierType.ARRAY,
                ).dict()
            )

        parties_buyer_not_buy_mask = (
            ~buy_mask & df[SteelEyeTradeBlotterColumns.COUNTERPARTY_ID].notnull()
        )

        if parties_buyer_not_buy_mask.any():
            result.loc[parties_buyer_not_buy_mask] = df.loc[
                parties_buyer_not_buy_mask, SteelEyeTradeBlotterColumns.COUNTERPARTY_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=Rts22TransactionModelField.PARTIES_BUYER,
                    type=IdentifierType.ARRAY,
                ).dict()
            )

        return result

    @staticmethod
    def _make_buyer_decision_maker_identifier(
        df: pd.DataFrame,
        buy_mask: bool,
        match_or_intc_mask: bool,
        deal_mask: pd.Series.bool,
    ) -> pd.Series:
        """
        For Buy Trades if:
            (Client_Mandate == Discretionary or TradingCapacity == Deal or
            InvestmentDecisionMaker is populated) then:
                PARTIES_BUYER_DECISION_MAKER = [Executing Entity ID]
        """
        result = pd.Series(pd.NA, index=df.index)

        discretionary_deal_mask = (
            (df[DFColumns.CLIENT_MANDATE] == ClientMandateType.DISCRETIONARY)
            | deal_mask
            | df[DFColumns.DECISION_MAKER_WITHIN_FIRM].notnull()
        )

        buyer_decision_maker_mask = (
            ~match_or_intc_mask
            & buy_mask
            & discretionary_deal_mask
            & (df[SteelEyeTradeBlotterColumns.EXECUTING_ENTITY_ID].notnull())
        )

        if buyer_decision_maker_mask.any():
            result.loc[buyer_decision_maker_mask] = df.loc[
                buyer_decision_maker_mask,
                SteelEyeTradeBlotterColumns.EXECUTING_ENTITY_ID,
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=Rts22TransactionModelField.PARTIES_BUYER_DECISION_MAKER,
                    type=IdentifierType.ARRAY,
                ).dict()
            )

        return result

    @staticmethod
    def _make_counterparty_identifier(df: pd.DataFrame) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)

        counterparty_mask = df[SteelEyeTradeBlotterColumns.COUNTERPARTY_ID].notnull()

        if counterparty_mask.any():
            result.loc[counterparty_mask] = df.loc[
                counterparty_mask, SteelEyeTradeBlotterColumns.COUNTERPARTY_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=Rts22TransactionModelField.PARTIES_COUNTERPARTY,
                    type=IdentifierType.OBJECT,
                ).dict()
            )

        return result

    @staticmethod
    def _make_executing_entity_identifier(df: pd.DataFrame) -> pd.Series:

        result = pd.Series(pd.NA, index=df.index)

        executing_entity_mask = df[
            SteelEyeTradeBlotterColumns.EXECUTING_ENTITY_ID
        ].notnull()

        if executing_entity_mask.any():
            result.loc[executing_entity_mask] = df.loc[
                executing_entity_mask, SteelEyeTradeBlotterColumns.EXECUTING_ENTITY_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=Rts22TransactionModelField.PARTIES_EXECUTING_ENTITY,
                    type=IdentifierType.OBJECT,
                ).dict()
            )

        return result

    @staticmethod
    def _make_execution_within_firm_identifier(df: pd.DataFrame) -> pd.Series:

        result = pd.Series(pd.NA, index=df.index)

        execution_within_firm_mask = df[
            SteelEyeTradeBlotterColumns.EXECUTION_WITHIN_FIRM
        ].notnull()

        if execution_within_firm_mask.any():
            result.loc[execution_within_firm_mask] = df.loc[
                execution_within_firm_mask,
                SteelEyeTradeBlotterColumns.EXECUTION_WITHIN_FIRM,
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=Rts22TransactionModelField.PARTIES_EXECUTION_WITHIN_FIRM,
                    type=IdentifierType.OBJECT,
                ).dict()
            )

        return result

    @staticmethod
    def _make_investment_decision_within_firm_identifier(df: pd.DataFrame) -> pd.Series:

        result = pd.Series(pd.NA, index=df.index)

        investment_decision_within_firm_mask = df[
            DFColumns.DECISION_MAKER_WITHIN_FIRM
        ].notnull()

        if investment_decision_within_firm_mask.any():
            result.loc[investment_decision_within_firm_mask] = df.loc[
                investment_decision_within_firm_mask,
                DFColumns.DECISION_MAKER_WITHIN_FIRM,
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=Rts22TransactionModelField.PARTIES_INVESTMENT_DECISION_WITHIN_FIRM,
                    type=IdentifierType.OBJECT,
                ).dict()
            )

        return result

    @staticmethod
    def _make_seller_identifier(df: pd.DataFrame, sell_mask: pd.Series) -> pd.Series:

        result = pd.Series(pd.NA, index=df.index)

        parties_seller_sell_mask = (
            sell_mask & df[SteelEyeTradeBlotterColumns.CLIENT_ID].notnull()
        )

        if parties_seller_sell_mask.any():
            result.loc[parties_seller_sell_mask] = df.loc[
                parties_seller_sell_mask, SteelEyeTradeBlotterColumns.CLIENT_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=Rts22TransactionModelField.PARTIES_SELLER,
                    type=IdentifierType.ARRAY,
                ).dict()
            )

        parties_seller_not_sell_mask = (
            ~sell_mask & df[SteelEyeTradeBlotterColumns.COUNTERPARTY_ID].notnull()
        )

        if parties_seller_not_sell_mask.any():
            result.loc[parties_seller_not_sell_mask] = df.loc[
                parties_seller_not_sell_mask,
                SteelEyeTradeBlotterColumns.COUNTERPARTY_ID,
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=Rts22TransactionModelField.PARTIES_SELLER,
                    type=IdentifierType.ARRAY,
                ).dict()
            )

        return result

    @staticmethod
    def _make_seller_decision_maker(
        df: pd.DataFrame, sell_mask: bool, deal_mask: bool, match_or_intc_mask: bool
    ) -> pd.Series:
        """
        For Sell Trades if:
            (Client_Mandate == Discretionary or TradingCapacity == Deal or
            InvestmentDecisionMaker is populated) then:
                PARTIES_SELLER_DECISION_MAKER = [Executing Entity ID]
        """
        result = pd.Series(pd.NA, index=df.index)
        discretionary_deal_mask = (
            (df[DFColumns.CLIENT_MANDATE] == ClientMandateType.DISCRETIONARY)
            | deal_mask
            | df[DFColumns.DECISION_MAKER_WITHIN_FIRM].notnull()
        )

        seller_decision_maker_mask = (
            ~match_or_intc_mask
            & sell_mask
            & discretionary_deal_mask
            & (df[SteelEyeTradeBlotterColumns.EXECUTING_ENTITY_ID].notnull())
        )

        if seller_decision_maker_mask.any():
            result.loc[seller_decision_maker_mask] = df.loc[
                seller_decision_maker_mask,
                SteelEyeTradeBlotterColumns.EXECUTING_ENTITY_ID,
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=Rts22TransactionModelField.PARTIES_SELLER_DECISION_MAKER,
                    type=IdentifierType.ARRAY,
                ).dict()
            )

        return result

    @staticmethod
    def _make_trader_identifier(df: pd.DataFrame) -> pd.Series:

        result = pd.Series(pd.NA, index=df.index)

        trader_mask = df[SteelEyeTradeBlotterColumns.TRADER_ID].notnull()

        if trader_mask.any():
            result.loc[trader_mask] = df.loc[
                trader_mask, SteelEyeTradeBlotterColumns.TRADER_ID
            ].apply(
                lambda x: Identifier(
                    labelId=x.lower(),
                    path=Rts22TransactionModelField.PARTIES_TRADER,
                    type=IdentifierType.ARRAY,
                ).dict()
            )

        return result
