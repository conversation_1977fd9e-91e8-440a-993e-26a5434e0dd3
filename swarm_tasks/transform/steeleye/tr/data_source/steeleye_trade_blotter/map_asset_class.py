import pandas as pd
from se_trades_tasks.order_and_tr.feed.order_and_tr_universal_steeleye_trade_blotter.map_asset_class import (
    Params as GenericParams,
)
from se_trades_tasks.order_and_tr.feed.order_and_tr_universal_steeleye_trade_blotter.map_asset_class import (
    run_map_asset_class,
)
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class MapAssetClass(TransformBaseTask):

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame,
            params=params,
            auditor=self.auditor,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame,
        params: GenericParams,
        auditor=None,
    ) -> pd.DataFrame:

        return run_map_asset_class(
            source_frame=source_frame,
            params=params,
            auditor=auditor,
        )
