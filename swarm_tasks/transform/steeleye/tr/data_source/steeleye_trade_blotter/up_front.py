import numpy as np
import pandas as pd
from se_core_tasks.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.orders.common_utils import static
from swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.static import (
    CreditDefaultSwaps,
)
from swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.static import (
    FxOptions,
)
from swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.static import (
    SteelEyeTradeBlotterColumns,
)

CREDIT_DEFAULT_SWAP_REGEX = r"^SC"
TRX_DTL_UP_FRONT_PAYMENT = "transactionDetails.upFrontPayment"
TRX_DTL_UP_FRONT_PAYMENT_CCY = "transactionDetails.upFrontPaymentCurrency"
ASSET_CLASS_COLUMN = "__asset_class__"


class TrxDtlUpFront(TransformBaseTask):
    """
    This task creates the transactionDetails.upFrontPayment and
    transactionDetails.upFrontPaymentCurrency.

    NOTE: Currently it is only being applied to credit default swaps. Makes sense?
    """

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: BaseParams = None,
        resources: BaseResources = None,
        **kwargs
    ) -> pd.DataFrame:

        df = pd.DataFrame(index=source_frame.index)

        credit_default_swaps_mask = pd.Series(False, index=source_frame.index)
        fx_options_mask = pd.Series(False, index=source_frame.index)
        if SteelEyeTradeBlotterColumns.INSTRUMENT_ASSET_CLASS in source_frame.columns:
            # Credit Default Swaps Mask
            credit_default_swaps_mask = credit_default_swaps_mask | (
                source_frame[SteelEyeTradeBlotterColumns.INSTRUMENT_ASSET_CLASS]
                .str.upper()
                .isin(CreditDefaultSwaps)
            )
        if (
            SteelEyeTradeBlotterColumns.INSTRUMENT_CLASSIFICATION
            in source_frame.columns
        ):
            credit_default_swaps_mask = credit_default_swaps_mask | (
                source_frame[
                    SteelEyeTradeBlotterColumns.INSTRUMENT_CLASSIFICATION
                ].str.match(CREDIT_DEFAULT_SWAP_REGEX, case=False, na=False)
            )

        # Fx Option Mask
        if ASSET_CLASS_COLUMN in source_frame.columns:
            fx_options_mask = (
                source_frame[ASSET_CLASS_COLUMN].str.upper().isin(FxOptions)
            )
        asset_class_mask = credit_default_swaps_mask | fx_options_mask
        data = source_frame.loc[asset_class_mask]

        if data.empty:
            df[TRX_DTL_UP_FRONT_PAYMENT] = np.nan
            df[TRX_DTL_UP_FRONT_PAYMENT_CCY] = np.nan
            return df

        # transactionDetails.upFrontPayment
        df[TRX_DTL_UP_FRONT_PAYMENT] = (
            data[SteelEyeTradeBlotterColumns.UPFRONT_PAYMENT].dropna().apply(abs)
        )
        df[TRX_DTL_UP_FRONT_PAYMENT] = df[TRX_DTL_UP_FRONT_PAYMENT].fillna(
            data[SteelEyeTradeBlotterColumns.NET_AMOUNT].dropna().apply(abs)
        )

        # transactionDetails.upFrontPaymentCurrency
        # read the static json file with minor currencies and conversion map
        minor_ccy_price_data = ConvertMinorToMajor.read_minor_ccy_and_price(
            static.MINOR_CURRENCIES_FILE_PATH
        )
        price_currency_not_null_mask = source_frame[
            SteelEyeTradeBlotterColumns.PRICE_CURRENCY
        ].notnull()
        up_front_or_quantity_currency_not_null_mask = (
            source_frame[SteelEyeTradeBlotterColumns.UPFRONT_PAYMENT_CURRENCY].notnull()
            | source_frame[SteelEyeTradeBlotterColumns.QUANTITY_CURRENCY].notnull()
        )

        # Only populate upfront currency if newly populated upfront payment is not null
        # and price currency is not null (note: df and source_frame have the same index)
        upfront_payment_not_null_mask = df[TRX_DTL_UP_FRONT_PAYMENT].notnull()
        ccy_and_upfront_payment_not_null_mask = (
            price_currency_not_null_mask & upfront_payment_not_null_mask
        )

        if (credit_default_swaps_mask & ccy_and_upfront_payment_not_null_mask).any():
            df.loc[
                credit_default_swaps_mask & ccy_and_upfront_payment_not_null_mask,
                TRX_DTL_UP_FRONT_PAYMENT_CCY,
            ] = source_frame.loc[
                ccy_and_upfront_payment_not_null_mask,
                SteelEyeTradeBlotterColumns.PRICE_CURRENCY,
            ].apply(
                (
                    lambda x: ConvertMinorToMajor.convert_currency(
                        source_ccy=x, conversion_map_list=minor_ccy_price_data
                    )
                )
            )

        ccy_and_upfront_payment_not_null_mask = (
            up_front_or_quantity_currency_not_null_mask & upfront_payment_not_null_mask
        )

        if (fx_options_mask & ccy_and_upfront_payment_not_null_mask).any():
            df.loc[
                fx_options_mask & ccy_and_upfront_payment_not_null_mask,
                TRX_DTL_UP_FRONT_PAYMENT_CCY,
            ] = (
                source_frame.loc[
                    ccy_and_upfront_payment_not_null_mask,
                    SteelEyeTradeBlotterColumns.UPFRONT_PAYMENT_CURRENCY,
                ]
                .fillna(
                    data.loc[fx_options_mask & ccy_and_upfront_payment_not_null_mask][
                        SteelEyeTradeBlotterColumns.QUANTITY_CURRENCY
                    ]
                )
                .apply(
                    (
                        lambda x: ConvertMinorToMajor.convert_currency(
                            source_ccy=x, conversion_map_list=minor_ccy_price_data
                        )
                    )
                )
            )

        return df
