import copy
from typing import Optional

import pandas as pd
from prefect import context
from prefect.engine import signals
from pydantic import Field
from pydantic import root_validator
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.generic.instrument_fallback.static import InstrumentFields


class Params(BaseParams):
    override_strike_price_currency_and_type: bool = Field(
        default=False, description="The override logic will be performed if True"
    )
    source_strike_price_type_column: Optional[str] = Field(
        default=None, description="Source Strike price column"
    )
    source_strike_price_currency_column: Optional[str] = Field(
        default=None, description="Source Strike currency column"
    )
    instrument_details_instrument_field: str = Field(
        default="instrumentDetails.instrument",
        description="Name of the instrument details instrument field",
    )

    @root_validator()
    def ensure_source_field_params_not_none_if_override_true(cls, values):
        """
        Ensures that the source fields are not None if override_strike_price_currency_and_type
        is True
        """
        override_strike_price_currency_and_type = values.get(
            "override_strike_price_currency_and_type"
        )
        source_strike_price_currency_column = values.get(
            "source_strike_price_currency_column"
        )
        source_strike_price_type_column = values.get("source_strike_price_type_column")

        if override_strike_price_currency_and_type and not (
            source_strike_price_type_column or source_strike_price_currency_column
        ):
            raise ValueError(
                f"if {override_strike_price_currency_and_type} is True, neither {source_strike_price_type_column} nor "
                f"{source_strike_price_currency_column} should be None"
            )

        return values


class OverrideStrikePriceCurrencyAndType(TransformBaseTask):
    """
    https://steeleye.atlassian.net/browse/EU-2803 - Override instrument.ext.strikePriceType
    and instrument.derivative.strikePriceCurrency if they are not fetched from SRP
    Note: The logic is applied only when override_strike_price_currency_and_type param is True.

    The source attribute columns for the 2 fields are defined in the params.
    """

    params_class = Params

    def execute(
        self, source_frame: pd.DataFrame = None, params: Params = None, **kwargs
    ) -> pd.DataFrame:
        return self.process(
            source_frame=source_frame, params=params, logger=self.logger
        )

    def process(
        cls,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        logger=context.get("logger"),
    ):
        if source_frame.empty:
            raise signals.FAIL(
                f"{params.instrument_details_instrument_field} not present in source frame"
            )

        if not params.override_strike_price_currency_and_type:
            return source_frame
        target = pd.DataFrame(index=source_frame.index)
        target[params.instrument_details_instrument_field] = pd.NA
        logger.info("Overriding strike price currency and type")

        data_mask = source_frame.loc[
            :, params.instrument_details_instrument_field
        ].notnull()

        required_columns = [
            params.instrument_details_instrument_field,
            params.source_strike_price_currency_column,
            params.source_strike_price_type_column,
        ]
        required_columns_mask = source_frame.columns.isin(required_columns)
        num_required_cols_present = required_columns_mask.sum()
        if required_columns_mask.sum() != 3:
            raise signals.FAIL(
                f"{3-num_required_cols_present} required columns not present in source frame"
            )

        target.loc[
            data_mask, params.instrument_details_instrument_field
        ] = source_frame.loc[data_mask, required_columns_mask].apply(
            lambda x: cls._update_fields(
                instrument_details=x[params.instrument_details_instrument_field],
                source_strike_price_currency=x[
                    params.source_strike_price_currency_column
                ],
                source_strike_price_type=x[params.source_strike_price_type_column],
            ),
            axis=1,
        )

        return target

    @staticmethod
    def _update_fields(
        instrument_details: dict,
        source_strike_price_currency: str,
        source_strike_price_type: float,
    ) -> Optional[dict]:
        """
        Updates the required fields (strike price currency and type) from the
        respective source attributes if they are null or empty
        :param instrument_details: dictionary with the contents of instrumentDetails.instrument
        :param source_strike_price_currency: strike price currency value
        :param source_strike_price_type: strike price type value
        :return Instrument dictionary with the updated values for the strike price type and
                currency fields
        """
        strike_price_type = instrument_details.get(
            InstrumentFields.EXT_STRIKE_PRICE_TYPE
        )
        strike_price_currency = instrument_details.get(
            InstrumentFields.DERIVATIVE_STRIKE_PRICE_CURRENCY
        )
        target_instrument_details = copy.deepcopy(instrument_details)
        if pd.isna(strike_price_type) or strike_price_type == "":
            target_instrument_details[
                InstrumentFields.EXT_STRIKE_PRICE_TYPE
            ] = source_strike_price_type
        if pd.isna(strike_price_currency) or strike_price_currency == "":
            target_instrument_details[
                InstrumentFields.DERIVATIVE_STRIKE_PRICE_CURRENCY
            ] = source_strike_price_currency
        return target_instrument_details
