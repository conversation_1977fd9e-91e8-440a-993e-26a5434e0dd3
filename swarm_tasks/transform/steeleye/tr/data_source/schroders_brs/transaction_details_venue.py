import pandas as pd
from pydantic import Field
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class StaticVenues:
    XXXX = "XXXX"
    XOFF = "XOFF"


class Params(BaseParams):
    venue_attribute: str
    target_attribute: str
    workflow_eligibility_attribute: str = Field(
        ..., description="Column with the nested workflow eligibility composite"
    )
    nested_totv_field: str = Field(..., description="Nested field for TOTV.")


class TransactionDetailsVenue(TransformBaseTask):
    """
    This task assign the venue according to the workflow eligibility,
        which affects XXXX and XOFF assignment
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:

        target = pd.DataFrame(index=source_frame.index)

        target[params.target_attribute] = self._get_transaction_details_venue(
            df=source_frame, params=params
        )

        return target

    @staticmethod
    def _get_transaction_details_venue(df: pd.DataFrame, params: Params) -> pd.Series:
        """
        Logic as per https://steeleye.atlassian.net/browse/ON-1351:
        "if [Venue] != ("XXXX","XOFF") then:
            [Venue]
        elif [workflow.eligibility.totv] = True AND [Venue] = "XXXX" then:
            "XOFF"
        elif ([workflow.eligibility.totv] = False or [workflow.eligibility.totv] is BLANK) AND [Venue] = "XOFF" then:
            "XXXX"
        else:
            [Venue]
        :param df: source frame
        :param params: params to define venue attribute and nested totv field
        :return: Series with the determined Venue
        """
        result = pd.Series(pd.NA, index=df.index)

        invalid_isin_mask = df[params.venue_attribute].isin(
            [StaticVenues.XXXX, StaticVenues.XOFF]
        )

        result.loc[~invalid_isin_mask] = df[params.venue_attribute]

        if not invalid_isin_mask.any():
            return result

        workflow_eligibility_not_null_mask = df[
            params.workflow_eligibility_attribute
        ].notnull()

        if workflow_eligibility_not_null_mask.any():

            totv_series = pd.Series(index=df.index, data=False)

            totv_series.loc[workflow_eligibility_not_null_mask] = df.loc[
                workflow_eligibility_not_null_mask,
                params.workflow_eligibility_attribute,
            ].apply(lambda x: x.get(params.nested_totv_field, False))

            # XOFF
            xoff_mask = (
                df.loc[:, params.venue_attribute] == StaticVenues.XXXX
            ) & totv_series

            result.loc[xoff_mask] = StaticVenues.XOFF

        result = result.fillna(df.loc[:, params.venue_attribute])

        return result
