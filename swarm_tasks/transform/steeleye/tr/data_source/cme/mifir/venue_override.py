from typing import Optional

import pandas as pd
from pydantic import Field
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

INSTRUMENT_PATH = "instrumentDetails.instrument"
WORKFLOW_ELIGIBILITY_PATH = "workflow.eligibility"
EQUITY_MASK = "ES"
VENUE_XXXX = "XXXX"


class Params(BaseParams):
    override_venue: bool = Field(
        False, description="The override logic will be performed if True"
    )


class InstrumentFields:
    NOTIONAL_CURRENCY_1 = "notionalCurrency1"
    INSTRUMENT_CLASSIFICATION = "instrumentClassification"


class WorkflowFields:
    ELIGIBILITY_TOTV = "totv"
    ELIGIBILITY_EXECUTION_VENUE = "executionVenue"


class Currencies:
    USD = "USD"


class TranactionFields:
    TRANSACTION_VENUE = "transactionDetails.venue"
    TRANSACTION_ULTIMATE_VENUE = "transactionDetails.ultimateVenue"


class VenueOverride(TransformBaseTask):
    """ON-1630 - Override 'Venue' for US Equities:
    if (instrumentDetails.instrument.notionalCurrency1 == USD
         and  instrumentDetails.instrument.instrumentClassification.startswith(ES)
         and  workflow.eligibility.totv == False)
         then:
            transactionDetails.venue = XXXX
            workflow.eligibility.executionVenue = XXXX
            transactionDetails.ultimateVenue = XXXX
    Note: The logic is applied only when venue_override param is True"""

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources: Optional[BaseResources] = None,
        **kwargs
    ) -> pd.DataFrame:
        target = pd.DataFrame(index=source_frame.index, columns=[source_frame.columns])
        if source_frame.empty:
            return target

        cols_used = [
            INSTRUMENT_PATH,
            WORKFLOW_ELIGIBILITY_PATH,
        ]
        df = source_frame.loc[:, source_frame.columns.intersection(cols_used)]
        target = source_frame
        for col in cols_used:
            if col not in source_frame.columns:
                df[col] = pd.NA

        data_mask = (
            df[INSTRUMENT_PATH].notnull() & df[WORKFLOW_ELIGIBILITY_PATH].notnull()
        )
        if params.override_venue and data_mask.any():
            mask = (
                (
                    df[INSTRUMENT_PATH].apply(
                        lambda x: x.get(
                            InstrumentFields.INSTRUMENT_CLASSIFICATION, ""
                        ).startswith(EQUITY_MASK)
                        if isinstance(x, dict)
                        else False
                    )
                )
                & ~(
                    df[WORKFLOW_ELIGIBILITY_PATH].apply(
                        lambda x: x.get(WorkflowFields.ELIGIBILITY_TOTV, True)
                        if isinstance(x, dict)
                        else True
                    )
                )
                & (
                    df[INSTRUMENT_PATH].apply(
                        lambda x: x.get(InstrumentFields.NOTIONAL_CURRENCY_1)
                        == Currencies.USD
                        if isinstance(x, dict)
                        else False
                    )
                )
            )
        else:
            mask = pd.Series(False, index=source_frame.index)

        target.loc[mask, TranactionFields.TRANSACTION_VENUE] = VENUE_XXXX
        target.loc[mask, TranactionFields.TRANSACTION_ULTIMATE_VENUE] = VENUE_XXXX
        target.loc[mask, WORKFLOW_ELIGIBILITY_PATH] = df.loc[
            mask, WORKFLOW_ELIGIBILITY_PATH
        ].apply(self.update_venue)

        return target

    @staticmethod
    def update_venue(value: Optional[dict]) -> Optional[dict]:
        if pd.notnull(value):
            value[WorkflowFields.ELIGIBILITY_EXECUTION_VENUE] = VENUE_XXXX
        return value
