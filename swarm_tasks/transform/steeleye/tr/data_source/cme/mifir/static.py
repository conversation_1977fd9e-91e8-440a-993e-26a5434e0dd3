class CmeMifirColumns:
    BUYER_DECISION_MAKER_ID = "BUYERDECISIONMAKERCODE"
    BUYER_ID = "BUYERIDENTIFICATIONCODE"
    DELIVERY_TYPE = "DELIVERYTYPE"
    EXECUTING_ENTITY_ID = "EXECUTINGENTITY<PERSON>EN<PERSON><PERSON>CATIONCODE"
    EXPIRY_DATE = "EXPIRYDATE"
    FIRM_EXECUTION_ID = "EXECUTIONWITHINFIRM"
    INSTRUMENT_CLASSIFICATION = "INSTRUMENTCLASSIFICATION"
    INSTRUMENT_ID = "INSTRUMENTI<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>CODE"
    INSTRUMENT_NAME = "INSTRUMENTFULLNAME"
    INVESTMENT_DECISION_ID = "INVESTMENTDECISIONWITHINFIRM"
    NOTIONAL_CURRENCY_1 = "NOTIONALCURRENCY1"
    NOTIONAL_CURRENCY_2 = "NOTIONALCURRENCY2"
    PRICE_MULTIPLIER = "PRICEMULTIPLIER"
    PRICE_TYPE = "PRICETYPE"
    QUANTITY_TYPE = "QUANTITYTYPE"
    SELLER_DECISION_MAKER_ID = "SELLERDECISIONMAKERCODE"
    SELLER_ID = "SELLERIDENTIFICATIONCODE"
    STRIKE_PRICE = "STRIKEPRICE"
    STRIKE_PRICE_CURRENCY = "STRIKEPRICECURRENCY"
    STRIKE_PRICE_TYPE = "STRIKEPRICETYPE"
    UNDERLYING_INDEX_ID = "UNDERLYINGINSTRUMENTCODE"
    UNDERLYING_INDEX_NAME = "UNDERLYINGINDEXNAME"
    UNDERLYING_INDEX_TERM = "TERMOFTHEUNDERLYINGINDEX"
    UNDERLYING_INSTRUMENT_ID = "UNDERLYINGINSTRUMENTCODE"
    VENUE = "VENUE"
    EXECUTION_WITHIN_FIRM_TYPE = "EXECUTIONWITHINFIRMTYPE"
    INVESTMENT_DECISION_WITHIN_FIRM_TYPE = "INVESTMENTDECISIONWITHINFIRMTYPE"
    BUYER_IDENTIFICATION_CODE_TYPE = "BUYERIDENTIFICATIONCODETYPE"
    BUYER_DECISION_MAKER_CODE_TYPE = "BUYERDECISIONMAKERCODETYPE"
    SELLER_IDENTIFICATION_CODE_TYPE = "SELLERIDENTIFICATIONCODETYPE"
    SELLER_DECISION_MAKER_CODE_TYPE = "SELLERDECISIONMAKERCODETYPE"
    MATURITYDATE = "temp.maturityDate"
    OPTIONEXERCISESTYLE = "OPTIONEXERCISESTYLE"
    OPTIONTYPE = "OPTIONTYPE"
    FIRM_EXEC = "temp.executionWithinFirm"
    INV_DEC_ID = "temp.investmentDecisionWithinFirm"

    BUYER_FIRST_NAMES = "BUYERFIRSTNAMES"
    BUYER_SURNAMES = "BUYERSURNAMES"
    BUYER_DATE_OF_BIRTH = "BUYERDATEOFBIRTH"
    BUYER_COUNTRY_OF_THE_BRANCH = "BUYERCOUNTRYOFTHEBRANCH"
    SELLER_FIRSTNAMES = "SELLERFIRSTNAMES"
    SELLER_SURNAMES = "SELLERSURNAMES"
    SELLER_DATE_OF_BIRTH = "SELLERDATEOFBIRTH"
    SELLER_COUNTRY_OF_THE_BRANCH = "SELLERCOUNTRYOFTHEBRANCH"
    BUYER_DECISION_MAKER_FIRST_NAMES = "BUYERDECISIONMAKERFIRSTNAMES"
    BUYER_DECISION_MAKER_SURNAMES = "BUYERDECISIONMAKERSURNAMES"
    BUYER_DECISION_MAKER_DATE_OF_BIRTH = "BUYERDECISIONMAKERDATEOFBIRTH"
    SELLER_DECISION_MAKER_FIRST_NAMES = "SELLERDECISIONMAKERFIRSTNAMES"
    SELLER_DECISION_MAKER_SURNAMES = "SELLERDECISIONMAKERSURNAMES"
    SELLER_DECISION_MAKER_DATE_OF_BIRTH = "SELLERDECISIONMAKERDATEOFBIRTH"
    INVESTMENT_DECISION_COUNTRY_OF_BRANCH = (
        "COUNTRYOFTHEBRANCHRESPONSIBLEFORTHEPERSONMAKINGTHEINVESTMENTDECISION"
    )
    EXECUTING_ENTITY_COUNTRY_OF_BRANCH = (
        "COUNTRYOFTHEBRANCHSUPERVISINGTHEPERSONRESPONSIBLEFORTHEEXECUTION"
    )
