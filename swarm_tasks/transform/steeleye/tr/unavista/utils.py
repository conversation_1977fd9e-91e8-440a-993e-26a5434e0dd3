import logging

import requests
from se_trades_tasks.tr.workflow.static import ScheduleTypeEnum

logger = logging.getLogger(__name__)


def fetch_tr_config_from_platform_config(
    tenant: str,
    stack: str,
    env: str,
    schedule_type: ScheduleTypeEnum,
    enabled: str = "true",
):
    """
    Fetches transaction reporting configuration from the data platform config API.

    :param tenant: The identifier for the tenant.
    :param stack: The stack where the tenant is based in.
    :param env: The environment (dev, uat, sit) to fetch the configuration from.
    :param schedule_type: An enumeration representing the type of schedule.
    :param enabled: Optional. A string indicating whether only enabled configuration is to be fetched.

    :return: A dictionary containing the fetched transaction reporting configuration.
    """
    # TODO: We need to change this based on what endpoint is exposed in prod
    config_endpoint_core = (
        "https://config-api.nonprod-eu-ie-1.steeleye.co"
        if env in ["dev", "uat", "sit"]
        else "https://config-api.prod-eu-ie-1.steeleye.co"
    )

    config_endpoint = (
        f"{config_endpoint_core}/api/v1.0/data-platform-config/"
        f"stacks/{stack}/tenants/{tenant}/mifir_schedules/"
        f"schedule_type/{schedule_type.value}?enabled={enabled}"
    )
    json_response = dict()
    try:
        response = requests.get(config_endpoint)
        response.raise_for_status()
        json_response = response.json()
        logging.info(
            f"{schedule_type.value} config for {tenant} tenant is \n {json_response}"
        )
    except Exception:
        logging.error(
            f"Could not fetch {schedule_type.value} config for "
            f"{tenant} tenant in {env} environment.\n Request which was sent {config_endpoint}",
            exc_info=True,
        )

    return json_response
