from typing import List
from typing import Optional

import numpy as np
from prefect import context
from se_elastic_schema.models import RTS22Transaction
from se_elastic_schema.static.mifid2 import RTS22ExternalStatus
from swarm.conf import Settings
from swarm.schema.static import SwarmColumns
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask
from swarm.task.io.read import FrameProducerResult


class Params(BaseParams):
    chunksize: int = 1000


class Resources(BaseResources):
    es_client_key: str = "tenant-data"


class UpdateNcaProducer(BaseTask):
    params_class = Params
    resources_class = Resources

    def execute(
        self,
        params: Optional[Params] = None,
        resources: Optional[Resources] = None,
        **kwargs,
    ) -> List[FrameProducerResult]:

        file_id = context.swarm.file_id
        self.logger.info(f"update transactions => {file_id}")

        es = self.get_es_client(resources)

        # extract tenant
        tenant = Settings.tenant

        # define rts22 transaction alias
        txn_alias = RTS22Transaction.get_elastic_index_alias(tenant=tenant)

        # for each report, scroll and add to batches
        batches = list()

        # fields to restrict in source
        id_fields = [
            es.meta.id,
            es.meta.model,
            es.meta.hash,
            es.meta.version,
        ]

        # These transactions already have been updated in the previous steps i.e. They all are either
        # PENDING or REJECTED from the NCA perspective. We don't need to update them again.
        # Apart from these transactions, we'll mark rest of the transactions as ACCEPTED.
        transactions_already_updated = []
        elastic_bulk_writers_list = kwargs.get("results", [])
        for elastic_bulk_writer in elastic_bulk_writers_list:
            df = elastic_bulk_writer.frame
            if df.empty:
                break

            transactions_already_updated += (
                df["id"].str.split(":", n=1, expand=True)[0].tolist()
            )

        # define query to retrieve elastic meta for txns in report
        body = {
            "_source": {"includes": id_fields},
            "query": {
                "bool": {
                    "filter": [
                        {"term": {es.meta.model: "RTS22Transaction"}},
                        {"term": {"workflow.arm.response": f"{file_id}"}},
                        {
                            "terms": {
                                "workflow.nca.status": [
                                    f"{RTS22ExternalStatus.PENDING}",
                                    f"{RTS22ExternalStatus.REJECTED}",
                                    f"{RTS22ExternalStatus.REJECTION_CLEARED}",
                                ],
                            }
                        },
                    ],
                    "must_not": [
                        {
                            "terms": {
                                "reportDetails.transactionRefNo": transactions_already_updated
                            }
                        }
                    ],
                }
            },
        }

        df = es.scroll(query=body, index=txn_alias, include_elastic_meta=False)

        for idx, (_, df_chunk) in enumerate(
            df.groupby(np.arange(len(df)) // params.chunksize)
        ):
            df_chunk.index.name = SwarmColumns.SWARM_RAW_INDEX
            df_chunk[es.meta.version] = df[es.meta.version] + 1
            result = FrameProducerResult(frame=df_chunk, batch_index=idx)
            batches.append(result)

        return batches

    def get_es_client(self, resources):
        return Settings.connections.get(resources.es_client_key)
