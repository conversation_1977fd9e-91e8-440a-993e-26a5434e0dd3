from typing import Set

import pandas as pd
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class In:
    REPORT_STATUS = "reportDetails.reportStatus"
    WAIVER_IND = "tradersAlgosWaiversIndicators.waiverIndicator"
    SHORT_SELLING_IND = "tradersAlgosWaiversIndicators.shortSellingIndicator"
    OTC_POST_TRADE_IND = "tradersAlgosWaiversIndicators.otcPostTradeIndicator"
    CMDTY_DERIV_IND = "tradersAlgosWaiversIndicators.commodityDerivativeIndicator"
    SEC_FIN_TXN_IND = "tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator"


class Out:
    WAIVER_IND = "AddtlAttrbts.WvrInd"
    SHORT_SELLING_IND = "AddtlAttrbts.ShrtSellgInd"
    OTC_POST_TRADE_IND = "AddtlAttrbts.OTCPstTradInd"
    RISK_REDUCING_TX = "AddtlAttrbts.RskRdcgTx"
    SEC_FIN_TXN_IND = "AddtlAttrbts.SctiesFincgTxInd"


class MapAdditionalAttributes(TransformBaseTask):
    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: BaseParams = None,
        resources: BaseResources = None,
        **kwargs
    ) -> pd.DataFrame:
        # mask source to new transactions only
        source_frame = source_frame.loc[source_frame[In.REPORT_STATUS] == "NEWT"]

        target = pd.DataFrame(index=source_frame.index)

        missing_cols = self.required_columns().difference(set(source_frame.columns))
        if missing_cols:
            source_frame = source_frame.assign(**{col: None for col in missing_cols})

        target.loc[:, Out.WAIVER_IND] = source_frame.loc[:, In.WAIVER_IND]

        target.loc[:, Out.SHORT_SELLING_IND] = source_frame.loc[:, In.SHORT_SELLING_IND]
        ssi_ntav_mask = source_frame[In.SHORT_SELLING_IND] == "NTAV"
        if ssi_ntav_mask.any():
            target.loc[ssi_ntav_mask, Out.SHORT_SELLING_IND] = "UNDI"

        target.loc[:, Out.OTC_POST_TRADE_IND] = source_frame.loc[
            :, In.OTC_POST_TRADE_IND
        ]
        target.loc[:, Out.RISK_REDUCING_TX] = source_frame.loc[:, In.CMDTY_DERIV_IND]
        target.loc[:, Out.SEC_FIN_TXN_IND] = source_frame.loc[:, In.SEC_FIN_TXN_IND]

        return target

    @staticmethod
    def required_columns() -> Set[str]:
        cols = {
            In.REPORT_STATUS,
            In.WAIVER_IND,
            In.SHORT_SELLING_IND,
            In.OTC_POST_TRADE_IND,
            In.CMDTY_DERIV_IND,
            In.SEC_FIN_TXN_IND,
        }
        return cols
