from typing import Set

import pandas as pd
import prefect.engine.signals
from se_elastic_schema.models import AccountFirm
from se_schema_meta import EXPIRY
from se_schema_meta import MOD<PERSON>
from se_schema_meta import TIMESTAMP
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.tr.static import TransactionReportColumns


class In:
    REPORT_STATUS = "reportDetails.reportStatus"
    TXN_REF_NO = "reportDetails.transactionRefNo"
    EXECUTING_ENTITY_LEI = "parties.executingEntity.firmIdentifiers.lei"
    SUBMITTING_ENTITY_ID_CODE = "reportDetails.submittingEntityIdCode"
    INV_FIRM_COV_DIRECTIVE = "reportDetails.investmentFirmCoveredDirective"


class Out:
    TXN_ID = "TxId"
    EXECUTING_PARTY = "ExctgPty"
    SUBMITTING_PARTY = "SubmitgPty"
    INVESTMENT_PARTY_IND = "InvstmtPtyInd"


class Resources(BaseResources):
    es_client_key: str


class MapIntersection(TransformBaseTask):
    resources_class = Resources

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: BaseParams = None,
        resources: Resources = None,
    ) -> pd.DataFrame:

        es = self.clients.get(resources.es_client_key)

        tenant = Settings.tenant

        # fetch account firm lei
        account_firm_alias = AccountFirm.get_elastic_index_alias(tenant=tenant)
        query = {
            "size": 1,
            "query": {
                "bool": {
                    "must_not": [{"exists": {"field": EXPIRY}}],
                    "filter": [{"terms": {MODEL: [AccountFirm.__name__]}}],
                }
            },
            "sort": [{TIMESTAMP: {"order": "desc"}}],
        }

        try:
            record = es.search(index=account_firm_alias, query=query)["hits"]["hits"][0]
            account_firm_lei = record["_source"]["firmIdentifiers"]["lei"]
        except (KeyError, IndexError):
            raise prefect.engine.signals.FAIL(
                f"No AccountFirm record found in the ES index {account_firm_alias}, with"
                "the `firmIdentifiers.lei` field. It is mandatory for TRv2."
            )

        self.logger.info(f"assigned account firm lei as {account_firm_lei}")

        target = pd.DataFrame(index=source_frame.index)

        missing_cols = self.required_columns().difference(set(source_frame.columns))
        if missing_cols:
            source_frame = source_frame.assign(**{col: None for col in missing_cols})

        # populate transaction id
        target[Out.TXN_ID] = source_frame.loc[:, In.TXN_REF_NO]

        # populate executing party
        target[Out.EXECUTING_PARTY] = source_frame.loc[:, In.EXECUTING_ENTITY_LEI]

        # populate submitting party as account firm lei
        # todo possibly revisit submitting party criteria
        target[Out.SUBMITTING_PARTY] = account_firm_lei

        # populate investment party ind if new transaction
        target[Out.INVESTMENT_PARTY_IND] = source_frame.loc[
            (source_frame[In.REPORT_STATUS] == "NEWT"), In.INV_FIRM_COV_DIRECTIVE
        ].fillna("true")

        # pass meta across to target
        # todo better solution for meta passing?
        target[es.meta.key] = source_frame.loc[:, es.meta.key]
        target[TransactionReportColumns.RTS22_REPORT_STATUS] = source_frame.loc[
            :, In.REPORT_STATUS
        ]
        target[TransactionReportColumns.RTS22_REPORT_ID] = source_frame.loc[
            :, TransactionReportColumns.RTS22_REPORT_ID
        ]
        target[TransactionReportColumns.RTS22_REPORT_USER] = source_frame.loc[
            :, TransactionReportColumns.RTS22_REPORT_USER
        ]
        target[TransactionReportColumns.RTS22_REPORT_S3_KEY] = source_frame.loc[
            :, TransactionReportColumns.RTS22_REPORT_S3_KEY
        ]
        return target

    @staticmethod
    def required_columns() -> Set[str]:
        cols = {
            In.REPORT_STATUS,
            In.TXN_REF_NO,
            In.EXECUTING_ENTITY_LEI,
            In.SUBMITTING_ENTITY_ID_CODE,
            In.INV_FIRM_COV_DIRECTIVE,
        }
        return cols
