import pandas as pd
from se_trades_tasks.meta.assign_meta_parent import Params as GenericParams
from se_trades_tasks.meta.assign_meta_parent import run_assign_meta_parent
from swarm.schema.base import AbstractComponent
from swarm.task.transform.base import TransformBaseTask


class Params(AbstractComponent, GenericParams):
    pass


class AssignMetaParent(TransformBaseTask):
    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:
        return run_assign_meta_parent(
            source_frame=source_frame, params=params, **kwargs
        )
