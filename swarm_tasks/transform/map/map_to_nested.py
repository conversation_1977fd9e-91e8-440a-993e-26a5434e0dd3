import pandas as pd
from se_core_tasks.map.map_to_nested import Params as GenericParams
from se_core_tasks.map.map_to_nested import run_map_to_nested
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class MapToNested(TransformBaseTask):
    """
    Given a pandas dataframe, this task maps a source attribute to a target attribute's
    nested dict path provided in the `nested_path` parameter.

    WARNING:
        - Currently it is only supported for flatten nested dicts (it doesn't handle
        lists of dicts).
        - Any non dict values in the nested path of target attribute column will be
        overriden (see second row in Example for more detail).

    Example:
        data = pd.DataFrame({
            "source_1" : ["hello", "world", "test", pd.NA, "universe"],
            "target_1" : [
                pd.NA,
                {"foo": {"bar": 0}},
                {"foo": {"bar": {"hello": 1}}},
                {"foo": {"bar": {"hello": 2}}},
                {"foo": 4},
            ]
        })

        params:
            source_attribute: source_1
            nested_path: foo.bar.foozz
            target_attribute: target_1

        target = pd.DataFrame({
            "target_1" : [
                pd.NA,
                {"foo":{"bar": {"foozz": "world"}}},
                {"foo":{"bar":{"hello": 1, "foozz": "test"}}},
                {"foo":{"bar":{"hello": 2}}},
                {"foo":{"bar": {"foozz": "universe"}}}]
        })
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources=None,
        **kwargs,
    ) -> pd.DataFrame:
        return run_map_to_nested(source_frame=source_frame, params=params, **kwargs)
