import pandas as pd
from pydantic import Field
from pydantic import root_validator
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams):
    source_attribute: str = Field(
        ..., description="Column in source_frame to match with"
    )
    target_attribute: str = Field(
        ..., description="Column to be created with the output values"
    )
    no_match_default_attribute: str = Field(
        None,
        description="source_frame column to fill target_attribute with"
        "when no match is found.",
    )
    no_match_default_value: str = Field(
        None,
        description="default value to fill target_attribute with"
        "when no match is found.",
    )
    matching_column: str = Field(
        ..., description="Column in the mapping table to match with"
    )
    output_column: str = Field(
        ..., description="Column in the mapping table with the output values"
    )

    @root_validator
    def validate_source_format(cls, values):
        if values.get("no_match_default_attribute") and values.get(
            "no_match_default_value"
        ):
            raise ValueError(
                "`no_match_default_attribute` or `no_match_default_value` are mutually exclusive."
            )

        return values


class MapFromMappingTable(TransformBaseTask):
    """
    This task maps values from dataframes with two columns.
    A matching column and an output column.
    The main purpose of this task is to be used with mapping tables.
    matching_column should have the values present on the source_frame's
    source_attribute; and output columns should have the corresponding values
    destined for the target_attribute.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        mapping_table: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:
        return self.process(
            source_frame=source_frame, mapping_table=mapping_table, params=params
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame = None,
        mapping_table: pd.DataFrame = None,
        params: Params = None,
    ):
        target = pd.DataFrame(index=source_frame.index)
        if source_frame.empty or mapping_table.empty:
            return target

        # create mapping dictionary
        mapping_table = mapping_table.fillna(pd.NA)
        mapping_dict = mapping_table.set_index(params.matching_column).to_dict()[
            params.output_column
        ]

        # mapping from mapping dictionary
        target[params.target_attribute] = source_frame.loc[
            :, params.source_attribute
        ].apply(lambda x: mapping_dict.get(x, pd.NA))

        # non matching rows
        no_match_mask = target[params.target_attribute].isnull()

        if no_match_mask.any():
            if params.no_match_default_attribute:
                target.loc[no_match_mask, params.target_attribute] = source_frame.loc[
                    no_match_mask, params.no_match_default_attribute
                ]
            elif params.no_match_default_value:
                target.loc[no_match_mask, params.target_attribute] = pd.Series(
                    params.no_match_default_value, index=target.index
                )

        return target
