import pandas as pd
from pydantic import Field
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams):
    source_attribute: str = Field(..., description="Column name in source_frame")
    key_attribute: str = Field(..., description="Column to match on source attribute")
    value_attribute: str = Field(
        ..., description="Column to reference value for target attribute"
    )
    target_attribute: str = Field(..., description="Column name in target")


class MapFromCsv(TransformBaseTask):
    """
    This task loads a reference csv file from an ExtractPathResult, builds a dictionary
     based on the columns and uses this to map from values in the source attribute to
     produce the values for the target attribute.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources=None,
        extractor_result: ExtractPathResult = None,
        **kwargs
    ) -> pd.DataFrame:
        target = pd.DataFrame(index=source_frame.index)
        reference = self.build_reference(extractor_result, params)
        target[params.target_attribute] = (
            source_frame[params.source_attribute].map(reference).fillna(pd.NA)
        )
        return target

    @staticmethod
    def build_reference(
        extractor_result: ExtractPathResult, params: Params
    ) -> pd.Series:
        df = pd.read_csv(filepath_or_buffer=extractor_result.path, dtype=str)
        values = df[params.value_attribute]
        values.index = df[params.key_attribute]
        return values
