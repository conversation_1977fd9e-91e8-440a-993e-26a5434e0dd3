import pandas as pd
from pydantic import Field
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class BooleanFieldsDictMap:

    BOOLEAN_MAP = {
        "true": True,
        "y": True,
        "1": True,
        "t": True,
        "on": True,
        "yes": True,
        "false": False,
        "n": False,
        "0": False,
        "f": False,
        "off": False,
        "no": False,
    }


class Params(BaseParams):
    source_attribute: str = Field(..., description="Column name in source_frame")
    target_attribute: str = Field(..., description="Target Attribute name")


class MapBooleanFields(TransformBaseTask):
    """
    This task maps the target_attribute, that should be a boolean field, based on the following dict:

    if field in ['true','y','1','t','on','yes']
        field = true

    elif field in ['false','n','o','f','off','no']
        field = false

    Params:
        target_attribute
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:

        target = pd.DataFrame(index=source_frame.index)

        target[params.target_attribute] = pd.NA

        if params.source_attribute not in source_frame.columns:
            self.logger.warning(
                f"Target attribute {params.source_attribute} not in source_frame columns."
            )

            return target

        target[params.target_attribute] = self._map_to_boolean(
            data=source_frame, data_column=params.source_attribute
        )

        return target

    @staticmethod
    def _map_to_boolean(data: pd.DataFrame, data_column: str) -> pd.Series:
        """
        Map target_attribute column in `data` based on the str_to_bool_dict provided
        :param data: Source Pandas DataFrame
        :param data_column: Data column to get data from
        :return: boolean mask with the correct mapping
        """
        boolean_mask = (
            data[data_column]
            .str.lower()
            .map(BooleanFieldsDictMap.BOOLEAN_MAP)
            .fillna(pd.NA)
        )

        return boolean_mask
