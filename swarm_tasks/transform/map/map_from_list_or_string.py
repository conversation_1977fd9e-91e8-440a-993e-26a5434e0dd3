import pandas as pd
from prefect.engine import signals
from pydantic import Field
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams):
    source_attribute: str = Field(
        default="ff_16118", description="Name of the ff_16118 column"
    )
    target_attribute: str = Field(
        ...,
        description="Name of the target_attribute. This will contain either the value of"
        "source_frame[source_ff_16118_field] (if it is a string) or the first"
        "element of source_frame[source_ff_16118_field] (if it is a list)",
    )


class MapFromListOrString(TransformBaseTask):
    """
    This task takes as input a column (ff-16118) containing either a single value
    or a list of values.

    If it is a single value, it is mapped directly to the output column. If it is
    a list, the first element of the list is mapped to the target
    """

    params_class = Params

    def execute(
        self, source_frame: pd.DataFrame = None, params: Params = None, **kwargs
    ) -> pd.DataFrame:
        return self.process(source_frame=source_frame, params=params)

    @classmethod
    def process(cls, source_frame: pd.DataFrame, params: Params) -> pd.DataFrame:

        if source_frame.empty or params.source_attribute not in source_frame.columns:
            raise signals.SKIP(
                f"Source frame empty or required {params.source_attribute} column not present"
            )

        target_frame = pd.DataFrame(index=source_frame.index)
        target_frame[params.target_attribute] = pd.NA
        # Create a new df of shape [x, 1], [x, 2], [x, 3] by calling to_list(). The shape
        # is based on the longest list in the params.source_ff_16118_field column

        target_frame.loc[:, params.target_attribute] = source_frame.loc[
            :, params.source_attribute
        ].map(lambda x: x[0] if isinstance(x, list) else x)

        return target_frame.fillna(pd.NA)
