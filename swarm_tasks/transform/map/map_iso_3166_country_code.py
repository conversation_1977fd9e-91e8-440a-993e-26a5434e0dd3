import logging
from enum import Enum
from typing import Optional

import pandas as pd
import pycountry
from prefect import context
from pydantic import Field
from swarm.task.auditor import Auditor
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class CountryCodeType(str, Enum):
    ALPHA_2 = "alpha_2"
    ALPHA_3 = "alpha_3"


class Params(BaseParams):
    source_country_column: str = Field(
        ...,
        description="Name of the column which contains country names",
    )
    target_country_column: str = Field(
        ...,
        description="Name of the target column which should contain the ISO-3166-1 country codes",
    )
    target_country_code_type: CountryCodeType = Field(
        ...,
        description="Type of code that should be returned in the target (alpha-2 or alpha-3)",
    )


# Get a list of all possible 2-letter country codes
ALPHA_2_COUNTRIES = [country.alpha_2 for country in pycountry.countries]
ALPHA_3_COUNTRIES = [country.alpha_3 for country in pycountry.countries]

AUDIT_INVALID_COUNTRIES_LIST = []


class MapIso3166CountryCode(TransformBaseTask):
    """
    This task fetches the ISO-3166-1 country code corresponding to a country
    name. You can choose to get either the Alpha 2 code or the Alpha 3 code
    using the target_country_code_type param

    The conversion is done based on the
    pycountry library (https://pypi.org/project/pycountry/).
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources=None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame,
            params=params,
            logger=self.logger,
            auditor=self.auditor,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        logger=context.get("logger"),
        auditor: Auditor = None,
    ) -> pd.DataFrame:
        """Class method which populates the ISO-3166-1 country code (alpha-2 or alpha-3)
        from the source country column value.
        """
        target = pd.DataFrame(index=source_frame.index)
        target[params.target_country_column] = pd.NA

        if (
            source_frame.empty
            or params.source_country_column not in source_frame.columns
        ):
            logging.warning(
                "Source frame empty/country column not present. Returning empty frame"
            )
            return target

        country_not_null_mask = source_frame.loc[
            :, params.source_country_column
        ].notnull()

        # pycountry does not detect 'UK' as a valid country, replace this with GB
        country_uk_mask = source_frame.loc[
            :, params.source_country_column
        ].str.fullmatch("uk", case=False, na=False)
        non_null_uk_mask = country_not_null_mask & country_uk_mask

        source_frame.loc[
            non_null_uk_mask, params.source_country_column
        ] = source_frame.loc[
            non_null_uk_mask, params.source_country_column
        ].str.replace(
            "UK", "GB"
        )

        if params.target_country_code_type == CountryCodeType.ALPHA_2:
            # Convert the nationality into an ISO-3166-compliant 2-letter code
            target.loc[
                country_not_null_mask, params.target_country_column
            ] = source_frame.loc[
                country_not_null_mask, params.source_country_column
            ].apply(
                lambda country: cls._get_alpha_2_country_code(
                    country=country, logger=logger
                )
            )
        elif params.target_country_code_type == CountryCodeType.ALPHA_3:
            # Convert the nationality into an ISO-3166-compliant 3-letter code
            target.loc[
                country_not_null_mask, params.target_country_column
            ] = source_frame.loc[
                country_not_null_mask, params.source_country_column
            ].apply(
                lambda country: cls._get_alpha3_country_code(
                    country=country, logger=logger
                )
            )
        # Audit
        if len(AUDIT_INVALID_COUNTRIES_LIST) > 0:
            invalid_values_message = f"Invalid {params.source_country_column} values: {AUDIT_INVALID_COUNTRIES_LIST}"
            auditor.add(
                message=f"{params.source_country_column} has {len(AUDIT_INVALID_COUNTRIES_LIST)} invalid values",
                ctx={"error": invalid_values_message},
            )
            logger.warning(invalid_values_message)

        return target.fillna(pd.NA)

    @staticmethod
    def _get_alpha_2_country_code(
        country: str, logger: logging.Logger
    ) -> Optional[str]:
        """
        Function which uses the country name as input, and converts it to an
        ISO-3166-compliant 2-letter code. The conversion is done based on the
        pycountry library (https://pypi.org/project/pycountry/).
        :param: country: the country name
        :param: logger: logger instance
        :returns: an ISO-3166-compliant 2-letter code, or None if it can't be parsed
        """
        if pd.isna(country):
            return
        try:
            alpha_2 = pycountry.countries.lookup(country).alpha_2
        except LookupError as le:
            # Handle the case where the country is already an alpha_2 code
            if country.upper() in ALPHA_2_COUNTRIES:
                return country.upper()
            logger.error(
                f"No country exists with country code {country}. Setting country = pd.NA. Exception: {le}"
            )
            AUDIT_INVALID_COUNTRIES_LIST.append(country)
            return
        return alpha_2

    @staticmethod
    def _get_alpha3_country_code(country: str, logger: logging.Logger) -> Optional[str]:
        """
        Function which uses the country name as input, and converts it to an
        ISO-3166-compliant 3-letter code. The conversion is done based on the
        pycountry library (https://pypi.org/project/pycountry/).
        :param: country: the country name
        :param: logger: logger instance
        :returns: an ISO-3166-compliant 3-letter code, or None if it can't be parsed
        """
        if pd.isna(country):
            return
        try:
            alpha_3 = pycountry.countries.lookup(country).alpha_3
        except LookupError as le:
            # Handle the case where the country is already an alpha_3 code
            if country.upper() in ALPHA_3_COUNTRIES:
                return country.upper()
            logger.error(
                f"No country exists with country code {country}. Setting country = pd.NA. Exception: {le}"
            )
            AUDIT_INVALID_COUNTRIES_LIST.append(country)
            return
        return alpha_3
