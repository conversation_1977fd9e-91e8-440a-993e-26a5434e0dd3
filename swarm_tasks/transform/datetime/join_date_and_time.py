import logging
from typing import List
from typing import Optional

import pandas as pd
import pytz
from prefect import context
from pydantic import Field
from pydantic import root_validator
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.utilities.data_util import dtm


class Params(BaseParams):
    source_date_attribute: str = Field(None, description="Date attribute to parse")
    source_time_attribute: Optional[str] = Field(
        None, description="Time attribute to parse"
    )
    target_attribute: str
    source_format: Optional[str] = Field(
        None, description="Single source datetime format to parse"
    )
    source_formats: Optional[List[str]] = Field(
        None, description="List of datetime formats to parse"
    )
    target_format: str = Field(
        "%Y-%m-%dT%H:%M:%S.%fZ", description="Target date-time format"
    )
    timezone_info: Optional[str] = Field(
        None, description="TimeZone information eg. 'US/Eastern'"
    )
    skip_time_attribute: Optional[bool] = Field(
        False,
        description="If true then both date and time data is expected in the "
        "`source_date_attribute` field. Else both source_date_attribute and "
        "source_time_attribute needs to be provided",
    )

    @root_validator
    def validate_source_format(cls, values):
        source_format = values.get("source_format")
        source_formats = values.get("source_formats")
        source_time_attribute = values.get("source_time_attribute")
        skip_time_attribute = values.get("skip_time_attribute")

        if all([source_format, source_formats]):
            raise ValueError(
                "`source_format` and `source_formats` are mutually exclusive."
            )

        if not any([source_format, source_formats]):
            raise ValueError(
                "Either `source_format` or `source_formats` should be provided"
            )
        if not any([source_time_attribute, skip_time_attribute]):
            raise ValueError(
                "Either `source_time_attribute` should be provided or "
                "`skip_time_attribute` should be true"
            )
        if all([source_time_attribute, skip_time_attribute]):
            raise ValueError(
                "`source_time_attribute` and `skip_time_attribute` are mutually exclusive"
            )
        return values


class JoinDateAndTimeFormat(TransformBaseTask):
    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources=None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame, params=params, logger=self.logger
        )

    @classmethod
    def process(
        cls, source_frame: pd.DataFrame, params: Params, logger=context.get("logger")
    ) -> pd.DataFrame:
        target = pd.DataFrame(
            index=source_frame.index, columns=[params.target_attribute]
        ).fillna(pd.NA)
        if source_frame.empty:
            return target

        cols_used = [params.source_date_attribute, params.source_time_attribute]
        for col in cols_used:
            if col not in source_frame.columns:
                source_frame.loc[:, col] = pd.NA

        if params.skip_time_attribute is True:
            source_frame[params.source_time_attribute] = ""

        data: pd.Series = (
            source_frame[params.source_date_attribute]
            + source_frame[params.source_time_attribute]
        )
        source_format_list = []
        if params.source_format:
            source_format_list.append(params.source_format)
        elif params.source_formats:
            source_format_list.extend(params.source_formats)

        mask = data.notnull()

        if not mask.any():
            logger.info("Empty source, no data after apply mask.")
            return target

        time_zone = None
        if params.timezone_info:
            time_zone = pytz.timezone(params.timezone_info)

        target.loc[mask, params.target_attribute] = (
            data.loc[mask]
            .apply(
                lambda x: cls._dtm_single_row(
                    dt=x,
                    source_format_list=source_format_list,
                    time_zone=time_zone,
                    params=params,
                    logger=logger,
                )
            )
            .fillna(pd.NA)
        )

        return target

    @staticmethod
    def _dtm_single_row(
        dt: str,
        source_format_list: List[str],
        time_zone: str,
        params: Params,
        logger: logging.Logger,
    ) -> Optional[str]:
        """Returns the datetime in the required format for a single datetime str. If the data does
        not match the source formats, this function returns None.
        :param: dt: the input datetime
        :param: source_format_list: list of possible source formats
        :param: time_zone: timezone string
        :param: params: Params instance
        :param: logger: Logger
        :returns: A pd datetime if dt matches the source format, None if it doesn't
        """
        try:
            return dtm(
                dt_str=dt,
                dt_fmt=source_format_list,
                timezone=time_zone,
                target_format=params.target_format,
            )
        except ValueError as ve:
            logger.warning(f"{ve}. Setting {params.target_attribute} to null")
            return
