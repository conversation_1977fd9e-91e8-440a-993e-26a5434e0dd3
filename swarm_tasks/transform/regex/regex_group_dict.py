import re
from enum import Enum
from typing import Optional
from urllib.parse import unquote
from urllib.parse import unquote_plus

import pandas as pd
from pydantic import Field
from pydantic import validator
from swarm.schema.static import SwarmColumns
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask
from swarm.task.transform.result import TransformResult


class StringParser(str, Enum):
    UNQUOTE = "unquote"
    UNQUOTE_PLUS = "unquote.plus"


class Params(BaseParams):
    pattern: str = Field(
        None, description="regex pattern with at least one named subgroup"
    )
    parse_string: Optional[StringParser]

    @validator("pattern")
    def ensure_pattern_contains_named_subgroup(cls, v: str):
        subgroup_pattern = r"\(\?P\<\w+\>[^)]+\)"
        if not re.search(subgroup_pattern, v):
            raise ValueError("No named subgroup in pattern")

        return v


class RegexSearchGroupDict(BaseTask):
    """Task creates a 1 row dataframe with each column from the regex groups"""

    params_class = Params

    def execute(
        self, params: Params = None, string: str = None, **kwargs
    ) -> TransformResult:
        if params.parse_string == StringParser.UNQUOTE:
            string = unquote(string)
        elif params.parse_string == StringParser.UNQUOTE_PLUS:
            string = unquote_plus(string)

        match = re.search(rf"{params.pattern}", string)
        if not match:
            raise ValueError(f"{params.pattern} not found in {string}")

        result = pd.DataFrame([match.groupdict()])
        result[SwarmColumns.SWARM_RAW_INDEX] = 0
        return TransformResult(target=result, batch_index=0)
