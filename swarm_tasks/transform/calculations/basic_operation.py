from typing import Dict
from typing import List
from typing import Optional

import numpy as np
import pandas as pd
from pydantic import Field
from pydantic import root_validator
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class OperatorMap:
    ADD = "ADD"
    MULTIPLY = "MULTIPLY"
    DIVIDE = "DIVIDE"

    OPERATORS = {"ADD": np.sum, "MULTIPLY": np.product, "DIVIDE": np.divide}


class DivisionElements:
    DIVIDEND = "dividend"
    DIVISOR = "divisor"


class Params(BaseParams):

    source_attributes: List[str]
    operator: str
    target_attribute: str
    division_elements: Optional[Dict[str, str]] = Field(
        None,
        description="Contains information on which source column should be used as dividend and divisor",
    )

    @root_validator
    def is_operator_valid(cls, values):

        if values.get("operator") not in OperatorMap.OPERATORS:
            raise ValueError(
                f"Invalid operator {values.get('operator')}, must be one of: {OperatorMap.OPERATORS.keys()}"
            )

        return values

    @root_validator
    def are_division_elements_valid(cls, values):

        if values.get("operator") == OperatorMap.DIVIDE:
            if not values.get("division_elements"):
                raise ValueError(
                    f"'division_elements' param is needed when 'operator' == '{OperatorMap.DIVIDE}'."
                )

            dividend = values.get("division_elements").get(DivisionElements.DIVIDEND)
            divisor = values.get("division_elements").get(DivisionElements.DIVISOR)

            if not (dividend and divisor):
                raise ValueError(
                    f"'division_elements' param needs to contain the '{DivisionElements.DIVIDEND}' and '{DivisionElements.DIVISOR}' columns."
                )

            for division_element in [dividend, divisor]:
                if division_element not in values.get("source_attributes"):
                    raise ValueError(
                        f"'{DivisionElements.DIVIDEND}' and '{DivisionElements.DIVISOR}' columns need to be listed in 'source_attributes'."
                        f" Missing '{division_element}'"
                    )

        return values


class BasicOperation(TransformBaseTask):
    """
    This task reads a Pandas `source_frame`, filters the `params.source_attributes` columns,
    and applies the `params.operator` numerical operation across each row.
    Any given row with at least one null occurrence from any of the `params.source_attributes` columns
    will not be target by the numerical operation.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:

        target = pd.DataFrame(index=source_frame.index)

        if source_frame.empty:
            return target

        target.loc[:, params.target_attribute] = pd.NA

        for col in params.source_attributes:
            if col not in source_frame.columns:
                self.logger.warning(
                    f"{col} absent from source_frame, skipping calculation"
                )
                return target

        columns_mask = source_frame.columns.isin(params.source_attributes)
        source_columns_df = source_frame.loc[:, columns_mask]
        all_column_values_not_null_mask = ~source_columns_df.isnull().any(axis=1)

        if params.operator == OperatorMap.DIVIDE:
            try:
                # set null divisor to 1
                null_divisor = source_columns_df.loc[
                    :, params.division_elements[DivisionElements.DIVISOR]
                ].isnull()
                source_columns_df.loc[
                    null_divisor, params.division_elements[DivisionElements.DIVISOR]
                ] = 1

                # Convert all the source columns to float
                source_columns_df = source_columns_df.fillna(np.nan).astype("float")
                target.loc[:, params.target_attribute] = np.divide(
                    source_columns_df.loc[
                        :, params.division_elements[DivisionElements.DIVIDEND]
                    ],
                    source_columns_df.loc[
                        :, params.division_elements[DivisionElements.DIVISOR]
                    ],
                )
            except Exception as e:

                self.logger.warning(f"Calculation was not possible: {e}")
        else:
            try:
                # Convert all the source columns to float
                source_columns_df = source_columns_df.fillna(np.nan).astype("float")
                target.loc[
                    all_column_values_not_null_mask, params.target_attribute
                ] = source_columns_df.apply(
                    lambda x: OperatorMap.OPERATORS.get(params.operator)(x), axis=1
                )
            except Exception as e:

                self.logger.warning(f"Calculation was not possible: {e}")

        return target
