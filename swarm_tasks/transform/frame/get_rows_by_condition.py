import pandas as pd
from prefect import context
from prefect.engine.signals import SKIP
from se_core_tasks.frame.get_rows_by_condition import Params as GenericParams
from se_core_tasks.frame.get_rows_by_condition import run_get_rows_by_condition
from se_core_tasks.frame.get_rows_by_condition import SkipIfSourceFrameEmpty
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class GetRowsByCondition(TransformBaseTask):

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            params=params,
            source_frame=source_frame,
            auditor=self.auditor,
            logger=self.logger,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame,
        params: Params,
        auditor=None,
        logger=context.get("logger"),
    ) -> pd.DataFrame:

        try:
            return run_get_rows_by_condition(
                params=params,
                source_frame=source_frame,
                auditor=auditor,
                logger=logger,
            )
        except SkipIfSourceFrameEmpty as e:
            raise SKIP(f"Flow will be skipped: {e.message}")
