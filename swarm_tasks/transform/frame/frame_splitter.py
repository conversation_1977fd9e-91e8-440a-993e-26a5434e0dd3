from typing import Optional

import pandas as pd
from pydantic import Field
from pydantic import root_validator
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams):
    prefix: Optional[str] = Field(
        None,
        description="Columns with this prefix will be picked to target DataFrame.",
    )
    except_prefix: Optional[str] = Field(
        None,
        description="Columns with this prefix will be removed from the DataFrame.",
    )
    strip_prefix: Optional[bool] = Field(
        None,
        description="This boolean param works"
        "hand-in-hand with prefix"
        "parameter. Columns in"
        "target(depending on prefix"
        "given) will be stripped off"
        "the prefix provided.",
    )

    @root_validator
    def at_least_one_param(cls, values):
        params_given = [x for x, y in values.items() if y is not None]
        expected_params = ["prefix", "except_prefix"]
        if not any(y in expected_params for y in params_given):
            raise ValueError("At least one param must be defined.")

        return values


class FrameSplitter(TransformBaseTask):
    """
    This task gets Union[TransformResult, FrameProducerResult] input and
    split them either horizontal or vertically according to the parameters/.

    Dev note: This task has to inherit from BaseTask.

    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Optional[Params] = None,
        resources: Optional[BaseResources] = None,
        **kwargs
    ) -> pd.DataFrame:

        if source_frame.empty:
            return pd.DataFrame()

        target = source_frame
        if params.prefix:
            target = target.loc[:, target.columns.str.startswith(params.prefix)]
            if params.strip_prefix:
                target.columns = target.columns.str.replace(params.prefix, "")

        if params.except_prefix:
            target = target.loc[
                :, ~source_frame.columns.str.startswith(params.except_prefix)
            ]

        return target
