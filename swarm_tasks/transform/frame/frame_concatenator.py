import pandas as pd
from prefect.engine.signals import SKIP
from se_core_tasks.core.core_dataclasses import (
    FrameProducerResult as CoreFrameProducerResult,
)
from se_core_tasks.core.core_dataclasses import TransformResult as CoreTransformResult
from se_core_tasks.frame.frame_concatenator import Params as GenericPara<PERSON>
from se_core_tasks.frame.frame_concatenator import run_frame_concatenator
from se_core_tasks.frame.frame_concatenator import SkipOnEmptyFrames
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask
from swarm.task.io.read import FrameProducerResult
from swarm.task.transform.result import TransformResult


class Params(BaseParams, GenericParams):
    pass


class FrameConcatenator(BaseTask):

    params_class = Params

    def execute(
        self,
        params: Params = None,
        **kwargs,
    ) -> TransformResult:

        # The TransformResult dataclass still lives in both swarm-sdk and se-core-tasks, as we have not yet
        # deprecated it from swarm. Thus, to preserve existing behavior, we need to convert the swarm-sdk data
        # structures to the se-core counterparts
        for task_result in kwargs:
            if isinstance(kwargs[task_result], TransformResult):
                kwargs[task_result] = CoreTransformResult(
                    target=kwargs[task_result].target,
                    batch_index=kwargs[task_result].batch_index,
                )
            if isinstance(kwargs[task_result], pd.DataFrame):
                kwargs[task_result] = CoreFrameProducerResult(
                    frame=kwargs[task_result],
                    batch_index=0,
                )
            if isinstance(kwargs[task_result], FrameProducerResult):
                kwargs[task_result] = CoreFrameProducerResult(
                    frame=kwargs[task_result].frame,
                    batch_index=kwargs[task_result].batch_index,
                )
        try:
            result = run_frame_concatenator(
                params=params,
                map_results=kwargs,
                auditor=self.auditor,
                logger=self.logger,
            )
            return TransformResult(target=result.target, batch_index=result.batch_index)

        except SkipOnEmptyFrames as e:
            raise SKIP(f"Flow will be skipped: {e.message}")
