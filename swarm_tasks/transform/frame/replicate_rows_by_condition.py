from typing import List
from typing import Optional

import pandas as pd
from prefect import context
from pydantic import Field
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams):
    query: str = Field(
        ...,
        description="Condition to apply on the source_frame. It should follow pandas query syntax.",
    )
    columns_to_set_as_null: Optional[List[str]] = Field(
        None, description="List of columns to set as null on the replicated rows"
    )


class ReplicateRowsByCondition(TransformBaseTask):
    """
    The purpose of this task is to replicate rows of the input Data Frame based on a given condition.
    This condition must be specified as Pandas .query syntax.
    The task can be used to easily generate extra records from input data i.e: the EMSO Red Deer Flow
    uses this task to generate synthetic standalone Order records for every Allocation.

    Additional parameters can be configured to manipulate the Data Frame, such as replicating rows
    but setting certain columns to null values.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame, params=params, logger=self.logger
        )

    @staticmethod
    def process(
        source_frame: pd.DataFrame = None,
        params: Params = None,
        logger=context.get("logger"),
    ) -> pd.DataFrame:

        if source_frame.empty:
            logger.warning("source_frame is empty")
            return source_frame

        data = source_frame.query(params.query, engine="python")

        if params.columns_to_set_as_null:
            cols_mask = data.columns.isin(params.columns_to_set_as_null)
            data.loc[data.index, cols_mask] = pd.NA
            return (
                pd.concat([source_frame, data], axis=0)
                .drop_duplicates()
                .reset_index(drop=True)
            )

        return pd.concat([source_frame, data], axis=0).reset_index(drop=True)
