from pathlib import Path
from typing import List
from typing import Optional

import prefect
from pydantic import Field
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask


class Params(BaseParams):
    s3_key_prefix: str = Field(..., description="directory of output s3 url")
    file_extension: str = Field(..., description="file extension of output s3 url")


class TransformS3Url(BaseTask):
    """
    This task returns an S3 Url derived by changing the file extension and key prefix of the input file_url
    """

    params_class = Params

    def execute(
        self,
        params: Params = None,
        resources: Optional[BaseResources] = None,
        file_url: str = None,
    ) -> List[str]:
        if not file_url:
            raise prefect.engine.signals.FAIL("Missing file_url")

        s3_bucket, s3_key = file_url[5:].split("/", 1)

        self.logger.info(f"Using file_url '{file_url}'")
        ext = params.file_extension
        if not ext.startswith("."):
            ext = f".{ext}"
        s3_key = Path(params.s3_key_prefix, Path(file_url).name).with_suffix(ext)
        result = f"s3://{s3_bucket}/{s3_key}"
        self.logger.info(f"result S3 url: {result}")
        return [result]
