import logging
from typing import Any
from typing import List
from typing import Union

from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.core.core_dataclasses import FileSplitterResult
from se_core_tasks.io.read.csv_file_splitter import Params as GenericParams
from se_core_tasks.io.read.csv_file_splitter import run_csv_file_splitter
from swarm.conf import Settings
from swarm.schema.static import SwarmColumns
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


class Params(BaseParams, GenericParams):
    pass


class CsvFileSplitter(BaseTask):

    params_class = Params

    def execute(
        self,
        params: Params = None,
        extractor_result: Union[ExtractPathResult, str] = None,
        skiprows: Union[int, List[int]] = None,
        **kwargs,
    ) -> List[FileSplitterResult]:
        return self.process(
            params=params,
            extractor_result=extractor_result,
            skiprows=skiprows,
            auditor=self.auditor,
            logger=self.logger,
        )

    @classmethod
    def process(
        cls,
        params: Params = None,
        extractor_result: Union[ExtractPathResult, str] = None,
        skiprows: Union[int, List[int]] = None,
        auditor: Any = None,
        logger: logging.Logger = None,
    ):
        result = run_csv_file_splitter(
            params=params,
            csv_path=extractor_result
            if isinstance(extractor_result, str)
            else extractor_result.path.as_posix(),
            realm=Settings.realm,
            sources_dir=Settings.context.sources_dir,
            raw_index_name=SwarmColumns.SWARM_RAW_INDEX,
            skiprows=skiprows,
            auditor=auditor,
            logger=logger,
        )

        return [
            FileSplitterResult(
                path=x.path, batch_index=x.batch_index, encoding=x.encoding
            )
            for x in result
        ]
