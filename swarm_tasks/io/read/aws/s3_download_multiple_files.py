import asyncio
import logging
import re
from pathlib import Path
from typing import Any
from typing import Optional
from typing import Union

import pandas as pd
from prefect import context
from prefect.engine import signals
from pydantic import Field
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_elastic_schema.elastic_schema.core.base import BaseStrEnum
from swarm.conf import Settings
from swarm.task.auditor import Auditor
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask
from swarm.task.io.read import FrameProducerResult

from swarm_tasks.cloud.aws.s3.utils import async_read_csv_from_s3
from swarm_tasks.utilities.s3 import s3_bucket_from_url
from swarm_tasks.utilities.s3 import s3_key_from_url


class SupportedMerges(BaseStrEnum):
    OUTER = "outer"
    INNER = "inner"
    LEFT = "left"
    RIGHT = "right"


class Params(BaseParams):
    audit_skipped_rows: bool = Field(
        default=False,
        description="If True, audits the no of rows which were skipped due to misaligned data.",
    )
    file_columns: Optional[list] = Field(
        None,
        description="Name of the column which contains the S3 URL of the 4 required files",
    )
    file_separator: Optional[str] = Field(
        default=",",
        description="Seperator of the file which is used by pandas to read the data",
    )
    detect_encoding: bool = Field(
        default=False,
        description="Flag to detect encoding of the downloaded data before loading it in dataframe."
        "If False defaults to utf-8",
    )
    detect_file_separator: bool = Field(
        default=False,
        description="Flag to indicate whether the file separator(delimiter) needs to be detected before reading"
        "the file. If True will override the param file_separator",
    )
    join_columns: Optional[list] = Field(
        default=None,
        description="Name of the column which is present in all of the dataframes "
        "and to be used as merge index",
    )
    join_type: Optional[SupportedMerges] = Field(
        default=SupportedMerges.OUTER,
        description="Type of join to be applied. Takes in values passed onto Pandas merge.",
    )
    suffix_identifier_regex: str = Field(
        default=" ",
        description="Regex which strips the file_columns to get the unique identifier out of them eg: "
        "s3_fsi_crims_(.*)_file_url regex for file column s3_fsi_crims_order_file_url will give order."
        "This required when files to be merged has "
        "common columns which when merged will require the suffixes",
    )


class S3DownloadMultipleFiles(BaseTask):
    """
    This task reads the required S3 files after downloading them based on the S3 links
    in the columns of the input data frame.
    Finally, it combines the files together to produce a single data frame.
    """

    params_class = Params

    def execute(
        self,
        producer_result: FrameProducerResult = None,
        params: Optional[Params] = None,
        **kwargs,
    ) -> Any:
        return self.process(
            producer_result=producer_result,
            params=params,
            logger=self.logger,
            auditor=self.auditor,
        )

    @classmethod
    def process(
        cls,
        producer_result: Union[FrameProducerResult, pd.DataFrame] = None,
        params: Optional[Params] = None,
        logger=context.get("logger"),
        auditor: Auditor = None,
    ) -> Any:

        if isinstance(producer_result, FrameProducerResult):
            source_frame = producer_result.frame
        else:
            source_frame = producer_result

        if source_frame.empty:
            raise signals.SKIP("Source frame empty, no files to read and join")

        data = source_frame.copy()
        suffix_identifier_regex = re.compile(params.suffix_identifier_regex)

        # Replace the col names in the source_frame with the regex pattern to capture
        # For example s3_fsi_crims_order_file_url becomes order.
        data = data.rename(
            columns={
                col: (lambda match: match.group(1) if match else "XXXX")(
                    re.search(suffix_identifier_regex, col)
                )
                for col in data.columns
            }
        )

        # If a list of file_columns to be downloaded are provided, the file_columns are also
        # replaced by the same regex match as above, to keep the dataframe columns and the
        # file_columns to download list consistent
        columns = (
            [
                (lambda match: match.group(1) if match else "XXXX")(
                    re.search(suffix_identifier_regex, col)
                )
                for col in params.file_columns
            ]
            if params.file_columns
            else data.columns
        )

        # The source_frame has 1 row with required columns. Get the file urls from each of the
        # cells in the row and download the data frames
        response_list = asyncio.run(
            cls._get_df_from_s3(
                source_frame=data,
                col_name_list=columns,
                file_sep=params.file_separator,
                detect_encoding=params.detect_encoding,
                detect_file_separator=params.detect_file_separator,
                logger=logger,
                auditor=auditor,
                audit_skipped_rows=params.audit_skipped_rows,
            )
        )

        downloaded_files = {
            next(iter(response.keys())) for response in response_list if response.keys()
        }
        error_files = set(columns) - downloaded_files
        if len(error_files) > 0:
            raise signals.SKIP(
                f"{data[error_files].values} files had issues processing"
            )

        # Joining the dataframe based on join columns if provided
        # (ordering in params.file_columns is maintained)
        if params.join_columns:
            target = pd.DataFrame()
            for index, df_dict in enumerate(response_list):
                df = next(iter(df_dict.values()))
                df = df.assign(flag=1)
                df[params.join_columns] = df[params.join_columns].convert_dtypes()
                if index == 0:
                    target = df
                else:
                    # Create suffixes for common columns in merge
                    left_suffix = "_" + next(iter(response_list[index - 1].keys()))
                    right_suffix = "_" + next(iter(df_dict.keys()))
                    target = target.merge(
                        right=df,
                        how=params.join_type.value,
                        on=params.join_columns,
                        suffixes=(left_suffix, right_suffix),
                    )

            source_dir = cls._get_source_dir()
            csv_file_path = source_dir.joinpath("data_merged.csv")
            target.to_csv(csv_file_path, index=False, encoding="utf-8", sep=",")
            result = ExtractPathResult(path=csv_file_path)
        else:
            result = {
                file_type: df
                for df_dict in response_list
                for file_type, df in df_dict.items()
            }
        return result

    @classmethod
    async def _get_df_from_s3(
        cls,
        source_frame: pd.DataFrame,
        col_name_list: list,
        file_sep: str,
        detect_encoding: bool,
        detect_file_separator: bool,
        logger: logging.Logger,
        auditor: Auditor,
        audit_skipped_rows: bool,
    ) -> dict:
        """
        Gets the S3 CSV file pointed to by the source_frame.loc[0, col_name], downloads
        it and returns the dataframe obtained by reading the csv using pd.read_csv
        :param source_frame: Source frame containing a column called col_name
        :param col_name_list: column containing link to an S3 CSV file
        :param file_sep: Seperator delimiter of the files. Used by pandas read_csv.
        :param detect_encoding: Flag to detect encoding of the bytes data before loading it in dataframe.
                                If False utf-8 is used
        :param logger: logger instance
        :return: DataFrame obtained by downloading the S3 CSV file in the col_name column
                 of source_frame
        """
        download_config = list()
        for col_name in col_name_list:
            s3_file_url = source_frame.loc[0, col_name]
            bucket = s3_bucket_from_url(s3_url=s3_file_url)
            key_ = s3_key_from_url(s3_url=s3_file_url)

            download_config.append({"bucket": bucket, "key": key_, "index": col_name})

        response_list = await async_read_csv_from_s3(
            download_config=download_config,
            logger=logger,
            auditor=auditor,
            file_sep=file_sep,
            detect_encoding=detect_encoding,
            detect_file_separator=detect_file_separator,
            audit_skipped_rows=audit_skipped_rows,
        )

        return response_list

    @staticmethod
    def _get_source_dir() -> Path:
        return Path(Settings.context.sources_dir)
