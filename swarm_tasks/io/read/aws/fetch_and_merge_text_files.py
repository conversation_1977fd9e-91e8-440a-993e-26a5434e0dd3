import json
from datetime import datetime
from datetime import timed<PERSON><PERSON>
from pathlib import Path
from typing import Any
from typing import Dict
from typing import List
from typing import Optional

import boto3
import pandas as pd
from prefect.engine.signals import FAIL
from prefect.engine.signals import SKIP
from pydantic import Field
from pydantic import ValidationError
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


class Params(BaseParams):
    delta_in_days: Optional[int] = Field(
        None,
        description=" if 0 -> download files from day of Flow execution (T);"
        "if 1 -> download FIX files from day before Flow Execution (T-1), etc...",
    )
    prefix: Optional[str] = Field(
        None, description="S3 Key Prefix to search for files in"
    )
    line_split: Optional[bool] = Field(
        True,
        description="If True splits each line of the file into its own dataframe row",
    )


class FetchAndMergeTextFiles(BaseTask):
    """
    This task is used to merge different files into a single dataframe. It can be used to do lookups
    on a given path by passing `prefix` param alone, or within a given date including the prefix, by passing
    the `delta_in_days` param. All files found will be downloaded and stored in a Pandas DataFrame with 2 columns,
    one with the file location in S3, and the other with the file's contents.

    Use case: This task is particularly useful to load the contents of FIX files into a single DataFrame, ready
    to be parsed by the FixParser task. This behavior is needed for the `order-feed-cme-fix` Flow, to ensure
    only FIX messages from a given day are processed.
    FIX files generally only contain one fix message. This task is able to handle multiple messages per file, when
    the line_split is enabled
    """

    params_class = Params

    def execute(
        self,
        flow_args: str = None,
        params: Optional[Params] = None,
        **kwargs,
    ) -> pd.DataFrame:

        # done to escape the logic below
        line_split = params.line_split
        params.line_split = None

        if not params.dict(exclude_none=True):
            args = self._load_flow_args(flow_args)
        else:
            args = {}

        prefix = self._determine_prefix(flow_args_dict=args, params=params)
        realm = Settings.realm
        s3_client = boto3.resource("s3")
        bucket = s3_client.Bucket(realm)

        try:
            objects = self._get_bucket_objects_by_prefix(bucket=bucket, prefix=prefix)
        except Exception as e:
            raise FAIL(
                f"Could not fetch files for the given path: {prefix}, with exception: {e}"
            )

        list_of_dfs = self.remote_files_to_list_of_dfs(
            objects=objects, realm=realm, s3_client=s3_client, line_split=line_split
        )

        if list_of_dfs:
            combined_batches = pd.concat(list_of_dfs).reset_index(drop=True)
            return combined_batches
        else:
            raise SKIP(f"No files found on s3 bucket {realm} with prefix {prefix}")

    @staticmethod
    def remote_files_to_list_of_dfs(
        objects: Any, realm: str, s3_client: Any, line_split: bool
    ) -> List[pd.DataFrame]:
        """
        Download text files from S3 and store its contents and location to a list of Pandas DataFrames.
        By default, it turns each line of the file content into its own row. `line_split` can be set to False
        to turn this off.
        :param objects: S3 Object iterator
        :param realm: Tenant realm to scroll for files
        :param s3_client: S3 client
        :param line_split: bool deciding whether content is split by new lines
        :return: List of Pandas DataFrames with 2 columns each, for absolute file path and its contents
        """
        list_of_dfs = []
        for batch_file in objects:

            if batch_file.size == 0:
                continue

            file_object = s3_client.Object(realm, batch_file.key).get()["Body"]

            file_content = file_object.read().decode("utf-8")

            file_content_list = [file_content]
            number_of_lines = 1
            if line_split:
                file_content_list = [line for line in file_content.split("\n") if line]
                number_of_lines = len(file_content_list)

            list_of_dfs.append(
                pd.DataFrame(
                    {
                        "file_path": ["s3://" + realm + "/" + batch_file.key]
                        * number_of_lines,
                        "file_content": file_content_list,
                    }
                )
            )
        return list_of_dfs

    @staticmethod
    def _get_bucket_objects_by_prefix(bucket: Any, prefix: str) -> Any:
        """
        Return S3 Object iterator. Extracted into a separate method to facilitate testing/mocking
        :param bucket: S3 Bucket
        :param prefix: S3 Key prefix
        :return: Object iterator
        """
        return bucket.objects.filter(Prefix=prefix)

    @staticmethod
    def _determine_prefix(flow_args_dict: Dict, params: Params) -> str:
        """
        Determine the S3 Key prefix to search for files for
        :param flow_args_dict: FlowArgs as a dictionary
        :param params: Task Params
        :return: S3 Key prefix based on current date and input time delta
        """

        prefix_args = flow_args_dict.get("prefix") or params.prefix
        if not prefix_args:
            raise FAIL(
                "The `prefix` attribute must be specified as a Param or FlowArg to fetch and merge text files"
            )

        delta_in_days = flow_args_dict.get("delta_in_days")
        if delta_in_days is None:
            delta_in_days = params.delta_in_days

        date_prefix = ""
        if delta_in_days is not None:
            date_prefix = (
                datetime.today() - timedelta(days=int(delta_in_days))
            ).strftime("%Y%m%d")

        flows_prefix = Path(prefix_args)
        prefix = flows_prefix.joinpath(date_prefix).as_posix()

        return prefix

    @staticmethod
    def _load_flow_args(flow_args: str) -> dict:
        """
        Load and validate FlowArgs
        :param flow_args: FlowArgs as string. Must match the fields and validations of the Params class
        :return: FlowArgs as dictionary
        """
        if not flow_args:
            raise FAIL("No params or flow_args have been passed to this Task")

        result = json.loads(flow_args)
        try:
            Params(**result)
        except ValidationError as e:
            raise FAIL(
                f"input flow_args: {flow_args} are not valid, with exception -> {e}"
            )

        return json.loads(flow_args)
