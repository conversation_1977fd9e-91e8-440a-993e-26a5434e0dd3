from typing import List
from typing import Optional
from typing import Union

from prefect.engine.signals import FAIL
from prefect.engine.signals import SKIP
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.io.read.unzip import FailIfEmptyArchive
from se_core_tasks.io.read.unzip import FailIfFileDoesNotExists
from se_core_tasks.io.read.unzip import FailIfMultiFileAndIsGzipFile
from se_core_tasks.io.read.unzip import (
    FailIfZipHasMultipleFilesAndParamsMultiFileIsFalse,
)
from se_core_tasks.io.read.unzip import Params as GenericParams
from se_core_tasks.io.read.unzip import run_unzip
from se_core_tasks.io.read.unzip import SkipIfInputFileIsEmpty
from se_core_tasks.io.read.unzip import SkipIfNotZip
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


class Params(BaseParams, GenericParams):
    pass


class Unzip(BaseTask):

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        extract_result: ExtractPathResult = None,
        **kwargs,
    ) -> Union[ExtractPathResult, List[ExtractPathResult]]:

        return self.process(
            params=params,
            extract_result=extract_result,
            auditor=self.auditor,
            logger=self.logger,
        )

    @classmethod
    def process(
        cls,
        params: Optional[Params] = None,
        extract_result: ExtractPathResult = None,
        auditor=None,
        logger=None,
    ):
        try:
            return run_unzip(
                extract_result=extract_result,
                params=params,
                auditor=auditor,
                logger=logger,
            )
        except (SkipIfInputFileIsEmpty, SkipIfNotZip) as e:
            raise SKIP(f"Downstream tasks will be skipped: {e}")

        except (
            FailIfFileDoesNotExists,
            FailIfMultiFileAndIsGzipFile,
            FailIfEmptyArchive,
            FailIfZipHasMultipleFilesAndParamsMultiFileIsFalse,
        ) as e:
            raise FAIL(f"Flow has failed: {e}")
