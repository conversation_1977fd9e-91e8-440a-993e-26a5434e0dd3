from typing import List

from prefect import context
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.core.core_dataclasses import FileSplitterResult
from se_core_tasks.io.read.xml_file_splitter import Params as GenericParams
from se_core_tasks.io.read.xml_file_splitter import run_xml_file_splitter
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


class Params(BaseParams, GenericParams):
    pass


class XMLFileSplitter(BaseTask):

    params_class = Params

    def execute(
        self,
        extractor_result: ExtractPathResult = None,
        params: Params = None,
        **kwargs,
    ) -> List[FileSplitterResult]:

        return self.process(
            extractor_result=extractor_result,
            params=params,
            auditor=self.auditor,
            logger=self.logger,
        )

    @classmethod
    def process(
        cls,
        extractor_result: ExtractPathResult = None,
        params: GenericParams = None,
        auditor=None,
        logger=context.get("logger"),
    ) -> List[FileSplitterResult]:
        return run_xml_file_splitter(
            extractor_result=extractor_result,
            sources_dir=context.swarm.sources_dir,
            params=params,
            auditor=auditor,
            logger=logger,
        )
