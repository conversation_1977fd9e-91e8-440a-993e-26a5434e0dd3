from typing import Dict
from typing import Optional
from typing import Union

import pandas as pd
from prefect.engine.signals import FAIL
from se_core_tasks.io.read.fetch_tenant_ingested_records import FailIfDateCannotBeParsed
from se_core_tasks.io.read.fetch_tenant_ingested_records import (
    Params as GenericParams,
)
from se_core_tasks.io.read.fetch_tenant_ingested_records import (
    run_fetch_tenant_ingested_records,
)
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask


class Params(BaseParams, GenericParams):
    pass


class Resources(BaseResources):
    es_client_key: str = "tenant-data"


class FetchTenantIngestedRecords(BaseTask):
    """
    Task which based on a particular time-frame returns the
    ingested records of the specified models for the given tenant.

    If multiple model's data is requested this task returns a dict of dataframes.
    If only one model's data is requested then it returns a dataframe of the ingested record data.
    """

    resources_class = Resources
    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        resources: Optional[Resources] = None,
        **kwargs,
    ) -> Union[Dict, pd.DataFrame]:

        es_client = Settings.connections.get(resources.es_client_key)

        try:
            result = run_fetch_tenant_ingested_records(
                params=params,
                es_client=es_client,
                tenant=Settings.tenant,
                auditor=self.auditor,
                logger=self.logger,
            )
        except FailIfDateCannotBeParsed:
            raise FAIL("Any one of the time ranges could not be parsed.")

        return result
