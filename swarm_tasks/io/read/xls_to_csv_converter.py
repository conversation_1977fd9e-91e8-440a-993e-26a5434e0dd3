from pathlib import Path
from typing import List
from typing import Optional

import pandas as pd
from prefect.engine import signals
from pydantic import Field
from pydantic import root_validator
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.utils.frame_manipulation import remove_duplicates
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask


class FileFormatFields:
    CSV = ".csv"
    XLS = ".xls"
    XLSX = ".xlsx"


dict_converter_datatypes = {
    "string": str,
    "integer": int,
    "float": float,
}


class Params(BaseParams):
    """
    Example params in bundle.yaml

    - path: swarm_tasks.io.read.xls_to_csv_converter:XlsToCsvConverter
    name: XlsToCsvConverter
    params:
       interpret_as_str: true
       source_date_columns:
         - TradeDate

    - path: swarm_tasks.io.read.xls_to_csv_converter:XlsToCsvConverter
      name: XlsToCsvConverter
      params:
        source_date_columns:
          - TradeDate
        source_convert_columns:
          ReceiveTime: string
          TransactTime: string
          SendingTime: string
          MaturityMonthYear: string

    """

    interpret_as_str: Optional[bool] = Field(
        default=False,
        description="Should pandas interpret the source columns as strings",
    )
    source_date_columns: Optional[List[str]] = Field(
        default=None,
        description="List of columns which should be parsed as date",
    )
    source_convert_columns: Optional[dict] = Field(
        default=None,
        description="Dict of columns and datatype to convert them to (override automatic datatype interpretation)"
        "The datatypes 'string', 'float' and 'integer' can be specified'",
    )
    target_date_format: str = Field(
        default="%Y-%m-%dT%H:%M:%S.%fZ",
        description="Date format for the date fields used while writing to csv",
    )
    target_float_format: str = Field(
        default="%.10f",
        description="Float format of the date fields used while writing to csv",
    )
    drop_duplicates_subset: Optional[List[str]] = Field(
        None,
        description="DataFrame parsed from the XLS will drop duplicate rows based on this list of columns",
    )

    keep_first_occurrence_from_sorted_column: Optional[str] = Field(
        None,
        description="DataFrame parsed from the XLS will drop duplicate rows"
        "based on the `drop_duplicates_column` and will keep the first occurrence of this column, in descending order",
    )

    @root_validator
    def check_keep_first_occurrence_from_sorted_column(cls, values):
        if values.get("keep_first_occurrence_from_column") and not values.get(
            "drop_duplicates_subset"
        ):
            raise ValueError(
                "`drop_duplicates_subset` should be populated if "
                "`keep_first_occurrence_from_sorted_column` is present"
            )
        return values

    @root_validator
    def validate_interpret_as_str_and_source_convert_columns_not_both_present(
        cls, values
    ):
        if values.get("interpret_as_str") and values.get("source_convert_columns"):
            raise ValueError(
                "Please use either interpret_as_str or source_convert_columns, not both!"
            )
        return values


class XlsToCsvConverter(BaseTask):

    """
    Takes a Path for an xls/xlsx file as input, reads it and loads it into memory,
    and writes the file as CSV. If anything goes wrong, downstream tasks are skipped
    """

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        resources: Optional[BaseResources] = None,
        extractor_result: ExtractPathResult = None,
    ) -> ExtractPathResult:

        file_path = extractor_result.path
        if not file_path.exists():
            raise signals.SKIP(f"File does not exist at location: {extractor_result}")

        self.logger.info(f"Using file_url `{extractor_result}`")

        if FileFormatFields.CSV in file_path.name:
            self.logger.info(
                "xls(x) to csv conversion not required as the file is already a CSV file"
            )
            return extractor_result

        if not (
            FileFormatFields.XLS in file_path.name
            or FileFormatFields.XLSX in file_path.name
        ):
            raise signals.SKIP(f"File is not in XLS/XLSX format: {extractor_result}")

        try:
            excel_dtype = "object" if params.interpret_as_str else None

            convert_cols_dict = (
                {
                    k: dict_converter_datatypes.get(v)
                    for k, v in params.source_convert_columns.items()
                }
                if params.source_convert_columns
                else None
            )
            data = pd.read_excel(
                file_path,
                dtype=excel_dtype,
                parse_dates=params.source_date_columns,
                converters=convert_cols_dict,
            )

            deduplicated_data = remove_duplicates(
                dataframe=data,
                drop_duplicates_subset=params.drop_duplicates_subset,
                keep_first_occurrence_from_sorted_column=params.keep_first_occurrence_from_sorted_column,
            )

            csv_path = file_path.with_suffix(FileFormatFields.CSV).as_posix()
            deduplicated_data.to_csv(
                csv_path,
                index=False,
                date_format=params.target_date_format,
                float_format=params.target_float_format,
            )
        except Exception as exc:
            self.logger.warning(exc)
            raise signals.SKIP("Failed to convert to CSV")

        response = ExtractPathResult(path=Path(csv_path))
        return response
