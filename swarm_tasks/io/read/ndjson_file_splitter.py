from typing import List
from typing import Optional
from typing import Union

from prefect.engine.signals import SKIP
from se_core_tasks.core.core_dataclasses import FileSplitterResult
from se_core_tasks.io.read.ndjson_file_splitter import Params as GenericParams
from se_core_tasks.io.read.ndjson_file_splitter import run_ndjson_file_splitter
from se_core_tasks.io.read.ndjson_file_splitter import SkipIfInputFileIsEmpty
from swarm.schema.static import SwarmColumns
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask
from swarm.task.io.read.result import ExtractPathResult


class Params(BaseParams, GenericParams):
    pass


class NDJSONFileSplitter(BaseTask):

    params_class = Params

    def execute(
        self,
        params: Optional[BaseParams] = None,
        resources: Optional[BaseResources] = None,
        extract_result: Union[ExtractPathResult, List[ExtractPathResult]] = None,
        **kwargs,
    ) -> List[FileSplitterResult]:

        return self.process(
            params=params,
            extract_result=extract_result,
            auditor=self.auditor,
            logger=self.logger,
        )

    @classmethod
    def process(
        cls,
        params: Optional[Params] = None,
        extract_result: Union[ExtractPathResult, List[ExtractPathResult]] = None,
        auditor=None,
        logger=None,
    ) -> List[FileSplitterResult]:
        try:
            return run_ndjson_file_splitter(
                extract_result=extract_result,
                params=params,
                auditor=auditor,
                index_name=SwarmColumns.SWARM_RAW_INDEX,
                logger=logger,
            )
        except SkipIfInputFileIsEmpty as e:
            raise SKIP(f"Input file is empty: {e}")
