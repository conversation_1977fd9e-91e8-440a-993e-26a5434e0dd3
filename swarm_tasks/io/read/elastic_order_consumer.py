import os
from json import dumps
from json import loads
from typing import List
from typing import Optional

import pandas as pd
import pendulum
from prefect.engine import signals
from se_elastic_schema.models import Order
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.tca import TCAFlagStatus
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask

STACK = os.environ.get("STACK")


class KafkaMessage:
    BUY_SELL = "buySell"
    BUY_SELL_INDICATOR = "buySellIndicator"
    CFI_ATTRIBUTE_1 = "cfiAttribute1"
    CFI_ATTRIBUTE_3 = "cfiAttribute3"
    CFI_CATEGORY = "cfiCategory"
    CFI_GROUP = "cfiGroup"
    CURRENCY = "priceCurrency"
    DATE = "date"
    FX_DERIVATIVES = "fxDerivatives"
    INDEX = "key._index"
    INSTRUMENT = "key.instrumentUniqueIdentifier"
    INSTRUMENT_DETAILS = "instrumentDetails"
    INSTRUMENT_CLASSIFICATION = "instrumentClassification"
    JSON_INSTRUMENT = "instrument"
    META_ID = "key.id"
    MODEL = "key.model"
    NOTIONAL_CCY_1 = "notionalCurrency1"
    NOTIONAL_CCY_2 = "notionalCurrency2"
    ORDER_RECEIVED = "key.orderReceived"
    ORDER_RECEIVED_PARENT = "key.orderReceivedParent"
    ORDER_SUBMITTED = "key.orderSubmitted"
    PARENT = "key.parent"
    PRICE = "price"
    PRICE_DATA = "priceFormingData"
    PRICE_NOTATION = "priceNotation"
    STACK = "key.stack"
    TENANT = "key.tenant"
    TRADING_DATE_TIME = "key.tradingDateTime"
    TRANSACTION = "transactionDetails"
    VALUE = "value.doc"


class PriceColumns:
    ASK_PRICE = "transactionDetails.pricingDetails.askPrice"
    PERCENT_ASK_PRICE = "transactionDetails.pricingDetails.percentVsAskPrice"
    BID_PRICE = "transactionDetails.pricingDetails.bidPrice"
    PERCENT_BID_PRICE = "transactionDetails.pricingDetails.percentVsBidPrice"
    BID_ASK_SPREAD = "transactionDetails.pricingDetails.bidAskSpread"
    PERCENT_BID_ASK_SPREAD = "transactionDetails.pricingDetails.percentVsBidAskSpread"
    NEAREST_QUOTE = "transactionDetails.pricingDetails.nearestQuote"
    PERCENT_NEAREST_QUOTE = "transactionDetails.pricingDetails.percentVsNearestQuote"
    MARKET_PRICE = "transactionDetails.pricingDetails.marketPrice"
    PERCENT_SLIPPAGE = "transactionDetails.pricingDetails.percentVsSlippage"
    VWAP = "transactionDetails.pricingDetails.vwap"
    OPEN_PRICE = "transactionDetails.pricingDetails.openPrice"
    PERCENT_VWAP = "transactionDetails.pricingDetails.percentVsVwap"
    ARRIVAL_PRICE = "transactionDetails.pricingDetails.arrivalPrice"
    PERCENT_ARRIVAL_PRICE = "transactionDetails.pricingDetails.percentVsArrival"
    VENUE_VWAP = "transactionDetails.pricingDetails.venueVwap"
    PERCENT_VS_VENUE_VWAP = "transactionDetails.pricingDetails.percentVsVenueVwap"
    BEST_ASK_PRICE = "transactionDetails.pricingDetails.bestAskPrice"
    PERCENT_BEST_ASK = "transactionDetails.pricingDetails.percentVsBestAsk"
    PERCENT_CLOSE = "transactionDetails.pricingDetails.percentVsClose"
    PERCENT_PREVIOUS_DAY_OPEN = (
        "transactionDetails.pricingDetails.percentVsPreviousDayOpen"
    )
    PERCENT_MARKET = "transactionDetails.pricingDetails.percentVsMarket"
    BEST_BID_PRICE = "transactionDetails.pricingDetails.bestBidPrice"
    PERCENT_BEST_BID = "transactionDetails.pricingDetails.percentVsBestBid"
    PREVIOUS_DAY_CLOSE_PRICE = "transactionDetails.pricingDetails.previousDayClosePrice"
    MOVING_VWAP = "transactionDetails.pricingDetails.movingVwap"
    SLIPPAGE_PRICE = "transactionDetails.pricingDetails.slippagePrice"
    PERCENT_MOVING_VWAP = "transactionDetails.pricingDetails.percentVsMovingVwap"
    PREVIOUS_DAY_OPEN_PRICE = "transactionDetails.pricingDetails.previousDayOpenPrice"
    PERCENT_PREVIOUS_DAY_CLOSE = (
        "transactionDetails.pricingDetails.percentVsPreviousDayClose"
    )
    PERCENT_VS_ARRIVAL = "transactionDetails.pricingDetails.percentVsArrival"
    PERCENT_OPEN = "transactionDetails.pricingDetails.percentVsOpen"
    CLOSE_PRICE = "transactionDetails.pricingDetails.closePrice"
    MVWAP_TEN_DAYS = "transactionDetails.pricingDetails.mVwap10days"
    PERCENT_MVWAP_TEN_DAYS = "transactionDetails.pricingDetails.percentVsMVwap10days"
    MVAWP_TWENTY_DAYS = "transactionDetails.pricingDetails.mVwap20days"
    PERCENT_MVAP_TWENTY_DAYS = "transactionDetails.pricingDetails.percentVsMVwap20days"
    MID_POINT_PRICE = "transactionDetails.pricingDetails.midPointPrice"
    PERCENT_MID_POINT_PRICE = "transactionDetails.pricingDetails.percentVsMidPointPrice"
    HIGH_PRICE = "transactionDetails.pricingDetails.highPrice"
    PERCENT_HIGH_PRICE = "transactionDetails.pricingDetails.percentVsHighPrice"
    LOW_PRICE = "transactionDetails.pricingDetails.lowPrice"
    PERCENT_LOW_PRICE = "transactionDetails.pricingDetails.percentVsLowPrice"
    OHLC = "transactionDetails.pricingDetails.ohlc"
    PERCENT_OHLC_PRICE = "transactionDetails.pricingDetails.percentVsOhlcPrice"
    INTERVAL_TWAP = "transactionDetails.pricingDetails.intervalTwap"
    PERCENT_INTERVAL_TWAP = "transactionDetails.pricingDetails.percentVsInternalTwap"
    INTERVAL_VWAP = "transactionDetails.pricingDetails.intervalVwap"
    PERCENT_INTERVAL_VWAP = "transactionDetails.pricingDetails.percentVsInternalVwap"
    NEAREST_QUOTE_DETAILS = "priceFormingData.externalPricing.quotes.PRIMARY.pre"
    NEAREST_TRADE_DETAILS = "priceFormingData.externalPricing.trades.PRIMARY.pre"
    PRICE_REF_RIC = "instrumentDetails.instrument.ext.pricingReferences.RIC"


class ESFields:
    INSTRUMENT_ID = "instrumentDetails.instrument.ext.instrumentUniqueIdentifier"
    ID = "&id"
    ES_ID = "__elastic__._id"
    QUERY = "query"
    BOOL = "bool"
    MUST_NOT = "must_not"
    EXISTS = "exists"
    FIELD = "field"
    RANGE = "range"
    FILTER = "filter"
    GTE = "gte"
    LTE = "lte"
    TERMS = "terms"
    TERM = "term"
    PARENT_ORDER_MODEL = "Order"
    SOURCE = "_source"
    ORDER_RECEIVED = "timestamps.orderReceived"


class ElasticOrderFields:
    STACK = "stack"
    TENANT = "tenant"
    MODEL = "model"
    KEY = "key"
    ID = "id"
    PARENT = "parent"
    ORDER_STATUS = "orderStatus"
    EXECUTION_DETAILS = "executionDetails"
    ORDER_RECEIVED = "orderReceived"
    TIMESTAMPS = "timestamps"
    ORDER_SUBMITTED = "orderSubmitted"
    TRADING_DATETIME = "tradingDateTime"
    INSTRUMENT_UID = "instrumentUniqueIdentifier"
    INSTRUMENT_DETAILS = "instrumentDetails"
    INSTRUMENT = "instrument"
    EXT = "ext"
    INSTRUMENT_MODEL = "instrumentModel"
    META_MODEL = "&model"
    META_KEY = "&key"
    META_ID = "&id"
    META_PARENT = "&parent"
    DICT_VALUE = "value"
    DICT_KEY = "key"
    META_INDEX = "_index"
    META_SOURCE = "_source"
    EXECUTION_ORDER_STATUS = "executionDetails.orderStatus"
    KEYWORD_FLAGS = "flags.TCAFlagStatus.keyword"


class FlowArgsKeys:
    ELASTIC = "elastic"
    TENANT = "tenant"
    date_from = "dateFrom"
    date_to = "dateTo"
    TIMESTAMP_ATTRIBUTE = "timestampAttribute"
    INSTRUMENT_UID = "instrumentUniqueIdentifier"
    ALL_ORDERS = "allOrders"
    TCA_FLAG_STATUS = "tcaFlagStatus"


class Resources(BaseResources):
    es_client_key: str


class ElasticOrderConsumer(BaseTask):
    """
    Formats and returns a data frame of orders using flow args as parameters in
    an elastic query
    """

    resources_class = Resources

    def execute(
        self,
        resources: Optional[Resources] = None,
        flow_args: Optional[str] = None,
        **kwargs,
    ) -> pd.DataFrame:
        """
        Elastic "consumer" that queries for given params and returns dataframe.
        :param resources: elastic client
        :param flow_args: parameters for the elastic query
        :return: `Dataframe` from elastic
        """

        # get the query information from flow args
        args = loads(flow_args) if flow_args else dict()

        # The tenant we are querying elastic
        tenant = args.get(FlowArgsKeys.ELASTIC, {}).get(FlowArgsKeys.TENANT)

        if not tenant:
            self.logger.error("No tenant specified, cannot produce orders")
            raise ValueError("No tenant specified in flow args")

        es = self.clients.get(resources.es_client_key)

        results: List[dict] = list()

        # Query elastic and format the results into the list
        alias = Order.get_elastic_index_alias(tenant=tenant)

        for order in self._get_orders(args=args, es=es, index=alias):
            results.append(
                self._format_order(
                    order=order, tenant=tenant, index=order["__elastic__"]["_index"]
                )
            )

        if not results:
            raise signals.SKIP("Flow run is skipped, there are no orders to process")

        consumed_messages: List[dict] = list()

        # Create DF
        for message in results:
            df_record = {
                f"{ElasticOrderFields.DICT_KEY}.{k}": v
                for k, v in message.get(ElasticOrderFields.DICT_KEY, {}).items()
            }
            df_record.update(
                {
                    f"{ElasticOrderFields.DICT_VALUE}.{k}": v
                    for k, v in message.get(ElasticOrderFields.DICT_VALUE, {}).items()
                }
            )
            consumed_messages.append(df_record)

        df = pd.DataFrame(consumed_messages)

        if df.empty:
            raise signals.SKIP("Flow run is skipped, there are no orders to process")

        # turn empty values to NaN
        df = df.replace(r"^\s*$", pd.NA, regex=True)

        df[KafkaMessage.ORDER_RECEIVED_PARENT] = self._get_order_ts_from_parent(
            frame=df,
            es=es,
            alias=alias,
        )
        parent_order_received_mask = df[KafkaMessage.ORDER_RECEIVED_PARENT].notnull()
        df.loc[parent_order_received_mask, KafkaMessage.ORDER_RECEIVED] = df.loc[
            parent_order_received_mask, KafkaMessage.ORDER_RECEIVED_PARENT
        ]

        df = df.drop([KafkaMessage.PARENT, KafkaMessage.ORDER_RECEIVED_PARENT], axis=1)

        return df

    @staticmethod
    def _build_query(args: dict, es_client) -> dict:
        """
        Builds elastic query based on arguments
        :param args: arguments for elastic query
        :return: Dictionary that is elastic query
        """
        date_from = args.get(FlowArgsKeys.ELASTIC, {}).get(FlowArgsKeys.date_from)
        date_to = args.get(FlowArgsKeys.ELASTIC, {}).get(FlowArgsKeys.date_to)
        ts_attribute = args.get(FlowArgsKeys.ELASTIC, {}).get(
            FlowArgsKeys.TIMESTAMP_ATTRIBUTE
        )
        instrument_ids = args.get(FlowArgsKeys.ELASTIC, {}).get(
            FlowArgsKeys.INSTRUMENT_UID
        )
        all_orders = args.get(FlowArgsKeys.ELASTIC, {}).get(FlowArgsKeys.ALL_ORDERS)
        status_flags = args.get(FlowArgsKeys.ELASTIC, {}).get(
            FlowArgsKeys.TCA_FLAG_STATUS
        )

        if not ts_attribute:
            raise ValueError(
                "`timestampAttribute` is required field when using Elastic Consumer"
            )

        if instrument_ids and not isinstance(instrument_ids, list):
            instrument_ids = [instrument_ids]

        if status_flags and not isinstance(status_flags, list):
            status_flags = [status_flags]

        if date_to is None:
            date_to = pendulum.now()

        if date_from is None:
            date_from = pendulum.today().subtract(days=1)

        body = {
            ESFields.QUERY: {
                ESFields.BOOL: {
                    ESFields.MUST_NOT: [
                        {ESFields.EXISTS: {ESFields.FIELD: es_client.meta.expiry}}
                    ],
                    ESFields.FILTER: [
                        {
                            ESFields.RANGE: {
                                ts_attribute: {
                                    ESFields.GTE: date_from,
                                    ESFields.LTE: date_to,
                                }
                            }
                        },
                        {
                            ESFields.TERMS: {
                                ElasticOrderFields.EXECUTION_ORDER_STATUS: [
                                    OrderStatus.FILL,
                                    OrderStatus.PARF,
                                ]
                            }
                        },
                        {
                            ESFields.TERM: {
                                es_client.meta.model: ESFields.PARENT_ORDER_MODEL
                            }
                        },
                    ],
                }
            }
        }

        if instrument_ids:
            body[ESFields.QUERY][ESFields.BOOL][ESFields.FILTER].append(
                {ESFields.TERMS: {ESFields.INSTRUMENT_ID: instrument_ids}}
            )

        if status_flags:
            body[ESFields.QUERY][ESFields.BOOL][ESFields.FILTER].append(
                {ESFields.TERMS: {ElasticOrderFields.KEYWORD_FLAGS: status_flags}}
            )

        if not (all_orders or status_flags):
            query_ignore = [
                {ESFields.EXISTS: {ESFields.FIELD: PriceColumns.MARKET_PRICE}},
                {
                    ESFields.TERMS: {
                        ElasticOrderFields.KEYWORD_FLAGS: [
                            # TCAFlagStatus.CURRENCY_MISMATCH,
                            TCAFlagStatus.INVALID_CURRENCY,
                            TCAFlagStatus.EXECUTION_BEFORE_MARKET,
                        ]
                    }
                },
            ]
            body[ESFields.QUERY][ESFields.BOOL][ESFields.MUST_NOT].extend(query_ignore)

        return body

    def _format_order(self, order: dict, tenant: str, index: str) -> dict:
        try:

            formatted_order = {
                ElasticOrderFields.DICT_VALUE: self._es_doc(order),
                ElasticOrderFields.DICT_KEY: {
                    ElasticOrderFields.STACK: STACK,
                    ElasticOrderFields.TENANT: tenant,
                    ElasticOrderFields.META_INDEX: index,
                    ElasticOrderFields.MODEL: self._ein(
                        order.get(ElasticOrderFields.META_MODEL)
                    ),
                    ElasticOrderFields.KEY: self._ein(
                        order.get(ElasticOrderFields.META_KEY)
                    ),
                    ElasticOrderFields.ID: self._ein(
                        order.get(ElasticOrderFields.META_ID)
                    ),
                    ElasticOrderFields.PARENT: self._ein(
                        order.get(ElasticOrderFields.META_PARENT)
                    ),
                    # Information to route the message
                    ElasticOrderFields.ORDER_STATUS: self._ein(
                        self._nested_read(
                            order,
                            ElasticOrderFields.EXECUTION_DETAILS,
                            ElasticOrderFields.ORDER_STATUS,
                        )
                    ),
                    ElasticOrderFields.ORDER_RECEIVED: self._ein(
                        self._nested_read(
                            order,
                            ElasticOrderFields.TIMESTAMPS,
                            ElasticOrderFields.ORDER_RECEIVED,
                        )
                    ),
                    ElasticOrderFields.ORDER_SUBMITTED: self._ein(
                        self._nested_read(
                            order,
                            ElasticOrderFields.TIMESTAMPS,
                            ElasticOrderFields.ORDER_SUBMITTED,
                        )
                    ),
                    ElasticOrderFields.TRADING_DATETIME: self._ein(
                        self._nested_read(
                            order,
                            ElasticOrderFields.TIMESTAMPS,
                            ElasticOrderFields.TRADING_DATETIME,
                        )
                    ),
                    ElasticOrderFields.INSTRUMENT_UID: self._ein(
                        self._nested_read(
                            order,
                            ElasticOrderFields.INSTRUMENT_DETAILS,
                            ElasticOrderFields.INSTRUMENT,
                            ElasticOrderFields.EXT,
                            ElasticOrderFields.INSTRUMENT_UID,
                        )
                    ),
                    ElasticOrderFields.INSTRUMENT_MODEL: self._ein(
                        self._nested_read(
                            order,
                            ElasticOrderFields.INSTRUMENT_DETAILS,
                            ElasticOrderFields.INSTRUMENT,
                            ElasticOrderFields.META_MODEL,
                        )
                    ),
                },
            }

        except Exception as e:
            self.logger.info("Error formatting order")
            self.logger.error(e)
            return dict()

        return formatted_order

    def _get_orders(
        self,
        es,
        args: dict,
        index: str,
    ) -> list:
        body = self._build_query(args=args, es_client=es)

        response = es.scroll(
            query=body, index=index, as_dataframe=False, include_elastic_meta=True
        )

        total_hits = len(response)

        self.logger.info(f"Total Hits Found: {total_hits}")

        if not total_hits:
            self.logger.info("No hits found, returning empty dict")
            return dict()

        for order in response:
            yield order

    @staticmethod
    def _es_doc(obj: dict) -> dict:
        return {"doc": dumps(obj)}

    @staticmethod
    def _ein(inp: str) -> str:
        return "" if inp is None else inp

    def _nested_read(self, dct: dict, key: str, *keys: Optional[str]) -> Optional[str]:
        if dct is None:
            return None
        array = key.endswith("[]")
        if array:
            key = key.replace("[]", "")
        value = dct.get(key, list() if array else None)
        if keys:
            if array:
                return self._nested_read(value[0] if len(value) > 0 else None, *keys)
            else:
                return self._nested_read(value, *keys)
        return value[0] if (array and len(value) > 0) else value

    def _get_order_ts_from_parent(
        self, frame: pd.DataFrame, es, alias: str
    ) -> Optional[pd.Series]:
        """

        :param frame:
        :param es:
        :param alias:
        :return:
        """
        parent_ids = frame[KafkaMessage.PARENT].dropna().unique().tolist()
        if not parent_ids:
            return pd.Series(index=frame.index)
        parent_df = self._get_parent_frame(ids=parent_ids, es=es, alias=alias)
        if ESFields.ORDER_RECEIVED not in parent_df:
            return pd.Series(index=frame.index)
        result = frame.join(
            parent_df.set_index(ESFields.ES_ID), on=[KafkaMessage.PARENT], how="left"
        )[ESFields.ORDER_RECEIVED]
        return result

    def _get_parent_frame(self, ids: List[str], es, alias: str) -> pd.DataFrame:
        """

        :param ids:
        :param es:
        :return:
        """
        body = self._parent_query(ids=ids, es_client=es)
        result = es.scroll(query=body, index=alias, include_elastic_meta=True)
        return result

    @staticmethod
    def _parent_query(ids: List[str], es_client) -> dict:
        """

        :param ids:
        :return:
        """
        return {
            ESFields.SOURCE: [ESFields.ORDER_RECEIVED],
            ESFields.QUERY: {
                ESFields.BOOL: {
                    ESFields.MUST_NOT: [
                        {ESFields.EXISTS: {ESFields.FIELD: es_client.meta.expiry}}
                    ],
                    ESFields.FILTER: [
                        {
                            ESFields.TERMS: {
                                ElasticOrderFields.EXECUTION_ORDER_STATUS: [
                                    OrderStatus.NEWO,
                                ]
                            }
                        },
                        {
                            ESFields.TERM: {
                                es_client.meta.model: ESFields.PARENT_ORDER_MODEL
                            }
                        },
                        {ESFields.TERMS: {ESFields.ID: ids}},
                    ],
                }
            },
        }
