from typing import Optional

import pandas as pd
from se_core_tasks.core.core_dataclasses import CloudProviderEnum
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_trades_tasks.order_and_tr.fix.fix_batch_csv_downloader import (
    Params as GenericParams,
)
from se_trades_tasks.order_and_tr.fix.fix_batch_csv_downloader import (
    run_fix_batch_csv_downloader,
)
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


class Params(BaseParams, GenericParams):
    pass


class FixBatchCsvDownloader(BaseTask):

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        extractor_result: ExtractPathResult = None,
        source_frame: pd.DataFrame = None,
        **kwargs,
    ) -> pd.DataFrame:

        result = run_fix_batch_csv_downloader(
            realm=Settings.realm,
            source_frame=source_frame,
            extractor_result=extractor_result,
            params=params,
            is_async=True,  # should always be True to improve performance (there are no async issues in Swarm as it uses boto3 instead of fsspec)  # noqa: E501
            cloud_provider=CloudProviderEnum.AWS,
        )

        return result
