from pathlib import Path
from typing import List
from typing import Optional
from typing import Union

import pandas as pd
from prefect import context
from pydantic import Field
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.core.core_dataclasses import FileSplitterResult
from swarm.schema.static import SwarmColumns
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask


class Params(BaseParams):
    delimiter: str = ","
    target_delimiter: Optional[str] = ","
    compression: str = None
    skip_rows: Optional[int]
    drop_tail: Optional[int]
    chunksize: int
    drop_na: Optional[str] = Field(
        None, description="Skip rows where the referenced column is empty"
    )
    remove_char: Optional[str] = Field(
        None,
        description="Removes the character(s) if present either in"
        "values or columns names of the data source.",
    )
    header: Optional[Union[int, List[int]]] = None
    encoding: Optional[str] = Field(
        None, description="Encoding to be passed into pandas.read_html"
    )
    column_to_string: Optional[List[str]] = Field(
        None,
        description="Named columns that need to be procesed as a string during "
        "read. Exists because read_html does not allow you to cast "
        "all columns to strings without specifying",
    )
    audit_input_rows: bool = Field(
        default=False,
        description="If True, audits the no of rows which were present in the input file",
    )


class HtmlFileSplitter(BaseTask):
    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        resources: Optional[BaseResources] = None,
        extractor_result: ExtractPathResult = None,
    ) -> List[FileSplitterResult]:

        source_dir = Path(context.swarm.sources_dir)

        if params.column_to_string:
            converters = {k: str for k in params.column_to_string}

        pd_config = dict(
            io=extractor_result.path.open(encoding=params.encoding),
            skiprows=params.skip_rows,
            header=params.header,
            converters=converters,
        )

        df_obj = pd.read_html(**pd_config)

        batches = []

        total_count = 0
        for idx, df_chunk in enumerate(df_obj):
            if params.drop_na:
                df_chunk = df_chunk.dropna(subset=[params.drop_na])

            if params.drop_tail:
                df_chunk.drop(df_chunk.tail(params.drop_tail).index, inplace=True)

            if df_chunk.empty:
                self.auditor.add("Source data empty")
                continue

            if params.remove_char:
                df_chunk = df_chunk.applymap(
                    lambda x: x.replace(params.remove_char, "")
                )
                df_chunk.columns = df_chunk.columns.str.replace(params.remove_char, "")

            df_chunk.index.name = SwarmColumns.SWARM_RAW_INDEX
            df_chunk = df_chunk.reset_index()
            total_count += df_chunk.shape[0]
            batch_csv = self._produce_csv_batch_file(
                batch_dir=source_dir,
                df=df_chunk,
                filename=extractor_result.path.name,
                batch_num=idx,
                params=params,
            )
            batch_extract_result = FileSplitterResult(path=batch_csv, batch_index=idx)
            batches.append(batch_extract_result)

        if params.audit_input_rows:
            # Auditing the number of records which were present in the input file
            audit_ctx = {"input_total_count": total_count}
            self.auditor.add(message="", ctx=audit_ctx)

        return batches

    def _produce_csv_batch_file(
        self,
        batch_dir: Path,
        df: pd.DataFrame,
        filename: str,
        batch_num: int,
        params: Params,
    ) -> Path:
        """Produces a batch CSV file from a dataframe object"""

        batch_file_path = batch_dir.joinpath(f"{filename}_batch_{batch_num}.csv")

        self.logger.info(f"Generating batch file: {batch_file_path}")

        df.to_csv(
            batch_file_path, index=False, encoding="utf-8", sep=params.target_delimiter
        )

        return batch_file_path
