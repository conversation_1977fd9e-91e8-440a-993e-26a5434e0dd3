from typing import List
from typing import Optional

from se_core_tasks.core.core_dataclasses import FileSplitterResult
from se_core_tasks.io.read.merge_and_chunk_csv_files import Params as GenericParams
from se_core_tasks.io.read.merge_and_chunk_csv_files import (
    run_merge_and_chunk_csv_files,
)
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


class Params(BaseParams, GenericParams):
    pass


class MergeAndChunkCsvFiles(BaseTask):
    """
    Merges csv file chunks created by CSVFileSplitter and outputs lesser number of csv files.
    it merges the as many csv files as provided in max_chunk_size param together into single csv.
    """

    params_class = Params

    def execute(
        self,
        params: Params = None,
        file_splitter_result_list: List[FileSplitterResult] = None,
        file_url: Optional[str] = None,
        **kwargs,
    ) -> List[FileSplitterResult]:

        result = run_merge_and_chunk_csv_files(
            file_splitter_result_list=file_splitter_result_list,
            params=params,
            file_url=file_url,
            auditor=self.auditor,
            logger=self.logger,
        )
        return result
