import logging
from pathlib import Path
from tempfile import mkdtemp
from typing import List
from typing import Op<PERSON>
from typing import Union

from prefect import context
from prefect.engine.signals import SKIP
from pydantic import Field
from pydantic import root_validator
from pysftp import Connection as SftpConnection
from pysftp import ConnectionException
from pysftp import CredentialException
from swarm.client.sftp import SftpClient
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask


class Params(BaseParams):
    remote_dirs: List[str] = Field(
        None, description="List of remote directories to inspect files to download."
    )
    skip_on_no_files_downloaded: bool = Field(
        False, description="If true, raise a signal.SKIP if no files downloaded."
    )

    @root_validator(pre=True)
    def sftp_file_path_legacy(cls, values: dict):
        """
        Move value in legacy param `sftp_file_path` to `remote_dirs`.
        """
        sftp_file_path = values.get("sftp_file_path")
        remote_dirs = values.get("remote_dirs")

        if sftp_file_path and remote_dirs:
            raise ValueError(
                "`sftp_file_path` and `remote_dirs` are mutually exclusive."
            )

        if sftp_file_path:
            values["remote_dirs"] = [sftp_file_path]
            values.pop("sftp_file_path")

        return values


class Resources(BaseResources):
    sftp_client_key: str


class SftpDownloader(BaseTask):
    params_class = Params
    resources_class = Resources

    def execute(
        self,
        params: Optional[Params] = None,
        resources: Optional[Resources] = None,
        file_list: Optional[Union[str, List[str]]] = None,
        **kwargs,
    ) -> List[Path]:

        if file_list:
            if isinstance(file_list, str):
                file_list = [file_list]

        sftp_client: SftpClient = self.clients.get(resources.sftp_client_key)
        # TODO this field should come from the registry in ES but there it is camelCase
        # and our ResourceConfig Pydantic model expects it to be snake_case
        # The proper solution is to add the camelCase alias to the model in se_elasticsearch repo
        # but we need this quick and dirty fix to accelerate Emir Prod issues resolution
        sftp_client._config.private_key_param = "/all/ssh/unavista/private"

        downloaded_files = self.process(
            sftp_client=sftp_client,
            params=params,
            file_list=file_list,
            logger=self.logger,
        )

        return downloaded_files

    @classmethod
    def process(
        cls,
        sftp_client: SftpClient,
        params: Params,
        file_list: Optional[List[str]] = None,
        logger: logging.Logger = context.get("logger"),
    ) -> List[Path]:
        result: List[Path] = []

        local_path = Path(mkdtemp())

        try:
            with sftp_client.connect() as sftp:
                original_remote_dir = sftp.pwd

                for remote_dir in params.remote_dirs:
                    downloaded_files = cls._download_files_from_remote_directory(
                        sftp=sftp,
                        remote_dir=remote_dir,
                        file_list=file_list,
                        local_path=local_path,
                        logger=logger,
                    )

                    if downloaded_files:
                        result.extend(downloaded_files)

                    sftp.chdir(original_remote_dir)

        except ConnectionException as e:
            logger.exception("SftpDownloader - connection error")
            raise e

        except CredentialException as e:
            logger.exception("SftpDownloader - credentials error")
            raise e

        except PermissionError as e:
            logger.exception("SftpDownloader - permission error")
            raise e

        if params.skip_on_no_files_downloaded and not result:
            raise SKIP("No files downloaded.")

        return result

    @staticmethod
    def _download_files_from_remote_directory(
        remote_dir: str,
        sftp: SftpConnection,
        local_path: Path,
        logger: logging.Logger,
        file_list: Optional[List[str]] = None,
    ) -> List[Path]:

        result: List[Path] = []

        logger.info(f"Change sftp directory to: {remote_dir}")

        sftp.chdir(remote_dir)

        remote_files = sftp.listdir()

        if file_list is not None:
            # if there's a specific list of file, only download those
            target_files = []
            for f in file_list:
                if f in remote_files:
                    target_files.append(f)
                else:
                    logger.info(f"SFTP - {remote_dir}: {f} not found.")
            if not target_files:
                return result

        else:
            # if files not specified, download all files
            target_files = remote_files

        for remote_file in target_files:
            local_file_path = local_path.joinpath(remote_file)
            try:
                if sftp.isdir(remotepath=remote_file):
                    logger.info(f"Skipping directory: {remote_file}")
                    continue
                logger.info(f"Get remote file {remote_file}")
                sftp.get(remote_file, localpath=local_file_path.as_posix())
                result.append(local_file_path)
            except Exception:
                logger.exception(f"SftpDownloader - failed to download {remote_file}")

        return result
