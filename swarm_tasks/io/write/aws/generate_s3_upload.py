from typing import List

from prefect import context
from prefect.engine.signals import FAIL
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.core.core_dataclasses import S3File
from se_core_tasks.io.write.aws.generate_s3_upload import (
    FailIfListIsNotExtractPathResult,
)
from se_core_tasks.io.write.aws.generate_s3_upload import FailIfReceivedEmptyList
from se_core_tasks.io.write.aws.generate_s3_upload import Params as GenericParams
from se_core_tasks.io.write.aws.generate_s3_upload import run_generate_s3_upload
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


class Params(BaseParams, GenericParams):
    pass


class GenerateS3Upload(BaseTask):

    params_class = Params

    def execute(
        self,
        params: Params = None,
        extract_results: List[ExtractPathResult] = None,
        **kwargs,
    ) -> List[S3File]:

        return self.process(
            params=params,
            extract_results=extract_results,
            auditor=self.auditor,
            logger=self.logger,
        )

    @classmethod
    def process(
        cls,
        extract_results: List[ExtractPathResult] = None,
        params: GenericParams = None,
        auditor=None,
        logger=context.get("logger"),
    ) -> List[S3File]:

        try:
            return run_generate_s3_upload(
                extract_results=extract_results,
                params=params,
                auditor=auditor,
                logger=logger,
            )
        except (FailIfReceivedEmptyList, FailIfListIsNotExtractPathResult) as e:
            raise FAIL(f"Flow has failed due to: {e}")
