from typing import List
from typing import Union

from prefect import context
from se_core_tasks.core.core_dataclasses import S3File
from se_core_tasks.core.core_dataclasses import S3TargetResult
from se_core_tasks.io.write.aws.s3_upload_file import Params as GenericParams
from se_core_tasks.io.write.aws.s3_upload_file import run_s3_upload_file
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


class Params(BaseParams, GenericParams):
    pass


class S3UploadFile(BaseTask):

    params_class = Params

    def execute(
        self,
        upload_target: Union[S3File, List[S3File]] = None,
        params: Params = None,
        **kwargs,
    ) -> Union[S3TargetResult, None]:

        return self.process(
            upload_target=upload_target,
            params=params,
            auditor=self.auditor,
            logger=self.logger,
        )

    @classmethod
    def process(
        cls,
        upload_target: Union[S3File, List[S3File]] = None,
        params: GenericParams = None,
        auditor=None,
        logger=context.get("logger"),
    ) -> Union[S3TargetResult, None]:
        return run_s3_upload_file(
            upload_target=upload_target,
            params=params,
            auditor=auditor,
            logger=logger,
        )
