import asyncio
import concurrent.futures
import json
import os
import time
import uuid
from collections import Counter
from datetime import datetime
from enum import Enum
from functools import partial
from pathlib import Path
from typing import Any
from typing import List
from typing import Optional
from typing import Type
from typing import Union

import numpy as np
import pandas as pd
from prefect import context
from prefect.engine.signals import FAIL
from pydantic import Field
from pydantic.error_wrappers import ValidationError as PydanticValidationError
from schema_sdk.steeleye_model.base.schema import SchemaExtra
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_elastic_schema.elastic_schema.core.steeleye_schema_model import (
    SteelEyeSchemaBaseModelES8,
)
from se_elastic_schema.models import find_model
from se_elastic_schema.models import Order
from se_elastic_schema.models import QuarantinedOrder
from se_elastic_schema.models import QuarantinedRTS22Transaction
from se_elastic_schema.models import RTS22Transaction
from se_elastic_schema.models import SinkRecordAudit
from se_schema.all_models import SteeleyeSchemaModel
from se_schema.utilities.inspector import find_model as srp_find_model
from se_schema_meta import ID
from swarm.conf import Settings
from swarm.schema.static import SwarmColumns
from swarm.schema.utils import sanitize
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.base import BaseTask
from swarm.task.io.read.result import FrameProducerResult
from swarm.task.io.write.elastic.result import ElasticBulkWriterResult
from swarm.task.io.write.elastic.static import BulkWriterColumns
from swarm.task.io.write.elastic.static import RECORD_INDEX_DELIMITER
from swarm.task.io.write.elastic.static import WriteStatus
from swarm.task.transform.result import TransformResult
from swarm.utilities.indict import Indict
from typing_extensions import Literal

from swarm_tasks.io.write.elasticsearch.static import ELASTIC_ROUTING_COLUMN
from swarm_tasks.utilities.audit import audit_pydantic_validation_errors
from swarm_tasks.utilities.es.query_utils import es_api_retriable_call

MAX_THREADS = os.getenv("MAX_THREADS", 2)


class ActionType(str, Enum):
    CREATE = "create"
    DELETE = "delete"
    INDEX = "index"
    UPDATE = "update"


# Models and respected quarantined models currently supported
MODELS_QUARANTINE_MAP = {
    Order.get_reference().name: QuarantinedOrder.get_reference().name,
    RTS22Transaction.get_reference()
    .name: QuarantinedRTS22Transaction.get_reference()
    .name,
}


class Params(BaseParams):
    action_type: Literal[
        ActionType.CREATE, ActionType.UPDATE, ActionType.INDEX, ActionType.DELETE
    ]
    quarantined: bool = False
    allow_empty: bool = Field(
        default=False,
        description="This value indicates whether empty fields are required to be removed or not. ",
    )
    dump_raw_ndjson: bool = Field(
        default=False,
        description="This value indicates whether we need to dump the raw ndjson values "
        "without any meta-data values appended to it."
        "Note that the ndjson generated from this will not be compatible with ElasticBulkWriter.",
    )


class Resources(BaseResources):
    es_client_key: str


class ElasticBulkTransformer(BaseTask):
    """
    This task converts a dataframe with records into a ndjson file
    ready to be ingested into ES
    """

    params_class = Params
    resources_class = Resources

    def execute(
        self,
        params: Optional[Params] = None,
        resources: Optional[Resources] = None,
        transform_result: TransformResult = None,
        producer_result: Optional[FrameProducerResult] = None,
        batch_index: Optional[int] = None,
        bulk_writer_result: ElasticBulkWriterResult = None,
    ) -> ExtractPathResult:

        # can be an ES5 SRP client or ES8 tenant-data client
        target_cluster_is_srp = resources.es_client_key == "reference-data"
        es = self.clients.get(resources.es_client_key)
        data = transform_result.target

        if data.empty or es.meta.id not in data.columns:
            return ExtractPathResult(path=None)

        if SwarmColumns.SWARM_RAW_INDEX not in data.columns:
            # Note: data.index should be SWARM_RAW_INDEX
            data[SwarmColumns.SWARM_RAW_INDEX] = data.index
            self.logger.warning(
                "Swarm Raw Index column not found in dataframe, added swarm raw index"
            )

        if es.meta.timestamp not in data.columns and params.action_type in [
            ActionType.UPDATE
        ]:
            data[es.meta.timestamp] = time.time_ns() // int(1e6)

        if params.quarantined:
            data = self._process_to_quarantine(
                data=data, bulk_writer_result=bulk_writer_result, es=es
            )

        null_meta_id_mask = data[es.meta.id].isnull()

        if null_meta_id_mask.any():
            data = data.loc[~null_meta_id_mask]

        result = asyncio.run(
            self.async_driver(
                data=data,
                es=es,
                params=params,
                target_cluster_is_srp=target_cluster_is_srp,
            )
        )

        result = "\n".join(result)

        output_path = self.write_content_locally(
            content=result,
            params=params,
            producer_result=producer_result,
            batch_index=batch_index,
        )

        return ExtractPathResult(path=output_path)

    async def async_driver(
        self, data: pd.DataFrame, es, params: Params, target_cluster_is_srp: bool
    ) -> Any:
        """
        Driver function to call compute intensive function in separate processes

        :param data: Input dataframe
        :param params: Params
        :param es: ES client
        :param target_cluster_is_srp: True if the output NDJSON will be written to SRP, False otherwise
        :return: Converted Ndjson string of the given inputs
        """
        try:
            max_workers = int(MAX_THREADS) if MAX_THREADS != -1 else None
            self.logger.info(f"Creating thread pool with {MAX_THREADS} threads")
            executor_pool = concurrent.futures.ThreadPoolExecutor(
                max_workers=max_workers
            )
            try:
                loop = asyncio.get_running_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            content_list = await asyncio.gather(
                *[
                    loop.run_in_executor(
                        executor_pool,
                        partial(
                            self._format_bulk,
                            **dict(
                                target_frame=data_chunk,
                                es_client=es,
                                params=params,
                                target_cluster_is_srp=target_cluster_is_srp,
                            ),
                        ),
                    )
                    for data_chunk in np.array_split(data, data.shape[0] // 1000 + 1)
                ]
            )
            executor_pool.shutdown()

            return content_list

        except Exception:
            self.logger.error(
                "Could not convert to Ndjson form the given df input", exc_info=True
            )
            return ""

    def write_content_locally(
        self,
        content: str,
        params: Params,
        producer_result: Optional[FrameProducerResult],
        batch_index: Optional[int],
    ) -> Path:
        """
        Stores the content in a local ndjson. Generates the filename
        from uuid4 to avoid clashes/overrides
        on ndjsons already created.

        :param content: str with ndjson
        :param params: Params
        :param producer_result: FrameProducerResult to retrieve the batch index
        :param batch_index: Batch index resolved from FrameProducerResult in task
        ExtractBatchFromFrameProducerResult
        :return:
        """
        output_dir = "targets_dir" if not params.quarantined else "quarantines_dir"

        if not batch_index:
            batch_index = (
                producer_result.batch_index
                if producer_result and producer_result.batch_index is not None
                else 0
            )

        # have unique batch folder path as flows may index
        # into two different models in same batch
        # example "comms-email-eml"
        batch_folder = f"batch-{batch_index}-{uuid.uuid4()}"
        output_path: Path = context.swarm.get(output_dir).joinpath(batch_folder)
        output_path.mkdir(parents=True, exist_ok=True)

        filename = f"target.{batch_index}.ndjson"

        self.logger.info(f"Storing locally ndjson content in {output_path.as_posix()}")

        output_path.joinpath(filename).open("w+").write(content)

        return output_path

    def _format_bulk(
        self,
        target_frame: pd.DataFrame,
        es_client,
        params: Params,
        target_cluster_is_srp: bool,
    ) -> str:

        meta = es_client.meta

        if SwarmColumns.SWARM_RAW_INDEX not in target_frame.columns:
            target_frame.index.name = SwarmColumns.SWARM_RAW_INDEX
            target_frame = target_frame.reset_index()
            self.logger.warning("Swarm Raw Index column not found in dataframe, added!")

        models = target_frame[meta.model].dropna()

        if models.empty:
            raise ValueError("No model present")

        models = models.unique().tolist()

        action_df = pd.DataFrame(index=target_frame.index)

        if target_cluster_is_srp:
            # If the model is not found in se-elastic-schema, the assumption
            # is that it is an SRP model available in se-schema
            models_map = {model: srp_find_model(model) for model in models}
        else:
            models_map = {model: find_model(model) for model in models}

        for model_name, model in models_map.items():

            model_mask = target_frame[meta.model] == model_name

            # _index
            action_df.loc[
                model_mask, f"{params.action_type}._index"
            ] = self._get_elastic_index(
                model=model,
                masked_target_frame=target_frame.loc[model_mask],
                es_client=es_client,
                target_cluster_is_srp=target_cluster_is_srp,
            )

            if target_cluster_is_srp:
                # _type should only be populated for ES5/6 clusters
                action_df.loc[model_mask, f"{params.action_type}._type"] = model_name

        # _id
        action_df.loc[:, f"{params.action_type}._id"] = target_frame[meta.id]

        # routing
        if ELASTIC_ROUTING_COLUMN in target_frame.columns:
            action_df.loc[:, f"{params.action_type}._routing"] = target_frame.loc[
                :, ELASTIC_ROUTING_COLUMN
            ]
            target_frame = target_frame.drop(columns=[ELASTIC_ROUTING_COLUMN])

        # Generate records identifiers
        try:
            records_identifiers = target_frame.loc[
                :, [SwarmColumns.SWARM_RAW_INDEX, meta.model, meta.hash]
            ].apply(
                lambda x: RECORD_INDEX_DELIMITER.join(
                    map(str, x.fillna("MISSING").tolist())
                ),
                axis=1,
            )
        except Exception as e:
            self.logger.info("Locals value - ", locals())
            self.logger.info(f"Dataframe columns: {target_frame.columns.tolist()}")
            self.logger.exception(
                "Error occured while generating record Idenntifiers", e
            )
            raise e

        # Action json
        action_json = self._action_data_to_json(
            df=action_df,
            params=params,
            records_identifiers=records_identifiers,
        )

        if params.action_type == ActionType.DELETE:
            # For delete, we don't need doc_json. We just need the action_json to be written to the file

            bulk_formatted = "\n".join(action_json)
            return bulk_formatted

        # Doc json
        doc_json = self._records_data_to_json(
            df=target_frame,
            params=params,
            records_identifiers=records_identifiers,
            target_cluster_is_srp=target_cluster_is_srp,
        )

        # handle invalid records which were removed
        invalid_records_mask = doc_json.isnull()
        if invalid_records_mask.any():
            self.logger.warning(
                f"{invalid_records_mask.sum()} invalid records were dropped"
            )
            action_json = action_json.loc[~invalid_records_mask]
            doc_json = doc_json.loc[~invalid_records_mask]

        bulk_formatted = "\n".join(
            pd.Series(
                (
                    pd.DataFrame(
                        {"action_json": action_json, "doc_json": doc_json}
                    ).astype("string")
                    + "\n"
                )
                .fillna("")
                .values.sum(axis=1)
            )
            .str.strip("\n")
            .replace("^$", pd.NA, regex=True)
        )

        return bulk_formatted

    def _action_data_to_json(
        self,
        df: pd.DataFrame,
        params: Params,
        records_identifiers: pd.Series,
    ) -> pd.Series:
        """
        Converts the data with the action headers for ES ingestion into a pd.Series of jsons.

        :return:
        """
        df = self._unflatten_data(
            df=df, records_identifiers=records_identifiers, params=params
        )

        if params.dump_raw_ndjson:
            df = df.drop(columns="identifiers", errors="ignore")

        df["data"] = df["data"].apply(lambda x: json.dumps(x, separators=(",", ":")))

        result = self._format_data_to_json(df=df)

        return result

    def _records_data_to_json(
        self,
        df: pd.DataFrame,
        params: Params,
        records_identifiers: pd.Series,
        target_cluster_is_srp: bool,
    ) -> pd.Series:
        """
        Converts the records to be ingested in ES into a pd.Series of jsons.
        :return:
        """
        # Remove Swarm raw index column
        df = df.drop(columns=[SwarmColumns.SWARM_RAW_INDEX])

        df = self._unflatten_data(
            df=df, records_identifiers=records_identifiers, params=params
        )

        find_model_func = srp_find_model if target_cluster_is_srp else find_model
        model_mask = {
            model: find_model_func(model)
            for model in df["data"].str.get("&model").unique()
        }

        if params.action_type not in [ActionType.UPDATE, ActionType.DELETE]:
            df["data"] = df.loc[:, ["data"]].apply(
                lambda x: self._parse_records_by_model(
                    record=x,
                    model_mask=model_mask,
                    index=x.name,
                ),
                axis=1,
            )

        df.loc[:, "identifiers"] = records_identifiers.loc[df["data"].notnull()]
        if params.dump_raw_ndjson:
            df = df.drop(columns="identifiers", errors="ignore")

        # Record Updates have to be in the format {doc: {record}}
        if params.action_type == ActionType.UPDATE:
            # TODO: The record below in the lambda will have to be
            #      `lambda x: json.dumps(dict(json.loads(x)))`
            #      when schema validations for updates are fixed
            #      As x will be dumped into a json by the validator
            df.loc[df["data"].notnull(), "data"] = df.loc[
                df["data"].notnull(), "data"
            ].apply(
                lambda x: json.dumps(dict(doc=x), separators=(",", ":"))
                if pd.notnull(x)
                else x
            )

        result = self._format_data_to_json(df=df)

        return result

    def _parse_records_by_model(self, record: pd.Series, model_mask: dict, index: int):
        try:
            if not isinstance(record["data"], dict) and pd.isna(pd.NA):
                return pd.NA
            return model_mask.get(record["data"].get("&model"))(**record["data"]).json(
                by_alias=True,
                exclude_none=True,
            )
        except PydanticValidationError as error:
            audit_pydantic_validation_errors(
                error=error,
                raw_index=index,
                record=record["data"],
                auditor=self.auditor,
                logger=self.logger,
            )
            return pd.NA

    @staticmethod
    def _unflatten_data(
        df: pd.DataFrame, records_identifiers: pd.Series, params: Params
    ) -> pd.DataFrame:
        """
        Unflatten data in df into dictionaries.

        :param df:
        :param records_identifiers:
        :param params: params passed to the task
        :return: DataFrame with identifiers and data as dicts
        """
        duplicate_columns = {
            k: v for k, v in Counter(df.columns.tolist()).items() if v > 1
        }

        if duplicate_columns:
            raise ValueError(
                f"Duplicated columns found in the dataframe to convert to json."
                f" Duplicates: {duplicate_columns}"
            )

        if not params.allow_empty:
            return pd.DataFrame(
                {
                    "identifiers": records_identifiers,
                    "data": Indict(
                        obj=df,
                        from_dataframe_params={
                            "date_format": "iso",
                            "orient": "records",
                        },
                    )
                    .remove_empty()
                    .unflatten()
                    .to_dict()
                    .get("data"),
                }
            ).dropna()

        return pd.DataFrame(
            {
                "identifiers": records_identifiers,
                "data": Indict(
                    obj=df,
                    from_dataframe_params={"date_format": "iso", "orient": "records"},
                )
                .unflatten()
                .to_dict()
                .get("data"),
            }
        ).dropna()

    @staticmethod
    def _format_data_to_json(df: pd.DataFrame) -> pd.Series:
        """
        Concatenates into a string df data with delimiter as RECORD_INDEX_DELIMITER

        :param df: DataFrame
        :return: Series with strings of data concatenated from df.
        """
        return (
            pd.Series(
                (df.astype("string").fillna("").values + RECORD_INDEX_DELIMITER).sum(
                    axis=1
                )
            )
            .str.strip(RECORD_INDEX_DELIMITER)
            .replace("^$", pd.NA, regex=True)
        )

    def _get_elastic_index(
        self,
        model: Union[Type[SteeleyeSchemaModel], Type[SteelEyeSchemaBaseModelES8]],
        masked_target_frame: pd.DataFrame,
        es_client,
        target_cluster_is_srp: bool,
    ) -> pd.Series:
        """
        Returns a Series with the indexes per row to be mapped to `_index`.
        Creates alias for inserted rows.

        If we intend to create a record in SRP, by passing a SteeleyeSchemaModel,
        the target alias is static. However, for ES8 clusters, due to ILM being enabled,
        the target aliases are not static. Thus, first we set the generic default alias as our target alias.
        Then, we query each of the input &ids against ALL the indices to which the alias is pointing to,
        for a single tenant, single model.

        If the record is not found in ES, it means that the record is new, and we can use the generic alias,
        which will result in creating a new document in the latest index.
        If the record is found in ES, we can use the exact index name in which the record is present,
        which will result in updating/deleting/generating a version conflict the document in that index.

        :param model: can be a subclass of SteeleyeSchemaModel, thus a se-schema SRP model,
            or it can be a subclass of SteelEyeSchemaBaseModelES8, thus a se-elastic-schema ES8 model
        :param masked_target_frame: Input DataFrame that contains the &id of all records
            we intend to write/update/delete in ES
        :param es_client: ElasticSearch Client
        :param target_cluster_is_srp: True if the output NDJSON will be written into SRP, False otherwise

        :return: A Pandas Series that lists the target ES alias/index for every input record
        """
        result = pd.Series(pd.NA, index=masked_target_frame.index)

        if target_cluster_is_srp:
            # means that it is a se-schema SRP model such as `SteelEyeInstrument`
            alias = model.schema().get(SchemaExtra.ALIAS)
            result.loc[:] = alias
            return result

        alias = model.get_elastic_index_alias(tenant=Settings.tenant)
        result.loc[:] = alias

        self.logger.info(
            "Checking %s records existence in ES", masked_target_frame.shape[0]
        )

        # Querying ES to check if the records are already present
        query = {
            "_source": False,
            "size": masked_target_frame.shape[0],
            "query": {"ids": {"values": masked_target_frame[ID].values.tolist()}},
        }

        response = es_api_retriable_call(
            es_client.search,
            # Index will always be the alias and read from all the indices to which that alias is pointing to
            query=query,
            index=alias,
        )

        response_df = pd.DataFrame.from_records(
            response.get("hits", {}).get("hits", [])
        ).rename({"_id": ID}, axis=1)

        if response_df.empty:
            # This means that none of the input records are present in ES
            # thus we can use the generic alias, which will point to the latest index
            return result

        record_is_already_in_es_mask = masked_target_frame[ID].isin(response_df[ID])

        # For records that are already present in ES,
        # we set the target alias to the index in which the record is present
        # note: reset_index() and set_index() is a trick to preserve the index of the left DF
        # otherwise the assignment with the `record_is_already_in_es_mask` mask will not work

        # reset_index() resets the index to a range default and creates a new column with the original index values and name
        # the problem here is that if the index is named "__swarm_raw_index__" but the DF also has a column with the same name
        # thus, we must temporarily drop this column to avoid a conflict
        index_name = masked_target_frame.index.name

        result.loc[record_is_already_in_es_mask] = (
            masked_target_frame.loc[record_is_already_in_es_mask, :]
            .drop([SwarmColumns.SWARM_RAW_INDEX], axis=1, errors="ignore")
            .reset_index()
            .merge(
                response_df,
                on=ID,
                how="left",
            )
            .set_index(index_name if index_name else "index")["_index"]
        )

        return result

    def _process_to_quarantine(
        self,
        data: pd.DataFrame,
        bulk_writer_result: ElasticBulkWriterResult,
        es,
    ) -> pd.DataFrame:
        """
        Process data from bulk writer result to create quarantined records
        and creates the respective SinkRecordAudit records.

        :param data: DataFrame with original records
        :param bulk_writer_result: BulkWriterResult
        :param es: ES8 ElasticSearchClient for tenant-data. SRP does not support Quarantine
        :return: DataFrame with the Quarantined<model> and SinkRecordAudit records
        """
        data.index = data[SwarmColumns.SWARM_RAW_INDEX]

        data = self._get_conflict_data(
            transform_frame=data, bulk_writer_frame=bulk_writer_result.frame, es=es
        )

        sink_record_audit = self._make_sink_record_audit(data=data, es=es)

        data[es.meta.model] = data[es.meta.model].map(MODELS_QUARANTINE_MAP)
        # strips the model in the beginning of the meta key and adds the quarantined model
        data[es.meta.key] = (
            data[es.meta.model]
            + ":"
            + data[es.meta.key].str.split(":").str[1:].str.join(":")
        )

        data = pd.concat([data, sink_record_audit])

        # Removing meta parent, not applied in quarantined records
        if es.meta.parent in data.columns:
            data = data.drop(columns=[es.meta.parent])

        return data

    def _get_conflict_data(
        self,
        transform_frame: pd.DataFrame,
        bulk_writer_frame: pd.DataFrame,
        es,
    ) -> pd.DataFrame:
        """
        This methods returns the transform frame filtered with original data that got conflicts
        when tried to ingest into ES.

        :param transform_frame: Original dataframe with records to ingest
        :param bulk_writer_frame: BulkWriterResult with the conflicts
        :return: DataFrame with original records which resulted in conflicts
        """
        conflicts = {}

        for model in bulk_writer_frame[BulkWriterColumns.MODEL].unique():
            if model not in MODELS_QUARANTINE_MAP:
                continue
            model_conflicts_mask = (
                bulk_writer_frame[BulkWriterColumns.MODEL] == model
            ) & (
                bulk_writer_frame[BulkWriterColumns.STATUS]
                == WriteStatus.VERSION_CONFLICT
            )

            if model_conflicts_mask.any():
                conflicts[model] = (
                    bulk_writer_frame.loc[
                        model_conflicts_mask, BulkWriterColumns.RAW_INDEX
                    ]
                    .dropna()
                    .unique()
                    .tolist()
                )

        data = pd.DataFrame()

        if conflicts:
            conflicts_to_log = {k: len(v) for k, v in conflicts.items()}
            self.logger.info(
                f"Number of records with conflicts by model: {conflicts_to_log}"
            )

            mask = pd.Series(False, index=transform_frame.index)

            for model, raw_indices in conflicts.items():
                mask = mask | (
                    (transform_frame[es.meta.model] == model)
                    & (transform_frame[SwarmColumns.SWARM_RAW_INDEX].isin(raw_indices))
                )

            data = transform_frame.loc[mask]

        if data.empty:
            # Raising failure as from the swarm_tasks.io.write.quarantine_condition
            # it is expected to exist conflicts and if the params.quarantined = True it is
            # expected for the flow to ingest quarantine records
            raise FAIL("Unexpected dataframe empty for quarantine.")

        return data

    def _make_sink_record_audit(self, data: pd.DataFrame, es) -> pd.DataFrame:
        """
        Create SinkRecordAudit

        :param data: DataFrame with records
        :param es: ElasticSearchClient
        :return: DataFrame with SinkRecordAudit records
        """

        sink_rec_audit = pd.DataFrame(index=data.index)
        # Note: data.index should be SWARM_RAW_INDEX
        sink_rec_audit[SwarmColumns.SWARM_RAW_INDEX] = data.index

        ids_to_keys = dict()
        for model, frame in data.groupby([es.meta.model]):
            identifiers = list(set(frame[es.meta.id].values.tolist()))
            matches = self._fetch_quarantine_match_data(
                es=es, identifiers=identifiers, model=model
            )
            id_to_key = (
                (
                    pd.Series(
                        matches[es.meta.key].values, index=matches[es.meta.id]
                    ).to_dict()
                )
                if {es.meta.id, es.meta.key}.issubset(matches.columns)
                else {}
            )
            ids_to_keys.update(id_to_key)

        sink_rec_audit["bucket"] = Settings.realm
        sink_rec_audit["key"] = self._key_from_context()
        sink_rec_audit["dataSourceName"] = Settings.bundle
        sink_rec_audit["taskStarted"] = self._task_start_time_from_context()
        sink_rec_audit["processed"] = datetime.utcnow().isoformat()[:19] + "Z"
        sink_rec_audit["index"] = data.index
        sink_rec_audit["recordType"] = data[es.meta.model]

        sink_rec_audit["recordKey"] = (
            data[es.meta.model].map(MODELS_QUARANTINE_MAP)
            + ":"
            + data[es.meta.key].str.split(":").str[1:].str.join(":")
        )
        sink_rec_audit["matchedKey"] = data[es.meta.id].map(ids_to_keys)
        sink_rec_audit["status"] = "QUARANTINED"

        id_props = SinkRecordAudit.id_props()

        sink_rec_audit[es.meta.user] = "system"
        sink_rec_audit[es.meta.model] = "SinkRecordAudit"
        sink_rec_audit[es.meta.id] = sink_rec_audit[id_props].apply(
            lambda x: ":".join([sanitize(x.get(y)) for y in id_props]), axis=1
        )
        sink_rec_audit[es.meta.timestamp] = pd.Timestamp.utcnow().value // int(1e6)

        sink_rec_audit[es.meta.key] = sink_rec_audit[
            [es.meta.model, es.meta.id, es.meta.timestamp]
        ].apply(lambda x: ":".join(map(str, x.tolist())), axis=1)

        hash_properties_fields = SinkRecordAudit.hash_props()
        hash_props_present = set(hash_properties_fields).intersection(
            set(sink_rec_audit.columns)
        )
        sink_rec_audit[es.meta.hash] = sink_rec_audit[hash_props_present].apply(
            lambda x: Indict(x).hash(fields=hash_properties_fields), axis=1
        )

        return sink_rec_audit

    @staticmethod
    def _key_from_context() -> str:
        file_url = context.parameters.get("file_url")
        if file_url:
            return file_url.split("/", 3)[-1]
        else:
            # This is for scenarios where the flow wasn't triggered by a file
            return Settings.bundle

    @staticmethod
    def _task_start_time_from_context() -> str:
        return context.scheduled_start_time.isoformat()[:19] + "Z"

    def _fetch_quarantine_match_data(self, es, identifiers: List[str], model: str):
        result = []

        # NOTE: this is 1000 because of  maxClauseCount,
        # so with must and must not if we set to es_client.MAX_TERMS_SIZE
        # it could overcome that value
        max_terms_size = 1000
        identifiers_chunks = [
            identifiers[ix : ix + max_terms_size]
            for ix in range(0, len(identifiers), max_terms_size)
        ]

        for chunk in identifiers_chunks:

            self.logger.info(f"scrolling records for {len(chunk)} identifiers")
            query = self._make_quarantine_query(es=es, model=model, identifiers=chunk)

            # we can use se-elastic-schema find_model because the Quarantined models
            # all come from that package
            alias = find_model(name=model).get_elastic_index_alias(
                tenant=Settings.tenant
            )

            hits = es.scroll(
                query=query,
                index=alias,
                as_dataframe=True,
            )
            result.append(hits)

        result = pd.concat(result).reset_index(drop=True)

        if result.empty:
            return pd.DataFrame()

        return result

    @staticmethod
    def _make_quarantine_query(es, model: str, identifiers: List[str]):
        body = {
            "_source": [es.meta.key, es.meta.id],
            "query": {
                "bool": {
                    "must_not": {"exists": {"field": es.meta.expiry}},
                    "filter": [
                        {"term": {es.meta.model: model}},
                        {"terms": {es.meta.id: identifiers}},
                    ],
                }
            },
        }
        return body
