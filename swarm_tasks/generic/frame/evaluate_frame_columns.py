from typing import List

import pandas as pd
import prefect
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams):
    source_attributes: List[str]


class EvaluateFrameColumns(TransformBaseTask):
    """
    return copy of input source_frame with columns in params.source_attributes evaluated,
    converting a string value into a python object e.g. "[1,2,3]" (string) -> [1,2,3] (list)

    supported stringified data types:
        - dict
        - list

    null values returned for:
        - columns missing from source_frame
        - columns in source frame that cannot be evaluated into lists or dicts e.g. integers
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources: BaseResources = None,
        **kwargs,
    ) -> pd.DataFrame:
        target = source_frame.copy()
        for column in params.source_attributes:
            if column not in source_frame.columns:
                target[column] = pd.NA
                continue

            # values string with [ or {, a stringified list or dict
            valid_dtype_mask = target[column].astype(str).str.contains(r"^[\{\[]")
            if valid_dtype_mask.any():
                try:
                    target.loc[valid_dtype_mask, column] = target.loc[
                        valid_dtype_mask, column
                    ].apply(eval)
                except Exception as e:
                    self.logger.error(f"Error evaluating column '{column}': {e}")
                    raise prefect.engine.signals.FAIL(e)

            target.loc[~valid_dtype_mask, column] = pd.NA

        return target
