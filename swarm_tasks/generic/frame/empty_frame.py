from typing import Optional

import pandas as pd
from pydantic import Field
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams):
    no_index: bool = Field(
        False,
        description="If True, this task will return an empty dataframe without index. By default, it returns "
        "an empty dataframe with the same index as source_frame",
    )


class EmptyFrame(TransformBaseTask):
    """
    This task returns an empty Pandas DataFrame with the same index as source_frame.
    If params.no_index = True this task will return an empty Pandas DataFrame without index
    This can be used with a Task Flow Controller to vertically concatenate results
    example: A Task flow controller has a True condition -> Run "foo" task that outputs a dataframe
    with same number of columns as source_frame
    and a False condition -> Do nothing
    If we intend to vertically concatenate the result of the flow controller we want the result
    of "do nothing" to be a completely empty DataFrame without indexes.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Optional[Params] = None,
        **kwargs,
    ):

        if params.no_index:
            return pd.DataFrame()

        return pd.DataFrame(index=source_frame.index)
