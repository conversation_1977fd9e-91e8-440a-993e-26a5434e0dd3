from typing import Optional

import pandas as pd
from se_trades_tasks.order_and_tr.instrument.fallback.instrument_fallback import (
    Params as GenericParams,
)
from se_trades_tasks.order_and_tr.instrument.fallback.instrument_fallback import (
    run_instrument_fallback,
)
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class InstrumentFallback(TransformBaseTask):
    """Instrument details and workflow details are populated here for the instruments
    that are not found/linked on SRP.
    For SE blotter specific logic please Refer
    https://steeleye.atlassian.net/wiki/spaces/IN/pages/1693483009/SteelEye+Blotter+-+Instrument+Fallback"""

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources: Optional[BaseResources] = None,
        **kwargs,
    ) -> pd.DataFrame:
        return run_instrument_fallback(
            source_frame=source_frame,
            params=params,
        )
