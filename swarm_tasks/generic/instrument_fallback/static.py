from se_trades_tasks.order.static import OrderColumns


class InstrumentFields:

    COMMODITIES_OR_EMISSION_ALLOWANCE_DERIVATIVE_IND = (
        "commoditiesOrEmissionAllowanceDerivativeInd"
    )

    # Derivative
    DERIVATIVE_DELIVERY_TYPE = "derivative.deliveryType"
    DERIVATIVE_EXPIRY_DATE = "derivative.expiryDate"
    DERIVATIVE_OPTION_EXCERCISE_STYLE = "derivative.optionExerciseStyle"
    DERIVATIVE_OPTION_TYPE = "derivative.optionType"
    DERIVATIVE_PRICE_MULTIPLIER = "derivative.priceMultiplier"
    DERIVATIVE_STRIKE_PRICE = "derivative.strikePrice"
    DERIVATIVE_STRIKE_PRICE_CURRENCY = "derivative.strikePriceCurrency"
    DERIVATIVE_UNDERLYING_INDEX_NAME = "derivative.underlyingIndexName"
    DERIVATIVE_UNDERLYING_INDEX_TERM = "derivative.underlyingIndexTerm"
    DERIVATIVE_UNDERLYING_INSTRUMENTS = "derivative.underlyingInstruments"

    # Ext
    EXT_ALTERNATIVE_INSTRUMENT_ID = "ext.alternativeInstrumentIdentifier"
    EXT_BEST_EX_ASSET_CLASS_MAIN = "ext.bestExAssetClassMain"
    EXT_BEST_EX_ASSET_CLASS_SUB = "ext.bestExAssetClassSub"
    EXT_EXCHANGE_SYMBOL_BBG = "ext.exchangeSymbolBbg"
    EXT_EXCHANGE_SYMBOL_ROOT = "ext.exchangeSymbolRoot"
    EXT_INSTRUMENT_ID_TYPE = "ext.instrumentIdCodeType"
    EXT_INSTRUMENT_UNIQUE_ID = "ext.instrumentUniqueIdentifier"
    EXT_ON_FIRDS = "ext.onFIRDS"
    EXT_PRICE_NOTATION = "ext.priceNotation"
    EXT_QUANTITY_NOTATION = "ext.quantityNotation"
    EXT_STRIKE_PRICE_TYPE = "ext.strikePriceType"
    EXT_UNDERLYING_INSTRUMENTS = "ext.underlyingInstruments"
    EXT_VENUE_NAME = "ext.venueName"

    # FX
    FX_DERIVATIVE_NOTIONAL_CURRENCY_2 = "fxDerivatives.notionalCurrency2"

    # non-nested fields
    INSTRUMENT_CLASSIFICATION = "instrumentClassification"
    INSTRUMENT_FULL_NAME = "instrumentFullName"
    INSTRUMENT_ID_CODE = "instrumentIdCode"
    NOTIONAL_CURRENCY_1 = "notionalCurrency1"
    UNDERLYING_INSTRUMENT_ID = "underlyingInstrumentCode"

    # Bond Maturity Date
    MATURITY_DATE = "bond.maturityDate"

    # underlying fields
    UNDERLYING_INSTRUMENT_CLASSIFICATION = "underlyingInstrumentClassification"
    UNDERLYING_INSTRUMENT_ID_CODE = "underlyingInstrumentCode"

    # venue fields
    VENUE_TRADING_VENUE = "venue.tradingVenue"


class WorkflowEligibilityFields:
    ELIGIBILITY_ELIGIBLE = "eligible"
    ELIGIBILITY_IS_DEFAULTED = "isDefaulted"
    ELIGIBILITY_ON_FIRDS = "onFirds"
    ELIGIBILITY_REASON = "reason"
    ELIGIBILITY_TOTV = "totv"
    ELIGIBILITY_TOTV_ON_EXECUTION_VENUE = "totvOnExecutionVenue"
    ELIGIBILITY_UNDERLYING_ON_FIRDS = "underlyingOnFirds"
    ELIGIBILITY_UTOTV = "utotv"


class WorkflowFields:
    STATUS = "workflow.status"


class InstrumentClassificationType:
    BASKET = "basket"
    BOND = "bond"
    CDS = "cds"
    CDX = "cdx"
    CFD = "cfd"
    COMMODITY = "commodity"
    CREDIT_DEFAULT_SWAP = "credit default swap"
    EQUITY = "equity"
    INDEX = "index"
    LOAN = "loan"
    RATE = "rate"
    REPO = "repo"
    SPOT = "spot"
    SWAP = "swap"
    TOTAL_RETURN_SWAP = "total return swap"
    TRS = "trs"


class InstrumentClassification:
    DBXXXX = "DBXXXX"
    ESXXXX = "ESXXXX"
    HFXXXX = "HFXXXX"
    FXXXXX = "FXXXXX"
    JEIXCX = "JEIXCX"
    JEIXSX = "JEIXSX"
    JESXSX = "JESXSX"
    JESXCX = "JESXCX"
    JESXFX = "JESXFX"
    JFXXXX = "JFXXXX"
    JFXXCX = "JFXXCX"
    JFXXSX = "JFXXSX"
    JRXXCX = "JRXXCX"
    JRXXSX = "JRXXSX"
    IFXXXX = "IFXXXX"
    JTXXCX = "JTXXCX"
    JTXXSX = "JTXXSX"
    LLXXXX = "LLXXXX"
    LRXXXX = "LRXXXX"
    OCXXXX = "OCXXXX"
    OCXSXX = "OCXSXX"
    OCAXXX = "OCAXXX"
    OCASXX = "OCASXX"
    OCBXXX = "OCBXXX"
    OCBSXX = "OCBSXX"
    OCEXXX = "OCEXXX"
    OCESXX = "OCESXX"
    OMXXXX = "OMXXXX"
    OMXSXX = "OMXSXX"
    OPXXXX = "OPXXXX"
    OPXSXX = "OPXSXX"
    OPAXXX = "OPAXXX"
    OPASXX = "OPASXX"
    OPBXXX = "OPBXXX"
    OPBSXX = "OPBSXX"
    OPEXXX = "OPEXXX"
    OPESXX = "OPESXX"
    OXXXXX = "OXXXXX"
    OXXSXX = "OXXSXX"
    SCEXXX = "SCEXXX"
    SCIXXX = "SCIXXX"
    SEXXXX = "SEXXXX"
    SFXXXX = "SFXXXX"
    SXXTXX = "SXXTXX"


class OptionTypeIdentifier:
    CALL = "C"
    MISCELLANEOUS = "M"
    PUT = "P"


class OptionStyleIdentifier:
    AMERICAN = "A"
    BERMUDIAN = "B"
    EUROPEAN = "E"


class CfiAttributes:
    CFI_ATTRIBUTE_1 = "cfiAttribute1"
    CFI_ATTRIBUTE_2 = "cfiAttribute2"
    CFI_ATTRIBUTE_3 = "cfiAttribute3"
    CFI_ATTRIBUTE_4 = "cfiAttribute4"
    CFI_CATEGORY = "cfiCategory"
    CFI_GROUP = "cfiGroup"


class CfiJsonFields:
    ATTR_1 = "attr1"
    ATTR_2 = "attr2"
    ATTR_3 = "attr3"
    ATTR_4 = "attr4"
    CAT = "cat"
    GROUP = "group"


class RegexPat:
    INSTRUMENT_CLASSIFICATION_REGEX = "^[A-Za-z]{6}$"


class TempFields:
    IS_CFD = "__IS_CFD__"
    TEMP_IDENTIFIER = "__"
    TRADING_VENUE = "TRADING_VENUE"
    NOTIONAL_CCY2_PRICE_CCY_EXPIRY_DT_STR = "NOTIONAL_CCY2_PRICE_CCY_EXPIRY_DT_STR"


class WorkflowEligibilityReason:
    DEFAULTED_DUE_TO_VENUE_OF_EXECUTION = "Defaulted due to Venue of Execution"


class BestExAssetClassMain:
    COMMODITIES_DERIV_AND_EMISSION_ALLOWANCES_DERIV = (
        "Commodities derivatives and emission allowances Derivatives"
    )
    CREDIT_DERIVATIVES = "Credit Derivatives"
    CONTRACTS_FOR_DIFFERENCE = "Contracts for Difference"
    CURRENCY_DERIVATIVES = "Currency Derivatives"
    DEBT_INSTRUMENTS = "Debt Instruments"
    EMISSION_ALLOWANCES = "Emission Allowances"
    EQUITY = "Equity"
    EQUITY_DERIVATIVES = "Equity Derivatives"
    EXCHANGE_TRADED_PRODUCTS = "Exchange Traded Products"
    INTEREST_RATE_DERIVATIVES = "Interest Rate Derivatives"
    # NOTE: OTHER_INSTRUMENTS_+ variables kept.
    #   Currently regulation says Other Instruments to be generic, but it might change
    #   in the future.
    OTHER_INSTRUMENTS = "Other Instruments"
    OTHER_INSTRUMENTS_COMMODITY_SPOT = OTHER_INSTRUMENTS
    OTHER_INSTRUMENTS_FX_SPOT = OTHER_INSTRUMENTS
    OTHER_INSTRUMENTS_OTHER = OTHER_INSTRUMENTS
    OTHER_INSTRUMENTS_CIVS = OTHER_INSTRUMENTS
    STRUCTURED_FINANCE_INSTRUMENTS = "Structured Finance Instruments"


class BestExAssetClassSub:
    BONDS = "Bonds"
    FUTURE_OPTIONS_ADMITTED = (
        "Futures and options admitted to trading on a trading venue"
    )
    MONEY_MARKET_INSTRUMENTS = "Money Market Instruments"
    OTHER_CREDIT_DERIVATIVES = "Other credit derivatives"
    OTHER_COMMODITIES_DERIVATIVES = (
        "Other commodities derivatives and emission allowances derivatives"
    )
    SWAPS_EQUITY_DERIVATIVES = "Swaps and other equity derivatives"
    SWAPS_FORWARDS_INTEREST_RATES = (
        "Swaps, forwards, and other interest rates derivatives"
    )
    SWAPS_FORWARDS_CURRENCY_DERIVATIVES = (
        "Swaps, forwards, and other currency derivatives"
    )


class DeliveryTypePatterns:
    CASH_PATTERNS = ["F..C..", "H....C", "J....C", "L....C", "O...C.", "S....C"]
    PHYSICAL_PATTERNS = [
        "F..P..",
        "H....P",
        "J....P",
        "L....P",
        "O...P.",
        "S....P",
        "I....P",
    ]


class InstrumentFullNamePrefixes:
    FOREIGN_EXCHANGE_OPTION_VANILLA_OPTION = "Foreign_Exchange Option Vanilla_Option "
    FOREIGN_EXCHANGE_OPTION_BARRIER_OPTION = "Foreign_Exchange Option Barrier_Option "
    FOREIGN_EXCHANGE_OPTION_LOOK_BACK_OPTION = (
        "Foreign_Exchange Option Lookback_Option "
    )
    FOREIGN_EXCHANGE_OPTION_BARRIER_DIGITAL_OPTION = (
        "Foreign_Exchange Option " "Barrier_Digital_Option "
    )
    FOREIGN_EXCHANGE_OPTION_OTHER_OPTION = "Foreign_Exchange Option Other_Option "
    FOREIGN_EXCHANGE_FORWARD = "Foreign_Exchange Forward "
    FOREIGN_EXCHANGE_SWAP = "Foreign_Exchange Swap "


class CommoditiesEmissionAllowancePatterns:
    PATTERNS = ["JT..C.", "JT..F.", "FC....", "HT....", "O..T..", "ST.T..", "ST.C..."]


INSTRUMENT_IDENTIFIER_TO_INSTRUMENT_FIELD_MAP = {
    InstrumentFields.DERIVATIVE_EXPIRY_DATE: "expiry_date_attribute",
    InstrumentFields.DERIVATIVE_STRIKE_PRICE: "option_strike_price_attribute",
    InstrumentFields.DERIVATIVE_OPTION_TYPE: "option_type_attribute",
    InstrumentFields.EXT_VENUE_NAME: "venue_attribute",
    InstrumentFields.EXT_EXCHANGE_SYMBOL_ROOT: "underlying_symbol_attribute",
    InstrumentFields.EXT_EXCHANGE_SYMBOL_BBG: "bbg_figi_id_attribute",
    InstrumentFields.EXT_PRICE_NOTATION: OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION,
    InstrumentFields.EXT_QUANTITY_NOTATION: OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION,
    InstrumentFields.FX_DERIVATIVE_NOTIONAL_CURRENCY_2: "notional_currency_2_attribute",
    InstrumentFields.INSTRUMENT_ID_CODE: "isin_attribute",
    InstrumentFields.NOTIONAL_CURRENCY_1: "currency_attribute",
    InstrumentFields.VENUE_TRADING_VENUE: "venue_attribute",
}

UNDERLYING_INSTRUMENT_IDENTIFIER_TO_INSTRUMENT_FIELD_MAP = {
    "underlyingInstrumentCode": {
        "source_field": "underlying_isin_attribute",
        "level": "derivative.underlyingInstruments",
    },
    "instrumentIdCode": {
        "source_field": "underlying_isin_attribute",
        "level": "ext.underlyingInstruments",
    },
    "derivative.underlyingIndexName": {
        "source_field": "underlying_index_name_attribute",
        "level": "ext.underlyingInstruments",
    },
    "derivative.underlyingIndexTerm": {
        "source_field": "underlying_index_term_attribute",
        "level": "ext.underlyingInstruments",
    },
}
