from abc import abstractmethod
from typing import Any
from typing import Optional

import pandas as pd
from se_core_tasks.abstractions.transformations.abstract_transformations import (
    AbstractTransformations,
)

from swarm_tasks.abstractions.transformations.mymarket.static import (
    MarketSuggestionColumns,
)


class AbstractMarketSuggestionTransformations(
    AbstractTransformations
):  # pragma: no cover
    """
    This is the abstract base class for all MarketSuggestion Primary Transformations. It contains
    a process, pre_process and post_process methods, along with methods corresponding to
    each field in the Text schema.
    pre_process(): used to populate temp columns (columns which are used in multiple
                     places) in the pre_process_df data frame
    process(): Used to call the public methods for each of the fields in the Message
                 schema. At the end of process(), target_df should be filled with values for the
                 schema fields.
    post_process(): Used to populate target_df with temp columns, drop columns, and for any
                      other postprocessing.
    Field-specific methods:
    There are 2 methods for each schema field -- a public method and a private method.

    Private Schema Field methods:
    The private methods for schema fields are abstract and need to be implemented in the child class.
    These private methods should always return a Pandas DataFrame (if the schema column is required)
    or be passed (if the schema column isn't required). Typically, private methods call tasks or populate static values.

    Public Schema Field methods:

    These methods actually populate the MarketSuggestion schema fields by calling the appropriate private
    methods. Private methods and public methods for a field have the same name. The only difference is that
    the private method has a '_' prefix.
    Public methods should NOT be implemented in the child classes.

    E.g.: self.metadata_source_client() -> populates metadata.source.client in the
    target_df by concatenating the existing target df with the data frame returned by the private method
    _metadata_source_client(). _metadata_source_client() might call a task like
    MapStatic to implement the actual logic.
    """

    def __init__(
        self, source_frame: pd.DataFrame, auditor: Optional[Any] = None, **kwargs
    ):
        super().__init__(source_frame=source_frame, auditor=auditor)

    def pre_process(self) -> pd.DataFrame:
        """Used for pre-processing. It simply calls the private method _pre_process"""
        return self._pre_process()

    @abstractmethod
    def _pre_process(self):
        """This pre-processing method should be used to populate temporary columns in pre_process_df.
        pre_process_df should in general only be populated for temporary columns which might be
        required to populate at least 2 other fields.
        """

    def process(self) -> pd.DataFrame:
        """All the schema target columns which need to be populated are populated in
        self.target_df by the public methods called by process().
        The process() method should generally be overridden by child classes by including only
        the public methods that are required for that particular flow in the appropriate order.

        process() always calls pre_process() at the beginning and post_process() at the end and
        returns self.target_df.
        """
        self.pre_process()
        self.company()
        self.emails()
        self.im_accounts()
        self.meta_model()
        self.name()
        self.sink_identifiers_order_file_identifiers()
        self.sink_identifiers_trade_file_identifiers()
        self.source()
        self.post_process()
        return self.target_df

    def post_process(self) -> pd.DataFrame:
        """Used for post-processing. It simply calls the private method _post_process"""
        return self._post_process()

    @abstractmethod
    def _post_process(self):
        """The post-processing method should be used to populate temporary columns in target_df,
        and any other post-processing tasks.
        E.g. if column A is used in identifiers() and is required
        in downstream tasks, it should be populated in pre_process_df in _pre_process() and copied
        over to target_df in _post_process()
        """

    def company(self):
        """Populates MarketSuggestionColumns.COMPANY in target by calling _company()"""
        self.target_df.loc[:, MarketSuggestionColumns.COMPANY] = self._company()

    @abstractmethod
    def _company(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column MarketSuggestionColumns.COMPANY"""

    def emails(self):
        """Populates MarketSuggestionColumns.EMAILS in target by calling _emails()"""
        self.target_df.loc[:, MarketSuggestionColumns.EMAILS] = self._emails()

    @abstractmethod
    def _emails(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column MarketSuggestionColumns.EMAILS"""

    def im_accounts(self):
        """Populates MarketSuggestionColumns.IM_ACCOUNTS in target by calling _im_accounts()"""
        self.target_df.loc[:, MarketSuggestionColumns.IM_ACCOUNTS] = self._im_accounts()

    @abstractmethod
    def _im_accounts(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column MarketSuggestionColumns.IM_ACCOUNTS"""

    def meta_model(self):
        """Populates MarketSuggestionColumns.META_MODEL in target_df by
        calling _meta_model()"""
        self.target_df.loc[:, MarketSuggestionColumns.META_MODEL] = self._meta_model()

    @abstractmethod
    def _meta_model(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to populate the
        column MarketSuggestionColumns.META_MODEL."""

    def name(self):
        """Populates MarketSuggestionColumns.NAME in target by calling _name()"""
        self.target_df.loc[:, MarketSuggestionColumns.NAME] = self._name()

    @abstractmethod
    def _name(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column MarketSuggestionColumns.NAME"""

    def sink_identifiers_order_file_identifiers(self):
        """Populates MarketSuggestionColumns.SINK_IDENTIFIERS_ORDER_FILE_IDENTIFIERS in target by calling
        _sink_identifiers_order_file_identifiers()"""
        self.target_df.loc[
            :, MarketSuggestionColumns.SINK_IDENTIFIERS_ORDER_FILE_IDENTIFIERS
        ] = self._sink_identifiers_order_file_identifiers()

    @abstractmethod
    def _sink_identifiers_order_file_identifiers(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column MarketSuggestionColumns.SINK_IDENTIFIERS_ORDER_FILE_IDENTIFIERS"""

    def sink_identifiers_trade_file_identifiers(self):
        """Populates MarketSuggestionColumns.SINK_IDENTIFIERS_TRADE_FILE_IDENTIFIERS in target by calling _sink_identifiers_trade_file_identifiers()"""
        self.target_df.loc[
            :, MarketSuggestionColumns.SINK_IDENTIFIERS_TRADE_FILE_IDENTIFIERS
        ] = self._sink_identifiers_trade_file_identifiers()

    @abstractmethod
    def _sink_identifiers_trade_file_identifiers(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column MarketSuggestionColumns.SINK_IDENTIFIERS_TRADE_FILE_IDENTIFIERS"""

    def source(self):
        """Populates MarketSuggestionColumns.SOURCE in target by calling _source()"""
        self.target_df.loc[:, MarketSuggestionColumns.SOURCE] = self._source()

    @abstractmethod
    def _source(self) -> pd.Series:
        """Abstract method which needs to be implemented in the child class to
        populate the column MarketSuggestionColumns.SOURCE"""
