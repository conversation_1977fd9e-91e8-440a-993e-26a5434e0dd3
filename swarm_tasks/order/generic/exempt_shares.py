import pandas as pd
from se_trades_tasks.order_and_tr.feed.emsi.base.exempt_shares import (
    Params as GenericParams,
)
from se_trades_tasks.order_and_tr.feed.emsi.base.exempt_shares import run_exempt_shares
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask


class Params(BaseParams, GenericParams):
    pass


class ExemptShares(TransformBaseTask):
    """
    This task reads the esma_registers_mifid_shsexs.json file.
    If the input value is present in the json file it outputs True, If not, False
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources: BaseResources = None,
        **kwargs,
    ) -> pd.DataFrame:

        return run_exempt_shares(source_frame=source_frame, params=params, **kwargs)
