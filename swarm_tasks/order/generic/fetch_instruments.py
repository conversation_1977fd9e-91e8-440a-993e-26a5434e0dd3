from typing import Optional

import pandas as pd
from pydantic import Field
from pydantic import root_validator
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.order.generic.static import InstrumentFields
from swarm_tasks.order.generic.static import RESOURCE_TYPE
from swarm_tasks.order.generic.utils import (
    get_instrument_from_exch_symbol_local,
)
from swarm_tasks.order.generic.utils import (
    get_venue_direct_instrument,
)


class Params(BaseParams):
    security_id_col: str = Field(
        default="SecurityID",
        description="Security ID Column",
    )
    exchange_symbol_col: str = Field(
        default="ext.exchangeSymbolLocal",
        description="exchangeSymbolLocal of the instrument in srp",
    )
    trade_date: str = Field(..., description="column having date of the trade")
    trade_date_format: str = Field(
        default="%Y-%m-%d", description="format of the trade date"
    )
    get_multileg_indicator: Optional[bool] = Field(
        default=False,
        description="If set to true, this will add a new col indicating whether the trade is multileg or not",
    )
    multileg_indicator: Optional[str] = Field(
        default=None,
        description="the col indicating whether the trade is multileg or not. "
        "To be used only when get_multileg_indicator is True",
    )
    instrument = Field(
        default="instrumentDetails.instrument",
        description="column having date of the trade",
    )
    exchange: Optional[str] = Field(
        default=None,
        description="Exchange for which instruments are to be fetcheg, e.g. CME, EUREX etc.",
    )
    default_multileg_indicator: bool = Field(
        default=True,
        description="Default value of multileg indicator col"
        "if instruments are not found",
    )
    exchange_venue_col: Optional[str] = Field(
        default=None,
        description="For feeds like tt where there can be multiple exchanges"
        "each row must represent the exchange to query",
    )

    @root_validator
    def multileg_indicator_validation(cls, values):
        get_multileg_indicator = values.get("get_multileg_indicator")
        multileg_indicator = values.get("multileg_indicator")
        if multileg_indicator and not get_multileg_indicator:
            raise ValueError(
                "`multileg_indicator` will be populated only when `get_multileg_indicator` is set to True"
            )
        return values

    @root_validator
    def exchange_venue(cls, values):
        exchange_venue_col = values.get("exchange_venue_col")
        exchange = values.get("exchange")
        if not (exchange or exchange_venue_col):
            raise ValueError(
                "`Exchange` data is mandatory to be used while fetching VDIs"
            )
        return values


class FetchInstruments(TransformBaseTask):
    """
    This task fetches instruments based on ext.exchangeSymbolLocal from srp.
    The index being searched is  '.venue_direct_instrument` matches them with
    the trade's SecurityID. This tasks returns the matched result with the column
    name as passed in the params.
    This task is a replacement for both the usually used InstrumentIdentifiers
    and LinkInstruments as it directly fethces the instrument to be linked from srp.
    More details and documentation regarding the same can be found here:
    https://steeleye.atlassian.net/wiki/spaces/IN/pages/2126446611/Order+CME+Drop+Copy+FIX#Security-Definition-Reference-Files
    """

    params_class = Params

    def execute(
        self, source_frame: pd.DataFrame = None, params: Params = None, **kwargs
    ) -> pd.DataFrame:
        return self.process(source_frame=source_frame, params=params)

    @classmethod
    def process(
        cls, source_frame: pd.DataFrame = None, params: Params = None
    ) -> pd.DataFrame:
        if source_frame.empty:
            return pd.DataFrame()
        es = Settings.connections.get(RESOURCE_TYPE)
        target_df = pd.DataFrame(
            data=pd.NA, index=source_frame.index, columns=[params.instrument]
        )
        security_ids_dict = cls._get_exchange_security_ids_dict(
            df=source_frame, params=params
        )
        instruments = get_venue_direct_instrument(
            es_client=es,
            security_ids_dict=security_ids_dict,
            exchange_symbol_local=params.exchange_symbol_col,
        )

        target_df[params.instrument] = (
            source_frame.loc[:, [params.security_id_col, params.trade_date]]
            .dropna()
            .apply(
                lambda x: get_instrument_from_exch_symbol_local(
                    security_id=x[0],
                    trade_date=x[1],
                    instruments=instruments,
                    date_format=params.trade_date_format,
                    instrument_symbol_field=params.exchange_symbol_col,
                ),
                axis=1,
            )
        )

        if params.get_multileg_indicator:
            instrument_exists_mask = target_df[params.instrument].notnull()
            target_df[params.multileg_indicator] = target_df.loc[
                instrument_exists_mask, params.instrument
            ].apply(lambda x: cls.set_multileg_indicator(instrument=x))
            # Setting ML as True so that these orders are skipped for processing
            # and audited in cme_multileg_trades_to_single_leg task downstream
            target_df.loc[
                ~instrument_exists_mask, params.multileg_indicator
            ] = params.default_multileg_indicator

        return target_df

    @staticmethod
    def set_multileg_indicator(instrument: dict) -> bool:
        """
        This method returns True if the trade is a multileg else returns False.
        The way this is detemined is if the instrument has `derivative.legs` it
        must be a multileg.
        :param instrument: instruments to look into
        :return: boolean indicator, True for multileg, False otherwise
        """
        if instrument and isinstance(
            instrument.get(InstrumentFields.DERIVATIVE_LEGS), list
        ):
            return True
        return False

    @staticmethod
    def _get_exchange_security_ids_dict(df: pd.DataFrame, params: Params):
        if params.exchange:
            return {
                params.exchange: list(
                    set(df.loc[:, params.security_id_col].dropna().to_list())
                )
            }
        elif params.exchange_venue_col:
            return (
                df.loc[:, [params.exchange_venue_col, params.security_id_col]]
                .groupby(params.exchange_venue_col)[params.security_id_col]
                .apply(list)
                .to_dict()
            )
