import pandas as pd
from se_trades_tasks.order_and_tr.feed.order_and_tr_universal_steeleye_trade_blotter.instrument_overrides import (
    run_instrument_overrides,
)
from swarm.task.transform.base import TransformBaseTask


class OrderBlotterInstrumentOverrides(TransformBaseTask):
    """
    This Task invokes several instances of the generic `MapToNested` Task to override
    specific fields of the embedded Order instruments (obtained in LinkInstruments)
    based on certain input fields from the Blotter file.
    """

    def execute(
        self, source_frame: pd.DataFrame = None, params=None, **kwargs
    ) -> pd.DataFrame:
        return run_instrument_overrides(source_frame=source_frame, **kwargs)
