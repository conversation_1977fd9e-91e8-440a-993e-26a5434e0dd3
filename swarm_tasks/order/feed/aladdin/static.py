class FileTypes:
    FILL = "fill"
    ORDER = "order"
    ORDER_DETAILS = "orderdetail"
    PLACEMENT = "placement"
    TRANSACTION = "transaction"


class FileTypeAssetClass:
    DERIV = "DERIV"
    EQ = "EQ"
    FI = "FI"
    FX = "FX"


class FillSourceColumns:
    DEALING_CAPACITY = "dealingCapacity"
    EXCHANGE = "exchange"
    EXECUTED_PRICE = "executedPrice"
    EXECUTED_QUANTITY = "executedQuantity"
    EXECUTED_TIMESTAMP_UTC = "executedTimestampUtc"
    FILL_ID = "fillId"
    PLACEMENT_ID = "placementId"


class OrderDetailSourceColumns:
    ORDER_ID = "orderId"
    ORDER_DETAIL_ID = "orderDetailId"
    ORIG_ORDER_ID = "origOrderId"
    PORTFOLIO_ID = "portfolioId"


class OrderSourceColumns:
    ACTIVATED_TIMESTAMP_UTC = "activatedTimestampUtc"
    AVG_PRICE = "avgPrice"
    BASKET_ID = "basketId"
    CREATED_TIMESTAMP_UTC = "createdTimestampUtc"
    DEALING_CAPACITY = "dealingCapacity"
    ISIN = "isin"
    MODIFIED_TIMESTAMP_UTC = "modifiedTimestampUtc"
    ORDER_ID = "orderId"
    ORDER_QUANTITY = "orderQuantity"
    ORDER_STATUS = "orderStatus"
    ORDER_TYPE = "orderType"
    ORDER_GEN_COMMENTS = "genComments"
    PM_INITIALS = "pmInitials"
    PRICE_CCY = "priceCcy"
    RIC = "ric"
    TIME_IN_FORCE = "timeInForce"
    TRADER = "trader"
    TRAN_TYPE = "tranType"


class PlacementSourceColumns:
    LIMIT_VALUE = "limitValue"
    STOP_VALUE = "stopValue"
    ORDER_ID = "orderId"
    PLACEMENT_ID = "placementId"
    SEND_TIME_UTC = "sendTimeUtc"


class TransactionSourceColumns:
    ASSET_ID = "assetId"
    CPTY_ID = "cptyId"
    DEALING_CAPACITY = "dealingCapacity"
    EXEC_CPCY_ID = "execCptyId"
    EXEC_CPCY_TYPE = "execCptyType"
    ISIN = "isin"
    MATURITY = "maturity"
    ORDER_ID = "orderId"
    PLACEMENT_ID = "placementId"
    PORTFOLIO_ID = "portfolioId"
    PORTFOLIO_TICKER = "portfolioTicker"
    RIC = "ric"
    SEC_DESC_1 = "secDesc1"
    SEC_GROUP = "secGroup"
    SEC_TICKER = "secTicker"
    SEC_TYPE = "secType"
    TRADE_COUPON = "tradeCoupon"
    TRADE_NUM = "tradeNum"
    TRADE_PURPOSE = "tradePurpose"
    TRADE_QUANTITY = "tradeQuantity"
    TRADER = "trader"
    UNDERLYING_SNP_CUSIP = "underlyingSnpCusip"


class CustomOrdType:
    CLIENT = "C"
    MARKET = "M"


class SecGroupValues:
    ABS = "ABS"
    BND = "BND"
    CASH = "CASH"
    CDS = "CDS"
    CDSWAP = "CDSWAP"
    CMBS = "CMBS"
    CMO = "CMO"
    EQUITY = "EQUITY"
    FUTURE = "FUTURE"
    FX = "FX"
    LOAN = "LOAN"
    MBS = "MBS"
    OPTION = "OPTION"
    SWAP = "SWAP"


class SecTypeValues:
    CDSWAP = "CDSWAP"
    CSWAP = "CSWAP"
    FWRD = "FWRD"
    OPT = "OPT"
    SPOT = "SPOT"


class DevColumns:
    AGGREGATE_PORTFOLIO_ID = "__aggregate_portfolio_id__"
    ASSET_CLASS = "__asset_class__"
    ASSET_ID = "__asset_id__"
    BEST_EX_ASSET_CLASS_MAIN = "__best_ex_asset_class_main__"
    BUY_SELL = "__buy_sell__"
    CLIENT = "__client__"
    CLIENT_CLIENT_SIDE = "__client_client_side__"
    CLIENT_MARKET_SIDE = "__client_market_side__"
    CURRENCY = "__currency__"
    COUNTERPARTY = "__counterparty__"
    CFI_CATEGORY = "__cfi_category__"
    CFI_GROUP = "__cfi_group__"
    DEALING_CAPACITY = "__dealing_capacity__"
    EXECUTING_ENTITY_WITH_LEI = "__executing_entity_with_lei__"
    EXPIRY_DATE = "__expiry_date__"
    FILE_TYPE_ASSET_CLASS = "__file_type_asset_class__"
    ID = "__id__"
    INSTRUMENT_CLASSIFICATION = "__instrument_classification__"
    INSTRUMENT_CREATED_THROUGH_FB = "__instrument_created_through_fb__"
    INVESTMENT_DECISION_WITHIN_FIRM = "__investment_decision_within_firm__"
    INSTRUMENT_UNIQUE_IDENTIFIER = "__instrument_unique_identifier__"
    INSTRUMENT_FULL_NAME = "__instrument_full_name__"
    ISIN = "__isin__"
    NEWO_IN_FILE = "__newo_in_file__"
    NOTIONAL_CURRENCY_2 = "__notional_currency_2__"
    OPTION_TYPE = "__option_type__"
    ORDER_STATUS_UPDATED = "__order_status_updated__"
    ORDER_TYPE = "__order_type__"
    PRICING_REFERENCE_LXID = "__pricing_reference_lxid__"
    PRICING_REFERENCE_REDCODE = "__pricing_reference_redcode__"
    RIC = "__ric__"
    STRIKE_PRICE = "__strike_price__"
    SYMBOL = "__symbol__"
    TEMP_ASSET_CLASS = "__temp_asset_class__"
    TIMESTAMPS_ORDER_SUBMITTED = "__timestamps_order_submitted__"
    TIMESTAMPS_VALIDITY_PERIOD = "__timestamps_validity_period__"
    TRADER = "__trader__"
    TRADING_CAPACITY = "__trading_capacity__"
    UNDERLYING_INDEX_TERM = "__underlying_index_term__"


ALADDIN_FILES_PATTERN = ".+[._](?P<asset_class>DERIV|EQ|FX|FI)[.].*"
