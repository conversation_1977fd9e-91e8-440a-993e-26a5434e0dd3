from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order_and_tr.party.static import PartiesFields


class TempColumns:
    ACCOUNT_PROPAGATED = "__account_propagated__"
    AGGREGATED_ORDER_ID = "__aggregated_order_id__"
    ALL_CLIENTS = "__all_clients__"
    ASSET_CLASS = "__asset_class__"
    ASSET_CLASS_PROPAGATED = "__asset_class_propagated__"
    BOND_MATURITY_DATE = "__bond_maturity_date__"
    BUYER = "__buyer__"
    BUY_SELL = "__buy_sell__"
    BUY_SELL_INDICATOR = "__buy_sell_indicator__"
    CLIENT = "__client__"
    COUNTERPARTY = "__counterparty__"
    CURRENCY_CODE = "__currency_code__"
    CURRENCY = "__currency__"
    DATE_TIME = "__date_time__"
    DELIVERY_TYPE = "__delivery_type__"
    EXECUTING_ENTITY = "__executing_entity__"
    EXECUTION_DETAILS_VALIDITY_PERIOD = "__execution_details_validity_period__"
    EXECUTION_WITHIN_FIRM = "__execution_within_firm__"
    EXPIRY_DATE = "__expiry_date__"
    EXPIRY_DATE_FALLBACK = "__expiry_date_fallback__"
    FIRST_ATTEMPT_ID = "__first_attempt_id__"
    FIRST_ATTEMPT_SEQ = "__first_attempt_seq__"
    HAS_AGGREGATE_INFO_FROM_ES = "__has_aggregate_info_from_es__"
    INITIAL_QUANTITY = "__initial_quantity__"
    INITIAL_QUANTITY_PROPAGATED = "__initial_quantity_propagated__"
    INSTR_QUANTITY_NOTATION = "__instr_quantity_notation__"
    INSTR_FULL_NAME = "__instr_full_name__"
    INT_ORDER_ID_CODE = "__int_order_id_code__"
    INVESTMENT_DECISION_MAKER = "__investment_decision_maker__"
    IS_CREATED_THROUGH_FALLBACK = "__is_created_through_fallback__"
    ISIN = "__isin__"
    NEWO_IN_FILE = "__newo_in_file__"
    NOTIONAL_CURRENCY_1 = "__notional_currency_1__"
    NOTIONAL_CURRENCY_2 = "__notional_currency_2__"
    OPTION_STRIKE_PRICE = "__option_strike_price__"
    OPTION_TYPE_MAPPED = "__option_type_mapped__"
    ORD_TYPE = "__ord_type__"
    ORDER_ID = "__order_id__"
    ES_AUDIT_ID_TKT_CLIENTS = "__es_audit_id_tkt_clients__"
    ES_CLIENTS = "__es_order_clients__"
    ES_ORDER_SIDES = "__es_order_sides__"
    ES_TS_ORD_NUM_CLIENTS = "__es_ts_ord_num_clients__"
    ORDER_FROM_AUDIT_ID_TKT = "__order_from_audit_id_tkt__"
    ORDER_STATUS = "__order_status__"
    PARENT_SYS_DATE_TIME = "__parent_sys_date_time__"
    PRICE = "__price__"
    QUANTITY = "__quantity__"
    QUANTITY_PROPAGATED = "__quantity_propagated__"
    REMAINING_QUANTITY = "__remaining_quantity__"
    ROUTE = "__route__"
    ROUTE_SYS_DATE_TIME = "__route_sys_date_time__"
    SECOND_ATTEMPT_ID = "__second_attempt_id__"
    SECOND_ATTEMPT_SEQ = "__second_attempt_seq__"
    SELLER = "__seller__"
    SETTLE_DT = "__settle_dt__"
    SEQUENCE_NUMBER = "__sequence_number__"
    SYNTH_ORDER = "__synth_order__"
    SYS_DATE = "__sys_date__"
    SYS_DATE_TIME = "__sys_date_time__"
    TEMP_COL_1 = "__temp_col_1__"
    TEMP_COL_2 = "__temp_col_2__"
    TEMP_COL_3 = "__temp_col_3__"
    TEMP_COL_4 = "__temp_col_4__"
    TEMP_COL_5 = "__temp_col_5__"
    TRADER = "__trader__"
    TRADER_UUID = "__trader_uuid__"
    TRADING_CAPACITY = "__trading_capacity__"
    TRANSACTION_REFERENCE_NUMBER = "__transaction_ref_no__"
    UNDERLYING_ISIN = "__underlying_isin__"
    UNDERLYING_SYMBOL = "__underlying_symbol__"


class AddlInfoColumns:
    ACCOUNT = "Account"
    AUDIT_ID = "Audit ID"
    BASKET_NAME = "Basket Name"
    BROKER = "Broker"
    CLIENT_ORDER_ID = "Client Order ID"
    DATE_TIME = "Date Time"
    EXECUTIONS_INSTR = "Execution Instructions"
    FUNCTION = "Function"
    IDENT_TYPE = "Ident Type"
    MASTER_NUMBER = "Master Number"
    MEASUREMENT_UNIT = "Measurement Unit"
    OLD_EXECUTION_INSTR = "Old Execution Instruction"
    OLD_INSTR = "Old Instruction"
    ORDER_INSTR = "Order Instructions"
    ORDER_NAME = "Order Name"
    PRICE = "Price"
    REASON_CODE = "Reason code"
    ROUTE_INSTR = "Route Instructions(EMSX)"
    SIDE = "Side"
    TRADING_DESK = "Trading desk"
    TICKET_NUMBER = "Ticket Number"
    USER = "User"
    UTC_TIME = "UTC Time"
    VENUE_ORDER_ID = "Venue Order ID"


class ShellPrefix:
    ALGO = "algo:"
    PM = "pm:"


class Function:
    EMSX = "EMSX"
    TSOX = "TSOX"


TIME_ZONE = "Europe/London"

ES_COLUMNS_FOR_AGGREGATE_BACKFILL = [
    OrderColumns.ID,
    OrderColumns.BUY_SELL,
    # execution fields
    OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
    OrderColumns.EXECUTION_DETAILS_ORDER_TYPE,
    OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY,
    OrderColumns.EXECUTION_DETAILS_VALIDITY_PERIOD,
    # price forming data fields
    OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
    OrderColumns.PRICE_FORMING_DATA_PRICE,
    OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY,
    # transaction details fields
    OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
    OrderColumns.TRANSACTION_DETAILS_PRICE,
    OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
    OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION,
    OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION,
    OrderColumns.TRANSACTION_DETAILS_SETTLEMENT_DATE,
    OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY,
    # parties fields
    PartiesFields.PARTIES_TRADER_FILE_ID,
    PartiesFields.PARTIES_EXEC_WITHIN_FIRM_FILE_ID,
    PartiesFields.PARTIES_INVEST_DEC_WITHIN_FIRM_FILE_ID,
    PartiesFields.PARTIES_CP_FILE_ID,
    PartiesFields.PARTIES_BUYER_FILE_ID,
    PartiesFields.PARTIES_SELLER_FILE_ID,
    # instrument fields
    "instrumentDetails.instrument.instrumentFullName",
    "instrumentDetails.instrument.instrumentIdCode",
]

ES_COL_PREFIX = "es_col_"

MAPPINGS_INSTR_COLS = [
    ES_COL_PREFIX + TempColumns.INSTR_FULL_NAME,
    ES_COL_PREFIX + TempColumns.ISIN,
]

ES_COLUMNS_FOR_AGGREGATE_BACKFILL_WITH_PREFIX = [
    ES_COL_PREFIX + col for col in ES_COLUMNS_FOR_AGGREGATE_BACKFILL[:-2]
] + MAPPINGS_INSTR_COLS

ES_COLS_NOT_NEEDED_IN_POST_PROCESS = [
    OrderColumns.ID,
    OrderColumns.BUY_SELL,
    OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
    OrderColumns.PRICE_FORMING_DATA_PRICE,
    OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY,
    OrderColumns.TRANSACTION_DETAILS_PRICE,
    PartiesFields.PARTIES_TRADER_FILE_ID,
    PartiesFields.PARTIES_EXEC_WITHIN_FIRM_FILE_ID,
    PartiesFields.PARTIES_INVEST_DEC_WITHIN_FIRM_FILE_ID,
    PartiesFields.PARTIES_CP_FILE_ID,
    PartiesFields.PARTIES_BUYER_FILE_ID,
    PartiesFields.PARTIES_SELLER_FILE_ID,
    "instrumentDetails.instrument.instrumentFullName",
    "instrumentDetails.instrument.instrumentIdCode",
]

QUANTITY_COLS = [
    OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
    OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY,
]
