from datetime import datetime
from typing import Dict
from typing import List

import pandas as pd
from se_elastic_schema.models import Order
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order.transformations.bbg.audt.static import SourceColumns
from se_trades_tasks.order_and_tr.party.static import PartiesFields
from swarm.conf import Settings
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.order.feed.shell.static import TempColumns
from swarm_tasks.order.generic.utils import es_scroll


class Resources(BaseResources):
    es_client_key: str


class FetchAggregatedOrderClients(TransformBaseTask):
    """
    Looks up columns used for Order ID in transformations and fetches the clientFileIdentifiers
    of orders from the last 30 days that have those value as an aggregatedOrderId

    Creates a dataframe with two columns containing the clientFileIdentifiers for each order id value
    """

    resources_class = Resources

    def execute(
        self, source_frame: pd.DataFrame = None, resources: Resources = None, **kwargs
    ) -> pd.DataFrame:
        return self.process(source_frame=source_frame, resources=resources)

    @classmethod
    def process(
        cls, source_frame: pd.DataFrame = None, resources: Resources = None
    ) -> pd.DataFrame:

        es = Settings.connections.get(resources.es_client_key)

        query = cls.get_query(source_frame=source_frame, es_client=es)

        index = Order.get_elastic_index_alias(tenant=Settings.tenant)

        hits = es_scroll(es_client=es, query=query, index=index)

        if hits.empty:
            return pd.DataFrame(
                data=pd.NA,
                index=source_frame.index,
                columns=[
                    TempColumns.ES_TS_ORD_NUM_CLIENTS,
                    TempColumns.ES_AUDIT_ID_TKT_CLIENTS,
                ],
            )

        aggregated_id_client_pairing = dict()

        hits.apply(
            lambda x: cls.update_aggregate_dict(
                aggregated_id_client_pairing,
                df_row=x,
                column=OrderColumns.ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID,
            ),
            axis=1,
        )

        ts_ord_num_clients = (
            source_frame.loc[:, SourceColumns.TS_ORD_NUM]
            .map(aggregated_id_client_pairing)
            .fillna(pd.NA)
        )
        audit_id_tkt_clients = (
            source_frame.loc[:, SourceColumns.AUDIT_ID_TKT]
            .map(aggregated_id_client_pairing)
            .fillna(pd.NA)
        )

        return pd.DataFrame(
            data={
                TempColumns.ES_TS_ORD_NUM_CLIENTS: ts_ord_num_clients.values,
                TempColumns.ES_AUDIT_ID_TKT_CLIENTS: audit_id_tkt_clients.values,
            },
            index=source_frame.index,
        )

    @staticmethod
    def get_query(source_frame: pd.DataFrame, es_client) -> Dict:
        """
        Generates query to look for orders in elastic that have Order ID values as aggregatedOrderId
        :param source_frame: input source_frame with SourceColumns.TS_ORD_NUM, SourceColumns.AUDIT_ID_TKT columns
        :param es_client: elastic search instance
        :return: query dictionary
        """

        order_id_values = list(
            set(
                source_frame.loc[
                    :, [SourceColumns.TS_ORD_NUM, SourceColumns.AUDIT_ID_TKT]
                ]
                .stack()
                .tolist()
            )
        )

        # current time minus 30 days (this does not need to be exactly one month)
        today_minus_thirty_days = int(datetime.now().timestamp() * 1000) - 2592000000

        query = {
            "query": {
                "bool": {
                    "filter": [
                        {"range": {"&timestamp": {"gte": today_minus_thirty_days}}},
                        {
                            "terms": {
                                OrderColumns.ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID: order_id_values
                            }
                        },
                    ],
                    "must_not": [{"exists": {"field": es_client.meta.expiry}}],
                    "must": [
                        {"exists": {"field": PartiesFields.PARTIES_CLIENT_FILE_ID}},
                    ],
                }
            },
            "_source": {
                "includes": [
                    OrderColumns.ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID,
                    PartiesFields.PARTIES_CLIENT_FILE_ID,
                ]
            },
        }

        return query

    @staticmethod
    def update_aggregate_dict(
        aggregate_dict: Dict[str, List[str]], df_row: pd.Series, column: str
    ) -> None:
        """
        Upgrades an existing dictionary with values from the dataframe
        dictionary keys are from `column`
        while values are sets from PartiesFields.PARTIES_CLIENT_FILE_ID
        :param aggregate_dict: dictionary to be updated
        :param df_row: pandas row with values to update dictionary
        :param column: column where to look up values in
        """

        if not aggregate_dict.get(df_row[column]):
            aggregate_dict[df_row[column]] = set()

        if not isinstance(df_row.get(PartiesFields.PARTIES_CLIENT_FILE_ID), str):
            return

        aggregate_dict[df_row[column]].add(df_row[PartiesFields.PARTIES_CLIENT_FILE_ID])
