from typing import Optional

import pandas as pd
from pydantic import Field
from se_elastic_schema.static.reference import PersonStructureType
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.transform.steeleye.orders.common_utils.static import PartiesFields
from swarm_tasks.transform.steeleye.tr.data_source.cme.mifir.parties_fallback import (
    PersonField,
)

META_KEY_PLACEHOLDER = "MarketPerson::"


class Params(BaseParams):
    source_column: str = Field(..., description="source column for client field")


class Resources(BaseResources):
    es_client_key: str


class PartiesClientFallback(TransformBaseTask):
    params_class = Params
    resources_class = Resources

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        resources: Optional[Resources] = None,
        **kwargs,
    ) -> pd.DataFrame:
        """
        populate clientIdentifiers.client and clientFileIdentifier fields from the
        source file as the client does not have this data in my-market data
        """
        target = pd.DataFrame(
            index=source_frame.index,
            columns=[
                PartiesFields.PARTIES_CLIENT,
                PartiesFields.PARTIES_CLIENT_FILE_ID,
            ],
        )
        if source_frame.empty or params.source_column not in source_frame.columns:
            # return empty target when source is empty
            return target

        es = self.clients.get(resources.es_client_key)

        df = source_frame[[params.source_column]]

        not_null_source_mask = df[params.source_column].notnull()
        if not_null_source_mask.any():
            # populate clientIdentifiers.client field
            target.loc[
                not_null_source_mask, PartiesFields.PARTIES_CLIENT
            ] = self._format_party(
                source=df.loc[not_null_source_mask], params=params, meta_key=es.meta.key
            )
            # populate client File Identifier
            target[
                PartiesFields.PARTIES_CLIENT_FILE_ID
            ] = self._make_client_file_identifier(df=df, params=params)
        return target

    @staticmethod
    def _format_party(source: pd.DataFrame, params: Params, meta_key: str) -> pd.Series:
        """
        populate the fields for a Client(Person) using data from source
        """
        df = pd.DataFrame(index=source.index)
        df[meta_key] = META_KEY_PLACEHOLDER
        df[PersonField.STRUCTURE_TYPE] = PersonStructureType.CLIENT
        df[PersonField.NAME] = source[params.source_column]
        result = df.loc[:, :].apply(lambda x: [x.dropna().to_dict()], axis=1)
        return result

    @staticmethod
    def _make_client_file_identifier(df: pd.DataFrame, params: Params) -> pd.Series:
        result = pd.Series(pd.NA, index=df.index)
        client_mask = df[params.source_column].notnull()

        if client_mask.any():
            result.loc[client_mask] = df.loc[
                client_mask, params.source_column
            ].str.lower()
        return result
