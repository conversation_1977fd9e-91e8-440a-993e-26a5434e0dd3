import pandas as pd
from prefect import context
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from swarm.task.auditor import Auditor
from swarm.task.base import BaseResources
from swarm.task.transform.base import TransformBaseTask

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ParamsConvertMinorToMajor,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ParamsConvertDatetime,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as ParamsMapAttribute
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.transform.map.map_static import MapStatic
from swarm_tasks.transform.map.map_static import Params as ParamsMapStatic
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as ParamsMapValue
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as ParamsMergeMarketIdentifers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    Params as ParamsInstrumentIdentifiers,
)


class TaskNames:
    AUX_MAP_ATTRIBUTE = "AuxMapAttribute"
    CONVERT_DATE_TIME = "ConvertDatetime"
    CONVERT_MINOR_TO_MAJOR = "ConvertMinorToMajor"
    INSTRUMENT_IDENTIFIERS = "InstrumentIdentifiers"
    MAP_ATTRIBUTE = "MapAttribute"
    MAP_CONDITIONAL = "MapConditional"
    MAP_STATIC = "MapStatic"
    MAP_VALUE = "MapValue"
    MERGE_MARKET_IDENTIFIERS = "MergeMarketIdentifiers"
    PARTY_IDENTIFIERS = "PartyIdentifiers"


class TempColumns:
    ARRIVAL_QUOTE_DATE = "__arrival_quote_date__"
    ARRIVAL_QUOTE_DATETIME = "__arrival_quote_datetime__"
    CLIENT_IDENTIFIER = "__client_identifier__"
    # This column is created in the generic PartyIdentifiers, but has to be
    # created in a party fallback task for Liquid Metrix.
    CLIENT_FILE_IDENTIFIER_FROM_PARTY_IDENTIFIERS = "clientFileIdentifier"
    CONDITIONAL_COUNTERPARTY = "__conditional_counterparty__"
    COUNTERPARTY_ID = "__counterparty_id__"
    COUNTERPARTY_ID_WITHOUT_PREFIX = "__counterparty_id_without_prefix__"
    DEFAULT_COUNTERPARTY = "__default_counterparty__"
    EXEC_ENTITY_ID = "__exec_entity_id__"
    FIRST_FILL_DATE = "__first_fill_date__"
    FIRST_FILL_DATETIME = "__first_fill_datetime__"
    META_MODEL = "__meta_model__"
    NEWO_IN_FILE = "__newo_in_file__"
    ORDER_STATUS = "__order_status__"
    VALIDITY_PERIOD = "__validity_period__"


class PrimaryTransformations(TransformBaseTask):
    """
    This task consolidates all PrimaryFrameConcatenator's upstream tasks and
    the PrimaryFrameConcatenator itself. This is a bespoke task for the bundle
    order-freetrade-liquid-metrix. This is done in order to reduce parallel processing,
    thereby reducing memory consumption.
    """

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        resources: BaseResources = None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame,
            logger=self.logger,
            auditor=self.auditor,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame,
        logger=context.get("logger"),
        auditor: Auditor = None,
    ) -> pd.DataFrame:
        target = pd.DataFrame(index=source_frame.index)
        if source_frame.empty:
            return target

        # Create a dict containing the results from the various tasks. Keys = task name,
        # values = data frame returned by said tasks
        primary_results = dict()

        # Convert Minor to Major
        primary_results[TaskNames.CONVERT_MINOR_TO_MAJOR] = cls.convert_minor_to_major(
            source_frame=source_frame
        )

        # Convert Datetime
        primary_results[TaskNames.CONVERT_DATE_TIME] = cls.convert_date_time(
            source_frame=source_frame
        )

        # Map Static
        primary_results[TaskNames.MAP_STATIC] = cls.map_static(
            source_frame=source_frame
        )

        # Map Attribute
        primary_results[TaskNames.MAP_ATTRIBUTE] = cls.map_attribute(
            source_frame=source_frame,
            logger=logger,
            auditor=auditor,
        )

        # Map Value
        primary_results[TaskNames.MAP_VALUE] = cls.map_value(
            source_frame=source_frame,
            logger=logger,
            auditor=auditor,
        )

        # Map Conditional
        map_conditional_source_frame = pd.concat(
            [
                source_frame,
                primary_results[TaskNames.CONVERT_DATE_TIME],
                primary_results[TaskNames.MAP_STATIC],
                primary_results[TaskNames.MAP_ATTRIBUTE],
                primary_results[TaskNames.MAP_VALUE],
            ],
            axis=1,
        )
        primary_results[TaskNames.MAP_CONDITIONAL] = cls.map_conditional(
            source_frame=map_conditional_source_frame
        )

        # Party Identifiers
        party_identifiers_source_frame = pd.concat(
            [
                primary_results[TaskNames.MAP_ATTRIBUTE],
                primary_results[TaskNames.MAP_VALUE],
                primary_results[TaskNames.MAP_CONDITIONAL],
            ],
            axis=1,
        )
        primary_results[TaskNames.PARTY_IDENTIFIERS] = cls.party_identifiers(
            source_frame=party_identifiers_source_frame
        )

        # Instrument Identifiers
        primary_results[TaskNames.INSTRUMENT_IDENTIFIERS] = cls.instrument_identifiers(
            source_frame=source_frame, auditor=auditor
        )

        # Merge Market Identifiers
        market_identifier_source_frame = pd.concat(
            [
                primary_results[TaskNames.INSTRUMENT_IDENTIFIERS],
                primary_results[TaskNames.PARTY_IDENTIFIERS],
            ],
            axis=1,
        )

        primary_results[
            TaskNames.MERGE_MARKET_IDENTIFIERS
        ] = cls.merge_market_identifiers(source_frame=market_identifier_source_frame)

        # Aux Map Attributes
        aux_map_attributes_source_frame = pd.concat(
            [
                primary_results[TaskNames.CONVERT_MINOR_TO_MAJOR],
                primary_results[TaskNames.MAP_VALUE],
                primary_results[TaskNames.MAP_CONDITIONAL],
                primary_results[TaskNames.MAP_STATIC],
            ],
            axis=1,
        )
        primary_results[TaskNames.AUX_MAP_ATTRIBUTE] = cls.aux_map_attribute(
            source_frame=aux_map_attributes_source_frame, logger=logger, auditor=auditor
        )

        # Concatenate all results
        for result in primary_results.values():
            target = pd.concat([target, result], axis=1)

        counterparty_id_not_null_mask = target[TempColumns.COUNTERPARTY_ID].notnull()
        target.loc[
            counterparty_id_not_null_mask, TempColumns.COUNTERPARTY_ID_WITHOUT_PREFIX
        ] = (
            target.loc[counterparty_id_not_null_mask, TempColumns.COUNTERPARTY_ID]
            .str.split(":")
            .str[1]
        )

        # Drop temp columns

        # Note: TempColumns.CLIENT_IDENTIFIER and TempColumns.NEWO_IN_FILE are not
        # dropped as they are used downstream
        temp_columns = [
            TempColumns.ARRIVAL_QUOTE_DATE,
            TempColumns.ARRIVAL_QUOTE_DATETIME,
            TempColumns.CONDITIONAL_COUNTERPARTY,
            TempColumns.COUNTERPARTY_ID,
            TempColumns.DEFAULT_COUNTERPARTY,
            TempColumns.EXEC_ENTITY_ID,
            TempColumns.FIRST_FILL_DATE,
            TempColumns.FIRST_FILL_DATETIME,
            TempColumns.ORDER_STATUS,
            TempColumns.VALIDITY_PERIOD,
        ]
        cols_mask = target.columns.isin(temp_columns)
        target = target.loc[:, ~cols_mask]
        return target

    @staticmethod
    def map_static(source_frame: pd.DataFrame) -> pd.DataFrame:
        """Static method which combines all the calls to MapStatic"""
        return pd.concat(
            [
                MapStatic.process(
                    source_frame=source_frame,
                    params=ParamsMapStatic(
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER, attribute=TempColumns.META_MODEL
                        ),
                        target_value="Order",
                    ),
                ),
                MapStatic.process(
                    source_frame=source_frame,
                    params=ParamsMapStatic(
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=TempColumns.META_MODEL,
                        ),
                        target_value="OrderState",
                    ),
                ),
                MapStatic.process(
                    source_frame=source_frame,
                    params=ParamsMapStatic(
                        target_attribute=OrderColumns.SOURCE_KEY,
                        from_env_var="SWARM_FILE_URL",
                    ),
                ),
                MapStatic.process(
                    source_frame=source_frame,
                    params=ParamsMapStatic(
                        target_attribute=OrderColumns.SOURCE_INDEX,
                        from_index=True,
                    ),
                ),
                MapStatic.process(
                    source_frame=source_frame,
                    params=ParamsMapStatic(
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                        ),
                        target_value="NEWO",
                    ),
                ),
                MapStatic.process(
                    source_frame=source_frame,
                    params=ParamsMapStatic(
                        target_attribute=OrderColumns.DATA_SOURCE_NAME,
                        target_value="FreeTrade LiquidMetrix",
                    ),
                ),
                MapStatic.process(
                    source_frame=source_frame,
                    params=ParamsMapStatic(
                        target_attribute=OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY,
                        target_value="AOTC",
                    ),
                ),
                MapStatic.process(
                    source_frame=source_frame,
                    params=ParamsMapStatic(
                        target_attribute=OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY,
                        target_value="AOTC",
                    ),
                ),
                MapStatic.process(
                    source_frame=source_frame,
                    params=ParamsMapStatic(
                        target_attribute=OrderColumns.TRANSACTION_DETAILS_VENUE,
                        target_value="XOFF",
                    ),
                ),
                MapStatic.process(
                    source_frame=source_frame,
                    params=ParamsMapStatic(
                        target_attribute=TempColumns.VALIDITY_PERIOD,
                        target_value="GTDV",
                    ),
                ),
                MapStatic.process(
                    source_frame=source_frame,
                    params=ParamsMapStatic(
                        target_attribute=TempColumns.CONDITIONAL_COUNTERPARTY,
                        target_value="lei:875500PNFZENVO437436",
                    ),
                ),
                # Populated hard coded-value as per
                # https://steeleye.atlassian.net/browse/EU-6487
                MapStatic.process(
                    source_frame=source_frame,
                    params=ParamsMapStatic(
                        target_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION,
                        target_value=PriceNotation.MONE.value,
                    ),
                ),
                MapStatic.process(
                    source_frame=source_frame,
                    params=ParamsMapStatic(
                        target_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION,
                        target_value=QuantityNotation.UNIT.value,
                    ),
                ),
            ],
            axis=1,
        )

    @staticmethod
    def convert_minor_to_major(source_frame: pd.DataFrame) -> pd.DataFrame:
        """Static method which combines all the calls to ConvertMinorToMajor"""
        return pd.concat(
            [
                ConvertMinorToMajor.process(
                    source_frame=source_frame,
                    params=ParamsConvertMinorToMajor(
                        source_ccy_attribute="Currency",
                        target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                    ),
                ),
                ConvertMinorToMajor.process(
                    source_frame=source_frame,
                    params=ParamsConvertMinorToMajor(
                        source_price_attribute="Price",
                        source_ccy_attribute="Currency",
                        target_price_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                        ),
                        cast_to="abs",
                    ),
                ),
            ],
            axis=1,
        )

    @staticmethod
    def map_attribute(
        source_frame: pd.DataFrame,
        logger=context.get("logger"),
        auditor: Auditor = None,
    ) -> pd.DataFrame:
        """Static method which combines all calls to MapAttribute"""
        return pd.concat(
            [
                # Order Identifiers
                MapAttribute.process(
                    source_frame=source_frame,
                    params=ParamsMapAttribute(
                        source_attribute="TraderID",
                        target_attribute=OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE,
                    ),
                    logger=logger,
                    auditor=auditor,
                ),
                MapAttribute.process(
                    source_frame=source_frame,
                    params=ParamsMapAttribute(
                        source_attribute="OrderRef",
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER, attribute=OrderColumns.ID
                        ),
                    ),
                    logger=logger,
                    auditor=auditor,
                ),
                MapAttribute.process(
                    source_frame=source_frame,
                    params=ParamsMapAttribute(
                        source_attribute="OrderRef",
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
                        ),
                        suffix="_FILL",
                    ),
                    logger=logger,
                    auditor=auditor,
                ),
                MapAttribute.process(
                    source_frame=source_frame,
                    params=ParamsMapAttribute(
                        source_attribute="OrderRef",
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE, attribute=OrderColumns.ID
                        ),
                    ),
                    logger=logger,
                    auditor=auditor,
                ),
                MapAttribute.process(
                    source_frame=source_frame,
                    params=ParamsMapAttribute(
                        source_attribute="OrderRef",
                        target_attribute=OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO,
                    ),
                    logger=logger,
                    auditor=auditor,
                ),
                MapAttribute.process(
                    source_frame=source_frame,
                    params=ParamsMapAttribute(
                        source_attribute="OrderRef",
                        target_attribute=OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE,
                    ),
                    logger=logger,
                    auditor=auditor,
                ),
                # Quantities
                MapAttribute.process(
                    source_frame=source_frame,
                    params=ParamsMapAttribute(
                        source_attribute="Quantity",
                        target_attribute=OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
                        cast_to="numeric.absolute",
                    ),
                    logger=logger,
                    auditor=auditor,
                ),
                MapAttribute.process(
                    source_frame=source_frame,
                    params=ParamsMapAttribute(
                        source_attribute="Quantity",
                        target_attribute=OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                        cast_to="numeric.absolute",
                    ),
                    logger=logger,
                    auditor=auditor,
                ),
                # Venue
                MapAttribute.process(
                    source_frame=source_frame,
                    params=ParamsMapAttribute(
                        source_attribute="Venue",
                        target_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                    ),
                    logger=logger,
                    auditor=auditor,
                ),
                # Fields for populating Party Identifiers
                MapAttribute.process(
                    source_frame=source_frame,
                    params=ParamsMapAttribute(
                        source_attribute="ParticipantCode",
                        target_attribute=TempColumns.EXEC_ENTITY_ID,
                        prefix="id:",
                    ),
                    logger=logger,
                    auditor=auditor,
                ),
                MapAttribute.process(
                    source_frame=source_frame,
                    params=ParamsMapAttribute(
                        source_attribute="TraderID",
                        target_attribute=TempColumns.CLIENT_IDENTIFIER,
                        prefix="id:",
                    ),
                    logger=logger,
                    auditor=auditor,
                ),
                MapAttribute.process(
                    source_frame=source_frame,
                    params=ParamsMapAttribute(
                        source_attribute="CounterpartyCode",
                        target_attribute=TempColumns.DEFAULT_COUNTERPARTY,
                        prefix="id:",
                    ),
                    logger=logger,
                    auditor=auditor,
                ),
            ],
            axis=1,
        )

    @staticmethod
    def convert_date_time(source_frame: pd.DataFrame) -> pd.DataFrame:
        """Static method which combines all calls to ConvertDateTime"""
        return pd.concat(
            [
                ConvertDatetime.process(
                    source_frame=source_frame,
                    params=ParamsConvertDatetime(
                        source_attribute="ArrivalTime_QuoteTime",
                        target_attribute=TempColumns.ARRIVAL_QUOTE_DATE,
                        convert_to="date",
                    ),
                ),
                ConvertDatetime.process(
                    source_frame=source_frame,
                    params=ParamsConvertDatetime(
                        source_attribute="ArrivalTime_QuoteTime",
                        target_attribute=TempColumns.ARRIVAL_QUOTE_DATETIME,
                        convert_to="datetime",
                    ),
                ),
                ConvertDatetime.process(
                    source_frame=source_frame,
                    params=ParamsConvertDatetime(
                        source_attribute="FirstFillTime_TradeTime",
                        target_attribute=TempColumns.FIRST_FILL_DATE,
                        convert_to="date",
                    ),
                ),
                ConvertDatetime.process(
                    source_frame=source_frame,
                    params=ParamsConvertDatetime(
                        source_attribute="FirstFillTime_TradeTime",
                        target_attribute=TempColumns.FIRST_FILL_DATETIME,
                        convert_to="datetime",
                    ),
                ),
            ],
            axis=1,
        )

    @staticmethod
    def map_value(
        source_frame: pd.DataFrame,
        logger=context.get("logger"),
        auditor: Auditor = None,
    ) -> pd.DataFrame:
        """Static method which combines all calls to MapValue"""
        return pd.concat(
            [
                MapValue.process(
                    source_frame=source_frame,
                    params=ParamsMapValue(
                        source_attribute="FlowType",
                        target_attribute=TempColumns.ORDER_STATUS,
                        case_insensitive=True,
                        value_map={"del": "CAME", "mod": "CHMO"},
                    ),
                    logger=logger,
                    auditor=auditor,
                ),
                MapValue.process(
                    source_frame=source_frame,
                    params=ParamsMapValue(
                        source_attribute="Side",
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER, attribute=OrderColumns.BUY_SELL
                        ),
                        case_insensitive=True,
                        value_map={
                            "b": "1",
                            "buy": "1",
                            "buy to cover": "3",
                            "buytocover": "3",
                            "cover": "3",
                            "s": "2",
                            "sell": "2",
                            "sell short": "5",
                            "short": "5",
                            "ss": "5",
                        },
                    ),
                    logger=logger,
                    auditor=auditor,
                ),
                MapValue.process(
                    source_frame=source_frame,
                    params=ParamsMapValue(
                        source_attribute="Side",
                        target_attribute=OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
                        case_insensitive=True,
                        value_map={
                            "b": "BUYI",
                            "buy": "BUYI",
                            "buy to cover": "BUYI",
                            "buytocover": "BUYI",
                            "cover": "BUYI",
                            "s": "SELL",
                            "sell": "SELL",
                            "sell short": "SELL",
                            "short": "SELL",
                            "ss": "SELL",
                        },
                    ),
                    logger=logger,
                    auditor=auditor,
                ),
                MapValue.process(
                    source_frame=source_frame,
                    params=ParamsMapValue(
                        source_attribute="OrderType",
                        target_attribute=OrderColumns.EXECUTION_DETAILS_ORDER_TYPE,
                        case_insensitive=True,
                        default_value="Market",
                        value_map={
                            "1": "Market",
                            "2": "Limit",
                            "3": "Stop/Loss",
                            "4": "Stop Limit",
                            "5": "Market On Close",
                            "6": "With or Without",
                            "7": "Limit Or Better",
                            "9": "On Basis",
                            "A": "On Close",
                            "B": "Limit On Close",
                            "C": "Forex Market",
                            "D": "Previously Quoted",
                            "E": "Previously Indicated",
                            "F": "Forex Limit",
                            "G": "Forex Swap",
                            "H": "Forex Previously Quoted",
                            "I": "Funari",
                            "J": "Market If Touched",
                            "K": "Market With Left Over as Limit",
                            "L": "Previous Fund Valuation Point",
                            "M": "Next Fund Valuation Point",
                            "P": "Pegged",
                            "Q": "Counter-order selection",
                            "R": "Stop on Bid or Offer",
                            "S": "Stop Limit on Bid or Offer",
                        },
                    ),
                    logger=logger,
                    auditor=auditor,
                ),
            ],
            axis=1,
        )

    @staticmethod
    def map_conditional(
        source_frame: pd.DataFrame,
    ) -> pd.DataFrame:
        """Static method which combines all calls to MapConditional"""
        return pd.concat(
            [
                MapConditional.process(
                    source_frame=source_frame,
                    params=ParamsMapConditional(
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                        ),
                        cases=[
                            {
                                "query": "index == index",
                                "attribute": TempColumns.ORDER_STATUS,
                            },
                            {
                                "query": "`IsAnOrder`.str.fullmatch('FALSE', case=False, na=False)",
                                "value": "FILL",
                            },
                        ],
                    ),
                ),
                MapConditional.process(
                    source_frame=source_frame,
                    params=ParamsMapConditional(
                        target_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                        cases=[
                            {
                                "query": "index == index",
                                "attribute": TempColumns.ARRIVAL_QUOTE_DATETIME,
                            },
                            {
                                "query": f"`{TempColumns.ARRIVAL_QUOTE_DATETIME}`.isnull()",
                                "attribute": TempColumns.FIRST_FILL_DATETIME,
                            },
                        ],
                    ),
                ),
                MapConditional.process(
                    source_frame=source_frame,
                    params=ParamsMapConditional(
                        target_attribute=OrderColumns.DATE,
                        cases=[
                            {
                                "query": "index == index",
                                "attribute": TempColumns.ARRIVAL_QUOTE_DATE,
                            },
                            {
                                "query": f"`{TempColumns.ARRIVAL_QUOTE_DATE}`.isnull()",
                                "attribute": TempColumns.FIRST_FILL_DATE,
                            },
                        ],
                    ),
                ),
                MapConditional.process(
                    source_frame=source_frame,
                    params=ParamsMapConditional(
                        target_attribute=OrderColumns.TIMESTAMPS_TRADING_DATE_TIME,
                        cases=[
                            {
                                "query": "index == index",
                                "attribute": TempColumns.FIRST_FILL_DATETIME,
                            },
                            {
                                "query": f"`{TempColumns.FIRST_FILL_DATETIME}`.isnull()",
                                "attribute": TempColumns.ARRIVAL_QUOTE_DATETIME,
                            },
                        ],
                    ),
                ),
                MapConditional.process(
                    source_frame=source_frame,
                    params=ParamsMapConditional(
                        target_attribute=TempColumns.COUNTERPARTY_ID,
                        cases=[
                            {
                                "query": "index == index",
                                "attribute": TempColumns.DEFAULT_COUNTERPARTY,
                            },
                            {
                                "query": "`IsAnOrder`.str.upper() == 'FALSE' & (`CounterpartyCode`.isnull())",
                                "attribute": TempColumns.CONDITIONAL_COUNTERPARTY,
                            },
                        ],
                    ),
                ),
                MapConditional.process(
                    source_frame=source_frame,
                    params=ParamsMapConditional(
                        target_attribute=TempColumns.NEWO_IN_FILE,
                        cases=[
                            {
                                "query": "`FlowType`.str.fullmatch('new', case=False, na=False)",
                                "value": True,
                            },
                            {
                                "query": "~`FlowType`.str.fullmatch('new', case=False, na=False)",
                                "value": False,
                            },
                        ],
                    ),
                ),
            ],
            axis=1,
        )

    @staticmethod
    def aux_map_attribute(
        source_frame: pd.DataFrame,
        logger=context.get("logger"),
        auditor: Auditor = None,
    ) -> pd.DataFrame:
        """Static method Aux Map Attribute which combines all calls to MapAttribute which
        depend on values from other tasks
        """
        return pd.concat(
            [
                MapAttribute.process(
                    source_frame=source_frame,
                    params=ParamsMapAttribute(
                        source_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER, attribute=OrderColumns.BUY_SELL
                        ),
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.BUY_SELL,
                        ),
                    ),
                    logger=logger,
                    auditor=auditor,
                ),
                MapAttribute.process(
                    source_frame=source_frame,
                    params=ParamsMapAttribute(
                        source_attribute=OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
                        target_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                    ),
                    logger=logger,
                    auditor=auditor,
                ),
                MapAttribute.process(
                    source_frame=source_frame,
                    params=ParamsMapAttribute(
                        source_attribute=OrderColumns.TIMESTAMPS_TRADING_DATE_TIME,
                        target_attribute=OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME,
                    ),
                    logger=logger,
                    auditor=auditor,
                ),
                MapAttribute.process(
                    source_frame=source_frame,
                    params=ParamsMapAttribute(
                        source_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                        target_attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
                    ),
                    logger=logger,
                    auditor=auditor,
                ),
                MapAttribute.process(
                    source_frame=source_frame,
                    params=ParamsMapAttribute(
                        source_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED,
                        ),
                    ),
                    logger=logger,
                    auditor=auditor,
                ),
                MapAttribute.process(
                    source_frame=source_frame,
                    params=ParamsMapAttribute(
                        source_attribute=OrderColumns.TIMESTAMPS_TRADING_DATE_TIME,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED,
                        ),
                    ),
                    logger=logger,
                    auditor=auditor,
                ),
                MapAttribute.process(
                    source_frame=source_frame,
                    params=ParamsMapAttribute(
                        source_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                        ),
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE,
                        ),
                    ),
                    logger=logger,
                    auditor=auditor,
                ),
                MapAttribute.process(
                    source_frame=source_frame,
                    params=ParamsMapAttribute(
                        source_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                        ),
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.TRANSACTION_DETAILS_PRICE,
                        ),
                    ),
                    logger=logger,
                    auditor=auditor,
                ),
                MapAttribute.process(
                    source_frame=source_frame,
                    params=ParamsMapAttribute(
                        source_attribute=TempColumns.VALIDITY_PERIOD,
                        target_attribute=OrderColumns.EXECUTION_DETAILS_VALIDITY_PERIOD,
                        cast_to="string.list",
                        list_delimiter=";",
                    ),
                    logger=logger,
                    auditor=auditor,
                ),
            ],
            axis=1,
        )

    @staticmethod
    def party_identifiers(source_frame: pd.DataFrame) -> pd.DataFrame:
        """Static method which creates party identifiers"""
        return GenericOrderPartyIdentifiers.process(
            source_frame=source_frame,
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                counterparty_identifier=TempColumns.COUNTERPARTY_ID,
                executing_entity_identifier=TempColumns.EXEC_ENTITY_ID,
                buyer_identifier=TempColumns.EXEC_ENTITY_ID,
                seller_identifier=TempColumns.COUNTERPARTY_ID,
                client_identifier=TempColumns.CLIENT_IDENTIFIER,
                buyer_decision_maker_identifier=TempColumns.EXEC_ENTITY_ID,
                seller_decision_maker_identifier=TempColumns.EXEC_ENTITY_ID,
                buy_sell_side_attribute=OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
                use_buy_mask_for_buyer_seller=True,
            ),
        )

    @staticmethod
    def instrument_identifiers(
        source_frame: pd.DataFrame, auditor: Auditor
    ) -> pd.DataFrame:
        """Static method which creates instrument identifiers"""
        return InstrumentIdentifiers.process(
            source_frame=source_frame,
            params=ParamsInstrumentIdentifiers(
                isin_attribute="ISIN",
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
            ),
            auditor=auditor,
        )

    @staticmethod
    def merge_market_identifiers(source_frame: pd.DataFrame) -> pd.DataFrame:
        """Static method which merges Party and instrument identifiers"""
        return MergeMarketIdentifiers.process(
            source_frame=source_frame,
            params=ParamsMergeMarketIdentifers(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                instrument_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )
