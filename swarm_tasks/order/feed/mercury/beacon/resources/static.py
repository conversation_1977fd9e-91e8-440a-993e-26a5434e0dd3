class MercuryBeaconColumns:
    """
    Columns present in the csv/excel Mercury Beacon template
    """

    EXPIRATION = "__expiry_date__"
    STRIKE = "__strike_price__"
    SYMBOL = "Symbol"
    TYPE = "Type"


class MercuryBeaconTempColumns:
    """
    Temp columns which won't go into the output
    """

    TEMP_CURRENCY = "temp_currency"
    TEMP_EXPIRY_DATE_MMMYYYY = "temp_expiry_date_mmmyyyy"


class InstrumentFallbackFields:

    # Derivative
    DERIVATIVE_EXPIRY_DATE = "derivative.expiryDate"
    DERIVATIVE_OPTION_EXERCISE_STYLE = "derivative.optionExerciseStyle"
    DERIVATIVE_OPTION_TYPE = "derivative.optionType"
    DERIVATIVE_PRICE_MULTIPLIER = "derivative.priceMultiplier"
    DERIVATIVE_STRIKE_PRICE = "derivative.strikePrice"
    DERIVATIVE_STRIKE_PRICE_CURRENCY = "derivative.strikePriceCurrency"

    # Ext
    EXT_AII_MIC = "ext.aii.mic"
    EXT_BEST_EX_ASSET_CLASS_MAIN = "ext.bestExAssetClassMain"
    EXT_BEST_EX_ASSET_CLASS_SUB = "ext.bestExAssetClassSub"
    EXT_PRICE_NOTATION = "ext.priceNotation"
    EXT_QUANTITY_NOTATION = "ext.quantityNotation"
    EXT_STRIKE_PRICE_TYPE = "ext.strikePriceType"

    # FX Derivatives
    FX_DERIVATIVES_NOTIONAL_CURRENCY_2 = "fxDerivatives.notionalCurrency2"

    # Non-nested fields
    INSTRUMENT_CLASSIFICATION = "instrumentClassification"
    INSTRUMENT_FULL_NAME = "instrumentFullName"
    IS_CREATED_THROUGH_FALLBACK = "isCreatedThroughFallback"
    NOTIONAL_CURRENCY_1 = "notionalCurrency1"
    TRADING_VENUE = "venue.tradingVenue"


class FallbackStaticValues:

    DEFAULTVENUE = "XXXX"
    MONE = "MONE"
    TYPE_FUTURES = "F"
    TYPE_FXCFD = "X"
    TYPE_OPTIONS = ["P", "C"]
    UNIT = "UNIT"
    USD = "USD"
