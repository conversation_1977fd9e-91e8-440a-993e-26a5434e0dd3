import re
from datetime import datetime
from pathlib import Path
from typing import Optional

import boto3
import pandas as pd
from botocore.exceptions import Client<PERSON>rror
from prefect import context
from prefect.engine.signals import FAIL
from prefect.engine.signals import SKIP
from pydantic import Field
from se_boltons.decorators import retry
from se_core_tasks.core.core_dataclasses import S3Action
from se_core_tasks.core.core_dataclasses import S3File
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseResources
from swarm.task.transform.base import BaseTask

from swarm_tasks.utilities.s3 import check_file_timestamps
from swarm_tasks.utilities.s3 import check_pair_file_on_s3


class Params(BaseParams):
    arc_events_file_name_pattern: str = Field(
        r"ARC_EVENTS-Exchange_Order-(?P<src_date>\d{8})-\w+.csv",
        description="Regex pattern for ARC_EVENTS file name.",
    )
    source_s3_key_prefix: str = Field(
        "flows/order-feed-fidessa-controller",
        description="S3 key prefix of source file.",
    )
    target_s3_key_prefix: str = Field(
        "flows/order-feed-fidessa-processor",
        description="S3 key prefix of target file.",
    )


class TargetColumns:
    ARC_EVENTS_FILE_PATH = "ARC_EVENTS_FILE_PATH"
    MARKET_ORDER_FILE_PATH = "MARKET_ORDER_FILE_PATH"
    REALM = "REALM"


ARC_EVENTS = "ARC_EVENTS"
MARKET_ORDER = "MARKET_ORDER"
SRC_DATE = "src_date"
TARGET_FILE_EXT = ".csv"


class FidessaController(BaseTask):
    """
    For a given source file_url, checks whether its pair file is already present on s3
    and then creates a csv file which has references to both source and pair files(
    ARC_EVENT and MARKET_ORDER) and places it on the s3 path for the flow
    order-feed-fidessa.
    """

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        file_url: str = None,
        resources: Optional[BaseResources] = None,
        source_frame: pd.DataFrame = None,
        **kwargs,
    ) -> S3File:

        s3 = boto3.resource("s3")
        source_file_key = f"{params.source_s3_key_prefix}/{file_url.split('/')[-1]}"
        if not any(
            prefix in source_file_key.upper() for prefix in [ARC_EVENTS, MARKET_ORDER]
        ):
            raise FAIL(
                f"File name does not contain either {ARC_EVENTS} or {MARKET_ORDER}"
            )
        pair_file_key = None
        try:
            # derive pair_file_key
            pair_file_key = self._get_pair_file_key(
                s3=s3, file_url=file_url, params=params
            )
            # Create a full S3 file_url from pair_file_key
            # (done this way to preserve backward compatibility)
            full_s3_pair_file_key = (
                f"s3://{Settings.realm}/{pair_file_key}"
                if Settings.realm not in pair_file_key
                else pair_file_key
            )

            # check if pair_file exists on s3
            check_pair_file_on_s3(s3=s3, pair_file_key=pair_file_key)
            check_file_timestamps(
                file_url=file_url, pair_file_key=full_s3_pair_file_key
            )

            arc_events_file_key = None
            market_order_file_key = None
            if ARC_EVENTS in file_url.upper():
                arc_events_file_key = source_file_key
                market_order_file_key = pair_file_key
            elif MARKET_ORDER in file_url.upper():
                arc_events_file_key = pair_file_key
                market_order_file_key = source_file_key

            result = self.create_file_for_fidessa_flow(
                arc_events_file_key=arc_events_file_key,
                market_order_file_key=market_order_file_key,
                params=params,
            )
            return result
        except ClientError as e:
            raise SKIP(f"Pair file not found:{pair_file_key}. Info:{e}")
        except SKIP as e:
            raise SKIP(f"{e}")
        except Exception as e:
            raise FAIL(f"Error fetching pair file:{pair_file_key}.Error:{e}")

    def _get_pair_file_key(
        self, s3: boto3.resource, file_url: str, params: Params
    ) -> Optional[str]:
        """
        :param s3: s3 resource.
        :param file_url: source file_url.
        :param params: input params.
        :return: Optional[str] returns the s3 key of the pair file for the given source
        file.
        """
        file_name = file_url.split("/")[-1]
        s3.Bucket(Settings.realm)
        if ARC_EVENTS in file_name.upper():
            # get FILE file key using ARC_EVENTS FILE
            pat = re.compile(params.arc_events_file_name_pattern)
            m = pat.search(file_name)
            file_name_prefix = file_name[: m.span()[0]]
            date_part = None
            if m:
                date_part = m.groupdict().get(SRC_DATE)
            if date_part:
                key_prefix = f"{file_name_prefix}{MARKET_ORDER}-{date_part}"
                pair_file_key = self._pair_key_for_arc_events(
                    s3=s3, params=params, key_prefix=key_prefix
                )
                return pair_file_key

        elif MARKET_ORDER in file_name.upper():
            # get ARC_EVENTS file key using MARKET_ORDER FILE
            pair_file_key = self._pair_key_for_market_order(
                s3=s3, params=params, file_name=file_name
            )
            return pair_file_key

    @staticmethod
    def create_file_for_fidessa_flow(
        arc_events_file_key: str, market_order_file_key: str, params: Params
    ) -> S3File:
        """
        :param arc_events_file_key: str
        :param market_order_file_key: str
        :param params: input params
        :return: S3File object
        """
        realm = Settings.realm

        target = pd.DataFrame(
            index=[0],
            columns=[
                TargetColumns.ARC_EVENTS_FILE_PATH,
                TargetColumns.MARKET_ORDER_FILE_PATH,
                TargetColumns.REALM,
            ],
        )
        output_path: Path = context.swarm.get("targets_dir").joinpath(
            params.source_s3_key_prefix
        )
        output_path.mkdir(parents=True, exist_ok=True)
        output_path = output_path.joinpath(datetime.now().isoformat())
        target.loc[:, TargetColumns.ARC_EVENTS_FILE_PATH] = arc_events_file_key
        target.loc[:, TargetColumns.MARKET_ORDER_FILE_PATH] = market_order_file_key
        target.loc[:, TargetColumns.REALM] = realm
        target.to_csv(output_path, index=False)

        s3_key = (
            f"{params.target_s3_key_prefix}/{datetime.now().isoformat()}"
            f"{TARGET_FILE_EXT}"
        )
        result = S3File(
            file_path=output_path,
            bucket_name=realm,
            key_name=s3_key,
            action=S3Action.UPLOAD,
            bytes=output_path.stat().st_size,
        )
        return result

    @staticmethod
    @retry(exceptions=(SKIP,), backoff_factor=40.0, max_backoff=40, max_retry=4)
    def _pair_key_for_arc_events(
        s3: boto3.resource, params: Params, key_prefix: str
    ) -> str:
        key = None
        bucket = s3.Bucket(Settings.realm)
        objects = bucket.objects.filter(
            Prefix=f"{params.source_s3_key_prefix}/{key_prefix}"
        )
        for obj in objects:
            key = obj.key
            if key is not None:
                return key
        if not key:
            raise SKIP("Pair file s3_key not fetched for the source arc_events file")

    @staticmethod
    @retry(exceptions=(SKIP,), backoff_factor=40.0, max_backoff=40, max_retry=4)
    def _pair_key_for_market_order(
        s3: boto3.resource, params: Params, file_name: str
    ) -> str:
        key = None
        bucket = s3.Bucket(Settings.realm)
        file_name_prefix = re.search(
            pattern=f"(.*)?(?={MARKET_ORDER})", string=file_name
        ).group()
        objects = bucket.objects.filter(
            Prefix=f"{params.source_s3_key_prefix}/{file_name_prefix}{ARC_EVENTS}"
        )
        date_part = file_name[13 + len(file_name_prefix) : 21 + len(file_name_prefix)]
        for obj in objects:
            if date_part in obj.key:
                key = obj.key
                if key is not None:
                    return key
        if not key:
            raise SKIP("Pair file s3_key not found for the source market_order file")
