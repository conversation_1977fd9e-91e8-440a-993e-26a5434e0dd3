import numpy as np
import pandas as pd
from prefect import context
from prefect.engine import signals
from swarm.task.auditor import Auditor
from swarm.task.transform.base import TransformBaseTask


class TempColumns:
    TEMP_VERSION = "__temp_version__"


class SourceColumns:
    BUSINESS_TRANSACTION_DESCRIPTION = "Business_Transaction_Description"
    DISCRETIONARY_ORDER = "Discretionary_Order"
    EXCHANGE_TRADE_CODE = "Exchange_Trade_Code"
    STATE = "State"
    TRADE_ID = "Trade_Id"
    TRADE_TYPE = "Trade_Type"
    VERSION_NUMBER = "Version_Number"
    BOOK_ID = "Book_Id"


class FrontOfficeSkipLogic(TransformBaseTask):
    """
    This task contains all the skip logic for Front Office Trades, and returns a data frame without
    the skipped rows.

    Record which are skipped:

    1. if there are two records which have the same value in [Trade_Id] and one record [State] = "I" with a
    [Version_Number] that is less than the [Version_Number] of the related [State] = "C"
    2. if [State] == "C"
    3. if [Trade_Type] == "AGTD"
    4. if [Trade_Type] == "BLOCK" AND [Business_Transaction_Description] != "Client trade"
    5. if [Exchange_Trade_Code] is more than 4 (>=) characters and [Discretionary_Order] is empty
    6. if [Business_Transaction_Description] == "Booking" or "Order warehousing transfer"
    """

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        **kwargs,
    ) -> pd.DataFrame:
        return self.process(
            source_frame=source_frame,
            logger=self.logger,
            auditor=self.auditor,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame = None,
        logger=context.get("logger"),
        auditor: Auditor = None,
    ) -> pd.DataFrame:

        if source_frame.empty:
            raise signals.SKIP("Source frame empty")

        df = source_frame.copy()

        # Skip_condition 1
        # Step 1: Make sure records for a trade id are sorted by version number
        df = df.sort_values(by=[SourceColumns.TRADE_ID, SourceColumns.VERSION_NUMBER])
        # Step 2: create a temp series with values for State='C' populated from Version_Number
        # . For cases where State != 'C', set the value = pd.NA
        c_series = df.loc[:, SourceColumns.VERSION_NUMBER].where(
            df[SourceColumns.STATE].str.fullmatch("C", case=False, na=False)
        )
        # Step 3: Back-fill the Version_Number for cases where State = 'I' from the next C's
        # Version_Number. Is which are not followed by Cs will be pd.NA, and should not be skipped.
        # Other Is are skipped.
        df[TempColumns.TEMP_VERSION] = np.where(
            df[SourceColumns.STATE].str.fullmatch("I", case=False, na=False),
            c_series.astype("object").groupby(df[SourceColumns.TRADE_ID]).bfill(),
            df[SourceColumns.VERSION_NUMBER],
        )

        no_skip_condition_1 = f"(`{TempColumns.TEMP_VERSION}`.isnull())"
        no_skip_condition_2 = (
            f"~(`{SourceColumns.STATE}`.str.fullmatch('C', case=False, na=False))"
        )
        no_skip_condition_3 = f"~(`{SourceColumns.TRADE_TYPE}`.str.fullmatch('AGTD', case=False, na=False))"
        no_skip_condition_4 = f"(~(`{SourceColumns.TRADE_TYPE}`.str.fullmatch('BLOCK', case=False, na=False)) | (`{SourceColumns.BUSINESS_TRANSACTION_DESCRIPTION}`.str.fullmatch('Client trade', case=False, na=False)))"
        no_skip_condition_5 = f"~(`{SourceColumns.BUSINESS_TRANSACTION_DESCRIPTION}`.str.fullmatch('Booking|Order warehousing transfer', case=False, na=False))"  # noqa: E501
        no_skip_condition_6 = f"~(`{SourceColumns.BOOK_ID}`.str.fullmatch('NETTNG', case=False, na=False))"

        query = " & ".join(
            [
                no_skip_condition_1,
                no_skip_condition_2,
                no_skip_condition_3,
                no_skip_condition_4,
                no_skip_condition_5,
                no_skip_condition_6,
            ]
        )
        data = df.query(query, engine="python")
        if data.empty:
            skip_message = "All rows were skipped"
            auditor.add(skip_message)
            raise signals.SKIP(skip_message)

        skipped_records = source_frame.shape[0] - data.shape[0]
        if skipped_records > 0:
            skip_message = f"Skipped records: {skipped_records}"
            logger.info(skip_message)
            auditor.add(skip_message)

        # Sort index to undo the previous sort_values and get back the original ordering
        data = data.sort_index()
        data = data.drop(columns=[TempColumns.TEMP_VERSION], axis=1)
        return data
