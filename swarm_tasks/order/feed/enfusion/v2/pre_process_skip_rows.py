import os
from dataclasses import dataclass
from pathlib import Path
from typing import Dict
from typing import List
from typing import Union

import fsspec
from prefect import context
from pydantic import Field
from swarm.task.base import BaseParams
from swarm.task.io.read.result import ExtractPathResult
from swarm.task.transform.base import BaseTask

from swarm_tasks.order.feed.enfusion.v2.static import EventTypes
from swarm_tasks.order.feed.enfusion.v2.static import FileTypes
from swarm_tasks.utilities.task_utils import match_enum_value


class Params(BaseParams):
    delimiter: str = Field(
        ",", description="Delimiter to be used when parsing the csv file"
    )


class RawEnfusionV2Columns:
    ORDER_ID = "Order Id"
    EVENT_TYPE = "Event Type"
    EXEC_ID = "Exec ID"
    EXEC_REF_ID = "Exec Ref ID"


@dataclass
class EnfusionIdValues:
    raw_index: int
    event_type: str
    exec_id: str
    exec_ref_id: str


class PreProcessSkipRows(BaseTask):
    """
    This task is invoked for the custom skip logic to be used in enfusion execution files.
    It identifies file lines to skip based on the logic below and returns a list of the
    row indexes to skip

    Logic Overview:
    For Execution files group all records based on [Order Id]
    in any of these groups, where the [Exec Ref Id] of a [Event Type] == Cancel matches the
        [Exec Id] of a [Event Type] == New, remove both rows

    Specs:
    https://steeleye.atlassian.net/wiki/spaces/IN/pages/2267938859/Order+Enfusion+V2#Skip-Logic
    """

    params_class = Params

    def execute(
        self,
        extractor_result: Union[ExtractPathResult, str] = None,
        params: Params = None,
        **kwargs,
    ) -> List[int]:

        return self.process(
            extractor_result=extractor_result, params=params, logger=self.logger
        )

    @classmethod
    def process(
        cls,
        extractor_result: Union[ExtractPathResult, str] = None,
        params: Params = None,
        logger=context.get("logger"),
    ) -> List[int]:
        file_name = Path(os.getenv("SWARM_FILE_URL")).name
        file_type = match_enum_value(file_name, FileTypes)
        if file_type == FileTypes.EXECUTIONS:
            target_col_indexes = cls.get_target_indexes(extractor_result, params)

            values_dict = cls.generate_values_per_order_id(
                extractor_result, target_col_indexes, params
            )

            all_indexes_to_skip = cls.get_all_indexes_to_skip(values_dict)
            if all_indexes_to_skip:
                logger.info(f"Found indexes to skip: {sorted(all_indexes_to_skip)}")
            return all_indexes_to_skip
        else:
            return list()

    @staticmethod
    def get_target_indexes(
        extractor_result: ExtractPathResult, params: Params
    ) -> Dict[str, int]:
        """
        Splits the first line of the text file and creates a dictionary with the
            pretended columns as keys and the found indexes as its values
        :param extractor_result: path to csv file
        :param params: task params
        :return: dictionary with the indexes for the pretended columns
        """
        with fsspec.open(extractor_result.path.as_posix(), "rt") as f:
            first_line = f.readline().split(params.delimiter)
        target_columns = [
            RawEnfusionV2Columns.ORDER_ID,
            RawEnfusionV2Columns.EVENT_TYPE,
            RawEnfusionV2Columns.EXEC_ID,
            RawEnfusionV2Columns.EXEC_REF_ID,
        ]
        return {
            col: first_line.index(col) for col in target_columns if col in first_line
        }

    @staticmethod
    def generate_values_per_order_id(
        extractor_result: ExtractPathResult,
        target_col_indexes: Dict[str, int],
        params: Params,
    ) -> Dict[str, List[EnfusionIdValues]]:
        """
        Iterates over the whole file, skipping the first line, and saving the pretended values
            into a dictionary with Order Id as key and EnfusionIdValues as values
        :param extractor_result: path to csv file
        :param target_col_indexes: dictionary with the indexes for the pretended columns
        :param params: task params
        :return: dictionary of EnfusionIdValues per Order Id
        """
        values_dict = dict()
        with fsspec.open(extractor_result.path.as_posix(), "rt") as enfusion_file:
            count = 0  # pandas read_csv skiprows is zero-indexed
            for line in enfusion_file:
                if count == 0:
                    count += 1
                    continue

                values = line.split(params.delimiter)

                order_id_index = target_col_indexes.get(RawEnfusionV2Columns.ORDER_ID)
                if order_id_index is not None:
                    order_id = values[order_id_index]

                    if not values_dict.get(order_id):
                        values_dict[order_id] = list()

                    values_dict[order_id].append(
                        EnfusionIdValues(
                            raw_index=count,
                            event_type=values[
                                target_col_indexes[RawEnfusionV2Columns.EVENT_TYPE]
                            ]
                            if target_col_indexes.get(RawEnfusionV2Columns.EVENT_TYPE)
                            else "",
                            exec_id=values[
                                target_col_indexes[RawEnfusionV2Columns.EXEC_ID]
                            ]
                            if target_col_indexes.get(RawEnfusionV2Columns.EXEC_ID)
                            else "",
                            exec_ref_id=values[
                                target_col_indexes[RawEnfusionV2Columns.EXEC_REF_ID]
                            ]
                            if target_col_indexes.get(RawEnfusionV2Columns.EXEC_REF_ID)
                            else "",
                        )
                    )
                count += 1

        return values_dict

    @classmethod
    def get_all_indexes_to_skip(
        cls, values_dict: Dict[str, List[EnfusionIdValues]]
    ) -> List[int]:
        """
        Iterates over all id values for an order id, whenever it finds a Cancel,
            looks to find all linked orders whose link chain ends in a New order.
        Linked in this case means where the Exec Ref Id matches the Exec Id of another order
        :param values_dict: dictionary of EnfusionIdValues per Order Id
        :return: list of indexes to be removed
        """
        all_indexes_to_skip = []
        for order_values in values_dict:

            list_of_values = values_dict.get(order_values)
            instance_dict = {inst.exec_id: inst for inst in list_of_values}

            order_indexes_to_skip = []
            for inst in list_of_values:
                # checks if it's a cancel order and if the reference id exists on the orders
                if (
                    inst.event_type == EventTypes.CANCEL
                    and inst.exec_ref_id in instance_dict
                ):
                    linked_inst = instance_dict[inst.exec_ref_id]
                    # if it links with a New, add the indexes of the orders to the list
                    if linked_inst.event_type == EventTypes.NEW:
                        order_indexes_to_skip.extend(
                            [inst.raw_index, linked_inst.raw_index]
                        )
                    # if it links with a Correct, finds all Correct linked in the same chain
                    elif linked_inst.event_type == EventTypes.CORRECT:
                        correct_indices = []
                        while (
                            linked_inst.event_type == EventTypes.CORRECT
                            and linked_inst.exec_ref_id in instance_dict
                        ):
                            correct_indices.append(linked_inst.raw_index)
                            # using .pop instead of .get as a safety precaution against an unexpected loop
                            linked_inst = instance_dict.pop(linked_inst.exec_ref_id)
                        # if the chain ends with a New, add all indexes to the list
                        if linked_inst.event_type == EventTypes.NEW:
                            order_indexes_to_skip.extend(
                                [inst.raw_index, linked_inst.raw_index]
                                + correct_indices
                            )

            all_indexes_to_skip.extend(order_indexes_to_skip)

        return all_indexes_to_skip
