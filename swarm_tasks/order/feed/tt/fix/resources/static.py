"""
Exchange map used for populating the `transactionDetails.ultimateVenue`
field for tenants that use the TT FIX protocol
"""

EXCHANGE_MAP = {
    "AJ": "A2XX",
    "ACE": "ACEX",
    "AFE": "AFET",
    "UA": "AMXO",
    "AMX": "AMXO",
    "UA__1": "AMXO",
    "UA__2": "AMXO",
    "PF": "APXL",
    "QE": "AQEU",
    "QX": "AQXE",
    "UP": "ARCO",
    "UP__1": "ARCX",
    "AQ": "ASXP",
    "EB": "BATE",
    "UF": "BATO",
    "UF__1": "BATS",
    "VY": "BATY",
    "RB": "BCSE",
    "E2": "BEUE",
    "BFX": "BFEX",
    "MU": "BIVA",
    "B3": "BLOX",
    "SI": "BMFM",
    "SI__1": "BMFM",
    "SIB": "BMFM",
    "B1": "BMTF",
    "XB": "BOAT",
    "XV": "BOTC",
    "AZ": "BSEX",
    "B4": "BTFE",
    "VS": "BVCA",
    "BMF": "BVMF",
    "BOV": "BVMF",
    "BOV__1": "BVMF",
    "BZ": "BVMF",
    "CB2": "C2OX",
    "UE": "C2OX",
    "X2": "CAPA",
    "UO": "CBSX",
    "CFF": "CCFX",
    "I2": "CEUX",
    "AH": "CHIA",
    "TX": "CHIC",
    "JI": "CHIJ",
    "IC": "CHIX",
    "IX": "CHIX",
    "DGC": "DGCX",
    "DFX": "DIFX",
    "DU": "DIFX",
    "DU__1": "DIFX",
    "DU__2": "DIFX",
    "DD": "DKTC",
    "QD": "DSMD",
    "DME": "DUMX",
    "NDM": "DUMX",
    "X1": "ECEU",
    "VJ": "EDGA",
    "UG": "EDGO",
    "UG__1": "EDGO",
    "VK": "EDGX",
    "AA": "ENAX",
    "ERI": "ERIS",
    "TE": "ETLX",
    "GW": "EUWX",
    "FPL": "FISH",
    "DF": "FNDK",
    "FF": "FNFI",
    "RF": "FNIS",
    "SF": "FNSE",
    "GBT": "GBOT",
    "UI": "GMNI",
    "NYE": "GREE",
    "TL": "GSXL",
    "HKM": "HKME",
    "HM": "HMTF",
    "XT": "HOTC",
    "VH": "HSTC",
    "ICD": "ICDX",
    "UI__1": "ICEL",
    "INX": "ICXL",
    "VF": "IEXG",
    "WCE": "IFCA",
    "ICE": "IFEU",
    "ICF": "IFLL",
    "LN": "IFLL",
    "LN__1": "IFLO",
    "LN__2": "IFLO",
    "ISG": "IFSG",
    "NYB": "IMAG",
    "NYF": "IMEQ",
    "FNX": "IMFX",
    "L1": "LEUE",
    "TK": "LICA",
    "L3": "LIQU",
    "DG": "LYNX",
    "MX": "MALX",
    "TR": "MATN",
    "UJ": "MCRY",
    "IG": "MCXX",
    "IG__1": "MCXX",
    "IG__2": "MCXX",
    "MSX": "MCXX",
    "MQ": "MESQ",
    "BD": "MFOX",
    "RX": "MISX",
    "UN": "MPRL",
    "M0": "MSAX",
    "IM": "MTAA",
    "IF": "MTAH",
    "GZ": "MUND",
    "N2X": "N2EX",
    "NJ": "NASX",
    "PMX": "NCEL",
    "EDX": "NDEX",
    "QF": "NEOE",
    "PZ": "NEXX",
    "EI": "NILX",
    "NPE": "NORX",
    "NS": "NOTC",
    "NZX": "NZFX",
    "TG": "OMGA",
    "OMP": "OMIP",
    "UV": "OOTC",
    "PQ": "OTCM",
    "QU": "PFTQ",
    "UZ": "PFTS",
    "PG": "PLUS",
    "CJ": "PURE",
    "TT": "ROCO",
    "RFX": "ROFX",
    "RW": "ROTC",
    "MCX": "RTSX",
    "RM": "RTSX",
    "RR": "RTSX",
    "RTS": "RTSX",
    "RU": "RTSX",
    "RT": "RUSX",
    "JE": "SBIJ",
    "JW": "SBIU",
    "S1": "SGMX",
    "A0": "SHAR",
    "H1": "SHSC",
    "SI__2": "SIMV",
    "SME": "SMEX",
    "SPX": "SPIM",
    "H2": "SZSC",
    "TB": "TFEX",
    "TEF": "TFEX",
    "TEF__1": "TFEX",
    "MT": "TOMX",
    "T1": "TQEX",
    "XZ": "TREA",
    "T2": "TREU",
    "SZ": "TRPX",
    "TQD": "TRQD",
    "TQ": "TRQX",
    "X9": "TWEA",
    "WT": "TWEM",
    "UK": "UKEX",
    "UKR": "UKEX",
    "PW": "WDER",
    "WSE": "WDER",
    "WSE__1": "WDER",
    "WX": "WDER",
    "PD": "WMTF",
    "AD": "XADE",
    "ADE": "XADE",
    "ADE__1": "XADE",
    "AP": "XADE",
    "GA": "XADE",
    "UD": "XADF",
    "DH": "XADS",
    "AG": "XALG",
    "JR": "XAMM",
    "NA": "XAMS",
    "NA__1": "XAMS",
    "AY": "XARM",
    "UA__3": "XASE",
    "ASX": "XASX",
    "ASX__1": "XASX",
    "AT": "XASX",
    "AU": "XASX",
    "GA__1": "XATH",
    "TN": "XATS",
    "BM": "XBAA",
    "BA": "XBAB",
    "BI": "XBAH",
    "SB": "XBAR",
    "CE": "XBCL",
    "HO": "XBCV",
    "AM": "XBCX",
    "BH": "XBDA",
    "AX": "XBDV",
    "SG": "XBEL",
    "GB": "XBER",
    "LB": "XBEY",
    "SO": "XBIL",
    "TB__1": "XBKK",
    "BK": "XBLB",
    "BNF": "XBLN",
    "CR": "XBNV",
    "CB": "XBOG",
    "CDE": "XBOG",
    "CX": "XBOG",
    "DVX": "XBOG",
    "VB": "XBOL",
    "BBX": "XBOM",
    "BBX__1": "XBOM",
    "IB": "XBOM",
    "IB__1": "XBOM",
    "IB__2": "XBOM",
    "UB": "XBOS",
    "UB__1": "XBOS",
    "BG": "XBOT",
    "SK": "XBRA",
    "BB": "XBRD",
    "BFO": "XBRD",
    "BW": "XBRN",
    "SR": "XBRN",
    "BB__1": "XBRU",
    "BFO__1": "XBRU",
    "IA": "XBRV",
    "BTS": "XBSD",
    "RO": "XBSD",
    "RE": "XBSE",
    "BSE": "XBUD",
    "BSE__1": "XBUD",
    "HB": "XBUD",
    "HB__1": "XBUD",
    "HB__2": "XBUD",
    "AF": "XBUE",
    "BU": "XBUL",
    "VR": "XBVC",
    "MZ": "XBVM",
    "UT": "XBXO",
    "EC": "XCAI",
    "MC": "XCAS",
    "KY": "XCAY",
    "CBF": "XCBF",
    "CBO": "XCBO",
    "UO__1": "XCBO",
    "UO__2": "XCBO",
    "CBT": "XCBT",
    "CMX": "XCEC",
    "CSE": "XCHG",
    "UM": "XCHI",
    "GU": "XCIE",
    "UC": "XCIS",
    "CM": "XCME",
    "CME": "XCME",
    "CF": "XCNQ",
    "SL": "XCOL",
    "COP": "XCSE",
    "COP__1": "XCSE",
    "DC": "XCSE",
    "DC__1": "XCSE",
    "DC__2": "XCSE",
    "KH": "XCSX",
    "DS": "XCX2",
    "DK": "XCXD",
    "CY": "XCYS",
    "TZ": "XDAR",
    "DCE": "XDCE",
    "IH": "XDES",
    "DB": "XDFM",
    "BD__1": "XDHA",
    "MIL": "XDMI",
    "MIL__1": "XDMI",
    "SY": "XDSE",
    "DE": "XDSX",
    "ID": "XDUB",
    "GD": "XDUS",
    "YC": "XECM",
    "EK": "XECS",
    "EEE": "XEEE",
    "EPD": "XEEE",
    "ELX": "XELX",
    "MD": "XEMD",
    "MDX": "XEMD",
    "MDX__1": "XEMD",
    "MM": "XEMD",
    "BQ": "XEQT",
    "GY": "XETR",
    "EOE": "XEUE",
    "EOE__1": "XEUE",
    "NA__2": "XEUE",
    "EUX": "XEUR",
    "EUX__1": "XEUR",
    "EUZ": "XEUR",
    "GR": "XEUR",
    "GR__1": "XEUR",
    "SW": "XEUR",
    "JF": "XFKA",
    "GF": "XFRA",
    "TH": "XGAT",
    "GN": "XGHA",
    "GME": "XGME",
    "GG": "XGSE",
    "GL": "XGTG",
    "EG": "XGUA",
    "GH": "XHAM",
    "GI": "XHAN",
    "FH": "XHEL",
    "FH__1": "XHEL",
    "FH__2": "XHEL",
    "HEX": "XHEL",
    "HEX__1": "XHEL",
    "HK": "XHKF",
    "HK__1": "XHKF",
    "HKG": "XHKF",
    "HKG__1": "XHKF",
    "HK__2": "XHKG",
    "VU": "XHNX",
    "IR": "XICE",
    "IR__1": "XICE",
    "IR__2": "XICE",
    "TW": "XICX",
    "IDX": "XIDX",
    "IJ": "XIDX",
    "IJ__1": "XIDX",
    "MCI": "XIMC",
    "IQ": "XIQS",
    "UL": "XISA",
    "UL__1": "XISA",
    "UL__2": "XISE",
    "IGE": "XIST",
    "TI": "XIST",
    "TI__1": "XIST",
    "TI__2": "XIST",
    "ISE": "XISX",
    "JA": "XJAM",
    "JQ": "XJAS",
    "SJ": "XJSE",
    "ODE": "XKAC",
    "PK": "XKAR",
    "PK__1": "XKAR",
    "KZ": "XKAZ",
    "KCB": "XKBT",
    "KF": "XKFB",
    "KFE": "XKFE",
    "KS": "XKFE",
    "KS__1": "XKLS",
    "MDE": "XKLS",
    "MDE__1": "XKLS",
    "MK": "XKLS",
    "MK__1": "XKLS",
    "KE": "XKON",
    "KQ": "XKOS",
    "KFE__1": "XKRX",
    "KP": "XKRX",
    "KB": "XKSE",
    "KK": "XKUW",
    "LS": "XLAO",
    "LD": "XLDN",
    "LIF": "XLIF",
    "PE": "XLIM",
    "BDP": "XLIS",
    "PL": "XLIS",
    "PL__1": "XLIS",
    "LH": "XLIT",
    "SV": "XLJU",
    "LME": "XLME",
    "LMF": "XLME",
    "LMS": "XLME",
    "DX": "XLOD",
    "DX__1": "XLOD",
    "LN__3": "XLON",
    "LY": "XLSM",
    "ZL": "XLUS",
    "LX": "XLUX",
    "MAE": "XMAB",
    "SN": "XMAD",
    "MS": "XMAE",
    "MV": "XMAL",
    "NC": "XMAN",
    "EOP": "XMAT",
    "MP": "XMAU",
    "AR": "XMEV",
    "SBA": "XMEV",
    "SBA__1": "XMEV",
    "MF": "XMEX",
    "MGE": "XMGE",
    "IM__1": "XMIL",
    "IM__2": "XMIL",
    "UM__1": "XMIO",
    "UM__2": "XMIO",
    "UY": "XMNT",
    "ME": "XMNX",
    "MCE": "XMOC",
    "CM__1": "XMOD",
    "MS__1": "XMOD",
    "MSE": "XMOD",
    "MSE__1": "XMOD",
    "MB": "XMOL",
    "EOP__1": "XMON",
    "FP": "XMON",
    "MFP": "XMPW",
    "MFM": "XMRV",
    "SM": "XMRV",
    "SM__1": "XMRV",
    "MW": "XMSW",
    "MBA": "XMTB",
    "GM": "XMUN",
    "OM": "XMUS",
    "KN": "XNAI",
    "NW": "XNAM",
    "UQ": "XNAS",
    "NDX": "XNCD",
    "UR": "XNCM",
    "AO": "XNEC",
    "NK": "XNEP",
    "NG": "XNGM",
    "JN": "XNGO",
    "UW": "XNGS",
    "UT__1": "XNIM",
    "NYL": "XNLI",
    "UQ__1": "XNMS",
    "NL": "XNSA",
    "IS": "XNSE",
    "IS__1": "XNSE",
    "IS__2": "XNSE",
    "NSE": "XNSE",
    "NSE__1": "XNSE",
    "NCP": "XNYE",
    "NYM": "XNYM",
    "UN__1": "XNYS",
    "NZ": "XNZE",
    "NZ__1": "XNZE",
    "OC": "XOCH",
    "OCG": "XOCH",
    "XO": "XOPV",
    "JO": "XOSE",
    "OSE": "XOSE",
    "JO__1": "XOSE",
    "OSE__1": "XOSE",
    "NO": "XOSL",
    "NO__1": "XOSL",
    "NO__2": "XOSL",
    "OBX": "XOSL",
    "OBX__1": "XOSL",
    "UU": "XOTC",
    "PS": "XPAE",
    "EOP__2": "XPAR",
    "FP__1": "XPAR",
    "NFE": "XPBT",
    "PHL": "XPHL",
    "UX": "XPHL",
    "UX__1": "XPHL",
    "PM": "XPHS",
    "PB": "XPOM",
    "PO": "XPOS",
    "PNX": "XPOW",
    "CK": "XPRA",
    "CP": "XPRA",
    "UX__2": "XPSX",
    "PP": "XPTY",
    "PXE": "XPXE",
    "SX": "XQMH",
    "QT": "XQTX",
    "EQ": "XQUI",
    "RQ": "XRAS",
    "LG": "XRIS",
    "RC": "XRMZ",
    "SA": "XSAF",
    "SAF": "XSAF",
    "SJ__1": "XSAF",
    "JS": "XSAP",
    "KA": "XSAT",
    "AB": "XSAU",
    "JU": "XSBI",
    "C2": "XSEC",
    "SP": "XSES",
    "SP__1": "XSES",
    "SP__2": "XSES",
    "CD/SF": "XSFE",
    "SF__1": "XSFE",
    "SFE": "XSFE",
    "SHF": "XSGE",
    "CC": "XSGO",
    "CS": "XSHE",
    "CG": "XSHG",
    "SGX": "XSIM",
    "XE": "XSMP",
    "FS": "XSPS",
    "MFA": "XSRM",
    "C1": "XSSC",
    "BT": "XSSE",
    "VM": "XSTC",
    "ZU": "XSTE",
    "PMI": "XSTO",
    "SS": "XSTO",
    "SS__1": "XSTO",
    "SS__2": "XSTO",
    "SSE": "XSTO",
    "SSE__1": "XSTO",
    "GS": "XSTU",
    "EL": "XSVA",
    "SD": "XSWA",
    "SE": "XSWX",
    "IT": "XTAE",
    "IT__1": "XTAE",
    "TAV": "XTAE",
    "TAV__1": "XTAE",
    "FTX": "XTAF",
    "FTX__1": "XTAF",
    "TT__1": "XTAF",
    "TT__2": "XTAI",
    "ET": "XTAL",
    "JG": "XTAM",
    "IE": "XTEH",
    "TFX": "XTFF",
    "AL": "XTIR",
    "JT": "XTKS",
    "JT__1": "XTKS",
    "TSE": "XTKS",
    "TSE__1": "XTKS",
    "TCM": "XTKT",
    "TP": "XTRN",
    "CT": "XTSE",
    "CV": "XTSX",
    "TU": "XTUN",
    "TKD": "XTUR",
    "S2": "XUBS",
    "UG__2": "XUGA",
    "MO": "XULA",
    "USE": "XUSE",
    "SA__1": "XVAL",
    "PN": "XVPA",
    "VX": "XVTX",
    "PW__1": "XWAR",
    "AV": "XWBO",
    "AV__1": "XWBO",
    "AV__2": "XWBO",
    "WBA": "XWBO",
    "WBA__1": "XWBO",
    "ZA": "XZAG",
    "ZCE": "XZCE",
    "ZH": "XZIM",
    "YLX": "YLDX",
}
