import json
from typing import List
from typing import Optional

from prefect import context
from prefect.engine.signals import SKIP
from se_core_tasks.core.core_dataclasses import S3File
from se_core_tasks.feeds.order.tt.fix.s3_fix_files_batcher import (
    Params as GenericParams,
)
from se_core_tasks.feeds.order.tt.fix.s3_fix_files_batcher import (
    run_s3_fix_files_batcher,
)
from se_core_tasks.feeds.order.tt.fix.s3_fix_files_batcher import (
    SkipIfNoFilesToDownload,
)
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.base import BaseTask


class Params(BaseParams, GenericParams):
    pass


class S3FixFilesBatcher(BaseTask):

    params_class = Params

    def execute(
        self,
        params: Params = None,
        flow_args: str = None,
        **kwargs,
    ) -> List[S3File]:

        args: dict = json.loads(flow_args)

        delta_in_days = args.get("delta_in_days")
        delta_in_days = int(delta_in_days) if delta_in_days else None

        return self.process(
            params=params,
            prefix=str(args.get("prefix")),
            delta_in_days=delta_in_days,
            bucket=Settings.realm,
            auditor=self.auditor,
            logger=self.logger,
        )

    @classmethod
    def process(
        cls,
        params: Params,
        prefix: str,
        bucket: str,
        delta_in_days: Optional[int] = None,
        auditor=None,
        logger=context.get("logger"),
    ) -> List[S3File]:
        try:
            return run_s3_fix_files_batcher(
                params=params,
                prefix=prefix,
                bucket=bucket,
                delta_in_days=delta_in_days,
                logger=logger,
                auditor=auditor,
            )
        except SkipIfNoFilesToDownload as e:
            raise SKIP(e.message)
