import logging
import tempfile
from pathlib import Path
from urllib.parse import unquote_plus
from urllib.parse import urlparse

import boto3
import pandas as pd
from botocore.exceptions import ClientError
from prefect.engine.signals import FAIL
from prefect.engine.signals import SKIP
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from swarm.task.base import BaseTask

from swarm_tasks.io.read.aws.s3_download_file import S3DownloadFile
from swarm_tasks.order.feed.thinkfolio.static import FileNameBits
from swarm_tasks.order.feed.thinkfolio.static import FileType
from swarm_tasks.order.feed.thinkfolio.static import FixedIncomeColumns
from swarm_tasks.order.feed.thinkfolio.utils import get_file_type
from swarm_tasks.utilities.s3 import check_file_timestamps
from swarm_tasks.utilities.s3 import check_pair_file_on_s3


class FixedIncomeFileMerger(BaseTask):
    """
    This is a pre-processor task for the `order-feed-thinkfolio` flow.
    Fixed Income files come in two parts:
        One TRD file, and one ADD file, where the only difference is 3 letters on the file name
        e.g. TEST_MARKITFI_TRD_1_20230601.csv and TEST_MARKITFI_ADD_20230601.csv
    If the file_url that triggered the flow is a `Fixed Income` type file, this task will look for its
        file counterpart in S3.
    If the file counterpart is not there or there was an error the flow is skipped.
    If the counterpart is there, but it was uploaded after the current file, the flow is skipped.
    If the counterpart is there, but it was uploaded on the same timestamp, the ADD file is skipped.
    Everything else, it loads the two csv files into dataframes and merges it on `Order ID` and
        the flow continues.
    """

    def execute(
        self,
        extractor_result: ExtractPathResult = None,
        file_url: str = None,
        **kwargs,
    ) -> ExtractPathResult:

        return self.process(
            extractor_result=extractor_result, file_url=file_url, logger=self.logger
        )

    @classmethod
    def process(
        cls,
        extractor_result: ExtractPathResult = None,
        file_url: str = None,
        logger: logging.Logger = None,
    ) -> ExtractPathResult:

        file_name = extractor_result.path.name
        file_type, trd_or_add = get_file_type(file_name=file_name)

        if not file_type == FileType.FIXED_INCOME:
            logger.info(
                f"Current file is not of type {FileType.FIXED_INCOME}. Returning input extractor_result."
            )
            return extractor_result

        pair_file_key = None
        try:
            pair_file_uri = cls._get_pair_file_uri(
                source_file_uri=file_url, trd_or_add=trd_or_add
            )
            pair_file_key = urlparse(pair_file_uri).path

            check_pair_file_on_s3(
                s3=boto3.resource("s3"), pair_file_key=pair_file_key[1:]
            )
            same_or_later_timestamp = check_file_timestamps(
                file_url=file_url, pair_file_key=pair_file_uri, allow_equal=True
            )

            if same_or_later_timestamp and trd_or_add == FileNameBits.ADD:
                raise SKIP(
                    "Pair file has the same timestamp, skipping processing for ADD file."
                )

            pair_file_extract = S3DownloadFile.process(
                file_url=pair_file_uri, logger=logger
            )

            trd_file = None
            add_file = None
            if trd_or_add == FileNameBits.TRD_1:
                trd_file = extractor_result
                add_file = pair_file_extract
            elif trd_or_add == FileNameBits.ADD:
                trd_file = pair_file_extract
                add_file = extractor_result

            joined_file = cls.join_trd_and_add_files(trd_file, add_file)

            logger.info(
                f"Merging of files {file_url} and {pair_file_uri} was successful."
            )
            return joined_file

        except ClientError as e:
            raise SKIP(f"Pair file not found: {pair_file_key}. Info:{e}")
        except SKIP as e:
            raise SKIP(f"{e}")
        except Exception as e:
            raise FAIL(f"Error fetching pair file for : {file_url}. Error: {e}")

    @staticmethod
    def _get_pair_file_uri(source_file_uri: str, trd_or_add: str):
        """
        Unquotes the S3 URI and converts it to the pair file uri
        :param source_file_uri: source uri
        :param trd_or_add: string indicating which pair file it is
        :return: pair source uri
        """
        converted_uri = unquote_plus(source_file_uri)
        if trd_or_add == FileNameBits.TRD_1:
            return converted_uri.replace(
                FileNameBits.TRD_1_UNDERSCORES, FileNameBits.ADD_UNDERSCORES
            )
        elif trd_or_add == FileNameBits.ADD:
            return converted_uri.replace(
                FileNameBits.ADD_UNDERSCORES, FileNameBits.TRD_1_UNDERSCORES
            )

    @staticmethod
    def join_trd_and_add_files(trd_file, add_file):
        # read files
        trd_df = pd.read_csv(trd_file.path.as_posix())
        add_df = pd.read_csv(add_file.path.as_posix())

        # select shared columns to ignore
        shared_columns_to_ignore = list(set(trd_df.columns) & set(add_df.columns))
        shared_columns_to_ignore.pop(
            shared_columns_to_ignore.index(FixedIncomeColumns.ORDER_ID)
        )

        # filter add dataframe
        add_df = add_df.loc[
            add_df.loc[:, FixedIncomeColumns.PORTFOLIO_CODE].isnull(),
            [col for col in add_df.columns if col not in shared_columns_to_ignore],
        ]

        # merge dataframes
        merged_df = trd_df.merge(add_df, on=FixedIncomeColumns.ORDER_ID)

        # save merged dataframe locally
        temp_dir = tempfile.gettempdir()
        temp_file_path = Path(temp_dir).joinpath("merged_df")
        merged_df.to_csv(temp_file_path, index=False)

        return ExtractPathResult(temp_file_path)
