import os

import pandas as pd
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.reference import OrderRecordType
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order_and_tr.static import AssetClass
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ParamsConvertMinorToMajor,
)
from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI
from swarm_tasks.steeleye.generic.get_tenant_lei import Params as ParamsGetTenantLEI
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ConvertDatetimeParams,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as ParamsMapAttribute
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as ParamsMapValue
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as ParamsMergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    Params as ParamsInstrumentIdentifiers,
)


class TempColumns:
    ASSET_CLASS = "__asset_class__"
    BUYER = "__buyer__"
    CLIENT_IDENTIFIER = "__client_identifier__"
    COUNTERPARTY = "__counterparty__"
    EXECUTING_ENTITY_WITH_LEI = "__executing_entity_with_lei__"
    EXPIRY_DATE = "__maturity_date__"
    IS_ORDER_ALLOCATION = "__is_order_allocation__"
    ISIN = "__isin__"
    SELLER = "__seller__"
    TRADER_ID = "__trader_id__"
    UNDERLYING_ISIN = "__underlying_isin__"


class SourceColumns:
    ALLOCATIONID = "ALLOCATIONID"
    ASSETTYPE = "ASSETTYPE"
    BLOCKID = "BLOCKID"
    FUND = "FUND"
    INSTRUMENTISIN = "INSTRUMENTISIN"
    INSTRUMENTNAME = "INSTRUMENTNAME"
    MARKETIDENTIFIERCODE = "MARKETIDENTIFIERCODE"
    MATURITYDATE = "MATURITYDATE"
    ORDERALLOCATIONFILLEDVOLUME = "ORDERALLOCATIONFILLEDVOLUME"
    ORDERAMENDEDDATE = "ORDERAMENDEDDATE"
    ORDERAVERAGEFILLPRICE = "ORDERAVERAGEFILLPRICE"
    ORDERBROKER = "ORDERBROKER"
    ORDERCANCELLEDDATE = "ORDERCANCELLEDDATE"
    ORDERCURRENCY = "ORDERCURRENCY"
    ORDERDIRECTION = "ORDERDIRECTION"
    ORDERFILLEDDATE = "ORDERFILLEDDATE"
    ORDERFILLEDVOLUME = "ORDERFILLEDVOLUME"
    ORDERID = "ORDERID"
    ORDERORDEREDVOLUME = "ORDERORDEREDVOLUME"
    ORDERPLACEDDATE = "ORDERPLACEDDATE"
    ORDERTRADERID = "ORDERTRADERID"
    ORDERTYPE = "ORDERTYPE"


class RedDeerOrderTransformations(AbstractOrderTransformations):
    """Transformations class for Red Deer Orders. Fields are created for Order and OrderState models.
    Each record of the source frame will generate a NEWO Order and an OrderState. By default, the OrderStates
    are considered FILLs, unless the `OrderCancelledDate` value is populated, making them CANC instead
    """

    def _pre_process(self):
        """This pre-processing method is used to populate columns in pre_process_df.
        These columns are used multiple times in process() or post_process()"""

        self.is_order_allocation_mask = self._get_is_order_allocation_mask()

    def process(self):
        """All the schema target columns which need to be populated are populated in
        self.target_df by the public methods called by process(). 3 temp columns are
        populated here as well as these are present in all Order flows: __meta_model__,
        marketIdentifiers.parties and marketIdentifiers.instrument.
        """
        self.pre_process()
        self.date()
        self.timestamps_order_received()
        self.timestamps_order_submitted()
        self.timestamps_order_status_updated()
        self.transaction_details_trading_date_time()
        self.timestamps_trading_date_time()
        self.id()
        self.order_identifiers_order_id_code()
        self.report_details_transaction_ref_no()
        self.order_identifiers_transaction_ref_no()
        self.execution_details_order_status()
        self.execution_details_order_type()
        self.execution_details_outgoing_order_addl_info()
        self.buy_sell()
        self.execution_details_buy_sell_indicator()
        self.transaction_details_buy_sell_indicator()
        self.transaction_details_record_type()
        self.transaction_details_price_average()
        self.price_forming_data_price()
        self.transaction_details_price()
        self.transaction_details_price_currency()
        self.transaction_details_price_notation()
        self.price_forming_data_initial_quantity()
        self.price_forming_data_remaining_quantity()
        self.price_forming_data_traded_quantity()
        self.transaction_details_quantity()
        self.transaction_details_quantity_currency()
        self.transaction_details_quantity_notation()
        self.transaction_details_ultimate_venue()
        self.transaction_details_venue()
        self.data_source_name()
        self.source_key()
        self.source_index()
        self.meta_model()
        self.market_identifiers_parties()
        self.market_identifiers_instrument()
        self.market_identifiers()
        self.post_process()
        return self.target_df

    def _post_process(self):
        """All the temp columns which are needed for downstream tasks are populated
        in self.target_df inside the _post_process() method.
        """
        self.target_df.loc[:, SourceColumns.INSTRUMENTNAME] = self.source_frame.loc[
            :, SourceColumns.INSTRUMENTNAME
        ]

    def _date(self) -> pd.DataFrame:
        """Returns a data frame containing date.
        This is populated from SourceColumns.ORDERPLACEDDATE"""

        return ConvertDatetime.process(
            self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.ORDERPLACEDDATE,
                source_attribute_format="%Y-%m-%dT%H:%M:%S",
                target_attribute=OrderColumns.DATE,
                convert_to="date",
            ),
        )

    def _timestamps_order_received(self) -> pd.DataFrame:
        """Returns a data frame containing date.
        This is populated from SourceColumns.ORDERPLACEDDATE"""

        return ConvertDatetime.process(
            self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.ORDERPLACEDDATE,
                source_attribute_format="%Y-%m-%dT%H:%M:%S",
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                convert_to="datetime",
            ),
        )

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        """Returns a data frame containing date.
        This is populated from SourceColumns.ORDERPLACEDDATE"""

        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [OrderColumns.TIMESTAMPS_ORDER_RECEIVED]
            ],
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
            ),
            auditor=self.auditor,
        )

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        """Returns a data frame containing date.
        This is populated from SourceColumns.ORDERAMENDEDDATE"""

        return ConvertDatetime.process(
            self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.ORDERAMENDEDDATE,
                source_attribute_format="%Y-%m-%dT%H:%M:%S",
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED,
                convert_to="datetime",
            ),
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """Returns a data frame containing date.
        This is populated from SourceColumns.ORDERFILLEDDATE"""

        return ConvertDatetime.process(
            self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.ORDERFILLEDDATE,
                source_attribute_format="%Y-%m-%dT%H:%M:%S",
                target_attribute=OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME,
                convert_to="datetime",
            ),
        )

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        """Returns a data frame containing date.
        This is populated from SourceColumns.ORDERFILLEDDATE"""

        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME]
            ],
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME,
                target_attribute=OrderColumns.TIMESTAMPS_TRADING_DATE_TIME,
            ),
            auditor=self.auditor,
        )

    def _id(self) -> pd.DataFrame:
        """
        For ORDER take the OrderId and add the 1st 5 chars of OrderBroker (spaces etc can be ignored)
        For ALLOCATION combine OrderId & AllocationId

        ORDERID and ALLOCATIONID are mandatory columns for Order and Allocations respectively,
        but ORDERBROKER is not (Cancelled Orders do not have an order broker)

        We need to populate it separately for Orders and OrderStates
        because the AssignMetaParent task is expecting a `_order.id` column to exist
        to map the OrderStates `&parent` field
        """
        order_col = add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.ID)
        order_state_col = add_prefix(
            prefix=ModelPrefix.ORDER_STATE, attribute=OrderColumns.ID
        )

        result = pd.DataFrame(
            columns=[order_col],
            index=self.source_frame.index,
        )

        # Order
        result.loc[:, order_col] = self.source_frame.loc[
            :, SourceColumns.ORDERID
        ] + self.source_frame.loc[:, SourceColumns.ORDERBROKER].apply(
            lambda x: "" if pd.isna(x) else x.replace(" ", "")[:5]
        )

        # Allocation
        result.loc[self.is_order_allocation_mask, order_col] = (
            self.source_frame.loc[self.is_order_allocation_mask, SourceColumns.ORDERID]
            + self.source_frame.loc[
                self.is_order_allocation_mask, SourceColumns.ALLOCATIONID
            ]
        )

        result[order_state_col] = result[order_col]
        return result

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.orderIdCode.
        It assumes that _id() has been called earlier"""
        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.ID)]
            ],
            params=ParamsMapAttribute(
                source_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER, attribute=OrderColumns.ID
                ),
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE,
            ),
            auditor=self.auditor,
        )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """Returns a data frame containing reportDetails.transactionRefNo.
        It assumes that _id() has been called earlier"""
        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.ID)]
            ],
            params=ParamsMapAttribute(
                source_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER, attribute=OrderColumns.ID
                ),
                target_attribute=OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO,
            ),
            auditor=self.auditor,
        )

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.transactionRefNo.
        It assumes that _id() has been called earlier"""
        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.ID)]
            ],
            params=ParamsMapAttribute(
                source_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER, attribute=OrderColumns.ID
                ),
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
            ),
            auditor=self.auditor,
        )

    def _execution_details_order_status(self) -> pd.DataFrame:
        """Returns a data frame containing _order.executionDetails.orderStatus and
        _orderState.executionDetails.orderStatus.

        For each ORDER or ALLOCATION create a NEWO and FILL eg:
        2 orders and 5 allocations would give 7 NEWOs and 7 corresponding FILLs
        If OrderCancelledDate is populated the order is CAME at the given date-time (YYYY-MM-DDTHH:MM:SS)
        """

        order_prefix = add_prefix(
            prefix=ModelPrefix.ORDER,
            attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
        )
        order_state_prefix = add_prefix(
            prefix=ModelPrefix.ORDER_STATE,
            attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
        )
        result = pd.DataFrame(
            data=[[OrderStatus.NEWO.value, OrderStatus.FILL.value]],
            index=self.source_frame.index,
            columns=[order_prefix, order_state_prefix],
        )

        cancelled_orders_mask = self.source_frame.loc[
            :, SourceColumns.ORDERCANCELLEDDATE
        ].notnull()

        result.loc[cancelled_orders_mask, order_state_prefix] = OrderStatus.CAME.value

        return result

    def _execution_details_order_type(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.orderType.
        This is populated with the source field `ORDERTYPE`
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.ORDERTYPE,
                target_attribute=OrderColumns.EXECUTION_DETAILS_ORDER_TYPE,
            ),
            auditor=self.auditor,
        )

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.outgoingOrderAddlInfo.
        This is populated with the source field `BLOCKID`
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.BLOCKID,
                target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
            ),
            auditor=self.auditor,
        )

    def _buy_sell(self) -> pd.DataFrame:
        """
        Returns a data frame containing buySell
        This is populated from SourceColumns.ORDERDIRECTION
        We need to populate it separately for Orders and OrderStates
        because the AssignMetaParent task is expecting a `_order.buySell` column to exist
        to map the OrderStates `&parent` field
        """

        return pd.concat(
            [
                MapValue.process(
                    source_frame=self.source_frame,
                    params=ParamsMapValue(
                        source_attribute=SourceColumns.ORDERDIRECTION,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER, attribute=OrderColumns.BUY_SELL
                        ),
                        case_insensitive=True,
                        value_map={"buy": "1", "sell": "2"},
                    ),
                    auditor=self.auditor,
                ),
                MapValue.process(
                    source_frame=self.source_frame,
                    params=ParamsMapValue(
                        source_attribute=SourceColumns.ORDERDIRECTION,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.BUY_SELL,
                        ),
                        case_insensitive=True,
                        value_map={"buy": "1", "sell": "2"},
                    ),
                    auditor=self.auditor,
                ),
            ],
            axis=1,
        )

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Returns a data frame containing `executionDetails.buySellIndicator`
        This is populated from SourceColumns.ORDERDIRECTION"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.ORDERDIRECTION,
                target_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                case_insensitive=True,
                value_map={
                    "buy": BuySellIndicator.BUYI.value,
                    "sell": BuySellIndicator.SELL.value,
                },
            ),
            auditor=self.auditor,
        )

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Returns a data frame containing `transactionDetails.buySellIndicator`
        This is populated from SourceColumns.ORDERDIRECTION"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.ORDERDIRECTION,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
                case_insensitive=True,
                value_map={
                    "buy": BuySellIndicator.BUYI.value,
                    "sell": BuySellIndicator.SELL.value,
                },
            ),
            auditor=self.auditor,
        )

    def _transaction_details_record_type(self):
        """Returns a data frame containing transactionDetails.recordType.
        For ORDER use Market Side'; for ALLOCATION use 'Allocation'
        """
        result = pd.DataFrame(
            data=OrderRecordType.MARKET_SIDE.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE],
        )
        result.loc[
            self.is_order_allocation_mask, OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE
        ] = OrderRecordType.ALLOCATION.value

        return result

    def _transaction_details_price_average(self):
        """
        Returns a data frame containing transactionDetails.priceAverage.
        Data is populated from source field `ORDERAVERAGEFILLPRICE`
        """

        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_price_attribute=SourceColumns.ORDERAVERAGEFILLPRICE,
                source_ccy_attribute=SourceColumns.ORDERCURRENCY,
                target_price_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_AVERAGE,
                cast_to="abs",
            ),
        )

    def _price_forming_data_price(self):
        """
        Returns a data frame containing priceFormingData.price.
        Data is populated from source field `ORDERAVERAGEFILLPRICE`
        It assumes that 'transaction_details_price_average() has been called earlier'
        """

        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [OrderColumns.TRANSACTION_DETAILS_PRICE_AVERAGE]
            ],
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_AVERAGE,
                target_attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_price(self):
        """
        Returns a data frame containing transactionDetails.price.
        Data is populated from source field `ORDERAVERAGEFILLPRICE`
        It assumes that 'transaction_details_price_average() has been called earlier'
        """

        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [OrderColumns.TRANSACTION_DETAILS_PRICE_AVERAGE]
            ],
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_AVERAGE,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.priceCurrency.
        the source column is SourceColumns.ORDERCURRENCY.
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.ORDERCURRENCY,
                target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
            ),
        )

    def _transaction_details_price_notation(self):
        """Returns a data frame containing transactionDetails.priceNotation.
        If AssetType = ‘Bond’; 'Bond Future ';' Bond Future Option' then transactionDetails.priceNotation = PERC;
        else MONE
        """
        bond_condition = f"`{SourceColumns.ASSETTYPE}`.str.upper().isin(['BOND', 'BOND FUTURE', 'BOND FUTURE OPTION'])"
        cases = [
            {"query": bond_condition, "value": "PERC"},
            {"query": f"~{bond_condition}", "value": "MONE"},
        ]
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION,
                cases=cases,
            ),
        )

    def _price_forming_data_initial_quantity(self):
        """
        Returns a data frame containing priceFormingData.initialQuantity.
        The source column is OrderOrderedVolume
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.ORDERORDEREDVOLUME,
                target_attribute=OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
            ),
            auditor=self.auditor,
        )

    def _price_forming_data_remaining_quantity(self) -> pd.DataFrame:
        """
        Returns a data frame containing priceFormingData.remainingQuantity.
        OrderOrderedVolume - OrderFilledVolume
        """
        result = pd.DataFrame(
            columns=[OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY],
            index=self.source_frame.index,
        )

        result.loc[:, OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY] = (
            self.source_frame.loc[:, SourceColumns.ORDERORDEREDVOLUME]
            - self.source_frame.loc[:, SourceColumns.ORDERFILLEDVOLUME]
        )

        result.loc[
            self.is_order_allocation_mask,
            OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY,
        ] = (
            self.source_frame.loc[
                self.is_order_allocation_mask, SourceColumns.ORDERORDEREDVOLUME
            ]
            - self.source_frame.loc[
                self.is_order_allocation_mask, SourceColumns.ORDERALLOCATIONFILLEDVOLUME
            ]
        )

        return result

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        """Returns a data frame containing priceFormingData.tradedQuantity."""
        result = pd.DataFrame(
            columns=[OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY],
            index=self.source_frame.index,
        )

        result.loc[
            :, OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY
        ] = self.source_frame.loc[:, SourceColumns.ORDERFILLEDVOLUME]

        result.loc[
            self.is_order_allocation_mask,
            OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
        ] = self.source_frame.loc[
            self.is_order_allocation_mask, SourceColumns.ORDERALLOCATIONFILLEDVOLUME
        ]

        return result

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.quantity."""
        result = pd.DataFrame(
            columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY],
            index=self.source_frame.index,
        )

        result.loc[
            :, OrderColumns.TRANSACTION_DETAILS_QUANTITY
        ] = self.source_frame.loc[:, SourceColumns.ORDERFILLEDVOLUME]

        result.loc[
            self.is_order_allocation_mask,
            OrderColumns.TRANSACTION_DETAILS_QUANTITY,
        ] = self.source_frame.loc[
            self.is_order_allocation_mask, SourceColumns.ORDERALLOCATIONFILLEDVOLUME
        ]

        return result

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        """
        Returns a data frame containing transactionDetails.quantityCurrency.
        """
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.ORDERCURRENCY,
                target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
            ),
        )

    def _transaction_details_quantity_notation(self):
        """Returns a data frame containing transactionDetails.quantityNotation.
        If AssetType = ‘Bond’; 'Bond Future ';' Bond Future Option' then transactionDetails.quantityNotation = NOML;
        else UNIT
        """
        bond_condition = f"`{SourceColumns.ASSETTYPE}`.str.upper().isin(['BOND', 'BOND FUTURE', 'BOND FUTURE OPTION'])"
        cases = [
            {"query": bond_condition, "value": "NOML"},
            {"query": f"~{bond_condition}", "value": "UNIT"},
        ]
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION,
                cases=cases,
            ),
        )

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        """
        Returns a data frame containing transactionDetails.ultimateVenue.
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.MARKETIDENTIFIERCODE,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_venue(self) -> pd.DataFrame:
        """
        Returns a data frame containing transactionDetails.venue.
        if InstrumentIsin is populated then “XOFF” else "XXXX"
        """
        isin_is_populated_condition = f"`{SourceColumns.INSTRUMENTISIN}`.notnull()"
        cases = [
            {"query": isin_is_populated_condition, "value": "XOFF"},
            {"query": f"~{isin_is_populated_condition}", "value": "XXXX"},
        ]
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_VENUE,
                cases=cases,
            ),
        )

    def _data_source_name(self) -> pd.DataFrame:
        """
        Returns a data frame containing dataSourceName.
        This is populated with the static value 'Red Deer'
        """
        return pd.DataFrame(
            data="Red Deer",
            index=self.source_frame.index,
            columns=[OrderColumns.DATA_SOURCE_NAME],
        )

    def _source_key(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceKey column from the SWARM_FILE_URL"""
        return pd.DataFrame(
            data=os.getenv("SWARM_FILE_URL"),
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_KEY],
        )

    def _source_index(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceIndex column"""
        return pd.DataFrame(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_INDEX],
        )

    def _meta_model(self) -> pd.DataFrame:
        """Returns a data frame containing _order.__meta_model__ and _orderState.__meta_model__.
        The columns are populated with the static values Order and OrderState respectively"""
        return pd.DataFrame(
            data=[["Order", "OrderState"]],
            index=self.source_frame.index,
            columns=[
                add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.META_MODEL),
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.META_MODEL,
                ),
            ],
        )

    def _order_class(self) -> pd.DataFrame:
        """Not Implemented"""

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.parties by calling PartyIdentifiers
        Assumes that _transaction_details_buy_sell_indicator() has been
        called earlier"""
        parties_source_frame = pd.concat(
            [
                self.source_frame,
                self._get_client_identifier(),
                self._get_trader(),
                self._get_executing_entity(),
                self._get_counterparty(),
                self.target_df.loc[
                    :, [OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR]
                ],
            ],
            axis=1,
        )

        parties_source_frame[TempColumns.BUYER] = pd.NA
        parties_source_frame[TempColumns.SELLER] = pd.NA

        # For Orders, Buyer is same as Executing Entity
        parties_source_frame.loc[
            ~self.is_order_allocation_mask, TempColumns.BUYER
        ] = parties_source_frame.loc[
            ~self.is_order_allocation_mask, TempColumns.EXECUTING_ENTITY_WITH_LEI
        ]

        # For Orders, Seller is same as Counterparty
        parties_source_frame.loc[
            ~self.is_order_allocation_mask, TempColumns.SELLER
        ] = parties_source_frame.loc[
            ~self.is_order_allocation_mask, TempColumns.COUNTERPARTY
        ]

        # For Allocations, Buyer is same as Client Identifiers
        parties_source_frame.loc[
            self.is_order_allocation_mask, TempColumns.BUYER
        ] = parties_source_frame.loc[
            self.is_order_allocation_mask, TempColumns.CLIENT_IDENTIFIER
        ]

        # For Allocations, Seller is same as Executing Entity
        parties_source_frame.loc[
            self.is_order_allocation_mask, TempColumns.SELLER
        ] = parties_source_frame.loc[
            self.is_order_allocation_mask, TempColumns.EXECUTING_ENTITY_WITH_LEI
        ]

        return GenericOrderPartyIdentifiers.process(
            source_frame=parties_source_frame,
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                buy_sell_side_attribute=OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
                client_identifier=TempColumns.CLIENT_IDENTIFIER,
                counterparty_identifier=TempColumns.COUNTERPARTY,
                executing_entity_identifier=TempColumns.EXECUTING_ENTITY_WITH_LEI,
                trader_identifier=TempColumns.TRADER_ID,
                execution_within_firm_identifier=TempColumns.TRADER_ID,
                buyer_identifier=TempColumns.BUYER,
                seller_identifier=TempColumns.SELLER,
                use_buy_mask_for_buyer_seller=True,
            ),
        )

    def _get_asset_class(self):
        """
        Gets the asset class
        """
        bond_condition = (
            f"`{SourceColumns.ASSETTYPE}`.str.fullmatch('Bond|Bond Warrant|Credit Linked Note', "
            f"case=False, na=False)"
        )
        cds_index_condition = (
            f"`{SourceColumns.ASSETTYPE}`.str.fullmatch('CDX Index Swap|CDS Index Swap', "
            f"case=False, na=False)"
        )
        cds_single_stock_condition = (
            f"`{SourceColumns.ASSETTYPE}`.str.fullmatch('CDS$', case=False, na=False)"
        )
        cfd_condition = (
            f"`{SourceColumns.ASSETTYPE}`.str.fullmatch('Exotic|FRA|Repo|Swaption|Total Return Swap', "
            f"case=False, na=False)"
        )
        equity_swap_condition = f"`{SourceColumns.ASSETTYPE}`.str.fullmatch('Equity Swap', case=False, na=False)"
        future_condition = (
            f"`{SourceColumns.ASSETTYPE}`.str.fullmatch('Bond Future|Commodity Future|Index Future|"
            f"Interest Rate Future', case=False, na=False)"
        )
        fx_spot_condition = (
            f"`{SourceColumns.ASSETTYPE}`.str.fullmatch('FX', case=False, na=False)"
        )
        fx_cfd_condition = f"`{SourceColumns.ASSETTYPE}`.str.fullmatch('Cross Currency Swap', case=False, na=False)"
        fx_forward_condition = f"`{SourceColumns.ASSETTYPE}`.str.fullmatch('FX Forward', case=False, na=False)"
        fx_option_condition = f"`{SourceColumns.ASSETTYPE}`.str.fullmatch('FX Option', case=False, na=False)"
        equity_condition = (
            f"`{SourceColumns.ASSETTYPE}`.str.fullmatch('Equity|Equity Short|Equity Warrant',"
            f" case=False, na=False)"
        )
        options_condition = (
            f"`{SourceColumns.ASSETTYPE}`.str.fullmatch('Bond Future Option|Commodity Future Option|"
            f"Equity Option|Index Option|Index Future Option|Interest Rate Future Option',"
            f" case=False, na=False)"
        )

        cases = [
            {"query": bond_condition, "value": AssetClass.BOND},
            {"query": cds_index_condition, "value": AssetClass.CDS_INDEX},
            {"query": cds_single_stock_condition, "value": AssetClass.CDS_SINGLE_STOCK},
            {"query": cfd_condition, "value": AssetClass.CFD},
            {"query": equity_swap_condition, "value": AssetClass.EQUITY_SWAP},
            {"query": future_condition, "value": AssetClass.FUTURE},
            {"query": fx_spot_condition, "value": AssetClass.FX_SPOT},
            {"query": fx_cfd_condition, "value": AssetClass.FX_CFD},
            {"query": fx_forward_condition, "value": AssetClass.FX_FORWARD},
            {"query": fx_option_condition, "value": AssetClass.FX_OPTION},
            {"query": equity_condition, "value": AssetClass.EQUITY},
            {"query": options_condition, "value": AssetClass.OPTION},
        ]
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.ASSET_CLASS, cases=cases
            ),
        )

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.instrument by calling InstrumentIdentifiers.
        Assumes that _transaction_details_quantity_currency() and _transaction_details_venue() have been
        called earlier"""
        instrument_source_df = pd.concat(
            [
                self.source_frame,
                self._get_asset_class(),
                self._get_expiry_date(),
                self.target_df.loc[
                    :,
                    [
                        OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
                        OrderColumns.TRANSACTION_DETAILS_VENUE,
                    ],
                ],
            ],
            axis=1,
        )

        # Needed for LinkInstruments downstream on the Flow
        self.target_df.loc[:, TempColumns.ASSET_CLASS] = instrument_source_df.loc[
            :, TempColumns.ASSET_CLASS
        ]

        instrument_source_df = pd.concat(
            [
                instrument_source_df,
                self._get_underlying_isin(
                    aux_frame=instrument_source_df
                ),  # needs asset class
                self._get_isin(aux_frame=instrument_source_df),  # needs asset class
            ],
            axis=1,
        )

        return InstrumentIdentifiers.process(
            source_frame=instrument_source_df,
            params=ParamsInstrumentIdentifiers(
                asset_class_attribute=TempColumns.ASSET_CLASS,
                currency_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
                venue_attribute=OrderColumns.TRANSACTION_DETAILS_VENUE,
                isin_attribute=TempColumns.ISIN,
                expiry_date_attribute=TempColumns.EXPIRY_DATE,
                underlying_isin_attribute=TempColumns.UNDERLYING_ISIN,
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                retain_task_inputs=True,
            ),
            auditor=self.auditor,
        )

    def _market_identifiers(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers by calling MergeMarketIdentifiers.
        Assumes _market_identifiers_instrument() and _market_identifiers_parties() have been
        called earlier"""
        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=ParamsMergeMarketIdentifiers(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                instrument_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    # The following methods are used to populate temporary columns

    def _get_trader(self):
        """
        Gets the trader value to be used in PartyIdentifiers
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.ORDERTRADERID,
                target_attribute=TempColumns.TRADER_ID,
                prefix=PartyPrefix.ID,
            ),
            auditor=self.auditor,
        )

    def _get_executing_entity(self):
        """
        Gets the executing entity value to be used in PartyIdentifiers from the AccountFirm record
        index is used for testing
        """
        return GetTenantLEI.process(
            source_frame=self.source_frame,
            params=ParamsGetTenantLEI(
                target_column_prefix=PartyPrefix.LEI,
                target_lei_column=TempColumns.EXECUTING_ENTITY_WITH_LEI,
            ),
        )

    def _get_client_identifier(self):
        """
        Gets the client identifier value to be used in PartyIdentifiers
        Fund (Populate for ALLOCATION only)
        """

        result = pd.DataFrame(
            columns=[TempColumns.CLIENT_IDENTIFIER],
            index=self.source_frame.index,
        )

        result.loc[self.is_order_allocation_mask, TempColumns.CLIENT_IDENTIFIER] = (
            PartyPrefix.ID
            + self.source_frame.loc[self.is_order_allocation_mask, SourceColumns.FUND]
        )

        return result

    def _get_counterparty(self):
        """
        Gets the counterparty value to be used in PartyIdentifiers
        OrderBroker (Populate for ORDER only)
        """
        result = pd.DataFrame(
            columns=[TempColumns.COUNTERPARTY],
            index=self.source_frame.index,
        )

        result.loc[~self.is_order_allocation_mask, TempColumns.COUNTERPARTY] = (
            PartyPrefix.ID
            + self.source_frame.loc[
                ~self.is_order_allocation_mask, SourceColumns.ORDERBROKER
            ]
        )

        return result

    def _get_expiry_date(self) -> pd.DataFrame:
        """
        Gets the expiry_date value to be used in InstrumentIdentifiers
        """

        return ConvertDatetime.process(
            self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.MATURITYDATE,
                source_attribute_format="%Y-%m-%d",
                target_attribute=TempColumns.EXPIRY_DATE,
                convert_to="date",
            ),
        )

    @staticmethod
    def _get_isin(aux_frame: pd.DataFrame) -> pd.DataFrame:
        """
        Gets the isin value to be used in InstrumentIdentifiers
        """

        cases = [
            {
                "query": f"~((`{TempColumns.ASSET_CLASS}` == '{AssetClass.CDS_SINGLE_STOCK}') & (`{SourceColumns.INSTRUMENTISIN}` != ''))",
                "attribute": SourceColumns.INSTRUMENTISIN,
            }
        ]
        return MapConditional.process(
            source_frame=aux_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.ISIN,
                cases=cases,
            ),
        )

    @staticmethod
    def _get_underlying_isin(aux_frame: pd.DataFrame) -> pd.DataFrame:
        """
        Gets the underlying_isin value to be used in InstrumentIdentifiers
        """

        cases = [
            {
                "query": f"(`{TempColumns.ASSET_CLASS}` == '{AssetClass.CDS_SINGLE_STOCK}') & (`{SourceColumns.INSTRUMENTISIN}` != '')",
                "attribute": SourceColumns.INSTRUMENTISIN,
            }
        ]
        return MapConditional.process(
            source_frame=aux_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.UNDERLYING_ISIN,
                cases=cases,
            ),
        )

    def _get_is_order_allocation_mask(self) -> pd.DataFrame:
        """
        Create mask to identify order allocations. This will be accessed frequently throughout the transformations
        as logic will vary between order and order allocations

        :return: Pandas DataFrame with mask to identify order allocations
        """
        return (
            self.source_frame.loc[:, SourceColumns.ALLOCATIONID]
            .notnull()
            .rename(TempColumns.IS_ORDER_ALLOCATION)
        )

    # Abstract methods that do not require implementation

    def _transaction_details_settlement_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_amount_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_date(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        pass

    def _transaction_details_swap_directionalities(self) -> pd.DataFrame:
        pass

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        pass

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_commission_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_commission_amount_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_commission_amount_type(self) -> pd.DataFrame:
        pass

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_cross_indicator(self) -> pd.DataFrame:
        pass

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        pass

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        pass

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        pass

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        pass

    def _transaction_details_position_effect(self) -> pd.DataFrame:
        pass

    def _transaction_details_position_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        pass

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        pass

    def _timestamps_validity_period(self) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_waiver_indicator(self) -> pd.DataFrame:
        pass

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        pass

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        pass

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        pass

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        pass

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        pass

    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        pass

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        pass

    def _execution_details_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        pass

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        pass

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        pass

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        pass

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        pass

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        pass

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        pass

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        pass

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_stop_price(self) -> pd.DataFrame:
        pass

    def _execution_details_validity_period(self) -> pd.DataFrame:
        pass

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        pass

    def _financing_type(self) -> pd.DataFrame:
        pass

    def _hierarchy(self) -> pd.DataFrame:
        pass

    def _is_discretionary(self) -> pd.DataFrame:
        pass

    def _is_iceberg(self):
        pass

    def _is_repo(self):
        pass

    def _is_synthetic(self):
        pass

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        pass

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        pass

    def _jurisdiction_country(self) -> pd.DataFrame:
        pass

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        pass

    def _order_identifiers_aggregated_order_id_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        pass

    def _order_identifiers_internal_order_id_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_parent_order_id(self) -> pd.DataFrame:
        pass

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        pass

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        pass

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        pass

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        pass

    def _transaction_details_trail_id(self) -> pd.DataFrame:
        pass

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        pass

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        pass

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        pass
