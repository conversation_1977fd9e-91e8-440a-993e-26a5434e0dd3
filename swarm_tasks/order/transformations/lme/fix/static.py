from se_elastic_schema.static.mifid2 import TradingCapacity


class SourceColumns:
    ACCOUNT_TYPE = "AccountType"
    AVG_PX = "AvgPx"
    CFI_CODE = "CFICode"
    EXEC_ID = "ExecID"
    EXEC_TYPE = "ExecType"
    FF_10010 = "ff_10010"
    FF_10051 = "ff_10051"
    FF_1057 = "ff_1057"
    LAST_PX = "LastPx"
    LAST_QTY = "LastQty"
    LEAVES_QTY = "LeavesQty"
    LEG_LAST_PX = "LegLastPx"
    LEG_SIDE = "LegSide"
    MATURITY_DATE = "MaturityDate"
    NO_LEGS = "NoLegs"
    ORDER_ID = "OrderID"
    ORDER_QTY = "OrderQty"
    ORD_STATUS = "OrdStatus"
    ORD_TYPE = "OrdType"
    ORDER_RESTRICTIONS = "OrderRestrictions"
    SENDING_TIME = "SendingTime"
    SENDER_COMP_ID = "SenderCompID"
    SIDE = "Side"
    STOP_PX = "StopPx"
    SYMBOL = "Symbol"
    PARTY_ID = "PartyID"
    PARTY_ROLE = "PartyRole"
    PRICE = "Price"
    PRICE_TYPE = "PriceType"
    TARGET_COMP_ID = "TargetCompID"
    TEXT = "Text"
    TIME_IN_FORCE = "TimeInForce"
    TRANSACT_TIME = "TransactTime"


class TempColumns:
    ASSET_CLASS = "__asset_class__"
    BEST_EX_ASSET_CLASS_MAIN = "__best_ex_asset_class_main__"
    IS_CREATED_THROUGH_FALLBACK = "__is_created_through_fallback__"
    EXPIRY_DATE = "__expiry_date__"
    LEG_SIDE = "__leg_side__"
    LEG_LAST_PX_1 = "__leg_last_px_1__"
    LEG_LAST_PX_2 = "__leg_last_px_2__"
    NEWO_IN_FILE = "__newo_in_file__"
    NUM_LEGS = "__num_legs__"


class TempPartyColumns:
    CLIENT = "__client__"
    COUNTERPARTY = "__counterparty__"
    TRADER = "__trader__"
    INVESTMENT_DM = "__investment_dm__"
    EXEC_WITHIN_FIRM = "__exec_within_firm__"

    @classmethod
    def get_temp_party_cols(cls):
        return [
            v
            for k, v in cls.__dict__.items()
            if not k.startswith("__") and not k.endswith("__") and isinstance(v, str)
        ]


ORD_STATUS_MAP = {
    "L": "TRIG",
    "3": "DNFD",
    "4": "CAME",
    "5": "REME",
    "6": "PNDC",
    "8": "REMO",
    "C": "EXPI",
    "1": "PARF",
    "2": "FILL",
    "K": "REMH",
    "7": "REMA",
    "9": "SUSP",
    "E": "CHME",
    "M": "PNDM",
}

BUY_SELL_MAP = {
    "1": "1",
    "2": "2",
    "3": "1",
    "4": "2",
    "5": "2",
    "6": "2",
    "H": "2",
}

TRADING_CAPACITY_MAP = {
    "1": TradingCapacity.DEAL.value,
    "2": TradingCapacity.MTCH.value,
    "3": TradingCapacity.AOTC.value,
}

INSTRUMENT_ID_COLUMNS = {
    "currency_attribute": "input_currency",
    "venue_attribute": "ultimate_venue",
    "expiry_date_attribute": "expiry_date",
    "underlying_symbol_attribute": "symbol",
    "asset_class_attribute": "asset_class",
}
