from typing import NoReturn

import pandas as pd
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.generic.frame.map_conditional_attribute_from_list_items import (
    MapConditionalAttributeFromListItems,
)
from swarm_tasks.generic.frame.map_conditional_attribute_from_list_items import (
    Params as MapFromListItemsParams,
)
from swarm_tasks.order.transformations.lme.fix.lme_fix_order_transformations import (
    LmeFixOrderTransformations,
)
from swarm_tasks.order.transformations.lme.fix.static import SourceColumns
from swarm_tasks.order.transformations.lme.fix.static import TempPartyColumns
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as PartyIdentifiersParams,
)


class LmeFixOrderSelectOverrideTransformations(LmeFixOrderTransformations):
    def _derive_temp_parties_in_target_df(self) -> NoReturn:
        """
        Derives all the required temp party columns and adds them to target_df. The
        PartyPrefix.ID + reason that they are added to target_df and not pre_process_df
        is that multi-leg records will be split downstream (i.e., 2 records
        will be created for 1 multi-leg record) in post_process().
        After this, target_df will have a different shape to source_frame
        and pre_process_df, So any fields required by PartyIdentifiers
        (which is called after post_process) will need to be in the
        target_df

        """

        self.target_df = pd.concat(
            [
                self.target_df,
                PartyPrefix.ID
                + MapConditionalAttributeFromListItems.process(
                    source_frame=self.source_frame,
                    params=MapFromListItemsParams(
                        source_attribute_for_pattern_search=SourceColumns.PARTY_ROLE,
                        source_attribute_for_mapping=SourceColumns.PARTY_ID,
                        regex_pattern="^11$",
                        target_attribute=TempPartyColumns.TRADER,
                    ),
                ),
                PartyPrefix.ID
                + MapConditionalAttributeFromListItems.process(
                    source_frame=self.source_frame,
                    params=MapFromListItemsParams(
                        source_attribute_for_pattern_search=SourceColumns.PARTY_ROLE,
                        source_attribute_for_mapping=SourceColumns.PARTY_ID,
                        regex_pattern="^3$",
                        target_attribute=TempPartyColumns.CLIENT,
                    ),
                ),
                PartyPrefix.ID
                + MapConditionalAttributeFromListItems.process(
                    source_frame=self.source_frame,
                    params=MapFromListItemsParams(
                        source_attribute_for_pattern_search=SourceColumns.PARTY_ROLE,
                        source_attribute_for_mapping=SourceColumns.PARTY_ID,
                        regex_pattern="^301$",
                        target_attribute=TempPartyColumns.EXEC_WITHIN_FIRM,
                    ),
                ),
                PartyPrefix.ID
                + MapConditionalAttributeFromListItems.process(
                    source_frame=self.source_frame,
                    params=MapFromListItemsParams(
                        source_attribute_for_pattern_search=SourceColumns.PARTY_ROLE,
                        source_attribute_for_mapping=SourceColumns.PARTY_ID,
                        regex_pattern="^300$",
                        target_attribute=TempPartyColumns.INVESTMENT_DM,
                    ),
                ),
            ],
            axis=1,
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """Populates OrderColumns.MARKET_IDENTIFIERS_PARTIES"""

        return GenericOrderPartyIdentifiers.process(
            source_frame=self.target_df,
            params=PartyIdentifiersParams(
                trader_identifier=TempPartyColumns.TRADER,
                investment_decision_within_firm_identifier=TempPartyColumns.INVESTMENT_DM,
                execution_within_firm_identifier=TempPartyColumns.EXEC_WITHIN_FIRM,
                client_identifier=TempPartyColumns.CLIENT,
                seller_identifier=TempPartyColumns.CLIENT,
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )
