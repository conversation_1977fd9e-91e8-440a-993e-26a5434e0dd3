from typing import NoReturn

import numpy as np
import pandas as pd
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_conditional import Case
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order_and_tr.fix.fix_parser_result_to_frame import StaticFields
from se_trades_tasks.order_and_tr.static import AssetClass
from se_trades_tasks.order_and_tr.static import (
    InstrumentIdentifierParamColumns,
)
from se_trades_tasks.order_and_tr.static import PartyPrefix
from se_trades_tasks.order_and_tr.static import Venue

from swarm_tasks.generic.frame.map_conditional_attribute_from_list_items import (
    MapConditionalAttributeFromListItems,
)
from swarm_tasks.generic.frame.map_conditional_attribute_from_list_items import (
    Params as MapFromListItemsParams,
)
from swarm_tasks.order.transformations.lme.fix import static
from swarm_tasks.order.transformations.lme.fix.static import BUY_SELL_MAP
from swarm_tasks.order.transformations.lme.fix.static import INSTRUMENT_ID_COLUMNS
from swarm_tasks.order.transformations.lme.fix.static import SourceColumns
from swarm_tasks.order.transformations.lme.fix.static import TempColumns
from swarm_tasks.order.transformations.lme.fix.static import TempPartyColumns
from swarm_tasks.order.transformations.lme.fix.static import TRADING_CAPACITY_MAP
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ConvertDatetimeParams,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as ParamsMapAttribute
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as MapConditionalParams
from swarm_tasks.transform.map.map_static import MapStatic
from swarm_tasks.transform.map.map_static import Params as MapStaticParams
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as MapValueParams
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as MergeMarketIdentifiersParams,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as PartyIdentifiersParams,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    Params as InstrumentIdentifiersParams,
)


class LmeFixOrderTransformations(AbstractOrderTransformations):
    __data_source_name__ = "LME Select Fix"
    __transact_time__ = "__transact_time__"

    def process(self) -> pd.DataFrame:
        self.pre_process()
        self.buy_sell()
        self.data_source_name()
        self.date()
        self.execution_details_buy_sell_indicator()
        self.execution_details_limit_price()
        self.execution_details_order_type()
        self.execution_details_order_status()
        self.execution_details_outgoing_order_addl_info()
        self.execution_details_routing_strategy()
        self.execution_details_stop_price()
        self.execution_details_trading_capacity()
        self.id()
        self.market_identifiers_instrument()
        self.meta_model()
        self.order_identifiers_order_id_code()
        self.order_identifiers_parent_order_id()
        self.order_identifiers_trading_venue_transaction_id_code()
        self.order_identifiers_transaction_ref_no()
        self.price_forming_data_initial_quantity()
        self.price_forming_data_price()
        self.price_forming_data_remaining_quantity()
        self.price_forming_data_traded_quantity()
        self.report_details_transaction_ref_no()
        self.source_index()
        self.source_key()
        self.timestamps_trading_date_time()
        self.timestamps_order_received()
        self.timestamps_order_status_updated()
        self.timestamps_order_submitted()
        self.transaction_details_buy_sell_indicator()
        self.transaction_details_quantity()
        self.transaction_details_quantity_currency()
        self.transaction_details_price()
        self.transaction_details_price_average()
        self.transaction_details_price_currency()
        self.transaction_details_price_notation()
        self.transaction_details_quantity_notation()
        self.transaction_details_settlement_date()
        self.transaction_details_trading_capacity()
        self.transaction_details_trading_date_time()
        self.transaction_details_ultimate_venue()
        self.transaction_details_venue()

        self._derive_temp_parties_in_target_df()
        self.post_process()
        self.market_identifiers_parties()
        self._drop_derived_temp_parties_in_target_df()
        self.market_identifiers()
        return self.target_df

    def _pre_process(self) -> NoReturn:
        self.pre_process_df.loc[:, self.__transact_time__] = ConvertDatetime.process(
            self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.TRANSACT_TIME,
                target_attribute=self.__transact_time__,
                convert_to=ConvertTo.DATETIME,
            ),
        )

    def _post_process(self):
        """Populates temp columns needed by downstream tasks"""
        self.target_df = pd.concat([self.target_df, self._temp_newo_in_file()], axis=1)
        self.target_df[TempColumns.EXPIRY_DATE] = np.where(
            self.source_frame[SourceColumns.MATURITY_DATE].str.contains(r"\[.*\]"),
            self.source_frame[SourceColumns.MATURITY_DATE].str.get(0),
            self.source_frame[SourceColumns.MATURITY_DATE],
        )
        self.target_df[TempColumns.IS_CREATED_THROUGH_FALLBACK] = True
        self.target_df[TempColumns.BEST_EX_ASSET_CLASS_MAIN] = "Other Instruments"
        self.target_df[TempColumns.ASSET_CLASS] = MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=TempColumns.ASSET_CLASS,
                cases=[
                    Case(
                        query=f"`{SourceColumns.CFI_CODE}`.str.startswith('F')",
                        value=AssetClass.FUTURE,
                    ),
                    Case(
                        query=f"`{SourceColumns.CFI_CODE}`.str.startswith('OP|OC')",
                        value=AssetClass.OPTION,
                    ),
                ],
            ),
        )

        # create new records for 2 legs contracts
        # Add temp column for num legs so it can be used downstream to calculate
        # the price
        self.target_df.loc[:, TempColumns.NUM_LEGS] = self.source_frame.loc[
            :, SourceColumns.NO_LEGS
        ]
        multi_leg_mask = self.source_frame["ff_10010"] == "2"

        if multi_leg_mask.any():
            second_legs = self.target_df[multi_leg_mask].copy()

            # Price calculation and replacement for multi-leg trades in both self.target_df
            # and second_legs. Note that this needs to be done in the second_legs
            # df before the buy sell indicator is flipped.
            self.target_df.loc[:, TempColumns.LEG_SIDE] = self.source_frame.loc[
                multi_leg_mask, SourceColumns.LEG_SIDE
            ].str.get(0)
            second_legs.loc[:, TempColumns.LEG_SIDE] = self.source_frame.loc[
                multi_leg_mask, SourceColumns.LEG_SIDE
            ].str.get(1)
            self.target_df.loc[:, TempColumns.LEG_LAST_PX_1] = self.source_frame.loc[
                multi_leg_mask, SourceColumns.LEG_LAST_PX
            ].str.get(0)
            second_legs.loc[:, TempColumns.LEG_LAST_PX_1] = self.source_frame.loc[
                :, SourceColumns.LEG_LAST_PX
            ].str.get(0)
            self.target_df.loc[:, TempColumns.LEG_LAST_PX_2] = self.source_frame.loc[
                multi_leg_mask, SourceColumns.LEG_LAST_PX
            ].str.get(1)
            second_legs.loc[:, TempColumns.LEG_LAST_PX_2] = self.source_frame.loc[
                :, SourceColumns.LEG_LAST_PX
            ].str.get(1)

            prefixed_price_forming_price = add_prefix(
                prefix=ModelPrefix.ORDER_STATE,
                attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
            )
            prefixed_transac_details_price = add_prefix(
                prefix=ModelPrefix.ORDER_STATE,
                attribute=OrderColumns.TRANSACTION_DETAILS_PRICE,
            )
            self.target_df.loc[
                :, prefixed_price_forming_price
            ] = self._apply_price_conditions_on_dataframe(df=self.target_df)
            self.target_df.loc[:, prefixed_transac_details_price] = self.target_df.loc[
                :, prefixed_price_forming_price
            ]

            second_legs.loc[
                :, prefixed_price_forming_price
            ] = self._apply_price_conditions_on_dataframe(df=second_legs)
            second_legs.loc[:, prefixed_transac_details_price] = second_legs.loc[
                :, prefixed_price_forming_price
            ]
            second_legs.loc[
                :, add_prefix(ModelPrefix.ORDER, OrderColumns.BUY_SELL)
            ] = np.where(
                second_legs[add_prefix(ModelPrefix.ORDER, OrderColumns.BUY_SELL)]
                == "1",
                "2",
                "1",
            )
            second_legs.loc[
                :, add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.BUY_SELL)
            ] = np.where(
                second_legs[add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.BUY_SELL)]
                == "1",
                "2",
                "1",
            )
            second_legs.loc[
                :, OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR
            ] = np.where(
                second_legs[OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR]
                == BuySellIndicator.SELL.value,
                BuySellIndicator.BUYI.value,
                BuySellIndicator.SELL.value,
            )
            second_legs.loc[
                :, OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
            ] = second_legs[OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR]
            second_legs = second_legs.drop(
                labels=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT, axis=1
            )
            second_legs.loc[:, TempColumns.EXPIRY_DATE] = self.source_frame.loc[
                multi_leg_mask, SourceColumns.MATURITY_DATE
            ].str.get(1)

            # Remove all parent InstrumentIdentifiers Columns so that they can be populated again for second legs
            second_legs = second_legs.drop(
                columns=InstrumentIdentifierParamColumns.list(), errors="ignore"
            )

            second_legs = pd.concat(
                [
                    second_legs,
                    self._market_identifiers_instrument_second_leg(
                        self.source_frame[multi_leg_mask]
                    ),
                ],
                axis=1,
            )
            self.target_df = pd.concat([self.target_df, second_legs])

        # Drop temp column leg_side
        cols_to_drop = [
            TempColumns.LEG_SIDE,
            TempColumns.LEG_LAST_PX_1,
            TempColumns.LEG_LAST_PX_2,
            TempColumns.NUM_LEGS,
        ]
        self.target_df = self.target_df.drop(columns=cols_to_drop, errors="ignore")
        # Reset index after creating multiple records for multi-legs
        self.target_df = self.target_df.reset_index(drop=True)

    def _apply_price_conditions_on_dataframe(self, df: pd.DataFrame):
        """
        Apply the different conditions based on the multi-leg mask
        for all multi-leg trades
        :param df: self.target_df or second_legs
        :returns Series containing the correct price
        """
        result = pd.Series(pd.NA, index=df.index)
        buy_mask = (
            df.loc[:, OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR]
            == BuySellIndicator.BUYI.value
        )
        sell_mask = (
            df.loc[:, OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR]
            == BuySellIndicator.SELL.value
        )
        num_legs_2_mask = df.loc[:, TempColumns.NUM_LEGS].astype(str) == "2"
        leg_side_1_mask = df.loc[:, TempColumns.LEG_SIDE] == "1"
        leg_side_2_mask = df.loc[:, TempColumns.LEG_SIDE] == "2"
        condition_list = [
            num_legs_2_mask & buy_mask & leg_side_1_mask,
            num_legs_2_mask & buy_mask & leg_side_2_mask,
            num_legs_2_mask & sell_mask & leg_side_1_mask,
            num_legs_2_mask & sell_mask & leg_side_2_mask,
            ~num_legs_2_mask,
        ]

        choice_list = [
            df.loc[:, TempColumns.LEG_LAST_PX_1],
            df.loc[:, TempColumns.LEG_LAST_PX_2],
            df.loc[:, TempColumns.LEG_LAST_PX_2],
            df.loc[:, TempColumns.LEG_LAST_PX_1],
            df.loc[
                :,
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                ),
            ],
        ]
        result.loc[:] = np.select(condlist=condition_list, choicelist=choice_list)
        return result

    def _market_identifiers_instrument_second_leg(
        self, frame: pd.DataFrame
    ) -> pd.DataFrame:
        """Populates OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT for second legs"""
        if frame.empty:
            return pd.DataFrame()
        market_id_df = pd.DataFrame()
        market_id_df.loc[
            :, INSTRUMENT_ID_COLUMNS["underlying_symbol_attribute"]
        ] = frame[SourceColumns.SYMBOL]

        market_id_df.loc[:, INSTRUMENT_ID_COLUMNS["expiry_date_attribute"]] = frame[
            SourceColumns.MATURITY_DATE
        ].str.get(1)
        market_id_df.loc[:, INSTRUMENT_ID_COLUMNS["currency_attribute"]] = "USD"
        market_id_df.loc[:, INSTRUMENT_ID_COLUMNS["venue_attribute"]] = "XLME"
        market_id_df.loc[
            :, INSTRUMENT_ID_COLUMNS["asset_class_attribute"]
        ] = MapConditional.process(
            source_frame=frame,
            params=MapConditionalParams(
                target_attribute=INSTRUMENT_ID_COLUMNS["asset_class_attribute"],
                cases=[
                    Case(
                        query=f"`{SourceColumns.CFI_CODE}`.str.startswith('F')",
                        value=AssetClass.FUTURE,
                    ),
                    Case(
                        query=f"`{SourceColumns.CFI_CODE}`.str.startswith('OP|OC')",
                        value=AssetClass.OPTION,
                    ),
                ],
            ),
        ).loc[
            :, INSTRUMENT_ID_COLUMNS["asset_class_attribute"]
        ]

        return InstrumentIdentifiers.process(
            source_frame=market_id_df,
            params=InstrumentIdentifiersParams(
                **INSTRUMENT_ID_COLUMNS, retain_task_inputs=True
            ),
        )

    def _buy_sell(self) -> pd.DataFrame:
        """Populates OrderColumns.BUY_SELL from SourceColumns.SIDE"""
        return pd.concat(
            [
                MapValue.process(
                    source_frame=self.source_frame,
                    params=MapValueParams(
                        source_attribute=SourceColumns.SIDE,
                        target_attribute=add_prefix(
                            ModelPrefix.ORDER,
                            OrderColumns.BUY_SELL,
                        ),
                        value_map=BUY_SELL_MAP,
                    ),
                ),
                MapValue.process(
                    source_frame=self.source_frame,
                    params=MapValueParams(
                        source_attribute=SourceColumns.SIDE,
                        target_attribute=add_prefix(
                            ModelPrefix.ORDER_STATE,
                            OrderColumns.BUY_SELL,
                        ),
                        value_map=BUY_SELL_MAP,
                    ),
                ),
            ],
            axis=1,
        )

    def _data_source_name(self) -> pd.DataFrame:
        """Returns a data frame containing dataSourceName."""
        return pd.DataFrame(
            data=self.__data_source_name__,
            index=self.source_frame.index,
            columns=[OrderColumns.DATA_SOURCE_NAME],
        )

    def _date(self) -> pd.DataFrame:
        """Returns a data frame containing date as the target column"""
        return ConvertDatetime.process(
            self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.TRANSACT_TIME,
                target_attribute=OrderColumns.DATE,
                convert_to=ConvertTo.DATE,
            ),
        )

    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Populates executionDetails.buySellIndicator column"""
        return MapValue.process(
            source_frame=self.target_df,
            params=MapValueParams(
                source_attribute=add_prefix(
                    ModelPrefix.ORDER,
                    OrderColumns.BUY_SELL,
                ),
                target_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                value_map={
                    "1": BuySellIndicator.BUYI.value,
                    "2": BuySellIndicator.SELL.value,
                },
            ),
            auditor=self.auditor,
        )

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_limit_price(self) -> pd.DataFrame:
        """Populates OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE"""
        return MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE,
                cases=[
                    Case(
                        query=f"(`{SourceColumns.ORD_TYPE}` == '2')",
                        attribute=SourceColumns.PRICE,
                    ),
                ],
            ),
        )

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_order_status(self) -> pd.DataFrame:
        """Populates OrderColumns.EXECUTION_DETAILS_ORDER_STATUS"""
        exec_type_f_mask = self.source_frame[SourceColumns.EXEC_TYPE] == "F"
        exec_type_f_result = MapValue.process(
            source_frame=self.source_frame[exec_type_f_mask],
            params=MapValueParams(
                source_attribute=SourceColumns.ORD_STATUS,
                target_attribute=add_prefix(
                    ModelPrefix.ORDER_STATE, OrderColumns.EXECUTION_DETAILS_ORDER_STATUS
                ),
                case_insensitive=True,
                value_map={"1": "PARF", "2": "FILL"},
            ),
            auditor=self.auditor,
        )

        non_exec_type_f_result = MapValue.process(
            source_frame=self.source_frame[~exec_type_f_mask],
            params=MapValueParams(
                source_attribute=SourceColumns.EXEC_TYPE,
                target_attribute=add_prefix(
                    ModelPrefix.ORDER_STATE, OrderColumns.EXECUTION_DETAILS_ORDER_STATUS
                ),
                case_insensitive=True,
                value_map=static.ORD_STATUS_MAP,
            ),
            auditor=self.auditor,
        )
        order_state_order_status = pd.concat(
            [exec_type_f_result, non_exec_type_f_result]
        )
        order_order_status = MapStatic.process(
            source_frame=self.source_frame,
            params=MapStaticParams(
                target_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                ),
                target_value=OrderStatus.NEWO.value,
            ),
        )
        return pd.concat([order_state_order_status, order_order_status], axis=1)

    def _execution_details_order_type(self) -> pd.DataFrame:
        """Populates OrderColumns.EXECUTION_DETAILS_ORDER_TYPE"""
        value_map = {
            "1": "Market",
            "2": "Limit",
            "3": "Stop / Stop Loss",
            "4": "Stop Limit",
            "5": "Market On Close",
            "6": "With Or Without",
            "7": "Limit Or Better",
            "8": "Limit With Or Without",
            "9": "On Basis",
        }
        return MapValue.process(
            source_frame=self.source_frame,
            params=MapValueParams(
                source_attribute=SourceColumns.ORD_TYPE,
                target_attribute=OrderColumns.EXECUTION_DETAILS_ORDER_TYPE,
                case_insensitive=True,
                value_map=value_map,
                default_value="Market",
            ),
            auditor=self.auditor,
        )

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Populates executionDetails.outgoingOrderAddlInfo"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=MapValueParams(
                source_attribute=SourceColumns.ACCOUNT_TYPE,
                target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
                value_map={
                    1: "Client ISA",
                    2: "House",
                    3: "Client OSA",
                    7: "Gross OSA",
                },
            ),
            auditor=self.auditor,
        )

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        """Populates OrderColumns.EXECUTION_DETAILS_PASSIVE_AGGRESSIVE_INDICATOR using ff_1057"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=MapValueParams(
                source_attribute=SourceColumns.FF_1057,
                target_attribute=OrderColumns.EXECUTION_DETAILS_PASSIVE_AGGRESSIVE_INDICATOR,
                value_map={"Y": "Aggressor", "N": "Passive"},
            ),
        )

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        """Populates OrderColumns.EXECUTION_DETAILS_ROUTING_STRATEGY"""
        return MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=OrderColumns.EXECUTION_DETAILS_ROUTING_STRATEGY,
                cases=[
                    Case(
                        query=f"`{SourceColumns.ORDER_RESTRICTIONS}`.str.fullmatch('D', case=False, na=False)",
                        value="Non Algorithmic",
                    ),
                    Case(
                        query=f"`{SourceColumns.ORDER_RESTRICTIONS}`.str.fullmatch('E', case=False, na=False)",
                        value="Algorithmic",
                    ),
                ],
            ),
        )

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_stop_price(self) -> pd.DataFrame:
        """Populates OrderColumns.EXECUTION_DETAILS_STOP_PRICE from SourceColumns.STOP_PX"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.STOP_PX].values,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_STOP_PRICE],
        )

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        """Populates OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY from SourceColumns.LAST_CAPACITY"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=MapValueParams(
                source_attribute=SourceColumns.FF_10051,
                target_attribute=OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY,
                value_map=TRADING_CAPACITY_MAP,
            ),
        )

    def _execution_details_validity_period(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        """Not implemented"""

    def _financing_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _hierarchy(self) -> pd.DataFrame:
        """Not Implemented"""

    def _id(self) -> pd.DataFrame:
        """Returns a data frame containing _order.id and _orderState.id.
        This is populated from SourceColumns.EXEC_ID"""
        return pd.concat(
            [
                pd.DataFrame(
                    data=self.source_frame.loc[:, SourceColumns.ORDER_ID].values,
                    index=self.source_frame.index,
                    columns=[add_prefix(ModelPrefix.ORDER, OrderColumns.ID)],
                ),
                pd.DataFrame(
                    data=self.source_frame.loc[:, SourceColumns.ORDER_ID].values,
                    index=self.source_frame.index,
                    columns=[add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.ID)],
                ),
            ],
            axis=1,
        )

    def _is_discretionary(self) -> pd.DataFrame:
        """Not Implemented"""

    def _is_iceberg(self):
        """Not Implemented"""

    def _is_repo(self):
        """Not Implemented"""

    def _is_synthetic(self):
        """Not Implemented"""

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        """Not Implemented"""

    def _market_identifiers(self) -> pd.DataFrame:
        """Populates OrderColumns.MARKET_IDENTIFIERS"""
        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=MergeMarketIdentifiersParams(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                instrument_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        """
        Populates OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT
        """
        market_id_df = pd.DataFrame()
        market_id_df.loc[
            :, INSTRUMENT_ID_COLUMNS["underlying_symbol_attribute"]
        ] = self.source_frame[SourceColumns.SYMBOL]

        list_entries = (
            self.source_frame[SourceColumns.MATURITY_DATE]
            .astype("str")
            .str.contains(r"\[.*\]")
        )

        market_id_df.loc[
            list_entries,
            INSTRUMENT_ID_COLUMNS["expiry_date_attribute"],
        ] = self.source_frame.loc[list_entries, SourceColumns.MATURITY_DATE].str.get(0)
        market_id_df.loc[
            ~list_entries, INSTRUMENT_ID_COLUMNS["expiry_date_attribute"]
        ] = self.source_frame.loc[~list_entries, SourceColumns.MATURITY_DATE]

        market_id_df.loc[:, INSTRUMENT_ID_COLUMNS["currency_attribute"]] = "USD"
        market_id_df.loc[:, INSTRUMENT_ID_COLUMNS["venue_attribute"]] = "XLME"
        market_id_df.loc[
            :, INSTRUMENT_ID_COLUMNS["asset_class_attribute"]
        ] = MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=INSTRUMENT_ID_COLUMNS["asset_class_attribute"],
                cases=[
                    Case(
                        query=f"`{SourceColumns.CFI_CODE}`.str.startswith('F')",
                        value=AssetClass.FUTURE,
                    ),
                    Case(
                        query=f"`{SourceColumns.CFI_CODE}`.str.startswith('OP|OC')",
                        value=AssetClass.OPTION,
                    ),
                ],
            ),
        ).loc[
            :, INSTRUMENT_ID_COLUMNS["asset_class_attribute"]
        ]

        return InstrumentIdentifiers.process(
            source_frame=market_id_df,
            params=InstrumentIdentifiersParams(
                **INSTRUMENT_ID_COLUMNS, retain_task_inputs=True
            ),
        )

    def _derive_temp_parties_in_target_df(self) -> NoReturn:
        """
        Derives all the required temp party columns and adds them to target_df. The
        PartyPrefix.ID + reason that they are added to target_df and not pre_process_df
        is that multi-leg records will be split downstream (i.e., 2 records
        will be created for 1 multi-leg record) in post_process().
        After this, target_df will have a different shape to source_frame
        and pre_process_df, So any fields required by PartyIdentifiers
        (which is called after post_process) will need to be in the
        target_df

        """

        self.target_df = pd.concat(
            [
                self.target_df,
                pd.Series(
                    data=PartyPrefix.ID + self.source_frame[SourceColumns.TEXT],
                    name=TempPartyColumns.TRADER,
                ),
                pd.Series(
                    data=PartyPrefix.ID
                    + self.source_frame[SourceColumns.SENDER_COMP_ID],
                    name=TempPartyColumns.COUNTERPARTY,
                ),
                PartyPrefix.ID
                + MapConditionalAttributeFromListItems.process(
                    source_frame=self.source_frame,
                    params=MapFromListItemsParams(
                        source_attribute_for_pattern_search=SourceColumns.PARTY_ROLE,
                        source_attribute_for_mapping=SourceColumns.PARTY_ID,
                        regex_pattern="^300$",
                        target_attribute=TempPartyColumns.INVESTMENT_DM,
                    ),
                ),
                PartyPrefix.ID
                + MapConditionalAttributeFromListItems.process(
                    source_frame=self.source_frame,
                    params=MapFromListItemsParams(
                        source_attribute_for_pattern_search=SourceColumns.PARTY_ROLE,
                        source_attribute_for_mapping=SourceColumns.PARTY_ID,
                        regex_pattern="^301$",
                        target_attribute=TempPartyColumns.EXEC_WITHIN_FIRM,
                    ),
                ),
            ],
            axis=1,
        )

    def _drop_derived_temp_parties_in_target_df(self) -> NoReturn:
        """
        Drops the temp parties derived in _derive_temp_parties_in_target_df

        """

        self.target_df = self.target_df.drop(
            columns=TempPartyColumns.get_temp_party_cols(), errors="ignore"
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """Populates OrderColumns.MARKET_IDENTIFIERS_PARTIES"""
        return GenericOrderPartyIdentifiers.process(
            source_frame=self.target_df,
            params=PartyIdentifiersParams(
                trader_identifier=TempPartyColumns.TRADER,
                investment_decision_within_firm_identifier=TempPartyColumns.INVESTMENT_DM,
                execution_within_firm_identifier=TempPartyColumns.EXEC_WITHIN_FIRM,
                client_identifier=TempPartyColumns.TRADER,
                buyer_identifier=TempPartyColumns.TRADER,
                counterparty_identifier=TempPartyColumns.COUNTERPARTY,
                seller_identifier=TempPartyColumns.COUNTERPARTY,
                buy_sell_side_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                use_buy_mask_for_buyer_seller=True,
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _meta_model(self) -> pd.DataFrame:
        """Returns a data frame containing _order.__meta_model__ and _orderState.__meta_model__.
        The columns are populated with the static values Order and OrderState respectively"""
        return pd.concat(
            [
                MapStatic.process(
                    source_frame=self.source_frame,
                    params=MapStaticParams(
                        target_value="Order",
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.META_MODEL,
                        ),
                    ),
                ),
                MapStatic.process(
                    source_frame=self.source_frame,
                    params=MapStaticParams(
                        target_value="OrderState",
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.META_MODEL,
                        ),
                    ),
                ),
            ],
            axis=1,
        )

    def _order_class(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_aggregated_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        """Not implemented"""

    def _order_identifiers_internal_order_id_code(self) -> pd.DataFrame:
        """Populates OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE using SourceColumns.ORDER_ID"""
        return pd.DataFrame(
            data=self.source_frame[SourceColumns.EXEC_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE],
        )

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.orderIdCode"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.ORDER_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE],
        )

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        """Not implemented"""

    def _order_identifiers_parent_order_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        """Not implemented"""

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        """Not implemented"""

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        """Populates OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO using SourceColumns.ORDER_ID"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.ORDER_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO],
        )

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        """Not implemented"""

    def _price_forming_data_initial_quantity(self) -> pd.DataFrame:
        """Populates OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY with SourceColumns.ORDER_QTY"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.ORDER_QTY].values,
            index=self.source_frame.index,
            columns=[OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY],
        )

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        """Not implemented"""

    def _price_forming_data_price(self) -> pd.DataFrame:
        """Populates OrderColumns.PRICE_FORMING_DATA_PRICE from SourceColumns.LAST_PX"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.LAST_PX].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                )
            ],
        )

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        """Not implemented"""

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        """Not implemented"""

    def _price_forming_data_remaining_quantity(self) -> pd.DataFrame:
        """Returns a df with the col priceFormingData.remainingQuantity"""
        return pd.concat(
            [
                pd.DataFrame(
                    data=self.source_frame.loc[:, SourceColumns.LEAVES_QTY].values,
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY,
                        )
                    ],
                ),
                pd.DataFrame(
                    data=self.source_frame.loc[:, SourceColumns.ORDER_QTY].values,
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY,
                        )
                    ],
                ),
            ],
            axis=1,
        )

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        """Returns a df with the col priceFormingData.tradedQuantity"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.LAST_QTY].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                )
            ],
        )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """Populates OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO using SourceColumns.ORDER_ID"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.EXEC_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO],
        )

    def _source_index(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceIndex column"""
        return pd.DataFrame(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_INDEX],
        )

    def _source_key(self) -> pd.DataFrame:
        """Returns a df with the col sourceKey"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=StaticFields.FILE_URL,
                target_attribute=OrderColumns.SOURCE_KEY,
            ),
            auditor=self.auditor,
        )

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        """Not implemented"""

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        """Not implemented"""

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        """Not implemented"""

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        """Not implemented"""

    def _timestamps_order_received(self) -> pd.DataFrame:
        """Populates OrderColumns.TIMESTAMPS_ORDER_RECEIVED from TrdRegTimestamp"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, self.__transact_time__].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TIMESTAMPS_ORDER_RECEIVED],
        )

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        """Populates OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED from TrdRegTimestamp"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, self.__transact_time__].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED],
        )

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        """Populates OrderColumns.TIMESTAMPS_ORDER_SUBMITTED from TrdRegTimestamp"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, self.__transact_time__].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TIMESTAMPS_ORDER_SUBMITTED],
        )

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        """Returns a df with col timestamps.tradingDateTime"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, self.__transact_time__].values,
            index=self.pre_process_df.index,
            columns=[
                add_prefix(
                    ModelPrefix.ORDER_STATE, OrderColumns.TIMESTAMPS_TRADING_DATE_TIME
                )
            ],
        )

    def _timestamps_validity_period(self) -> pd.DataFrame:
        """Populates OrderColumns.TIMESTAMPS_VALIDITY_PERIOD using SourceColuns.TIME_IN_FORCE"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.TIME_IN_FORCE]
            .apply(lambda x: [x])
            .values,
            index=self.source_frame.index,
            columns=[OrderColumns.TIMESTAMPS_VALIDITY_PERIOD],
        )

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(
        self,
    ) -> pd.DataFrame:
        """Not implemented"""

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        """Not implemented"""

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        """Not implemented"""

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(
        self,
    ) -> pd.DataFrame:
        """Not implemented"""

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        """Not implemented"""

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        """Not implemented"""

    def _traders_algos_waivers_indicators_waiver_indicator(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Populates OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR from SourceColumns.SIDE"""
        return MapValue.process(
            source_frame=self.target_df,
            params=MapValueParams(
                source_attribute=add_prefix(ModelPrefix.ORDER, OrderColumns.BUY_SELL),
                target_attribute=OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
                value_map={
                    "1": BuySellIndicator.BUYI.value,
                    "2": BuySellIndicator.SELL.value,
                },
            ),
            auditor=self.auditor,
        )

    def _transaction_details_commission_amount(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_commission_amount_currency(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_commission_amount_type(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_cross_indicator(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_position_effect(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_position_id(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_price(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_PRICE from OrderColumns.PRICE_FORMING_DATA_PRICE"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :,
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                ),
            ].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.TRANSACTION_DETAILS_PRICE,
                )
            ],
        )

    def _transaction_details_price_average(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.AVG_PX].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_AVERAGE],
        )

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY using USD"""
        return pd.DataFrame(
            data="USD",
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY],
        )

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION from SourceColumns.PRICE_TYPE"""
        return pd.DataFrame(
            data=PriceNotation.MONE.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION],
        )

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """Returns a df with the col transactionDetails.quantity"""
        return MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY,
                cases=[
                    Case(
                        query=f"`{SourceColumns.LAST_QTY}`.isnull()",
                        attribute=SourceColumns.ORDER_QTY,
                    ),
                    Case(
                        query=f"`{SourceColumns.LAST_QTY}`.notnull()",
                        attribute=SourceColumns.LAST_QTY,
                    ),
                ],
            ),
        )

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY from SourceColumns.CURRENCY"""
        return pd.DataFrame(
            data="USD",
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY],
        )

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION with UNIT"""
        return pd.DataFrame(
            data=QuantityNotation.UNIT.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION],
        )

    def _transaction_details_record_type(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE with Market Side"""
        return pd.DataFrame(
            data="Market Side",
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE],
        )

    def _transaction_details_settlement_amount(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_settlement_amount_currency(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_settlement_date(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_swap_directionalities(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY from SourceColumns.LAST_CAPACITY"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=MapValueParams(
                source_attribute=SourceColumns.FF_10051,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY,
                value_map=TRADING_CAPACITY_MAP,
            ),
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """Returns a df with the col transactionDetails.transactionDetailsTradingDateTime"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, self.__transact_time__].values,
            index=self.pre_process_df.index,
            columns=[
                add_prefix(
                    ModelPrefix.ORDER_STATE,
                    OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME,
                )
            ],
        )

    def _transaction_details_trail_id(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE from SourceColumns.LAST_MKT"""
        return pd.DataFrame(
            data=Venue.XLME,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_VENUE],
        )

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_venue(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_VENUE from SourceColumns.LAST_MKT"""
        return pd.DataFrame(
            data=Venue.XLME,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE],
        )

    def _temp_newo_in_file(self) -> pd.DataFrame:
        """
        Temporary column for RemoveDuplicateNEWO
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=TempColumns.NEWO_IN_FILE,
                cases=[
                    Case(
                        query=f"`{SourceColumns.EXEC_TYPE}`.str.fullmatch('0', case=False, na=False)",
                        value=True,
                    ),
                    Case(
                        query=f"~(`{SourceColumns.EXEC_TYPE}`.str.fullmatch('0', case=False, na=False))",
                        value=False,
                    ),
                ],
            ),
        )

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        """Not implemented"""
