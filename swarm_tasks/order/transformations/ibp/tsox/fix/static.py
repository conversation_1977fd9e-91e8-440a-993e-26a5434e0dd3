from se_elastic_schema.static.mifid2 import TradingCapacity


class SourceColumns:
    AVG_PX = "AvgPx"
    COUPON_RATE = "CouponRate"
    CURRENCY = "Currency"
    EXEC_ID = "ExecID"
    EXEC_TYPE = "ExecType"
    FF_1903 = "ff_1903"
    FF_7014 = "ff_7014"
    ISSUER = "Issuer"
    LAST_CAPACITY = "LastCapacity"
    LAST_PX = "LastPx"
    LAST_QTY = "LastQty"
    LAST_MKT = "LastMkt"
    LEAVES_QTY = "LeavesQty"
    ORDER_ID = "OrderID"
    ORDER_QTY = "OrderQty"
    ORD_TYPE = "OrdType"
    ORD_STATUS = "OrdStatus"
    SENDING_TIME = "SendingTime"
    SETTL_DATE = "SettlDate"
    SIDE = "Side"
    SECONDARY_ORDER_ID = "SecondaryOrderID"
    SENDER_LOCATION_ID = "SenderLocationID"
    PARTY_ID = "PartyID"
    PARTY_ROLE = "PartyRole"
    PRICE_TYPE = "PriceType"
    QTY_TYPE = "QtyType"
    TRANSACT_TIME = "TransactTime"
    TRD_REG_TIMESTAMP = "TrdRegTimestamp"
    TRD_REG_TIMESTAMP_TYPE = "TrdRegTimestampType"
    YIELD = "Yield"


class TempColumns:
    NEWO_IN_FILE = "__newo_in_file__"
    INSTRUMENT_FULL_NAME = "__instrument_full_name__"
    INSTRUMENT_UNIQUE_IDENTIFIER = "__instrument_unique_identifier__"


ORD_STATUS_MAP = {
    "0": "NEWO",
    "L": "TRIG",
    "3": "DNFD",
    "4": "CAME",
    "5": "REME",
    "6": "PNDC",
    "8": "REMO",
    "C": "EXPI",
    "1": "PARF",
    "2": "FILL",
    "K": "REMH",
    "7": "REMA",
    "9": "SUSP",
    "E": "CHME",
    "M": "PNDM",
}

BUY_SELL_MAP = {
    "1": "1",
    "2": "2",
    "3": "1",
    "4": "2",
    "5": "2",
    "6": "2",
    "H": "2",
}

TRADING_CAPACITY_MAP = {
    "1": TradingCapacity.AOTC.value,
    "2": TradingCapacity.AOTC.value,
    "3": TradingCapacity.MTCH.value,
    "4": TradingCapacity.DEAL.value,
    "5": TradingCapacity.DEAL.value,
}
