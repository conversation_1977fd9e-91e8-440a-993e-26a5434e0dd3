import pandas as pd
from se_core_tasks.map.map_conditional import Case
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.order.transformations.eze.eclipse.eze_eclipse_transformations import (
    EzeEclipseOrderTransformations,
)
from swarm_tasks.order.transformations.eze.eclipse.static import DerivedCols
from swarm_tasks.order.transformations.eze.eclipse.static import SourceCols
from swarm_tasks.order.transformations.eze.eclipse.static import TempColumns
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)


class SellarondaEzeEclipseOrderTransformations(EzeEclipseOrderTransformations):
    def _get_counterparty(self) -> pd.DataFrame:
        """
        - Counterparty: If DestinationDisplayName="FILM" then use CounterpartyCode,
                        else use AGGREGATED_DESTINATION_DISPLAY_NAME_WITH_PREFIX
        """
        self.source_frame[TempColumns.TEMP_COUNTERPARTY] = self.source_frame[
            SourceCols.EXECUTIONS
        ].apply(
            lambda x: x[0].get(SourceCols.COUNTERPARTY_CODE, pd.NA)
            if isinstance(x, list) and x and isinstance(x[0], dict)
            else pd.NA
        )
        self.source_frame[
            DerivedCols.COUNTERPARTY_CODE_WITH_PREFIX
        ] = self.source_frame[TempColumns.TEMP_COUNTERPARTY].apply(
            lambda val: PartyPrefix.ID + val if val is not pd.NA else pd.NA
        )
        self.source_frame.drop(columns=[TempColumns.TEMP_COUNTERPARTY], inplace=True)

        cases = [
            Case(
                query=f"`{SourceCols.DESTINATION_DISPLAY_NAME}`.str.fullmatch('FILM', case=False, na=False)",
                attribute=DerivedCols.COUNTERPARTY_CODE_WITH_PREFIX,
            ),
            Case(
                query=f"~`{SourceCols.DESTINATION_DISPLAY_NAME}`.str.fullmatch('FILM', case=False, na=False)",
                attribute=DerivedCols.AGGREGATED_DESTINATION_DISPLAY_NAME_WITH_PREFIX,
            ),
        ]
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=DerivedCols.COUNTERPARTY,
                cases=cases,
            ),
        )
