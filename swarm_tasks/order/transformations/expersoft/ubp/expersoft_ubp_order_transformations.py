import os

import pandas as pd
from se_core_tasks.map.map_conditional import Case
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ParamsConvertMinorToMajor,
)
from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI
from swarm_tasks.steeleye.generic.get_tenant_lei import Params as ParamsGetTenantLEI
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ParamsConcatAttributes,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ConvertDatetimeParams,
)
from swarm_tasks.transform.datetime.join_date_and_time import JoinDateAndTimeFormat
from swarm_tasks.transform.datetime.join_date_and_time import (
    Params as ParamsJoinDateAndTimeFormat,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as ParamsMapAttribute
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as ParamsMapValue
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as ParamsMergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    Params as ParamsInstrumentIdentifiers,
)


class TempColumns:
    COUNTERPARTY_WITH_ID = "__counterparty_with_id__"
    TRADER_ID = "__trader_id__"
    CLIENT_ID = "__client_id__"
    EXECUTING_ENTITY_WITH_LEI = "__executing_entity_with_lei__"
    DECISION_MAKER_ID = "__decision_maker_id__"
    INDEX = "__index__"


class SourceColumns:
    ORDER_DATE = "ORDERDATE"
    T_CODE = "T.CODE"
    TIME_ORDER_RECEIVED = "TIMEORDERRECEIVED"
    TIME_ORDER_PLACED_TO_TRADER = "TIMEORDERPLACEDTOTRADER"
    SETTLEMENT_DATE = "SETTLEMENTDATE"
    TIME_EXEC_PRICE_RECEIVED = "TIMEEXEC.PRICERECEIVED"
    ORDER_TYPE = "ORDERTYPE(MARKET,LIMIT,VWAP,ETC)"
    ORDER_NATURE = "ORDERNATURE(DISCR.ADV.OREXEC.)"
    ALGORITHM_USED = "ALGORITHMUSED"
    ORDER_DETAILS_BUY_SELL = "ORDERDETAILS(BUY/SELL)"
    EXECUTION_PRICE = "EXECUTIONPRICE"
    EXECUTION_PRICE_CCY = "EXECUTIONPRICECCY"
    UNITS_NOMINALVALUE_MONETARYVALUE = "UNITS/NOMINALVALUE/MONETARYVALUE"
    ORDER_DETAILS_QUANTITY = "ORDERDETAILS(QUANTITY)"
    VENUE = "VENUE"
    ISIN = "ISIN"
    COUNTER_PARTY = "COUNTERPARTY"
    ORDER_PLACED_BY = "ORDERPLACEDBY"
    DECISION_MAKER = "DECISIONMAKER"


class ExpersoftUbpOrderTransformations(AbstractOrderTransformations):
    """Transformations class for Expersoft UBP Orders.
    Fields are created for Order and OrderState models.
    """

    def _pre_process(self):
        pass

    def process(self):
        """All the schema target columns which need to be populated are populated in
        self.target_df by the public methods called by process(). 3 temp columns are
        populated here as well as these are present in all Order flows: __meta_model__,
        marketIdentifiers.parties and marketIdentifiers.instrument.
        """
        self.pre_process()
        self.buy_sell()
        self.data_source_name()
        self.date()
        self.execution_details_buy_sell_indicator()
        self.execution_details_limit_price()
        self.execution_details_order_status()
        self.execution_details_order_type()
        self.execution_details_outgoing_order_addl_info()
        self.execution_details_passive_aggressive_indicator()
        self.execution_details_routing_strategy()
        self.execution_details_trading_capacity()
        self.hierarchy()
        self.meta_model()
        self.source_index()
        self.source_key()
        self.order_identifiers_order_id_code()  # need Source_index output
        self.id()  # needs order_identifiers_order_id_code() output
        self.order_identifiers_transaction_ref_no()  # needs order_identifiers_order_id_code() output
        self.price_forming_data_initial_quantity()
        self.price_forming_data_price()
        self.price_forming_data_traded_quantity()  # needs price_forming_data_initial_quantity() output
        self.report_details_transaction_ref_no()  # needs order_identifiers_order_id_code() output
        self.timestamps_order_received()
        self.timestamps_order_status_updated()
        self.timestamps_order_submitted()  # needs timestamps_order_received() output
        self.timestamps_trading_date_time()  # needs timestamps_order_received() output
        self.transaction_details_buy_sell_indicator()  # needs execution_details_buy_sell_indicator() output
        self.transaction_details_complex_trade_component_id()
        self.transaction_details_price()  # needs price_forming_data_price() output
        self.transaction_details_price_currency()
        self.transaction_details_price_notation()
        self.transaction_details_quantity()  # needs price_forming_data_initial_quantity() output
        self.transaction_details_quantity_currency()
        self.transaction_details_quantity_notation()
        self.transaction_details_record_type()
        self.transaction_details_settlement_date()
        self.transaction_details_trading_capacity()  # needs execution_details_trading_capacity() output
        self.transaction_details_trading_date_time()  # needs timestamps_order_received() output
        self.transaction_details_ultimate_venue()
        self.transaction_details_venue()
        self.market_identifiers_instrument()
        self.market_identifiers_parties()
        self.market_identifiers()
        self.post_process()
        return self.target_df

    def _post_process(self):
        pass

    def _buy_sell(self) -> pd.DataFrame:
        """Returns a data frame containing _order.buySell and _orderState.buySell.
        This is populated from SourceColumns.ORDER_DETAILS_BUY_SELL"""
        return pd.concat(
            [
                MapAttribute.process(
                    source_frame=self.source_frame,
                    params=ParamsMapAttribute(
                        source_attribute=SourceColumns.ORDER_DETAILS_BUY_SELL,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER, attribute=OrderColumns.BUY_SELL
                        ),
                    ),
                    auditor=self.auditor,
                ),
                MapAttribute.process(
                    source_frame=self.source_frame,
                    params=ParamsMapAttribute(
                        source_attribute=SourceColumns.ORDER_DETAILS_BUY_SELL,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.BUY_SELL,
                        ),
                    ),
                    auditor=self.auditor,
                ),
            ],
            axis=1,
        )

    def _data_source_name(self) -> pd.DataFrame:
        """Returns a data frame containing dataSourceName.
        This is populated with the static value 'Expersoft UBP'"""
        return pd.DataFrame(
            data="Expersoft UBP",
            index=self.source_frame.index,
            columns=[OrderColumns.DATA_SOURCE_NAME],
        )

    def _date(self) -> pd.DataFrame:
        """Returns a data frame containing date.
        This is populated from SourceColumns.ORDER_DATE"""
        return ConvertDatetime.process(
            self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.ORDER_DATE,
                source_attribute_format="%d%m%Y",
                target_attribute=OrderColumns.DATE,
                convert_to="date",
            ),
        )

    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        pass

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.buySellIndicator.
        This is populated from SourceColumns.ORDER_DETAILS_BUY_SELL"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.ORDER_DETAILS_BUY_SELL,
                target_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                case_insensitive=True,
                value_map={
                    "buy": BuySellIndicator.BUYI.value,
                    "sell": BuySellIndicator.SELL.value,
                },
            ),
            auditor=self.auditor,
        )

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        pass

    def _execution_details_limit_price(self) -> pd.DataFrame:
        """Returns a dataframe with executionDetails.limitPrice populated.
        Maps SourceColumns.EXECUTION_PRICE to executionDetails.limitPrice only when SourceColumns.Order_Type is "limit".
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE,
                cases=[
                    Case(
                        query=f"`{SourceColumns.ORDER_TYPE}`.str.fullmatch('limit', case=False, na=False)",
                        attribute=SourceColumns.EXECUTION_PRICE,
                    ),
                ],
            ),
        )

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        pass

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        pass

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        pass

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        pass

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        pass

    def _execution_details_order_status(self) -> pd.DataFrame:
        """Returns a data frame containing _order.executionDetails.orderStatus and
        _orderState.executionDetails.orderStatus.
        This is populated with the static values NEWO and FILL respectively"""
        return pd.DataFrame(
            data=[[OrderStatus.NEWO.value, OrderStatus.FILL.value]],
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                ),
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                ),
            ],
        )

    def _execution_details_order_type(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.orderType.
        This is populated with 'LIMIT', 'MARKET' or 'VWAP' based on the value in
        SourceColumns.ORDER_TYPE"""

        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.ORDER_TYPE,
                target_attribute=OrderColumns.EXECUTION_DETAILS_ORDER_TYPE,
            ),
            auditor=self.auditor,
        )

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.outgoingOrderAddlInfo."""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.ORDER_NATURE,
                target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
            ),
            auditor=self.auditor,
        )

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.routingStrategy.
        This is populated from SourceColumns.ROUTED_ORDER_CODE"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.ALGORITHM_USED,
                target_attribute=OrderColumns.EXECUTION_DETAILS_ROUTING_STRATEGY,
            ),
            auditor=self.auditor,
        )

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        pass

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        pass

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_stop_price(self) -> pd.DataFrame:
        pass

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.tradingCapacity.
        This is populated with the static value 'AOTC'"""
        return pd.DataFrame(
            data=TradingCapacity.AOTC.value,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY],
        )

    def _execution_details_validity_period(self) -> pd.DataFrame:
        pass

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        pass

    def _financing_type(self) -> pd.DataFrame:
        pass

    def _hierarchy(self) -> pd.DataFrame:
        pass

    def _id(self) -> pd.DataFrame:
        """Returns a data frame containing _order.id and _orderState.id.
        This is populated from OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE
        Assumes that  _order_identifiers_order_id_code() has been called earlier
        """
        return pd.concat(
            [
                MapAttribute.process(
                    source_frame=self.target_df.loc[
                        :, [OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE]
                    ],
                    params=ParamsMapAttribute(
                        source_attribute=OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER, attribute=OrderColumns.ID
                        ),
                    ),
                    auditor=self.auditor,
                ),
                MapAttribute.process(
                    source_frame=self.target_df.loc[
                        :, [OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE]
                    ],
                    params=ParamsMapAttribute(
                        source_attribute=OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.ID,
                        ),
                    ),
                    auditor=self.auditor,
                ),
            ],
            axis=1,
        )

    def _is_discretionary(self) -> pd.DataFrame:
        pass

    def _is_iceberg(self):
        pass

    def _is_repo(self):
        pass

    def _is_synthetic(self):
        pass

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        pass

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        pass

    def _jurisdiction_country(self) -> pd.DataFrame:
        pass

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        pass

    def _market_identifiers(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers by calling MergeMarketIdentifiers.
        Assumes _market_identifiers_instrument() and _market_identifiers_parties() have been
        called earlier"""
        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=ParamsMergeMarketIdentifiers(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                instrument_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.instrument by calling InstrumentIdentifiers."""
        return InstrumentIdentifiers.process(
            source_frame=self.source_frame,
            params=ParamsInstrumentIdentifiers(
                isin_attribute=SourceColumns.ISIN,
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
            ),
            auditor=self.auditor,
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.parties by calling PartyIdentifiers
        Assumes that _transaction_details_buy_sell_indicator() has been
        called earlier"""
        parties_source_frame = pd.concat(
            [
                self.source_frame,
                self._get_trader(),
                self._get_executing_entity(),
                self._get_counterparty(),
                self._get_client_identifier_client(),
                self._get_decision_maker(),
                self.target_df.loc[
                    :, [OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR]
                ],
            ],
            axis=1,
        )
        return GenericOrderPartyIdentifiers.process(
            source_frame=parties_source_frame,
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                counterparty_identifier=TempColumns.COUNTERPARTY_WITH_ID,
                client_identifier=TempColumns.CLIENT_ID,
                executing_entity_identifier=TempColumns.EXECUTING_ENTITY_WITH_LEI,
                buyer_identifier=TempColumns.EXECUTING_ENTITY_WITH_LEI,
                seller_identifier=TempColumns.COUNTERPARTY_WITH_ID,
                trader_identifier=TempColumns.TRADER_ID,
                buyer_decision_maker_identifier=TempColumns.DECISION_MAKER_ID,
                buy_sell_side_attribute=OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
                use_buy_mask_for_buyer_seller=True,
                use_buy_mask_for_buyer_seller_decision_maker=True,
            ),
        )

    def _meta_model(self) -> pd.DataFrame:
        """Returns a data frame containing _order.__meta_model__ and _orderState.__meta_model__.
        The columns are populated with the static values Order and OrderState respectively"""
        return pd.DataFrame(
            data=[["Order", "OrderState"]],
            index=self.source_frame.index,
            columns=[
                add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.META_MODEL),
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.META_MODEL,
                ),
            ],
        )

    def _order_class(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_aggregated_order_id_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        pass

    def _order_identifiers_internal_order_id_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.orderIdCode.
        This is populated from SourceColumns.ORDER_DATE SourceColumns.T_CODE self.source_frame.index"""

        index_source_df = self._get_index_string()

        return ConcatAttributes.process(
            source_frame=index_source_df,
            params=ParamsConcatAttributes(
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE,
                source_attributes=[
                    SourceColumns.ORDER_DATE,
                    SourceColumns.T_CODE,
                    TempColumns.INDEX,
                ],
                delimiter="",
            ),
        )

    def _order_identifiers_parent_order_id(self):
        pass

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        pass

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.transactionRefNo.
        It assumes that _order_identifiers_order_id_code() has been called earlier"""
        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE]
            ],
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE,
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
            ),
            auditor=self.auditor,
        )

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        pass

    def _price_forming_data_initial_quantity(self):
        """Returns a data frame containing priceFormingData.initialQuantity.
        This is populated from SourceColumns.ORDER_DETAILS_QUANTITY"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.ORDER_DETAILS_QUANTITY,
                target_attribute=OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
                cast_to="numeric.absolute",
            ),
            auditor=self.auditor,
        )

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price(self) -> pd.DataFrame:
        """Returns a data frame containing priceFormingData.price.
        mapped to SourceColumns.EXECUTION_PRICE"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.EXECUTION_PRICE,
                target_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                ),
            ),
            auditor=self.auditor,
        )

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        pass

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        """Returns a data frame containing priceFormingData.tradedQuantity.
        It assumes that _price_forming_data_initial_quantity() has been called earlier"""
        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY]
            ],
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
                target_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                ),
            ),
            auditor=self.auditor,
        )

    def _price_forming_data_remaining_quantity(self):
        pass

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """Returns a data frame containing reportDetails.transactionRefNo.
        It assumes that _order_identifiers_order_id_code() has been called earlier"""
        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE]
            ],
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE,
                target_attribute=OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO,
            ),
            auditor=self.auditor,
        )

    def _source_index(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceIndex column"""
        return pd.DataFrame(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_INDEX],
        )

    def _source_key(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceKey column from the SWARM_FILE_URL"""
        return pd.DataFrame(
            data=os.getenv("SWARM_FILE_URL"),
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_KEY],
        )

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        pass

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        pass

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        pass

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        pass

    def _timestamps_order_received(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.orderReceived.
        This is populated from SourceColumns.ORDER_DATE and SourceColumns.TIME_ORDER_RECEIVED"""
        return JoinDateAndTimeFormat.process(
            source_frame=self.source_frame,
            params=ParamsJoinDateAndTimeFormat(
                source_date_attribute=SourceColumns.ORDER_DATE,
                source_time_attribute=SourceColumns.TIME_ORDER_RECEIVED,
                source_format="%d%m%Y%H:%M:%S",
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
            ),
        )

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.orderSubmitted.
        This is populated from SourceColumns.ORDER_DATE and SourceColumns.TIME_ORDER_PLACED_TO_TRADER"""
        return JoinDateAndTimeFormat.process(
            source_frame=self.source_frame,
            params=ParamsJoinDateAndTimeFormat(
                source_date_attribute=SourceColumns.ORDER_DATE,
                source_time_attribute=SourceColumns.TIME_ORDER_PLACED_TO_TRADER,
                source_format="%d%m%Y%H:%M:%S",
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
            ),
        )

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.orderStatusUpdated."""
        return JoinDateAndTimeFormat.process(
            source_frame=self.source_frame,
            params=ParamsJoinDateAndTimeFormat(
                source_date_attribute=SourceColumns.ORDER_DATE,
                source_time_attribute=SourceColumns.TIME_EXEC_PRICE_RECEIVED,
                source_format="%d%m%Y%H:%M:%S",
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED,
            ),
        )

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.tradingDateTime.
        This is populated from SourceColumns.ORDER_DATE and SourceColumns.TIME_ORDER_PLACED_TO_TRADER"""
        return JoinDateAndTimeFormat.process(
            source_frame=self.source_frame,
            params=ParamsJoinDateAndTimeFormat(
                source_date_attribute=SourceColumns.ORDER_DATE,
                source_time_attribute=SourceColumns.TIME_ORDER_PLACED_TO_TRADER,
                source_format="%d%m%Y%H:%M:%S",
                target_attribute=OrderColumns.TIMESTAMPS_TRADING_DATE_TIME,
            ),
        )

    def _timestamps_validity_period(self):
        pass

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(self):
        pass

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(self):
        pass

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_short_selling_indicator(self):
        pass

    def _traders_algos_waivers_indicators_waiver_indicator(self) -> pd.DataFrame:
        pass

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        pass

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.buySellIndicator.
        Assumes that _execution_details_buy_sell_indicator() has been called earlier"""
        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR]
            ],
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_commission_amount(self):
        pass

    def _transaction_details_commission_amount_currency(self):
        pass

    def _transaction_details_commission_amount_type(self):
        pass

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_cross_indicator(self):
        pass

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        pass

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        pass

    def _transaction_details_net_amount(self):
        pass

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        pass

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        pass

    def _transaction_details_position_effect(self):
        pass

    def _transaction_details_position_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_price(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.price.
        Assumes that _price_forming_data_price() has been called earlier"""

        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :,
                [
                    add_prefix(
                        prefix=ModelPrefix.ORDER_STATE,
                        attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                    )
                ],
            ],
            params=ParamsMapAttribute(
                source_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                ),
                target_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.TRANSACTION_DETAILS_PRICE,
                ),
            ),
            auditor=self.auditor,
        )

    def _transaction_details_price_average(self):
        pass

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.priceCurrency.
        Mapped from SourceColumns.EXECUTION_PRICE_CCY"""
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.EXECUTION_PRICE_CCY,
                target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
            ),
        )

    def _transaction_details_price_notation(self):
        """Returns a data frame containing transactionDetails.priceNotation.
        This is populated with the static value 'MONE'"""
        return pd.DataFrame(
            data=PriceNotation.MONE.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION],
        )

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        pass

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        pass

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.quantity.
        Assumes that _price_forming_data_initial_quantity() has been called earlier"""

        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY]
            ],
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
                target_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY,
                ),
            ),
            auditor=self.auditor,
        )

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.quantityCurrency.
        Mapped from SourceColumns.EXECUTION_PRICE_CCY"""

        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.EXECUTION_PRICE_CCY,
                target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
            ),
        )

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.quantityNotation.
        Mapped from  SourceColumns.UNITS_NOMINALVALUE_MONETARYVALUE"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.UNITS_NOMINALVALUE_MONETARYVALUE,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION,
                case_insensitive=True,
                value_map={
                    "Nominal Value": QuantityNotation.NOML.value,
                    "Units": QuantityNotation.UNIT.value,
                    "MonetaryValue": QuantityNotation.MONE.value,
                    "ounces": QuantityNotation.UNIT.value,
                    "contracts": QuantityNotation.UNIT.value,
                },
            ),
            auditor=self.auditor,
        )

    def _transaction_details_record_type(self):
        pass

    def _transaction_details_settlement_amount(self):
        pass

    def _transaction_details_settlement_amount_currency(self):
        pass

    def _transaction_details_settlement_date(self):
        """Returns a data frame containing transactionDetails.settlementDate."""
        return ConvertDatetime.process(
            self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.SETTLEMENT_DATE,
                source_attribute_format="%d%m%Y",
                target_attribute=OrderColumns.TRANSACTION_DETAILS_SETTLEMENT_DATE,
                convert_to="date",
            ),
        )

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        pass

    def _transaction_details_swap_directionalities(self):
        pass

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.tradingCapacity.
        This is populated with the static value 'AOTC'"""
        return pd.DataFrame(
            data=TradingCapacity.AOTC.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY],
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.tradingDateTime.
        This is populated from SourceColumns.ORDER_DATE and SourceColumns.TIME_ORDER_PLACED_TO_TRADER"""
        return JoinDateAndTimeFormat.process(
            source_frame=self.source_frame,
            params=ParamsJoinDateAndTimeFormat(
                source_date_attribute=SourceColumns.ORDER_DATE,
                source_time_attribute=SourceColumns.TIME_ORDER_PLACED_TO_TRADER,
                source_format="%d%m%Y%H:%M:%S",
                target_attribute=OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME,
            ),
        )

    def _transaction_details_trail_id(self):
        pass

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        pass

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        pass

    def _transaction_details_upfront_payment_currency(self):
        pass

    def _transaction_details_venue(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.venue.
        This is populated with SourceColumns.VENUE"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.VENUE,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_VENUE,
            ),
            auditor=self.auditor,
        )

    # The following functions are used to populate temporary columns
    def _get_client_identifier_client(self):
        """Gets the clientIdentifier.client value to be used in PartyIdentifiers
        the source column is SourceColumns.T_CODE
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.T_CODE,
                target_attribute=TempColumns.CLIENT_ID,
                prefix=PartyPrefix.ID,
            ),
            auditor=self.auditor,
        )

    def _get_trader(self):
        """Gets the trader value to be used in PartyIdentifiers
        the source column is SourceColumns.BROKER_BROKER_NAME
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.ORDER_PLACED_BY,
                target_attribute=TempColumns.TRADER_ID,
                prefix=PartyPrefix.ID,
            ),
            auditor=self.auditor,
        )

    def _get_executing_entity(self):
        """Gets the executing entity value to be used in PartyIdentifiers from the
        AccountFirm record"""
        return GetTenantLEI.process(
            source_frame=self.source_frame,
            params=ParamsGetTenantLEI(
                target_column_prefix=PartyPrefix.LEI,
                target_lei_column=TempColumns.EXECUTING_ENTITY_WITH_LEI,
            ),
        )

    def _get_counterparty(self):
        """Gets the counterparty value to be used in PartyIdentifiers from SourceColumns.LEI"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.COUNTER_PARTY,
                target_attribute=TempColumns.COUNTERPARTY_WITH_ID,
                prefix=PartyPrefix.ID,
            ),
            auditor=self.auditor,
        )

    def _get_decision_maker(self):
        """Gets the counterparty value to be used in PartyIdentifiers from SourceColumns.LEI"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.DECISION_MAKER,
                target_attribute=TempColumns.DECISION_MAKER_ID,
                prefix=PartyPrefix.LEI,
            ),
            auditor=self.auditor,
        )

    def _get_index_string(self):
        """Returns index value in string format to be used in concatination"""
        self.source_frame.loc[:, TempColumns.INDEX] = self.source_frame.index.astype(
            "str"
        )
        return self.source_frame

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        """Not implemented"""
