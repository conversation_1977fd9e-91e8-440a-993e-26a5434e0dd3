from typing import Optional

import pandas as pd
from dateutil.parser import <PERSON><PERSON>r<PERSON>rror
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_conditional import Case
from se_core_tasks.utils.datetime import DatetimeFormat
from se_elastic_schema.static.reference import PricingSource
from se_trades_tasks.order_and_tr.instrument.identifiers.identifiers import (
    InstrumentIdentifiers,
)
from se_trades_tasks.order_and_tr.static import (
    AssetClass as TrAssetClass,
)
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.order.feed.flextrade.static import FlextradeFixColumns
from swarm_tasks.order.feed.flextrade.static import FlextradeSourceColumns
from swarm_tasks.order.feed.flextrade.static import FlextradeTempColumns
from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI
from swarm_tasks.steeleye.generic.get_tenant_lei import Params as ParamsGetTenantLEI
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.utilities.market.instruments.instrument_utils import (
    get_expiry_date_from_desc,
)


class AssetClass(TrAssetClass):
    EQUITY = "Equity"
    EQUITY_DERIVATIVES = "Equity Derivatives"


def get_executing_entity(
    source_frame: pd.DataFrame, temp_column_name: str
) -> pd.DataFrame:
    """Gets the executing entity value to be used in PartyIdentifiers from the
    AccountFirm record. Returns a dataframe with the same index as source_frame
    :param: source_frame: DataFrame, used to create and output data frame with the correct index
    :param: Name of temp column in the target df
    :param: logger: Logger instance
    :returns Df containing the LEI from the AccountFirm
    """
    return GetTenantLEI.process(
        source_frame=source_frame,
        params=ParamsGetTenantLEI(
            target_column_prefix=PartyPrefix.LEI, target_lei_column=temp_column_name
        ),
    )


def get_price_reference_ric(df: pd.DataFrame):
    """Returns a df containing FlextradeTempColumns.PRICE_REFERENCE_RIC.
    The value is taken fro, df[FlextradeTempColumns.INSTR_RIC]
    """
    ric_df = pd.DataFrame(index=df.index)
    ric_df[FlextradeTempColumns.PRICE_REFERENCE_RIC] = pd.NA
    notnull_mask = df.loc[:, FlextradeTempColumns.INSTR_RIC].notnull()
    ric_df.loc[notnull_mask, FlextradeTempColumns.PRICE_REFERENCE_RIC] = df.loc[
        notnull_mask, FlextradeTempColumns.INSTR_RIC
    ].map(lambda x: {PricingSource.RIC.value: x})
    return ric_df


def get_expiry_date_and_underlying_symbol(df: pd.DataFrame, date_col: str):
    """
    Returns a df containing FlextradeTempColumns.INSTR_EXPIRY_DATE
    and FlextradeTempColumns.INSTR_UNDERLYING_SYMBOL
    :param df: Df containing trade date and required fix columns
    :param date_col: name of date column
    :return: Dataframe containing expiry date and underlying symbol
    """
    sec_id_source_query = (
        f"`{FlextradeFixColumns.EXE_USER_FIX_TAG_SECURITY_ID_SOURCE}` == '8'"
    )
    sec_id_alt_source_query = (
        f"`{FlextradeFixColumns.EXE_USER_FIX_TAG_SECURITY_ID_SOURCE}` != '8' "
        f"& (`{FlextradeFixColumns.EXE_USER_FIX_TAG_SECURITY_ALT_ID_SOURCE}` == '8')"
    )
    both_not_eight_query = (
        f"`{FlextradeFixColumns.EXE_USER_FIX_TAG_SECURITY_ID_SOURCE}` != '8'"
        f" & (`{FlextradeFixColumns.EXE_USER_FIX_TAG_SECURITY_ALT_ID_SOURCE}` != '8')"
    )
    symbol_df = MapConditional.process(
        source_frame=df,
        params=ParamsMapConditional(
            target_attribute=FlextradeTempColumns.INSTR_UNDERLYING_SYMBOL_RAW,
            cases=[
                {
                    "query": sec_id_source_query,
                    "attribute": FlextradeFixColumns.EXE_USER_FIX_TAG_SECURITY_ID,
                },
                {
                    "query": sec_id_alt_source_query,
                    "attribute": FlextradeFixColumns.EXE_USER_FIX_TAG_SECURITY_ALT_ID,
                },
                {
                    "query": both_not_eight_query,
                    "attribute": FlextradeSourceColumns.EXE_SYMBOL,
                },
            ],
        ),
    )
    symbol_df.loc[
        :, FlextradeTempColumns.INSTR_UNDERLYING_SYMBOL
    ] = InstrumentIdentifiers.format_symbol(
        df=symbol_df,
        src_col=FlextradeTempColumns.INSTR_UNDERLYING_SYMBOL_RAW,
        dest_col=FlextradeTempColumns.INSTR_UNDERLYING_SYMBOL,
    )

    symbol_df.loc[:, FlextradeTempColumns.INSTR_UNDERLYING_SYMBOL_NO_SPL_CHARS] = (
        symbol_df.loc[:, FlextradeTempColumns.INSTR_UNDERLYING_SYMBOL_RAW]
        .str.replace("|", "")
        .str.replace(".", "")
    )

    datetime_df = pd.to_datetime(df.loc[:, FlextradeTempColumns.DATE]).to_frame()

    expiry_date_df = get_expiry_date_from_desc(
        data=pd.concat([symbol_df, datetime_df.loc[:, [date_col]]], axis=1),
        trade_date_column=date_col,
        desc_column=FlextradeTempColumns.INSTR_UNDERLYING_SYMBOL_NO_SPL_CHARS,
        target_expiry_date=FlextradeTempColumns.INSTR_EXPIRY_DATE,
    )

    return pd.concat(
        [
            symbol_df.loc[:, [FlextradeTempColumns.INSTR_UNDERLYING_SYMBOL]],
            expiry_date_df.loc[:, [FlextradeTempColumns.INSTR_EXPIRY_DATE]],
        ],
        axis=1,
    ).fillna(pd.NA)


def get_asset_class(df: pd.DataFrame):
    """
    Gets the asset class from exe_user_fix_tag_SecurityType if the value is FUT
    :param df: Data frame containing the exe_user_fix_tag_SecurityType col
    :return: Asset class if it is a future, null otherwise
    """
    return MapConditional.process(
        source_frame=df,
        params=ParamsMapConditional(
            target_attribute=FlextradeTempColumns.ASSET_CLASS,
            cases=[
                {
                    "query": f"`{FlextradeFixColumns.EXE_USER_FIX_TAG_SECURITY_TYPE}`.str.fullmatch('FUT', case=False, na=False)",
                    "value": AssetClass.FUTURE,
                }
            ],
        ),
    )


def get_asset_class_main(df: pd.DataFrame):
    """
    Gets the asset class main from exe_user_fix_tag_SecurityType if the value is FUT
    :param df: Data frame containing the exe_user_fix_tag_SecurityType col
    :return: Equity Derivative for Futures, Equity otherwise
    """
    return MapConditional.process(
        source_frame=df,
        params=ParamsMapConditional(
            target_attribute=FlextradeTempColumns.ASSET_CLASS_MAIN,
            cases=[
                Case(
                    query=f"`{FlextradeFixColumns.EXE_USER_FIX_TAG_SECURITY_TYPE}`.str.fullmatch('FUT', case=False, na=False)",
                    value=AssetClass.EQUITY_DERIVATIVES,
                ),
                Case(
                    query=f"~(`{FlextradeFixColumns.EXE_USER_FIX_TAG_SECURITY_TYPE}`.str.fullmatch('FUT', case=False, na=False))",
                    value=AssetClass.EQUITY,
                ),
            ],
        ),
    )


def get_datetime_attributes(
    df: pd.DataFrame,
    source_attribute: str,
    target_attribute: str,
    convert_to: ConvertTo,
) -> pd.DataFrame:

    """
    Calls pd.to_datetime() for a normal datetime object. If this cannot be parsed, this function
    assumes that it is a Unix Timestamp and calls pd.to_datetime() with the unit param.

    :param df: Source df
    :param source_attribute: Source datetime/unix timestamp attribute
    :param target_attribute: target column in df which is returned
    :return:
    """
    not_null_mask = df.eval(f"`{source_attribute}`.notnull()", engine="python")
    target = pd.DataFrame(index=df.index)
    target[target_attribute] = pd.NA
    target.loc[not_null_mask, target_attribute] = (
        df.loc[not_null_mask, source_attribute]
        .map(lambda x: get_datetime_or_unix_datetime(datetime_str=x))
        .fillna(pd.NA)
    )

    target[target_attribute] = pd.to_datetime(target[target_attribute])
    if convert_to == ConvertTo.DATE:
        target[target_attribute] = (
            target[target_attribute].dropna().dt.strftime(DatetimeFormat.DATE)
        )
    if convert_to == ConvertTo.DATETIME:
        target[target_attribute] = (
            target[target_attribute].dropna().dt.strftime(DatetimeFormat.DATETIME)
        )

    return target.fillna(pd.NA)


def get_datetime_or_unix_datetime(
    datetime_str: str,
) -> Optional[pd.Timestamp]:
    """
    Calls pd.to_datetime() for a normal datetime object. If this cannot be parsed, this function
    assumes that it is a Unix Timestamp and calls pd.to_datetime() with the unit param.

    :param datetime_str: Datetime str
    :return:
    """
    try:
        return pd.to_datetime(datetime_str)
    except (ParserError, OverflowError):
        # Source Unit (Unix Timestamp)
        try:
            if len(datetime_str) == 13:
                return pd.to_datetime(datetime_str, unit="ms")
            else:
                return pd.to_datetime(datetime_str, unit="s", errors="coerce")
        except ParserError:
            return


def get_date(df: pd.DataFrame) -> pd.DataFrame:
    """Returns a data frame containing date.
    This is populated from FlextradeSourceColumns.PRIM_RPL_ORDER_TIME

    :param df: Source frame containing FlextradeSourceColumns.PRIM_RPL_ORDER_TIME
    :return data frame containing the date
    """
    return get_datetime_attributes(
        df=df,
        source_attribute=FlextradeSourceColumns.PRIM_RPL_ORDER_TIME,
        target_attribute=FlextradeTempColumns.DATE,
        convert_to=ConvertTo.DATE,
    )
