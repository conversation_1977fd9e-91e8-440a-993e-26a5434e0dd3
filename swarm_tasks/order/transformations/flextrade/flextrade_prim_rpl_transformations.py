import os

import pandas as pd
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import HierarchyEnum
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.mifid2 import Trading<PERSON>apacity
from se_elastic_schema.static.reference import OrderRecordType
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ParamsConvertMinorToMajor,
)
from swarm_tasks.order.feed.flextrade.static import FlextradeFixColumns
from swarm_tasks.order.feed.flextrade.static import FlextradeSourceColumns
from swarm_tasks.order.feed.flextrade.static import FlextradeTempColumns
from swarm_tasks.order.transformations.flextrade.helper_functions import get_asset_class
from swarm_tasks.order.transformations.flextrade.helper_functions import (
    get_asset_class_main,
)
from swarm_tasks.order.transformations.flextrade.helper_functions import get_date
from swarm_tasks.order.transformations.flextrade.helper_functions import (
    get_datetime_attributes,
)
from swarm_tasks.order.transformations.flextrade.helper_functions import (
    get_executing_entity,
)
from swarm_tasks.order.transformations.flextrade.helper_functions import (
    get_expiry_date_and_underlying_symbol,
)
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as ParamsMapValue
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as ParamsMergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    Params as ParamsInstrumentIdentifiers,
)


class FlextradePrimRplTransformations(AbstractOrderTransformations):
    """Transformations class for Flextrade PRIM RPL Transformations."""

    def _pre_process(self):
        """This pre-processing method is used to populate columns in pre_process_df"""
        self.pre_process_df = pd.concat(
            [self.pre_process_df, get_date(df=self.source_frame)], axis=1
        )
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                get_expiry_date_and_underlying_symbol(
                    df=pd.concat(
                        [
                            self.source_frame,
                            self.pre_process_df.loc[:, [FlextradeTempColumns.DATE]],
                        ],
                        axis=1,
                    ),
                    date_col=FlextradeTempColumns.DATE,
                ),
            ],
            axis=1,
        )

    def process(self):
        """All the schema target columns which need to be populated are populated in
        self.target_df by the public methods called by process(). 3 temp columns are
        populated here as well as these are present in all Order flows: __meta_model__,
        marketIdentifiers.parties and marketIdentifiers.instrument.
        """
        self.pre_process()
        self.buy_sell()
        self.data_source_name()
        self.date()
        self.execution_details_buy_sell_indicator()
        self.execution_details_order_status()
        self.execution_details_order_type()
        self.execution_details_limit_price()
        self.execution_details_outgoing_order_addl_info()
        self.execution_details_trading_capacity()
        self.hierarchy()
        self.id()
        self.meta_model()
        self.order_identifiers_aggregated_order_id_code()
        self.order_identifiers_internal_order_id_code()
        self.order_identifiers_order_id_code()
        self.price_forming_data_initial_quantity()
        self.source_index()
        self.source_key()
        self.timestamps_order_received()
        self.timestamps_order_submitted()
        self.timestamps_order_status_updated()
        self.transaction_details_buy_sell_indicator()
        self.transaction_details_price_currency()
        self.transaction_details_price_notation()
        self.transaction_details_quantity_notation()
        self.transaction_details_record_type()
        self.transaction_details_trading_capacity()
        self.transaction_details_ultimate_venue()
        self.transaction_details_venue()
        self.market_identifiers_instrument()
        self.market_identifiers_parties()
        self.market_identifiers()
        self.post_process()
        return self.target_df

    def _post_process(self):
        """All the temp columns which are needed for downstream tasks are populated
        in self.target_df inside the _post_process() method.
        """
        self.target_df = pd.concat(
            [
                self.target_df,
                pd.DataFrame(
                    data=True,
                    index=self.target_df.index,
                    columns=[FlextradeTempColumns.IS_CREATED_THROUGH_FALLBACK],
                ),
                get_asset_class_main(self.source_frame),
            ],
            axis=1,
        )

    def _buy_sell(self) -> pd.DataFrame:
        """Returns a data frame containing buySell.
        This is populated from SourceColumns.PRIM_RPL_PORTFOLIO_SIDE"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=FlextradeSourceColumns.PRIM_RPL_PORTFOLIO_SIDE,
                target_attribute=OrderColumns.BUY_SELL,
                case_insensitive=True,
                value_map={"buy": "1", "sel": "2", "ssh": "2"},
            ),
            auditor=self.auditor,
        )

    def _data_source_name(self) -> pd.DataFrame:
        """Returns a data frame containing dataSourceName.
        This is populated with the static value 'FlexTrade'"""
        return pd.DataFrame(
            data="FlexTrade",
            index=self.source_frame.index,
            columns=[OrderColumns.DATA_SOURCE_NAME],
        )

    def _date(self) -> pd.DataFrame:
        """Returns a data frame containing date.
        This is populated from FlextradeSourceColumns.PRIM_RPL_ORDER_TIME"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, FlextradeTempColumns.DATE].values,
            index=self.source_frame.index,
            columns=[OrderColumns.DATE],
        )

    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.buySellIndicator.
        This is populated from SourceColumns.PRIM_RPL_PORTFOLIO_SIDE"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=FlextradeSourceColumns.PRIM_RPL_PORTFOLIO_SIDE,
                target_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                case_insensitive=True,
                value_map={
                    "buy": BuySellIndicator.BUYI.value,
                    "sel": BuySellIndicator.SELL.value,
                    "ssh": BuySellIndicator.SELL.value,
                },
            ),
            auditor=self.auditor,
        )

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_limit_price(self) -> pd.DataFrame:
        """Populates OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE based on
        FlextradeSourceColumns.PRIM_RPL_LIMIT_PRICE"""
        limit_query = f"`{OrderColumns.EXECUTION_DETAILS_ORDER_TYPE}`.str.fullmatch('LIMIT', case=False, na=False)"
        return MapConditional.process(
            source_frame=pd.concat(
                [
                    self.target_df,
                    self.source_frame.loc[
                        :, [FlextradeSourceColumns.PRIM_RPL_LIMIT_PRICE]
                    ],
                ],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE,
                cases=[
                    {
                        "query": limit_query,
                        "attribute": FlextradeSourceColumns.PRIM_RPL_LIMIT_PRICE,
                    }
                ],
            ),
        )

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_order_status(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.orderStatus
        As this flow only creates NEWOs, this is populated with the static value 'NEWO'"""
        return pd.DataFrame(
            data=OrderStatus.NEWO.value,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_ORDER_STATUS],
        )

    def _execution_details_order_type(self) -> pd.DataFrame:
        """Populates OrderColumns.EXECUTION_DETAILS_ORDER_TYPE based on
        FlextradeSourceColumns.PRIM_RPL_LIMIT_PRICE"""
        mkt_day_qry = f"`{FlextradeSourceColumns.PRIM_RPL_LIMIT_PRICE}`.str.fullmatch('MKT-DAY', case=False, na=False)"
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.EXECUTION_DETAILS_ORDER_TYPE,
                cases=[
                    {
                        "query": mkt_day_qry,
                        "value": "MARKET",
                    },
                    {
                        "query": f"~{mkt_day_qry}",
                        "value": "LIMIT",
                    },
                ],
            ),
        )

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Populates OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO from
        FlextradeSourceColumns.PRIM_RPL_FIX_TAGS"""
        return pd.DataFrame(
            data=self.source_frame.loc[
                :, FlextradeSourceColumns.PRIM_RPL_FIX_TAGS
            ].values,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO],
        )

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_stop_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        """Populates OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY based with the static value 'DEAL'"""
        return pd.DataFrame(
            data=TradingCapacity.DEAL.value,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY],
        )

    def _execution_details_validity_period(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _financing_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _hierarchy(self) -> pd.DataFrame:
        """Populates hierarchy based with the static value 'Standalone'"""
        return pd.DataFrame(
            data=HierarchyEnum.STANDALONE.value,
            index=self.source_frame.index,
            columns=[OrderColumns.HIERARCHY],
        )

    def _id(self) -> pd.DataFrame:
        """Populates OrderColumns.ID from FlextradeSourceColumns.PRIM_RPL_ORDER_ID"""
        return pd.DataFrame(
            data=self.source_frame.loc[
                :, FlextradeSourceColumns.PRIM_RPL_ORDER_ID
            ].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ID],
        )

    def _is_discretionary(self) -> pd.DataFrame:
        """Not Implemented"""

    def _is_iceberg(self):
        """Not Implemented"""

    def _is_repo(self):
        """Not Implemented"""

    def _is_synthetic(self):
        """Not Implemented"""

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        """Not Implemented"""

    def _market_identifiers(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers by calling MergeMarketIdentifiers.
        Assumes _market_identifiers_instrument() and _market_identifiers_parties() have been
        called earlier"""
        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=ParamsMergeMarketIdentifiers(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                instrument_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.parties by calling InstrumentIdentifiers"""
        return InstrumentIdentifiers.process(
            source_frame=pd.concat(
                [
                    self.pre_process_df,
                    self.source_frame.loc[
                        :,
                        [
                            FlextradeSourceColumns.PRIM_RPL_SYMBOL,
                            FlextradeSourceColumns.PRIM_RPL_ISIN,
                        ],
                    ],
                    get_asset_class(df=self.source_frame),
                    self.target_df.loc[
                        :,
                        [
                            OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                            OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                        ],
                    ],
                ],
                axis=1,
            ),
            params=ParamsInstrumentIdentifiers(
                asset_class_attribute=FlextradeTempColumns.ASSET_CLASS,
                bbg_figi_id_attribute=FlextradeSourceColumns.PRIM_RPL_SYMBOL,
                currency_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                expiry_date_attribute=FlextradeTempColumns.INSTR_EXPIRY_DATE,
                isin_attribute=FlextradeSourceColumns.PRIM_RPL_ISIN,
                underlying_symbol_attribute=FlextradeTempColumns.INSTR_UNDERLYING_SYMBOL,
                venue_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                retain_task_inputs=True,
            ),
            auditor=self.auditor,
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.parties by calling PartyIdentifiers
        Assumes that execution_details_buy_sell_indicator() has been called earlier, as well as
        pre_process()"""

        with_prefix_df = pd.concat(
            [
                get_executing_entity(
                    source_frame=self.source_frame,
                    temp_column_name=FlextradeTempColumns.EXECUTING_ENTITY_WITH_LEI,
                ),
                PartyPrefix.ID
                + self.source_frame.loc[
                    :, FlextradeSourceColumns.PRIM_RPL_USER_INITIALS
                ],
                PartyPrefix.ID
                + self.source_frame.loc[:, FlextradeSourceColumns.PRIM_RPL_PORTFOLIO],
                PartyPrefix.ID
                + self.source_frame.loc[:, FlextradeSourceColumns.PRIM_RPL_DESTINATION],
                PartyPrefix.LEI
                + self.source_frame.loc[
                    :, FlextradeFixColumns.EXE_USER_FIX_TAG_FF_20001
                ],
            ],
            axis=1,
        )

        return GenericOrderPartyIdentifiers.process(
            source_frame=pd.concat(
                [
                    with_prefix_df,
                    self.target_df.loc[
                        :, [OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR]
                    ],
                ],
                axis=1,
            ),
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                executing_entity_identifier=FlextradeTempColumns.EXECUTING_ENTITY_WITH_LEI,
                trader_identifier=FlextradeSourceColumns.PRIM_RPL_USER_INITIALS,
                client_identifier=FlextradeSourceColumns.PRIM_RPL_PORTFOLIO,
                counterparty_identifier=FlextradeSourceColumns.PRIM_RPL_DESTINATION,
                investment_decision_within_firm_identifier=FlextradeFixColumns.EXE_USER_FIX_TAG_FF_20001,
                buy_sell_side_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                buyer_identifier=FlextradeTempColumns.EXECUTING_ENTITY_WITH_LEI,
                seller_identifier=FlextradeSourceColumns.PRIM_RPL_DESTINATION,
                use_buy_mask_for_buyer_seller=True,
            ),
        )

    def _meta_model(self) -> pd.DataFrame:
        """Populates __meta_model__ with the static value 'Order'"""
        return pd.DataFrame(
            data="Order",
            index=self.source_frame.index,
            columns=[OrderColumns.META_MODEL],
        )

    def _order_class(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_aggregated_order_id_code(self) -> pd.DataFrame:
        """Populates OrderColumns.ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID from
        FlextradeSourceColumns.PRIM_RPL_REFERENCE_ORDER_ID"""
        return pd.DataFrame(
            data=self.source_frame.loc[
                :, FlextradeSourceColumns.PRIM_RPL_REFERENCE_ORDER_ID
            ].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID],
        )

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_internal_order_id_code(self) -> pd.DataFrame:
        """Populates OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE from
        FlextradeSourceColumns.PRIM_RPL_INCOMING_FIX_ORDER_ID_STRATEGY_ID"""
        return pd.DataFrame(
            data=self.source_frame.loc[
                :, FlextradeSourceColumns.PRIM_RPL_INCOMING_FIX_ORDER_ID_STRATEGY_ID
            ].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE],
        )

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        """Populates OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE from
        FlextradeSourceColumns.PRIM_RPL_ORDER_ID"""
        return pd.DataFrame(
            data=self.source_frame.loc[
                :, FlextradeSourceColumns.PRIM_RPL_ORDER_ID
            ].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE],
        )

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_parent_order_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_initial_quantity(self) -> pd.DataFrame:
        """Populates OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY from
        FlextradeSourceColumns.PRIM_RPL_ORDERED_SHRS"""
        return pd.DataFrame(
            data=self.source_frame.loc[
                :, FlextradeSourceColumns.PRIM_RPL_ORDERED_SHRS
            ].values,
            index=self.source_frame.index,
            columns=[OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY],
        )

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_remaining_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """Not Implemented"""

    def _source_index(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceIndex column"""
        return pd.DataFrame(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_INDEX],
        )

    def _source_key(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceKey column from the SWARM_FILE_URL"""
        return pd.DataFrame(
            data=os.getenv("SWARM_FILE_URL"),
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_KEY],
        )

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_order_received(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.TIMESTAMPS_ORDER_RECEIVED.
        This is populated from FlextradeSourceColumns.PRIM_RPL_ORDER_TIME"""
        return get_datetime_attributes(
            df=self.source_frame,
            source_attribute=FlextradeSourceColumns.PRIM_RPL_ORDER_TIME,
            target_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
            convert_to=ConvertTo.DATETIME,
        )

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED.
        Assumes that _timestamps_order_received() has been called earlier"""
        return pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.TIMESTAMPS_ORDER_RECEIVED].values,
            index=self.target_df.index,
            columns=[OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED],
        )

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.orderSubmitted.
        Assumes that _timestamps_order_received() has been called earlier"""
        return pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.TIMESTAMPS_ORDER_RECEIVED].values,
            index=self.target_df.index,
            columns=[OrderColumns.TIMESTAMPS_ORDER_SUBMITTED],
        )

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_validity_period(self) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_waiver_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR.
        Assumes that execution_details_buy_sell_indicator() has been called earlier"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR],
        )

    def _transaction_details_commission_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_commission_amount_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_commission_amount_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_cross_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_position_effect(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_position_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_price_average(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY"""
        prim_rpl_currency_not_null = (
            f"`{FlextradeFixColumns.PRIM_RPL_FIX_TAG_CURRENCY}`.notnull()"
        )
        currency_df = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=FlextradeTempColumns.PRICE_CURRENCY,
                cases=[
                    {
                        "query": prim_rpl_currency_not_null,
                        "attribute": FlextradeFixColumns.PRIM_RPL_FIX_TAG_CURRENCY,
                    },
                    {
                        "query": f"~{prim_rpl_currency_not_null}",
                        "attribute": FlextradeFixColumns.EXE_USER_FIX_TAG_CURRENCY,
                    },
                ],
            ),
        )

        return ConvertMinorToMajor.process(
            source_frame=currency_df,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=FlextradeTempColumns.PRICE_CURRENCY,
                target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
            ),
        )

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.priceNotation (MONE)"""
        return pd.DataFrame(
            data=PriceNotation.MONE.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION],
        )

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.quantityNotation (UNIT)"""
        return pd.DataFrame(
            data=QuantityNotation.UNIT.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION],
        )

    def _transaction_details_record_type(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.recordType (Market)"""
        return pd.DataFrame(
            data=OrderRecordType.MARKET_SIDE.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE],
        )

    def _transaction_details_settlement_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_amount_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_date(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_swap_directionalities(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY.
        Assumes that execution_details_trading_capacity() has been called earlier"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY],
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_trail_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE"""
        last_mkt_not_null = (
            f"`{FlextradeFixColumns.EXE_USER_FIX_TAG_LAST_MKT}`.notnull()"
        )
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                cases=[
                    {
                        "query": last_mkt_not_null,
                        "attribute": FlextradeFixColumns.EXE_USER_FIX_TAG_LAST_MKT,
                    },
                    {
                        "query": f"~{last_mkt_not_null}",
                        "value": "XXXX",
                    },
                ],
            ),
        )

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_venue(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.TRANSACTION_DETAILS_VENUE"""
        return pd.DataFrame(
            data="XOFF",
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_VENUE],
        )

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        """Not implemented"""
