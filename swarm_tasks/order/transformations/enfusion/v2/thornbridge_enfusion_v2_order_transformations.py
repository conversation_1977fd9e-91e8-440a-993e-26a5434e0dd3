import pandas as pd
from se_core_tasks.map.map_conditional import Case
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.order.feed.enfusion.v2.static import SourceColumns
from swarm_tasks.order.feed.enfusion.v2.static import TempColumns
from swarm_tasks.order.transformations.enfusion.v2.enfusion_v2_order_transformations import (
    EnfusionV2OrderTransformations,
)
from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI
from swarm_tasks.steeleye.generic.get_tenant_lei import Params as GetTenantLEIParams
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as MapConditionalParams


class ThornbridgeEnfusionV2OrderTransformations(EnfusionV2OrderTransformations):
    """
    This is a Thornbridge exclusive override to map executingEntity against other party trader fields
    """

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        """
        Overriding the parent method to avoid the need of the new column in the source frame
        """

    def _temp_executing_entity(self) -> pd.DataFrame:
        """
        Map to Tenant LEI from AccountFirm
        """

        return GetTenantLEI.process(
            source_frame=self.source_frame,
            params=GetTenantLEIParams(
                target_column_prefix=PartyPrefix.LEI,
                target_lei_column=TempColumns.EXECUTING_ENTITY,
            ),
        )

    @staticmethod
    def get_buyer_seller_first_case_column():
        """
        Overriding Executing Entity with EXECUTION_ENTITY_IDENTIFICATION_CODE for some buyer seller conditions
        """
        return SourceColumns.EXECUTION_ENTITY_IDENTIFICATION_CODE

    def _temp_party_ids_buyer_decision_maker(self) -> pd.DataFrame:
        """
        Map to Tenant LEI from AccountFirm if BUY
        """

        return MapConditional.process(
            source_frame=self.pre_process_df,
            params=MapConditionalParams(
                target_attribute=TempColumns.BUYER_DECISION_MAKER,
                cases=[
                    Case(
                        query=f"`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.BUYI.value}'",
                        attribute=TempColumns.EXECUTING_ENTITY,
                    )
                ],
            ),
        )

    def _temp_party_ids_seller_decision_maker(self) -> pd.DataFrame:
        """
        Map to Tenant LEI from AccountFirm if SELL
        """

        return MapConditional.process(
            source_frame=self.pre_process_df,
            params=MapConditionalParams(
                target_attribute=TempColumns.SELLER_DECISION_MAKER,
                cases=[
                    Case(
                        query=f"`{TempColumns.BUY_SELL_INDICATOR}` == '{BuySellIndicator.SELL.value}'",
                        attribute=TempColumns.EXECUTING_ENTITY,
                    )
                ],
            ),
        )

    def _temp_party_ids_investment_decision_maker(self) -> pd.DataFrame:
        """
        Map to Trader
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.TRADER].values,
            index=self.pre_process_df.index,
            columns=[TempColumns.INVESTMENT_DECISION_MAKER],
        )
