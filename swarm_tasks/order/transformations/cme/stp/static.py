MARKET_SIDE = "Market Side"
DATA_SOURCE = "CME STP"


class SourceColumns:
    AGGRESSOR_INDICATOR = "AggressorIndicator"
    CL_ORD_ID = "ClOrdID"
    CUST_ORDER_CAPACITY = "CustOrderCapacity"
    EXEC_ID = "ExecID"
    LAST_PX = "LastPx"
    LAST_QTY = "LastQty"
    PARTY_ID = "PartyID"
    PARTY_ROLE = "PartyRole"
    PARTY_SUB_ID = "PartySubID"
    PRICE_TYPE = "PriceType"
    SECONDARY_EXEC_ID = "SecondaryExecID"
    SECURITY_EXCHANGE = "SecurityExchange"
    SENDING_TIME = "SendingTime"
    SIDE = "Side"
    SYMBOL = "Symbol"
    TRADE_REQUEST_ID = "TradeRequestID"
    TRADE_REPORT_TRANS_TYPE = "TradeReportTransType"
    TRD_TYPE = "TrdType"
    VENUE_TYPE = "VenueType"


class TempColumns:
    ACCOUNT = "__account__"
    BUY_SELL = "__buy_sell__"
    CLEARING_ORG = "__clearing_org__"
    CLIENT = "__client__"
    COUNTERPARTY = "__counterparty__"
    LAST_PX = "__last_px__"
    ORDER_ID = "__order_id__"
    ORDER_ID_SYMBOL = "__order_id_symbol__"
    ORDER_ID_WITH_INVERTED_SIDE = "__order_id_with_inverted_side__"
    PARTY_ID = "__party_id__"
    PRICE = "__price__"
    SELLER = "__seller__"
    STOP_PX = "__stop_px__"
    SYMBOL = "__symbol__"
    TRADER = "__trader__"
