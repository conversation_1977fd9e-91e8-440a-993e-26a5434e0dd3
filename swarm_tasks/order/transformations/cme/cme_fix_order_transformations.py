import pandas as pd
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_attribute import CastTo
from se_elastic_schema.static.mifid2 import OrderStatus
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order_and_tr.fix.fix_parser_result_to_frame import StaticFields
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI
from swarm_tasks.steeleye.generic.get_tenant_lei import Params as ParamsGetTenantLEI
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ParamsConcatAttributes,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ConvertDatetimeParams,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as ParamsMapAttribute
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as ParamsMapValue
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as ParamsMergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)
from swarm_tasks.utilities.static import Delimiters

MARKET_SIDE = "Market Side"
CME = "CME"


class SourceColumns:
    ACCOUNT = "Account"
    CL_ORD_ID = "ClOrdID"
    EXEC_ID = "ExecID"
    FF_1031 = "ff_1031"
    FF_5979 = "ff_5979"
    LAST_PX = "LastPx"
    LAST_QTY = "LastQty"
    LEAVES_QTY = "LeavesQty"
    MULTI_LEG_REPORTING_TYPE = "MultiLegReportingType"
    ORDER_ID = "OrderID"
    ORDER_QTY = "OrderQty"
    ORD_TYPE = "OrdType"
    ORIG_CL_ORD_ID = "OrigClOrdID"
    POSITION_EFFECT = "PositionEffect"
    PRICE = "Price"
    SECURITY_ID = "SecurityID"
    SENDING_TIME = "SendingTime"
    SIDE = "Side"
    STOP_PX = "StopPx"
    TARGET_SUB_ID = "TargetSubID"
    TIME_IN_FORCE = "TimeInForce"


class TempColumns:
    ADDITIONAL_INFO_1 = "__additional_info_1__"
    ADDITIONAL_INFO_2 = "__additional_info_2__"
    BUY_SELL = "__buy_sell__"
    CLIENT = "__client__"
    COUNTERPARTY = "__counterparty__"
    EXECUTING_ENTITY_WITH_LEI = "__executing_entity_with_lei__"
    LAST_PX = "__last_px__"
    MULTI_LEG_REPORTING_TYPE = "__multi_leg_reporting_type__"
    NEWO_IN_FILE = "__newo_in_file__"
    ORDER_ID = "__order_id__"
    PRICE = "__price__"
    SECURITY_ID = "__security_id__"
    STOP_PX = "__stop_px__"
    TRADER = "__trader__"
    VALIDITY_PERIOD = "__validity_period__"


class CmeFixOrderTransformations(AbstractOrderTransformations):
    def _pre_process(self):
        self.pre_process_df.loc[:, TempColumns.BUY_SELL] = self._get_buy_sell()
        self.pre_process_df.loc[:, TempColumns.ORDER_ID] = self._get_order_id()

    def process(self):
        self.pre_process()
        self.buy_sell()
        self.data_source_name()
        self.date()
        self.execution_details_aggregated_order()
        self.execution_details_buy_sell_indicator()
        self.execution_details_order_status()
        self.execution_details_order_type()
        self.execution_details_outgoing_order_addl_info()
        self.execution_details_passive_only_indicator()
        self.execution_details_validity_period()
        self.id()
        self.market_identifiers_parties()
        self.market_identifiers()
        self.meta_model()
        self.order_identifiers_initial_order_designation()
        self.order_identifiers_internal_order_id_code()
        self.order_identifiers_order_id_code()
        self.order_identifiers_trading_venue_transaction_id_code()
        self.order_identifiers_transaction_ref_no()
        self.price_forming_data_initial_quantity()
        self.price_forming_data_remaining_quantity()
        self.price_forming_data_traded_quantity()
        self.report_details_transaction_ref_no()
        self.source_key()
        self.timestamps_order_received()
        self.timestamps_order_submitted()  # needs timestamps_order_received() output
        self.timestamps_trading_date_time()  # needs timestamps_order_received() output
        self.transaction_details_buy_sell_indicator()
        self.transaction_details_position_effect()
        self.transaction_details_record_type()
        self.transaction_details_trading_date_time()
        self.transaction_details_quantity()
        self.post_process()
        return self.target_df

    def _post_process(self):
        """Populates few temp cols which are required by downstream tasks"""
        self.target_df.loc[
            :, TempColumns.MULTI_LEG_REPORTING_TYPE
        ] = self.source_frame.loc[:, SourceColumns.MULTI_LEG_REPORTING_TYPE]
        self.target_df.loc[:, TempColumns.SECURITY_ID] = self.source_frame.loc[
            :, SourceColumns.SECURITY_ID
        ]
        self.target_df.loc[:, TempColumns.PRICE] = self.source_frame.loc[
            :, SourceColumns.PRICE
        ]
        self.target_df.loc[:, TempColumns.LAST_PX] = self.source_frame.loc[
            :, SourceColumns.LAST_PX
        ]
        self.target_df.loc[:, TempColumns.STOP_PX] = self.source_frame.loc[
            :, SourceColumns.STOP_PX
        ]
        self.target_df.loc[:, TempColumns.NEWO_IN_FILE] = self._get_newo_in_file()

    def _buy_sell(self) -> pd.DataFrame:
        """Returns a data frame containing _order.buySell and _orderState.buySell.
        This is populated from SourceColumns.ACTION"""
        return pd.concat(
            [
                MapAttribute.process(
                    source_frame=self.pre_process_df,
                    params=ParamsMapAttribute(
                        source_attribute=TempColumns.BUY_SELL,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER, attribute=OrderColumns.BUY_SELL
                        ),
                    ),
                    auditor=self.auditor,
                ),
                MapAttribute.process(
                    source_frame=self.pre_process_df,
                    params=ParamsMapAttribute(
                        source_attribute=TempColumns.BUY_SELL,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.BUY_SELL,
                        ),
                    ),
                    auditor=self.auditor,
                ),
            ],
            axis=1,
        )

    def _data_source_name(self) -> pd.DataFrame:
        """Returns a data frame containing dataSourceName.
        This is populated with the static value 'CME'"""
        return pd.DataFrame(
            data=CME,
            index=self.source_frame.index,
            columns=[OrderColumns.DATA_SOURCE_NAME],
        )

    def _date(self) -> pd.DataFrame:
        """Returns a data frame containing date as the target column"""
        return ConvertDatetime.process(
            self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.SENDING_TIME,
                target_attribute=OrderColumns.DATE,
                convert_to=ConvertTo.DATE,
            ),
        )

    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        """Returns a dataframe populating the column orderIdentifiers.aggregatedOrderId"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.ORIG_CL_ORD_ID,
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID,
            ),
            auditor=self.auditor,
        )

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Populates and returns executionDetails.buySellIndicator column"""
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.BUY_SELL,
                target_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
            ),
            auditor=self.auditor,
        )

    def _execution_details_order_status(self) -> pd.DataFrame:
        """Returns a data frame containing _order.executionDetails.orderStatus and
        _orderState.executionDetails.orderStatus"""
        order_execution_details_order_status = pd.DataFrame(
            data=OrderStatus.NEWO.value,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                )
            ],
        )
        cases = [
            {"query": "`ExecType`.str.upper().isin(['1', 'F'])", "value": "PARF"},
            {"query": "`ExecType`.str.upper() == '2'", "value": "FILL"},
            {"query": "`ExecType`.str.upper() == '3'", "value": "DNFD"},
            {"query": "`ExecType`.str.upper().isin(['4', 'H'])", "value": "CAME"},
            {"query": "`ExecType`.str.upper().isin(['5', 'D', 'G'])", "value": "REME"},
            {"query": "`ExecType`.str.upper() == '6'", "value": "PNDC"},
            {"query": "`ExecType`.str.upper().isin(['7', '9'])", "value": "REMA"},
            {"query": "`ExecType`.str.upper() == '8'", "value": "REMO"},
            {"query": "`ExecType`.str.upper() == 'C'", "value": "EXPI"},
        ]
        order_state_execution_details_order_status = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                ),
                cases=cases,
            ),
        )
        return pd.concat(
            [
                order_execution_details_order_status,
                order_state_execution_details_order_status,
            ],
            axis=1,
        )

    def _execution_details_order_type(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.orderType.
        This is populated with the static value mapping."""
        value_map = {
            "1": "Market",
            "2": "Limit",
            "3": "Stop / Stop Loss",
            "4": "Stop Limit",
            "5": "Market On Close",
            "6": "With Or Without",
            "7": "Limit Or Better",
            "8": "Limit With Or Without",
            "9": "On Basis",
            "D": "Previously Quoted",
            "E": "Previously Indicated",
            "G": "Forex Swap",
            "I": "Funari",
            "J": "Market If Touched",
            "K": "Market With Left Over as Limit",
            "L": "Previous Fund Valuation Point",
            "M": "Next Fund Valuation Point",
            "P": "Pegged",
            "Q": "Counter-order selection",
        }
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.ORD_TYPE,
                target_attribute=OrderColumns.EXECUTION_DETAILS_ORDER_TYPE,
                case_insensitive=True,
                value_map=value_map,
            ),
            auditor=self.auditor,
        )

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.outgoingOrderAddlInfo."""
        additional_info_1 = self._temp_additional_info_1()
        additional_info_2 = self._temp_additional_info_2()
        source_frame = pd.concat([additional_info_1, additional_info_2], axis=1)
        return ConcatAttributes.process(
            source_frame=source_frame,
            params=ParamsConcatAttributes(
                target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
                source_attributes=[
                    TempColumns.ADDITIONAL_INFO_1,
                    TempColumns.ADDITIONAL_INFO_2,
                ],
                delimiter=Delimiters.COMMA_SPACE,
            ),
        )

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        """Returns a dataframe with the column executionDetails.passiveOnlyIndicator with a static boolean value = True"""
        return pd.DataFrame(
            data=True,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_PASSIVE_ONLY_INDICATOR],
        )

    def _execution_details_validity_period(self) -> pd.DataFrame:
        """Returns a df with the col executionDetails.validityPeriod
        This is populated using source col TimeInForce delimited by ;"""
        value_map = {
            "0": "DAVY",
            "1": "GTCV",
            "3": "IOCV",
            "4": "FOKV",
            "5": "GTXV",
            "6": "GTDV",
        }
        validity_period = MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.TIME_IN_FORCE,
                target_attribute=TempColumns.VALIDITY_PERIOD,
                value_map=value_map,
            ),
            auditor=self.auditor,
        )
        return MapAttribute.process(
            source_frame=validity_period,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.VALIDITY_PERIOD,
                target_attribute=OrderColumns.EXECUTION_DETAILS_VALIDITY_PERIOD,
                cast_to=CastTo.STRING_LIST,
                list_delimiter=Delimiters.SEMI_COLON,
            ),
            auditor=self.auditor,
        )

    def _id(self) -> pd.DataFrame:
        """Returns a data frame containing _order.id and _orderState.id.
        This is populated from SourceColumns.ORDER_ID and SECURITY_ID"""
        return pd.concat(
            [
                MapAttribute.process(
                    source_frame=self.pre_process_df,
                    params=ParamsMapAttribute(
                        source_attribute=TempColumns.ORDER_ID,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER, attribute=OrderColumns.ID
                        ),
                    ),
                    auditor=self.auditor,
                ),
                MapAttribute.process(
                    source_frame=self.pre_process_df,
                    params=ParamsMapAttribute(
                        source_attribute=TempColumns.ORDER_ID,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE, attribute=OrderColumns.ID
                        ),
                    ),
                    auditor=self.auditor,
                ),
            ],
            axis=1,
        )

    def _market_identifiers(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers by calling MergeMarketIdentifiers.
        Assumes _market_identifiers_parties() has been called earlier"""
        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=ParamsMergeMarketIdentifiers(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.parties by calling PartyIdentifiers"""
        parties_source_frame = pd.concat(
            [
                self._get_client(),
                self._get_counterparty(),
                self._get_trader(),
                self._get_executing_entity_with_lei(),
            ],
            axis=1,
        )
        return GenericOrderPartyIdentifiers.process(
            source_frame=parties_source_frame,
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                executing_entity_identifier=TempColumns.EXECUTING_ENTITY_WITH_LEI,
                trader_identifier=TempColumns.TRADER,
                client_identifier=TempColumns.CLIENT,
                counterparty_identifier=TempColumns.COUNTERPARTY,
                buyer_identifier=TempColumns.CLIENT,
                seller_identifier=TempColumns.COUNTERPARTY,
            ),
        )

    def _meta_model(self) -> pd.DataFrame:
        """Returns a data frame containing _order.__meta_model__ and _orderState.__meta_model__.
        The columns are populated with the static values Order and OrderState respectively"""
        return pd.DataFrame(
            data=[["Order", "OrderState"]],
            index=self.source_frame.index,
            columns=[
                add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.META_MODEL),
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.META_MODEL,
                ),
            ],
        )

    def _order_class(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        """returns a df populating the col orderIdentifiers.initialOrderDesignation"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.FF_5979,
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_INITIAL_ORDER_DESIGNATION,
            ),
            auditor=self.auditor,
        )

    def _order_identifiers_internal_order_id_code(self) -> pd.DataFrame:
        """Returns a dataframe with the col orderIdentifiers.internalOrderIdCode"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.CL_ORD_ID,
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE,
            ),
            auditor=self.auditor,
        )

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.orderIdCode.
        It assumes that _id() has been called earlier"""
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.ORDER_ID,
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE,
            ),
            auditor=self.auditor,
        )

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        """Returns a dataframe with the col orderIdentifiers.tradingVenueTransactionIdCode"""
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.ORDER_ID,
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_TRADING_VENUE_TRANSACTION_ID_CODE,
            ),
            auditor=self.auditor,
        )

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        """Returns a df with the col orderIdentifiers.transactionRefNo"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.EXEC_ID,
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
            ),
            auditor=self.auditor,
        )

    def _price_forming_data_initial_quantity(self) -> pd.DataFrame:
        """Returns a df with the col priceFormingData.initialQuantity"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.ORDER_QTY,
                target_attribute=OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
            ),
            auditor=self.auditor,
        )

    def _price_forming_data_remaining_quantity(self) -> pd.DataFrame:
        """Returns a df with the col priceFormingData.remainingQuantity"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.LEAVES_QTY,
                target_attribute=OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY,
                fill_nan=0,
            ),
            auditor=self.auditor,
        )

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        """Returns a df with the col priceFormingData.tradedQuantity"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.LAST_QTY,
                target_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                ),
            ),
            auditor=self.auditor,
        )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """Returns a df with the col reportDetails.transactionRefNo"""
        return MapAttribute.process(
            source_frame=self.target_df,
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
                target_attribute=OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO,
            ),
            auditor=self.auditor,
        )

    def _source_key(self) -> pd.DataFrame:
        """Returns a df with the col sourceKey"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=StaticFields.FILE_URL,
                target_attribute=OrderColumns.SOURCE_KEY,
            ),
            auditor=self.auditor,
        )

    def _timestamps_order_received(self) -> pd.DataFrame:
        """Returns a df with col timestamps.orderReceived"""
        return ConvertDatetime.process(
            self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.SENDING_TIME,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                convert_to=ConvertTo.DATETIME,
            ),
        )

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        """Returns a df with col timestamps.orderSubmitted"""
        return MapAttribute.process(
            source_frame=self.target_df,
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
            ),
            auditor=self.auditor,
        )

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        """Returns a df with col timestamps.tradingDateTime"""
        return MapAttribute.process(
            source_frame=self.target_df,
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                target_attribute=OrderColumns.TIMESTAMPS_TRADING_DATE_TIME,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Returns a df with the col transactionDetails.buySellIndicator"""
        return MapAttribute.process(
            source_frame=self.pre_process_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.BUY_SELL,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_position_effect(self) -> pd.DataFrame:
        """Returns a df with the col transactionDetails.positionEffect"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.POSITION_EFFECT,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_POSITION_EFFECT,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """Returns a df with the col transactionDetails.quantity populated from priceFormingData.tradedQuantity"""
        return MapAttribute.process(
            source_frame=self.target_df,
            params=ParamsMapAttribute(
                source_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                ),
                target_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY,
                ),
            ),
            auditor=self.auditor,
        )

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        """The field transactionDetails.priceNotation  is populated
        downstream from the instrument fetched"""

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """The field transactionDetails.quantityNotation  is populated
        downstream from the instrument fetched"""

    def _transaction_details_record_type(self) -> pd.DataFrame:
        """Returns a df with col transactionDetails.recordType with a static value
        'Market Side'"""
        return pd.DataFrame(
            data=MARKET_SIDE,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE,
                )
            ],
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        return MapAttribute.process(
            source_frame=self.target_df,
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME,
            ),
            auditor=self.auditor,
        )

    def _get_buy_sell(self) -> pd.Series:
        """Populates a temp col __buy_sell__ to be used to populate other buy_sell fields"""

        value_map = {
            "1": "BUYI",
            "2": "SELL",
            "3": "BUYI",
            "4": "SELL",
            "5": "SELL",
            "6": "SELL",
        }
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.SIDE,
                target_attribute=TempColumns.BUY_SELL,
                value_map=value_map,
            ),
            auditor=self.auditor,
        )[TempColumns.BUY_SELL]

    def _get_client(self) -> pd.DataFrame:
        """Adds a prefix id: to Account and returns a df with col __client__"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.ACCOUNT,
                target_attribute=TempColumns.CLIENT,
                prefix=PartyPrefix.ID,
            ),
            auditor=self.auditor,
        )

    def _get_counterparty(self) -> pd.DataFrame:
        """Returns a df with col __counterparty__ with static value 'id:CME'"""
        return pd.DataFrame(
            data=PartyPrefix.ID + CME,
            index=self.source_frame.index,
            columns=[TempColumns.COUNTERPARTY],
        )

    def _get_executing_entity_with_lei(self) -> pd.DataFrame:
        """Returns df with col __executing_entity_with_lei__"""
        return GetTenantLEI.process(
            source_frame=self.source_frame,
            params=ParamsGetTenantLEI(
                target_column_prefix=PartyPrefix.LEI,
                target_lei_column=TempColumns.EXECUTING_ENTITY_WITH_LEI,
            ),
        )

    def _get_trader(self) -> pd.DataFrame:
        """Returns a df with col __trader__"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.TARGET_SUB_ID,
                target_attribute=TempColumns.TRADER,
                prefix=PartyPrefix.ID,
            ),
            auditor=self.auditor,
        )

    def _temp_additional_info_1(self) -> pd.DataFrame:
        """Returns a df with col __additional_info_1__"""
        cases = [
            {"query": "`ff_1028`=='Y'", "value": "Manual Order"},
            {"query": "`ff_1028` != 'Y'", "value": "ALGO"},
        ]
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.ADDITIONAL_INFO_1, cases=cases
            ),
        )

    def _temp_additional_info_2(self):
        """Returns a df with col __additional_info_2__"""
        value_map = {
            "W": "Desk",
            "Y": "Electronic (Default)",
            "C": "Vendor",
            "G": "Sponsored Access",
            "H": "Premium Algorithmic Trading",
            "D": "Other",
        }

        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.FF_1031,
                target_attribute=TempColumns.ADDITIONAL_INFO_2,
                case_insensitive=True,
                value_map=value_map,
            ),
            auditor=self.auditor,
        )

    def _get_order_id(self):
        """Returns a df with temp col __order_id__ to be used to
        populate all id fields"""
        return ConcatAttributes.process(
            source_frame=self.source_frame,
            params=ParamsConcatAttributes(
                target_attribute=TempColumns.ORDER_ID,
                source_attributes=[
                    SourceColumns.ORDER_ID,
                    SourceColumns.SECURITY_ID,
                ],
                delimiter=Delimiters.PIPE,
            ),
        )

    def _get_newo_in_file(self) -> pd.DataFrame:
        """Returns a df with col __newo_in_file__ in post-process"""
        cases = [
            {"query": "`ExecType` == '0'", "value": True},
            {"query": "`ExecType` != '0'", "value": False},
        ]
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.NEWO_IN_FILE, cases=cases
            ),
        )

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        pass

    def _execution_details_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        pass

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        pass

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        pass

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        pass

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        pass

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        pass

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        pass

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        pass

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_stop_price(self) -> pd.DataFrame:
        pass

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        pass

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        pass

    def _financing_type(self) -> pd.DataFrame:
        pass

    def _hierarchy(self) -> pd.DataFrame:
        pass

    def _is_discretionary(self) -> pd.DataFrame:
        pass

    def _is_iceberg(self):
        pass

    def _is_repo(self):
        pass

    def _is_synthetic(self):
        pass

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        pass

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        pass

    def _jurisdiction_country(self) -> pd.DataFrame:
        pass

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        pass

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        pass

    def _order_identifiers_aggregated_order_id_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_parent_order_id(self) -> pd.DataFrame:
        pass

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        pass

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        pass

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        pass

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        pass

    def _source_index(self) -> pd.DataFrame:
        pass

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        pass

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        pass

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        pass

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        pass

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        pass

    def _timestamps_validity_period(self) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_waiver_indicator(self) -> pd.DataFrame:
        pass

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        pass

    def _transaction_details_commission_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_commission_amount_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_commission_amount_type(self) -> pd.DataFrame:
        pass

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_cross_indicator(self) -> pd.DataFrame:
        pass

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        pass

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        pass

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        pass

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        pass

    def _transaction_details_position_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_price(self) -> pd.DataFrame:
        pass

    def _transaction_details_price_average(self) -> pd.DataFrame:
        pass

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        pass

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        pass

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_amount_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_date(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        pass

    def _transaction_details_swap_directionalities(self) -> pd.DataFrame:
        pass

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        pass

    def _transaction_details_trail_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        pass

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        pass

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_venue(self) -> pd.DataFrame:
        pass

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        pass
