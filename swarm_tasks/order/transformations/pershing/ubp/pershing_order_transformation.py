import os

import pandas as pd
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_conditional import Case
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_elastic_schema.static.reference import OrderRecordType
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order_and_tr.static import AssetClass
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ParamsConvertMinorToMajor,
)
from swarm_tasks.io.read.aws.df_from_s3_csv import DfFromS3Csv
from swarm_tasks.io.read.aws.df_from_s3_csv import Params as ParamsDfFromS3Csv
from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI
from swarm_tasks.steeleye.generic.get_tenant_lei import Params as ParamsGetTenantLEI
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ParamsConcatAttributes,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ParamsConvertDatetime,
)
from swarm_tasks.transform.datetime.join_date_and_time import JoinDateAndTimeFormat
from swarm_tasks.transform.datetime.join_date_and_time import (
    Params as ParamsJoinDataTimeFormat,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as ParamsMapAttribute
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.transform.map.map_from_mapping_table import MapFromMappingTable
from swarm_tasks.transform.map.map_from_mapping_table import (
    Params as ParamsMapFromMappingTable,
)
from swarm_tasks.transform.map.map_static import MapStatic
from swarm_tasks.transform.map.map_static import Params as ParamsMapStatic
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as ParamsMapValue
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as ParamsMergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    Params as ParamsInstrumentIdentifiers,
)


class TempColumns:
    ASSET_CLASS = "ASSET_CLASS"
    COUNTERPARTY_ID = "__counterparty_id__"
    CURRENCY = "CURRENCY"
    EXECUTING_ENTITY_WITH_LEI = "__executing_entity_with_lei__"
    EXECUTION_DETAILS_SETTLEMENT_AMOUNT = "__execution_details_settlement_amount__"
    F4_SDL_BID_PRC_C = "F4-Sdl Bid Prc C"
    FALLBACK_CREATED_THROUGH_FB = "__fallback_is_created_through_fb__"
    FALLBACK_ID_CODE = "__fallback_id_code__"
    FALLBACK_INSTRUMENT_FULL_NAME = "__fallback_instrument_fullname__"
    FALLBACK_INSTRUMENT_NATIONAL_CURRENCY = "__fallback_instrument_national_currency__"
    FALLBACK_PRICE_NOTATION = "__fallback_price_notation__"
    FALLBACK_QUANTITY_NOTATION = "__fallback_quantity_notation__"
    INITIAL_QUANTITY = "__initial_quantity__"
    ISIN = "__isin__"
    NEWO_IN_FILE = "__newo_in_file_col__"
    PRICE = "__PRICE__"
    RS17_ISIN = "RS17-ISIN"
    SEDOL = "Sedol"
    TEMP_DATE_TIME_C = "TEMP_DATE_TIME_C"
    TIME = "__time__"
    TRADER_ID = "__trader_id__"
    TRADING_DATE_TIME_C = "TRADING_DATE_TIME_C"
    TRADING_DATE_TIME_M = "TRADING_DATE_TIME_M"
    TRANSACTION_DETAILS_PRICE_CURRENCY = "__transcation_details_price_currency__"
    VENUE = "VENUE"


class SourceColumns:
    AGT_PPL = "AGT/PPL"
    BGN_REF = "BGN_REF"
    BGT_SLD = "BGT/SLD"
    CLIENT_NAME = "CLIENT_NAME"
    CLT_CONS = "CLT_CONS"
    CLT_MKT = "CLT/MKT"
    CLT_REF = "CLT_REF"
    CLT_STL_CCY = "CLT_STL_CCY"
    CMN_REF = "CMN_REF"
    COMM_CCY = "COMM_CCY"
    COMPANY = "COMPANY"
    DATE = "DATE"
    EXCH_CCY1 = "EXCH_CCY1"
    EXCH_CCY2 = "EXCH_CCY2"
    EXCH_RATE2 = "EXCH_RATE2"
    MARKET = "MARKET"
    MIFID_CAT_DSC = "MIFID_CAT_DSC"
    MIFID_REL_DSC = "MIFID_REL_DSC"
    MKT_CONS = "MKT_CONS"
    MKT_NAME = "MKT_NAME"
    PR_CC = "PR_CC"
    PRICE = "PRICE"
    RS17_ISIN = "RS17-ISIN"
    SEDOL = "SEDOL"
    SETTLE = "SETTLE"
    STK_QTY = "STK_QTY"
    STL_EXCH = "STL_EXCH"
    STOCK = "STOCK"
    TIME = "TIME"
    TRADE = "TRADE"
    USERCODE = "USERCODE"


class PershingUbpOrderTransformations(AbstractOrderTransformations):
    def _pre_process(self):
        self.pre_process_df[TempColumns.TIME] = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.TIME,
                target_attribute=TempColumns.TIME,
                start_index=2,
            ),
            auditor=self.auditor,
        )

        self._pre_process_temp_initial_quantity()

    def process(self):
        self.pre_process()
        self.buy_sell()
        self.data_source_name()
        self.date()
        self.execution_details_buy_sell_indicator()
        self.execution_details_order_status()
        self.execution_details_order_type()
        self.execution_details_outgoing_order_addl_info()
        self.execution_details_trading_capacity()
        self.hierarchy()
        self.meta_model()
        self.order_identifiers_aggregated_order_id_code()
        self.order_identifiers_internal_order_id_code()
        self.order_identifiers_order_id_code()
        self.order_identifiers_transaction_ref_no()
        self.id()  # need input from order_identifiers_order_id_code
        self.price_forming_data_initial_quantity()
        self.price_forming_data_traded_quantity()
        self.report_details_transaction_ref_no()
        self.source_index()
        self.source_key()
        self.timestamps_order_submitted()
        self.timestamps_order_received()  # needs input from timestamps_order_submitted()
        self.timestamps_trading_date_time()  # needs input from timestamps_order_submitted()
        self.transaction_details_buy_sell_indicator()  # needs input from execution_details_buy_sell_indicator
        self.timestamps_order_status_updated()
        self.transaction_details_price_currency()
        self.price_forming_data_price()  # needs input from transaction_details_price_currency()
        self.transaction_details_price()  # needs input from price_forming_data_price()
        self.transaction_details_price_notation()
        self.transaction_details_quantity()
        self.transaction_details_quantity_notation()
        self.transaction_details_record_type()
        self.transaction_details_settlement_date()
        self.transaction_details_settlement_amount_currency()
        self.execution_details_settlement_amount()  # needs input from transaction_details_settlement_amount_currency
        self.transaction_details_trading_date_time()  # needs input from timestamps_trading_date_time()
        self.transaction_details_ultimate_venue()
        self.transaction_details_venue()
        self.market_identifiers_instrument()
        self.market_identifiers_parties()
        self.market_identifiers()
        self.post_process()
        return self.target_df

    def _post_process(self):
        """All the temp columns which are needed for downstream tasks are populated
        in self.target_df inside the _post_process() method.
        """
        self.target_df.loc[:, TempColumns.FALLBACK_ID_CODE] = "ID"
        self.target_df.loc[
            :, TempColumns.FALLBACK_PRICE_NOTATION
        ] = PriceNotation.MONE.value
        self.target_df.loc[
            :, TempColumns.FALLBACK_QUANTITY_NOTATION
        ] = QuantityNotation.UNIT.value
        self.target_df.loc[:, TempColumns.FALLBACK_CREATED_THROUGH_FB] = True
        self.target_df.loc[:, TempColumns.FALLBACK_INSTRUMENT_FULL_NAME] = (
            self.source_frame.loc[:, SourceColumns.COMPANY]
            + self.source_frame.loc[:, SourceColumns.STOCK]
        )
        self.target_df.loc[
            :, TempColumns.FALLBACK_INSTRUMENT_NATIONAL_CURRENCY
        ] = self.source_frame.loc[:, SourceColumns.CLT_STL_CCY]
        self.target_df.loc[:, TempColumns.NEWO_IN_FILE] = False

    def _buy_sell(self) -> pd.DataFrame:
        """Returns a data frame containing _order.buySell and _orderState.buySell.
        This is populated from SourceColumns.BGT_SLD"""
        return pd.concat(
            [
                MapValue.process(
                    source_frame=self.source_frame,
                    params=ParamsMapValue(
                        source_attribute=SourceColumns.BGT_SLD,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER, attribute=OrderColumns.BUY_SELL
                        ),
                        case_insensitive=True,
                        value_map={
                            "B": BuySellIndicator.BUYI.value,
                            "S": BuySellIndicator.SELL.value,
                        },
                    ),
                    auditor=self.auditor,
                ),
                MapValue.process(
                    source_frame=self.source_frame,
                    params=ParamsMapValue(
                        source_attribute=SourceColumns.BGT_SLD,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.BUY_SELL,
                        ),
                        case_insensitive=True,
                        value_map={
                            "B": BuySellIndicator.BUYI.value,
                            "S": BuySellIndicator.SELL.value,
                        },
                    ),
                    auditor=self.auditor,
                ),
            ],
            axis=1,
        )

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.settlementAmount
        this is populated with source columns [Mkt_Cons],[Clt_Cons]"""

        df_amt = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.EXECUTION_DETAILS_SETTLEMENT_AMOUNT,
                cases=[
                    Case(
                        query=f"`{SourceColumns.CLT_MKT}`.str.fullmatch('M', case=False, na=False)",
                        attribute=SourceColumns.MKT_CONS,
                    ),
                    Case(
                        query=f"`{SourceColumns.CLT_MKT}`.str.fullmatch('C', case=False, na=False)",
                        attribute=SourceColumns.CLT_CONS,
                    ),
                ],
            ),
        )

        return ConvertMinorToMajor.process(
            source_frame=pd.concat([self.source_frame, df_amt], axis=1),
            params=ParamsConvertMinorToMajor(
                source_price_attribute=TempColumns.EXECUTION_DETAILS_SETTLEMENT_AMOUNT,
                source_ccy_attribute=SourceColumns.CLT_STL_CCY,
                target_price_attribute=OrderColumns.EXECUTION_DETAILS_SETTLEMENT_AMOUNT,
                cast_to="abs",
            ),
        )

    def _data_source_name(self) -> pd.DataFrame:
        """Returns a data frame containing dataSourceName.
        This is populated with the static value 'Pershing'"""
        return pd.DataFrame(
            data="Pershing",
            index=self.source_frame.index,
            columns=[OrderColumns.DATA_SOURCE_NAME],
        )

    def _date(self) -> pd.DataFrame:
        """Returns a data frame containing date.
        This is populated from SourceColumns.TRADE"""
        return ConvertDatetime.process(
            self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.TRADE,
                source_attribute_format="%d/%m/%Y",
                target_attribute=OrderColumns.DATE,
                convert_to=ConvertTo.DATE,
            ),
        )

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.buySellIndicator.
        This is populated from SourceColumns.BGT_SLD"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.BGT_SLD,
                target_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                case_insensitive=True,
                value_map={
                    "B": BuySellIndicator.BUYI.value,
                    "S": BuySellIndicator.SELL.value,
                },
            ),
            auditor=self.auditor,
        )

    def _execution_details_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        pass

    def _execution_details_order_status(self) -> pd.DataFrame:
        """Returns a data frame containing _order.executionDetails.orderStatus and
                _orderState.executionDetails.orderStatus.
        This is populated with the static values NEWO and FILL respectively"""
        return pd.concat(
            [
                MapStatic.process(
                    source_frame=self.source_frame,
                    params=ParamsMapStatic(
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                        ),
                        target_value=OrderStatus.NEWO.value,
                    ),
                ),
                MapStatic.process(
                    source_frame=self.source_frame,
                    params=ParamsMapStatic(
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                        ),
                        target_value=OrderStatus.FILL.value,
                    ),
                ),
            ],
            axis=1,
        )

    def _execution_details_order_type(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.orderType.
        This is populated with static value Market"""
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.EXECUTION_DETAILS_ORDER_TYPE,
                cases=[
                    Case(
                        query=f"`{SourceColumns.CLT_MKT}`.str.fullmatch('M', case=False, na=False)",
                        value="Market Order",
                    ),
                    Case(
                        query=f"`{SourceColumns.CLT_MKT}`.str.fullmatch('C', case=False, na=False)",
                        value="Allocation",
                    ),
                ],
            ),
        )

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.outgoingOrderAddlInfo."""
        return ConcatAttributes.process(
            source_frame=self.source_frame,
            params=ParamsConcatAttributes(
                target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
                source_attributes=[
                    SourceColumns.STL_EXCH,
                    SourceColumns.MIFID_CAT_DSC,
                    SourceColumns.MIFID_REL_DSC,
                    SourceColumns.MARKET,
                ],
                delimiter=",",
            ),
        )

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        pass

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_stop_price(self) -> pd.DataFrame:
        pass

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.tradingCapacity.
        This is populated according to AGT/PPL value"""
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY,
                cases=[
                    Case(
                        query=f"`{SourceColumns.AGT_PPL}`.str.fullmatch('A', case=False, na=False)",
                        value=TradingCapacity.AOTC.value,
                    ),
                    Case(
                        query=f"`{SourceColumns.AGT_PPL}`.str.fullmatch('P', case=False, na=False)",
                        value=TradingCapacity.DEAL.value,
                    ),
                ],
            ),
        )

    def _execution_details_validity_period(self) -> pd.DataFrame:
        pass

    def _financing_type(self) -> pd.DataFrame:
        pass

    @staticmethod
    def _get_instrument_mapping_table_from_s3():
        """Gets the Instrument mapping from the required S3 path (this is required for populating
        the ISIN)"""
        instrument_mapping_key = "mapping_tables/order-ubp-pershing/STK380.TXT"
        return DfFromS3Csv.process(
            params=ParamsDfFromS3Csv(s3_key=instrument_mapping_key, delimiter="^")
        )

    def _hierarchy(self) -> pd.DataFrame:
        """Returns a data frame containing hierarchy
        This is populated with static value Standalone"""
        return MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_attribute=OrderColumns.HIERARCHY,
                target_value="Standalone",
            ),
        )

    def _id(self) -> pd.DataFrame:
        """Returns a data frame containing _order.id and _orderState.id.
        This is populated from orderIdentifiers.orderIdCode
        """
        return pd.concat(
            [
                MapAttribute.process(
                    source_frame=self.target_df,
                    params=ParamsMapAttribute(
                        source_attribute=OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.ID,
                        ),
                    ),
                    auditor=self.auditor,
                ),
                MapAttribute.process(
                    source_frame=self.target_df,
                    params=ParamsMapAttribute(
                        source_attribute=OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.ID,
                        ),
                    ),
                    auditor=self.auditor,
                ),
            ],
            axis=1,
        )

    def _is_discretionary(self) -> pd.DataFrame:
        pass

    def _is_iceberg(self):
        pass

    def _is_repo(self):
        pass

    def _is_synthetic(self):
        pass

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        pass

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        pass

    def _jurisdiction_country(self) -> pd.DataFrame:
        pass

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        pass

    def _market_identifiers(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers by calling MergeMarketIdentifiers.
        Assumes _market_identifiers_instrument() and _market_identifiers_parties() have been
        called earlier"""
        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=ParamsMergeMarketIdentifiers(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                instrument_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.instrument by calling InstrumentIdentifiers."""
        # Get mapping table from S3
        isin_mapping_table = self._get_instrument_mapping_table_from_s3()

        df_isin = MapFromMappingTable.process(
            source_frame=self.source_frame,
            mapping_table=isin_mapping_table,
            params=ParamsMapFromMappingTable(
                source_attribute=SourceColumns.SEDOL,
                target_attribute=TempColumns.ISIN,
                matching_column=TempColumns.SEDOL,
                output_column=TempColumns.RS17_ISIN,
            ),
        )

        for col in [TempColumns.ASSET_CLASS, TempColumns.CURRENCY]:
            if col not in df_isin.columns:
                df_isin.loc[:, col] = pd.NA

        if self.fx_mask.any():
            df_isin.loc[self.fx_mask, TempColumns.ASSET_CLASS] = AssetClass.FX_SPOT
            df_isin.loc[self.fx_mask, TempColumns.CURRENCY] = self.source_frame.loc[
                self.fx_mask, SourceColumns.EXCH_CCY1
            ]

        df_isin.loc[~self.fx_mask, TempColumns.CURRENCY] = self.target_df.loc[
            self.fx_mask, OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY
        ]
        df_isin.loc[:, TempColumns.VENUE] = self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE
        ]

        # This is a bit of a departure from the convention of one private method -> one target field
        # but these fields are needed for LinkInstruments, and it is not worth wasting memory/performance
        # by assigning this column to the pre_process/post_process dfs and then reassigning to target_df
        self.target_df.loc[:, TempColumns.ASSET_CLASS] = df_isin.loc[
            :, TempColumns.ASSET_CLASS
        ]

        return InstrumentIdentifiers.process(
            source_frame=pd.concat([self.source_frame, df_isin], axis=1),
            params=ParamsInstrumentIdentifiers(
                currency_attribute=TempColumns.CURRENCY,
                isin_attribute=TempColumns.ISIN,
                venue_attribute=TempColumns.VENUE,
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                notional_currency_2_attribute=SourceColumns.EXCH_CCY2,
                asset_class_attribute=TempColumns.ASSET_CLASS,
                retain_task_inputs=True,
            ),
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.parties by calling PartyIdentifiers
        Assumes that _transaction_details_buy_sell_indicator() has been
        called earlier"""
        parties_source_frame = pd.concat(
            [
                self.source_frame,
                self._get_trader(),
                self._get_executing_entity(),
                self._get_counterparty(),
                self.target_df.loc[
                    :, [OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR]
                ],
            ],
            axis=1,
        )
        return GenericOrderPartyIdentifiers.process(
            source_frame=parties_source_frame,
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                counterparty_identifier=TempColumns.COUNTERPARTY_ID,
                client_identifier=TempColumns.COUNTERPARTY_ID,
                executing_entity_identifier=TempColumns.EXECUTING_ENTITY_WITH_LEI,
                buyer_identifier=TempColumns.EXECUTING_ENTITY_WITH_LEI,
                seller_identifier=TempColumns.COUNTERPARTY_ID,
                trader_identifier=TempColumns.TRADER_ID,
                buy_sell_side_attribute=OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
                use_buy_mask_for_buyer_seller=True,
            ),
        )

    def _meta_model(self) -> pd.DataFrame:
        """Returns a data frame containing _order.__meta_model__ and _orderState.__meta_model__.
        The columns are populated with the static values Order and OrderState respectively"""
        return pd.DataFrame(
            data=[["Order", "OrderState"]],
            index=self.source_frame.index,
            columns=[
                add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.META_MODEL),
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.META_MODEL,
                ),
            ],
        )

    def _order_class(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_aggregated_order_id_code(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.aggregatedOrderId.
        This is populated from SourceColumns.CMN_REF"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.CMN_REF,
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID,
            ),
            auditor=self.auditor,
        )

    def _order_identifiers_internal_order_id_code(self) -> pd.DataFrame:
        """Returns a data frame containing internalOrderIdCode.
        This is populated from SourceColumns.CLT_REF"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.CLT_REF,
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE,
            ),
            auditor=self.auditor,
        )

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.orderIdCode.
        This is populated from SourceColumns.CMN_REF"""
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE,
                cases=[
                    Case(
                        query=f"`{SourceColumns.CLT_MKT}`.str.fullmatch('M', case=False, na=False)",
                        attribute=SourceColumns.CMN_REF,
                    ),
                    Case(
                        query=f"`{SourceColumns.CLT_MKT}`.str.fullmatch('C', case=False, na=False)",
                        attribute=SourceColumns.CMN_REF,
                        attribute_prefix="ALLOCATION|",
                    ),
                ],
            ),
        )

    def _order_identifiers_parent_order_id(self) -> pd.DataFrame:
        pass

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.transactionRefNo.
        This is populated from SourceColumns.BGN_REF"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.BGN_REF,
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
            ),
            auditor=self.auditor,
        )

    def _price_forming_data_initial_quantity(self) -> pd.DataFrame:
        """Returns a data frame containing priceFormingData.initialQuantity.
        This is populated from SourceColumns.STK_QTY"""
        return MapConditional.process(
            source_frame=pd.concat([self.pre_process_df, self.source_frame], axis=1),
            params=ParamsMapConditional(
                target_attribute=OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
                cases=[
                    Case(
                        query=f"`{SourceColumns.CLT_MKT}`.str.fullmatch('C', case=False, na=False)",
                        attribute=SourceColumns.STK_QTY,
                    ),
                    Case(
                        query=f"`{SourceColumns.CLT_MKT}`.str.fullmatch('M', case=False, na=False)",
                        attribute=TempColumns.INITIAL_QUANTITY,
                    ),
                ],
            ),
        )

    def _price_forming_data_price(self) -> pd.DataFrame:
        """Returns a data frame containing priceFormingData.price.
        mapped to SourceColumns.PRICE"""
        df_temp = pd.DataFrame(index=self.source_frame.index)
        df_temp.loc[
            :, TempColumns.TRANSACTION_DETAILS_PRICE_CURRENCY
        ] = self._get_transaction_details_currency().loc[
            :, TempColumns.TRANSACTION_DETAILS_PRICE_CURRENCY
        ]
        self.fx_mask = (
            self.source_frame.loc[:, SourceColumns.STL_EXCH].str.upper() == "FX"
        )
        df_temp.loc[self.fx_mask, TempColumns.PRICE] = self.source_frame.loc[
            self.fx_mask, SourceColumns.EXCH_RATE2
        ]
        df_temp.loc[~self.fx_mask, TempColumns.PRICE] = self.source_frame.loc[
            ~self.fx_mask, SourceColumns.PRICE
        ]

        return ConvertMinorToMajor.process(
            source_frame=df_temp,
            params=ParamsConvertMinorToMajor(
                source_price_attribute=TempColumns.PRICE,
                source_ccy_attribute=TempColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                target_price_attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                cast_to="abs",
            ),
        )

    def _price_forming_data_remaining_quantity(self) -> pd.DataFrame:
        pass

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        """Returns a data frame containing priceFormingData.tradedQuantity.
        This is populated from SourceColumns.STK_QTY"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.STK_QTY,
                target_attribute=OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
            ),
            auditor=self.auditor,
        )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """Returns a data frame containing reportDetails.transactionRefNo.
        It assumes that _order_identifiers_transaction_ref_no() has been called earlier"""
        return MapAttribute.process(
            source_frame=self.target_df,
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
                target_attribute=OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO,
            ),
            auditor=self.auditor,
        )

    def _source_index(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceIndex column"""
        return pd.DataFrame(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_INDEX],
        )

    def _source_key(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceKey column from the SWARM_FILE_URL"""
        return pd.DataFrame(
            data=os.getenv("SWARM_FILE_URL"),
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_KEY],
        )

    def _timestamps_order_received(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.orderReceived.
        Assumes that _timestamps_order_submitted() has been called earlier"""
        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [OrderColumns.TIMESTAMPS_ORDER_SUBMITTED]
            ],
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
            ),
            auditor=self.auditor,
        )

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.orderStatusUpdated"""
        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [OrderColumns.TIMESTAMPS_ORDER_SUBMITTED]
            ],
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED,
            ),
            auditor=self.auditor,
        )

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.orderSubmitted."""

        temp_df = self._provide_temp_trading_date_time_df()
        return MapConditional.process(
            source_frame=pd.concat([self.source_frame, temp_df], axis=1),
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
                cases=[
                    Case(
                        query=f"`{SourceColumns.CLT_MKT}`.str.fullmatch('M', case=False, na=False)",
                        attribute=TempColumns.TRADING_DATE_TIME_M,
                    ),
                    Case(
                        query=f"`{SourceColumns.CLT_MKT}`.str.fullmatch('C', case=False, na=False)",
                        attribute=TempColumns.TRADING_DATE_TIME_C,
                    ),
                ],
            ),
        )

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.tradingDateTime."""
        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [OrderColumns.TIMESTAMPS_ORDER_SUBMITTED]
            ],
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
                target_attribute=OrderColumns.TIMESTAMPS_TRADING_DATE_TIME,
            ),
            auditor=self.auditor,
        )

    def _timestamps_validity_period(self) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_waiver_indicator(self) -> pd.DataFrame:
        pass

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        pass

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.buySellIndicator.
        Assumes that _execution_details_buy_sell_indicator() has been called earlier"""
        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR]
            ],
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_commission_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_commission_amount_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_commission_amount_type(self) -> pd.DataFrame:
        pass

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_cross_indicator(self) -> pd.DataFrame:
        pass

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        pass

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_position_effect(self) -> pd.DataFrame:
        pass

    def _transaction_details_price(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.price.
        Mapped using OrderColumns.PRICE_FORMING_DATA_PRICE"""
        return MapAttribute.process(
            source_frame=self.target_df.loc[:, [OrderColumns.PRICE_FORMING_DATA_PRICE]],
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_price_average(self) -> pd.DataFrame:
        pass

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.priceCurrency.
        Mapped according to SourceColumns.CLT_MKT"""
        df_cur = self._get_transaction_details_currency()

        return ConvertMinorToMajor.process(
            source_frame=df_cur,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=TempColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
            ),
        )

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.priceNotation.
        This is populated with the static value 'MONE'"""
        return pd.DataFrame(
            data=PriceNotation.MONE.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION],
        )

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        pass

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.quantity.
        mapped using SourceColumns.STK_QTY"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.STK_QTY,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.quantityNotation.
        Mapped to static UNIT value"""
        return MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION,
                target_value=QuantityNotation.UNIT.value,
            ),
        )

    def _transaction_details_record_type(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.recordType.
        Mapped from SourceColumns.CLT_MKT"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.CLT_MKT,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE,
                case_insensitive=True,
                value_map={
                    "C": OrderRecordType.CLIENT_SIDE.value,
                    "M": OrderRecordType.MARKET_SIDE.value,
                },
            ),
            auditor=self.auditor,
        )

    def _transaction_details_settlement_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_amount_currency(self) -> pd.DataFrame:
        """Returns a data frame with transactionDetails.settlementAmountCurrency
        mapped using source column Clt_Stl_Ccy"""

        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.CLT_STL_CCY,
                target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_SETTLEMENT_AMOUNT_CURRENCY,
            ),
        )

    def _transaction_details_settlement_date(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.settlementDate."""
        return ConvertDatetime.process(
            self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.SETTLE,
                source_attribute_format="%d/%m/%Y",
                target_attribute=OrderColumns.TRANSACTION_DETAILS_SETTLEMENT_DATE,
                convert_to=ConvertTo.DATE,
            ),
        )

    def _transaction_details_swap_directionalities(self) -> pd.DataFrame:
        pass

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        pass

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.tradingDateTime."""

        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [OrderColumns.TIMESTAMPS_TRADING_DATE_TIME]
            ],
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.TIMESTAMPS_TRADING_DATE_TIME,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME,
            ),
            auditor=self.auditor,
        )

    def _provide_temp_trading_date_time_df(self) -> pd.DataFrame:
        temp_df = pd.DataFrame(index=self.source_frame.index)
        temp_df.loc[:, SourceColumns.TRADE] = self.source_frame.loc[
            :, SourceColumns.TRADE
        ]
        temp_df.loc[:, TempColumns.TIME] = self.pre_process_df.loc[:, TempColumns.TIME]
        temp_df.loc[:, TempColumns.TEMP_DATE_TIME_C] = "22:30:00"
        df_dt_m = JoinDateAndTimeFormat.process(
            source_frame=temp_df,
            params=ParamsJoinDataTimeFormat(
                source_date_attribute=SourceColumns.TRADE,
                source_time_attribute=TempColumns.TIME,
                target_attribute=TempColumns.TRADING_DATE_TIME_M,
                source_format="%d/%m/%Y%H%M%S",
                target_format="%Y-%m-%d %H:%M:%S",
            ),
        )
        df_dt_c = JoinDateAndTimeFormat.process(
            source_frame=temp_df,
            params=ParamsJoinDataTimeFormat(
                source_date_attribute=SourceColumns.TRADE,
                source_time_attribute=TempColumns.TEMP_DATE_TIME_C,
                target_attribute=TempColumns.TRADING_DATE_TIME_C,
                source_format="%d/%m/%Y%H:%M:%S",
                target_format="%Y-%m-%d %H:%M:%S",
            ),
        )
        return pd.concat([df_dt_m, df_dt_c], axis=1)

    def _transaction_details_trail_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.ultimateVenue."""
        return MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                target_value="XOFF",
            ),
        )

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        pass

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_venue(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.venue."""
        return MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_VENUE,
                target_value="XOFF",
            ),
        )

    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        pass

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        pass

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        pass

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        pass

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        pass

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        pass

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        pass

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        pass

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        pass

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        pass

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        pass

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        pass

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        pass

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        pass

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        pass

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        pass

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        pass

    def _transaction_details_position_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        pass

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        pass

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        pass

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        pass

    def _get_trader(self) -> pd.DataFrame:
        """Gets the trader value to be used in PartyIdentifiers
        the source column is SourceColumns.BROKER_BROKER_NAME
        """
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.USERCODE,
                target_attribute=TempColumns.TRADER_ID,
                prefix=PartyPrefix.ID,
            ),
            auditor=self.auditor,
        )

    def _get_executing_entity(self) -> pd.DataFrame:
        """Gets the executing entity value to be used in PartyIdentifiers from the
        AccountFirm record"""
        return GetTenantLEI.process(
            source_frame=self.source_frame,
            params=ParamsGetTenantLEI(
                target_column_prefix=PartyPrefix.LEI,
                target_lei_column=TempColumns.EXECUTING_ENTITY_WITH_LEI,
            ),
        )

    def _get_counterparty(self) -> pd.DataFrame:
        """Gets the counterparty value to be used in PartyIdentifiers
        from SourceColumns.MKT_NAME or SourceColumns.Client_Name
        """
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.COUNTERPARTY_ID,
                cases=[
                    Case(
                        query=f"`{SourceColumns.CLT_MKT}`.str.fullmatch('M', case=False, na=False)",
                        attribute=SourceColumns.MKT_NAME,
                        attribute_prefix=PartyPrefix.ID,
                    ),
                    Case(
                        query=f"`{SourceColumns.CLT_MKT}`.str.fullmatch('C', case=False, na=False)",
                        attribute=SourceColumns.CLIENT_NAME,
                        attribute_prefix=PartyPrefix.ID,
                    ),
                ],
            ),
        )

    def _get_transaction_details_currency(self) -> pd.DataFrame:
        """returns TRANSACTION_DETAILS_PRICE_CURRENCY
        mapped according to SourceColumns.CLT_MKT"""
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                cases=[
                    Case(
                        query=f"`{SourceColumns.CLT_MKT}`.str.fullmatch('M', case=False, na=False)",
                        attribute=SourceColumns.PR_CC,
                    ),
                    Case(
                        query=f"`{SourceColumns.CLT_MKT}`.str.fullmatch('C', case=False, na=False)",
                        attribute=SourceColumns.COMM_CCY,
                    ),
                ],
            ),
        )

    # aux question for pre process

    def _pre_process_temp_initial_quantity(self):
        """
        sum_per_cmn_ref outputs a pd.Series where the index is unique SourceColumns.CMN_REF values and the content
        are the sum of STK_QTY values for any STK_QTY where CLT_MKT == M and CMN_REF == the respective index
        e.g.
        sum_per_cmn_ref
        CMN_REF
        00360179    10000.0
        00360180     5000.0
        00360181     5000.0
        00360182      250.0
        00360183     6500.0
        ...
        """
        not_null_mask = (
            self.source_frame.loc[
                :, [SourceColumns.CLT_MKT, SourceColumns.CMN_REF, SourceColumns.STK_QTY]
            ]
            .notnull()
            .all(axis=1)
        )
        not_null_and_clt_mkt_is_m = not_null_mask & (
            self.source_frame.loc[:, SourceColumns.CLT_MKT] == "M"
        )

        # sum of STK_QTY per CMN_REF for CLT_MKT == M
        sum_per_cmn_ref = (
            self.source_frame.loc[not_null_and_clt_mkt_is_m, :]
            .groupby([SourceColumns.CLT_MKT, SourceColumns.CMN_REF])[
                SourceColumns.STK_QTY
            ]
            .sum()
            .M
        )

        # assigning the sum of STK_QTY for each CLT_MKT/CMN_REF combination to any matching row
        self.pre_process_df.loc[
            not_null_and_clt_mkt_is_m, TempColumns.INITIAL_QUANTITY
        ] = self.source_frame.loc[
            not_null_and_clt_mkt_is_m, SourceColumns.CMN_REF
        ].apply(
            lambda x: sum_per_cmn_ref[x]
        )

        # setting not filled rows as pd.NA
        self.pre_process_df = self.pre_process_df.fillna(pd.NA)

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        """Not implemented"""
