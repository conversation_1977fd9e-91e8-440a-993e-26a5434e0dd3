from se_core_tasks.abstractions.transformations.transform_map import TransformMap

from swarm_tasks.order.transformations.aladdin.aladdin_order_transformations import (
    AladdinOrderTransformations,
)
from swarm_tasks.order.transformations.aladdin.barings_aladdin_order_transformations import (
    BaringsAladdinOrderTransformations,
)
from swarm_tasks.order.transformations.bbg.emsi import (
    audt_emsi_order_transformations,
)
from swarm_tasks.order.transformations.bbg.emsi.arisaig_audt_emsi_order_transformations import (
    ArisaigBbgEmsiOrderTransformations,
)
from swarm_tasks.order.transformations.bbg.emsi.caxton_audt_emsi_order_transformations import (
    CaxtonBbgEmsiOrderTransformations,
)
from swarm_tasks.order.transformations.bbg.emsi.partners_audt_emsi_order_transformations import (
    PartnersBbgEmsiOrderTransformations,
)
from swarm_tasks.order.transformations.beacon.beacon_lighthouse_order_transformations import (
    BeaconLighthouseOrderTransformations,
)
from swarm_tasks.order.transformations.charles_river.charles_river_order_transformations import (
    CharlesRiverOrderTransformations,
)
from swarm_tasks.order.transformations.charles_river.first_sentier_charles_river_order_transformations import (
    FirstSentierCharlesRiverOrderTransformations,
)
from swarm_tasks.order.transformations.charles_river.robeco_charles_river_order_transformations import (
    RobecoCharlesRiverOrderTransformations,
)
from swarm_tasks.order.transformations.cme.cme_fix_order_transformations import (
    CmeFixOrderTransformations,
)
from swarm_tasks.order.transformations.cme.stp.cme_stp_fix_order_transformations import (
    CmeStpFixOrderTransformations,
)
from swarm_tasks.order.transformations.cme.stp.socar import (
    socar_cme_stp_fix_order_transformations,
)
from swarm_tasks.order.transformations.emsx.fix import emsx_fix_transformations
from swarm_tasks.order.transformations.enfusion.v2 import (
    enfusion_v2_order_transformations,
)
from swarm_tasks.order.transformations.enfusion.v2 import (
    thornbridge_enfusion_v2_order_transformations,
)
from swarm_tasks.order.transformations.expersoft.ubp import (
    expersoft_ubp_order_transformations,
)
from swarm_tasks.order.transformations.eze.eclipse.eze_eclipse_transformations import (
    EzeEclipseOrderTransformations,
)
from swarm_tasks.order.transformations.eze.eclipse.sellaronda_eze_eclipse_transformations import (
    SellarondaEzeEclipseOrderTransformations,
)
from swarm_tasks.order.transformations.fidessa.front_office_trades import (
    cenkos_fidessa_front_office_trades_transformations,
)
from swarm_tasks.order.transformations.fidessa.front_office_trades import (
    fidessa_front_office_trades_transformations,
)
from swarm_tasks.order.transformations.fidessa.front_office_trades import (
    investec_fidessa_front_office_trades_transformations,
)
from swarm_tasks.order.transformations.fidessa.front_office_trades import (
    singer_fidessa_front_office_trades_transformations,
)
from swarm_tasks.order.transformations.fidessa.order_progress import (
    fidessa_order_progress_transformations,
)
from swarm_tasks.order.transformations.flextrade import flextrade_cxl_transformations
from swarm_tasks.order.transformations.flextrade import flextrade_exe_transformations
from swarm_tasks.order.transformations.flextrade import (
    flextrade_prim_rpl_transformations,
)
from swarm_tasks.order.transformations.ibp.tsox.fix import (
    ibp_tsox_fix_order_transformations,
)
from swarm_tasks.order.transformations.ice.pof.fix import (
    ice_pof_fix_order_transformations,
)
from swarm_tasks.order.transformations.ice.pof.fix.arrow_ice_pof_fix_order_transformations import (
    ArrowIcePofFixOrderTransformations,
)
from swarm_tasks.order.transformations.ice.pof.fix.eracommodities_ice_pof_fix_order_transformations import (
    EraCommoditiesIcePofFixOrderTransformations,
)
from swarm_tasks.order.transformations.kooltra.monsas import (
    kooltra_monsas_order_transformations,
)
from swarm_tasks.order.transformations.lme.eod import (
    lme_eod_transformations,
)
from swarm_tasks.order.transformations.lme.fix import (
    lme_fix_order_select_override_transformations,
)
from swarm_tasks.order.transformations.lme.fix import lme_fix_order_transformations
from swarm_tasks.order.transformations.metatrader.mt4.mt4_trades_transformations import (
    MT4TradesTransformations,
)
from swarm_tasks.order.transformations.metatrader.mt4.oanda.mt4_oanda_trades_transformations import (
    MT4OandaTradesTransformations,
)
from swarm_tasks.order.transformations.metatrader.mt4.onefinancial.mt4_onefinancial_trades_transformations import (
    MT4OneFinancialTradesTransformations,
)
from swarm_tasks.order.transformations.metatrader.mt5 import mt5_deals_transformations
from swarm_tasks.order.transformations.metatrader.mt5 import mt5_order_transformations
from swarm_tasks.order.transformations.metatrader.mt5.oanda import (
    mt5_oanda_deals_transformations,
)
from swarm_tasks.order.transformations.metatrader.mt5.oanda import (
    mt5_oanda_order_transformations,
)
from swarm_tasks.order.transformations.metatrader.mt5.onefinancial import (
    mt5_onefinancial_deals_transformations,
)
from swarm_tasks.order.transformations.metatrader.mt5.onefinancial import (
    mt5_onefinancial_order_transformations,
)
from swarm_tasks.order.transformations.oanda import oanda_v20_transformations
from swarm_tasks.order.transformations.pershing.ubp.pershing_order_transformation import (
    PershingUbpOrderTransformations,
)
from swarm_tasks.order.transformations.red_deer import red_deer_order_transformations
from swarm_tasks.order.transformations.saxo_bank import saxo_bank_order_transformations
from swarm_tasks.order.transformations.saxo_bank import (
    thornbridge_saxo_bank_order_transformations,
)
from swarm_tasks.order.transformations.shell import (
    shell_order_transformations,
)
from swarm_tasks.order.transformations.thinkfolio import (
    thinkfolio_order_transformations,
)

aladdin_transform_map = TransformMap(
    default=AladdinOrderTransformations,
    map={
        "barings": BaringsAladdinOrderTransformations,
        "barings-uat": BaringsAladdinOrderTransformations,
        "ben": BaringsAladdinOrderTransformations,
    },
)


bbg_audt_emsi_transform_map = TransformMap(
    default=audt_emsi_order_transformations.BbgEmsiOrderTransformations,
    map={
        "partners": PartnersBbgEmsiOrderTransformations,
        "caxton": CaxtonBbgEmsiOrderTransformations,
        "arisaig": ArisaigBbgEmsiOrderTransformations,
    },
)

beacon_lighthouse_transform_map = TransformMap(
    default=BeaconLighthouseOrderTransformations, map={}
)

charles_river_transform_map = TransformMap(
    default=CharlesRiverOrderTransformations,
    map={
        "firstsentier": FirstSentierCharlesRiverOrderTransformations,
        "robeco-uat": RobecoCharlesRiverOrderTransformations,
        "robeco": RobecoCharlesRiverOrderTransformations,
        "tom": RobecoCharlesRiverOrderTransformations,
    },
)

cme_fix_transform_map = TransformMap(default=CmeFixOrderTransformations, map={})

cme_stp_transform_map = TransformMap(
    default=CmeStpFixOrderTransformations,
    map={
        "socar": socar_cme_stp_fix_order_transformations.SocarCmeStpFixOrderTransformations
    },
)

emsx_fix_transform_map = TransformMap(
    default=emsx_fix_transformations.EMSXFixTransformations,
    map={},
)

enfusion_v2_transform_map = TransformMap(
    default=enfusion_v2_order_transformations.EnfusionV2OrderTransformations,
    map={
        "thornbridge": thornbridge_enfusion_v2_order_transformations.ThornbridgeEnfusionV2OrderTransformations,
    },
)

expersoft_ubp_transform_map = TransformMap(
    default=expersoft_ubp_order_transformations.ExpersoftUbpOrderTransformations,
    map={},
)
eze_eclipse_transform_map = TransformMap(
    default=EzeEclipseOrderTransformations,
    map={
        "sellaronda": SellarondaEzeEclipseOrderTransformations,
        "puneeth": SellarondaEzeEclipseOrderTransformations,
        "ben": SellarondaEzeEclipseOrderTransformations,
    },
)

fidessa_front_office_trades_transform_map = TransformMap(
    default=fidessa_front_office_trades_transformations.FidessaFrontOfficeTradesTransformations,
    map={
        "cenkos": cenkos_fidessa_front_office_trades_transformations.CenkosFidessaFrontOfficeTradesTransformations,
        "investec": investec_fidessa_front_office_trades_transformations.InvestecFidessaFrontOfficeTradesTransformations,
        "iris": singer_fidessa_front_office_trades_transformations.SingerFidessaFrontOfficeTradesTransformations,
        "singer": singer_fidessa_front_office_trades_transformations.SingerFidessaFrontOfficeTradesTransformations,
        "mar": singer_fidessa_front_office_trades_transformations.SingerFidessaFrontOfficeTradesTransformations,
    },
)

fidessa_order_progress_transform_map = TransformMap(
    default=fidessa_order_progress_transformations.FidessaOrderProgressTransformations,
    map={},
)

flextrade_order_cxl_transform_map = TransformMap(
    default=flextrade_cxl_transformations.FlextradeCxlTransformations,
    map={},
)

flextrade_order_exe_transform_map = TransformMap(
    default=flextrade_exe_transformations.FlextradeExeTransformations,
    map={},
)

flextrade_order_prim_rpl_transform_map = TransformMap(
    default=flextrade_prim_rpl_transformations.FlextradePrimRplTransformations,
    map={},
)

ibp_tsox_fix_transform_map = TransformMap(
    default=ibp_tsox_fix_order_transformations.IbpTsoxFixOrderTransformations, map={}
)

ice_pof_transform_map = TransformMap(
    default=ice_pof_fix_order_transformations.IcePofFixOrderTransformations,
    map={
        "eracommodities": EraCommoditiesIcePofFixOrderTransformations,
        "arrow": ArrowIcePofFixOrderTransformations,
    },
)

kooltra_monsas_transform_map = TransformMap(
    default=kooltra_monsas_order_transformations.KooltraMonsasOrderTransformations,
    map={},
)

lme_eod_transform_map = TransformMap(
    default=lme_eod_transformations.LmeEodTransformations, map={}
)

lme_fix_transform_map = TransformMap(
    default=lme_fix_order_transformations.LmeFixOrderTransformations,
    map={
        "select": lme_fix_order_select_override_transformations.LmeFixOrderSelectOverrideTransformations
    },
)

metatrader_mt5_orders_transform_map = TransformMap(
    default=mt5_order_transformations.MT5OrderTransformations,
    map={
        "onefinancial": mt5_onefinancial_order_transformations.MT5OneFinancialOrderTransformations,
        "oanda": mt5_oanda_order_transformations.MT5OandaOrderTransformations,
    },
)

metatrader_mt5_deals_transform_map = TransformMap(
    default=mt5_deals_transformations.MT5DealsTransformations,
    map={
        "onefinancial": mt5_onefinancial_deals_transformations.MT5OneFinancialDealsTransformations,
        "oanda": mt5_oanda_deals_transformations.MT5OandaDealsTransformations,
    },
)

metatrader_mt4_trades_transform_map = TransformMap(
    default=MT4TradesTransformations,
    map={
        "onefinancial": MT4OneFinancialTradesTransformations,
        "oanda": MT4OandaTradesTransformations,
    },
)

oanda_v20_transform_map = TransformMap(
    default=oanda_v20_transformations.OandaV20OrderTransformations, map={}
)

pershing_ubp_transform_map = TransformMap(
    default=PershingUbpOrderTransformations,
    map={},
)

red_deer_transform_map = TransformMap(
    default=red_deer_order_transformations.RedDeerOrderTransformations,
    map={},
)

saxo_bank_orders_transform_map = TransformMap(
    default=saxo_bank_order_transformations.SaxoBankOrderTransformations,
    map={
        "thornbridge": thornbridge_saxo_bank_order_transformations.ThornbridgeSaxoBankOrderTransformations
    },
)

shell_orders_transform_map = TransformMap(
    default=shell_order_transformations.ShellOrderTransformations,
    map={},
)

thinkfolio_transform_map = TransformMap(
    default=thinkfolio_order_transformations.ThinkfolioOrderTransformations,
    map={},
)
