from swarm_tasks.order.transformations.bbg.emsi.audt_emsi_order_transformations import (
    BbgEmsiOrderTransformations,
)


class CaxtonBbgEmsiOrderTransformations(BbgEmsiOrderTransformations):
    @staticmethod
    def _set_timezone_info() -> str:
        """
        Sets the timezone info for the ConvertDateTime tasks \n
        Using London timezone for Caxton
        """
        return "Europe/London"
