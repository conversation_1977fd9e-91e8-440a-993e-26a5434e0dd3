import logging
import os
from pathlib import Path

import pandas as pd
from prefect.engine import signals
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import HierarchyEnum
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.mifid2 import PassiveAggressiveStatus
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_elastic_schema.static.reference import OrderRecordType
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order_and_tr.static import AssetClass
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ParamsConvertMinorToMajor,
)
from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI
from swarm_tasks.steeleye.generic.get_tenant_lei import Params as ParamsGetTenantLEI
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ParamsConcatAttributes,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ConvertDatetimeParams,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as ParamsMapAttribute
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as ParamsMapValue
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as ParamsMergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    Params as ParamsInstrumentIdentifiers,
)


class TempColumns:
    ASSET_CLASS = "__asset_class__"
    COUNTERPARTY_WITH_LEI = "__counterparty_with_lei__"
    EXECUTING_ENTITY_WITH_LEI = "__executing_entity_with_lei__"
    EXPIRATION_CONVENTION_WITH_PREFIX = "__expiration_convention_with_prefix__"
    EXPIRY_DATE = "__expiry_date__"
    FIXING_DATE_WITH_PREFIX = "__fixing_date_with_prefix__"
    FIXING_SOURCE_WITH_PREFIX = "__fixing_source_with_prefix__"
    FX_OPTIONS_CURRENCY_1 = "__fx_options_currency1__"
    FX_OPTIONS_CURRENCY_2 = "__fx_options_currency2__"
    FX_TRADES_CURRENCY_PAIR = "__fx_trades_currency_pair__"
    FX_TRADES_CURRENCY_PAIR_WITH_PREFIX = "__fx_trades_currency_pair_with_prefix__"
    NOTIONAL_CURRENCY_2 = "__notional_currency_2__"
    OPTION_STRATEGY_WITH_PREFIX = "__option_strategy_with_prefix__"
    OPTION_STRIKE_PRICE = "__option_strike_price__"
    OPTION_TYPE = "__option_type__"
    PREMIUM_DATE_WITH_PREFIX = "__premium_date_with_prefix__"
    TRADER_ID = "__trader_id__"
    TYPE_WITH_PREFIX = "__type_with_prefix__"


class SourceColumns:
    ACTION = "ACTION"
    AMOUNT_1 = "AMOUNT1"
    BROKER_BROKER_NAME = "BROKER:BROKERNAME"
    CURRENCY_1 = "CURRENCY1"
    CURRENCY_2 = "CURRENCY2"
    CURRENCY_PAIR = "CURRENCYPAIR"
    EXPIRATION_CONVENTION = "EXPIRATIONCONVENTION"
    EXPIRATION_DATE = "EXPIRATIONDATE"
    FIXING_DATE = "FIXINGDATE"
    FIXING_SOURCE = "FIXINGSOURCE"
    FX_OPTION_CREATED_BY = "FXOPTION:CREATEDBY"
    FX_OPTION_FX_OPTION_NAME = "FXOPTION:FXOPTIONNAME"
    LEI = "LEI"
    LEG_CLASSIFICATION = "LEGCLASSIFICATION"
    OPTION_EXECUTION_TIME = "OPTIONEXECUTIONTIME"
    OPTION_NAME = "OPTIONNAME"
    OPTION_STRATEGY = "OPTIONSTRATEGY"
    PREMIUM = "PREMIUM"
    PREMIUM_CURRENCY = "PREMIUMCURRENCY"
    PREMIUM_DATE = "PREMIUMDATE"
    RATE = "RATE"
    STANDARD_PAIR_AMOUNT_1 = "STANDARDPAIRAMOUNT1"
    STRIKE = "STRIKE"
    TRADE_EXECUTION_TIME = "TRADEEXECUTIONTIME"
    TRADE_NAME = "TRADENAME"
    VALUE_DATE = "VALUEDATE"
    TYPE = "TYPE"


class FileTypes:
    FX_OPTION = "fx_option"
    FX_TRADE = "fx_trade"
    INVALID = "invalid"


class KooltraMonsasOrderTransformations(AbstractOrderTransformations):
    """Transformations class for Kooltra Monsas Orders. 2 types of files are handled:
    Fx Trade and Fx Option files. Fields are created for Order and OrderState models.
    """

    def __init__(self, source_frame: pd.DataFrame, logger: logging.Logger, auditor):
        super().__init__(source_frame=source_frame, logger=logger, auditor=auditor)
        self.file_name = Path(os.getenv("SWARM_FILE_URL")).name.lower()
        self.file_type = (
            FileTypes.FX_TRADE
            if "trade" in self.file_name
            else FileTypes.FX_OPTION
            if "option" in self.file_name
            else FileTypes.INVALID
        )
        if self.file_type == FileTypes.INVALID:
            raise signals.FAIL(
                message="This flow cannot handle this file type. It can only handle"
                " FX Trade files (which have 'trade' in the file name) and "
                "FX Option files (which have 'option' in the file name) "
            )

    def _pre_process(self):
        """This pre-processing method is used to populate columns in pre_process_df. The columns
        populated are __fx_options_currency1__, __fx_options_currency2__, __option_strike_price__
        and __expiry_date__. These columns are used multiple times in process() or post_process()"""
        if self.file_type == FileTypes.FX_OPTION:
            # Extract currencies from 6-letter column SourceColumns.CURRENCY_PAIR
            self.pre_process_df = pd.concat(
                [
                    self.pre_process_df,
                    MapAttribute.process(
                        source_frame=self.source_frame,
                        params=ParamsMapAttribute(
                            source_attribute=SourceColumns.CURRENCY_PAIR,
                            target_attribute=TempColumns.FX_OPTIONS_CURRENCY_1,
                            start_index=0,
                            end_index=3,
                        ),
                        auditor=self.auditor,
                    ),
                    MapAttribute.process(
                        source_frame=self.source_frame,
                        params=ParamsMapAttribute(
                            source_attribute=SourceColumns.CURRENCY_PAIR,
                            target_attribute=TempColumns.FX_OPTIONS_CURRENCY_2,
                            start_index=3,
                            end_index=6,
                        ),
                        auditor=self.auditor,
                    ),
                ],
                axis=1,
            )
        self.pre_process_df = pd.concat(
            [self.pre_process_df, self._get_strike_price(), self._get_expiry_date()],
            axis=1,
        )

    def process(self):
        """All the schema target columns which need to be populated are populated in
        self.target_df by the public methods called by process(). 3 temp columns are
        populated here as well as these are present in all Order flows: __meta_model__,
        marketIdentifiers.parties and marketIdentifiers.instrument.
        """
        self.pre_process()
        self.buy_sell()
        self.data_source_name()
        self.date()
        self.execution_details_buy_sell_indicator()
        self.execution_details_order_status()
        self.execution_details_order_type()
        self.execution_details_outgoing_order_addl_info()
        self.execution_details_passive_aggressive_indicator()
        self.execution_details_trading_capacity()
        self.hierarchy()
        self.id()
        self.meta_model()
        self.order_identifiers_order_id_code()  # needs id() output
        self.order_identifiers_transaction_ref_no()
        self.price_forming_data_initial_quantity()
        self.price_forming_data_price()
        self.price_forming_data_traded_quantity()  # needs price_forming_data_initial_quantity() output
        self.report_details_transaction_ref_no()  # needs order_identifiers_transaction_ref_no() output
        self.source_index()
        self.source_key()
        self.timestamps_order_received()
        self.timestamps_order_status_updated()  # needs timestamps_order_received() output
        self.timestamps_order_submitted()  # needs timestamps_order_received() output
        self.timestamps_trading_date_time()  # needs timestamps_order_received() output
        self.transaction_details_buy_sell_indicator()  # needs execution_details_buy_sell_indicator() output
        self.transaction_details_complex_trade_component_id()
        self.transaction_details_price()  # needs price_forming_data_price() output
        self.transaction_details_price_currency()
        self.transaction_details_price_notation()
        self.transaction_details_quantity()  # needs price_forming_data_initial_quantity() output
        self.transaction_details_quantity_currency()
        self.transaction_details_quantity_notation()
        self.transaction_details_record_type()
        self.transaction_details_trading_capacity()  # needs execution_details_trading_capacity() output
        self.transaction_details_trading_date_time()  # needs timestamps_order_received() output
        self.transaction_details_ultimate_venue()
        self.transaction_details_venue()
        self.market_identifiers_instrument()
        self.market_identifiers_parties()
        self.market_identifiers()
        self.post_process()
        return self.target_df

    def _post_process(self):
        """All the temp columns which are needed for downstream tasks are populated
        in self.target_df inside the _post_process() method. The temp
        columns included here are __expiry_date__ and __option_strike_price__
        """
        self.target_df.loc[:, TempColumns.EXPIRY_DATE] = self.pre_process_df.loc[
            :, TempColumns.EXPIRY_DATE
        ]
        self.target_df.loc[
            :, TempColumns.OPTION_STRIKE_PRICE
        ] = self.pre_process_df.loc[:, TempColumns.OPTION_STRIKE_PRICE]

    def _buy_sell(self) -> pd.DataFrame:
        """Returns a data frame containing _order.buySell and _orderState.buySell.
        This is populated from SourceColumns.ACTION"""
        return pd.concat(
            [
                MapValue.process(
                    source_frame=self.source_frame,
                    params=ParamsMapValue(
                        source_attribute=SourceColumns.ACTION,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.BUY_SELL,
                        ),
                        case_insensitive=True,
                        value_map={"buy": "1", "sell": "2"},
                    ),
                    auditor=self.auditor,
                ),
                MapValue.process(
                    source_frame=self.source_frame,
                    params=ParamsMapValue(
                        source_attribute=SourceColumns.ACTION,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.BUY_SELL,
                        ),
                        case_insensitive=True,
                        value_map={"buy": "1", "sell": "2"},
                    ),
                    auditor=self.auditor,
                ),
            ],
            axis=1,
        )

    def _data_source_name(self) -> pd.DataFrame:
        """Returns a data frame containing dataSourceName.
        This is populated with the static value 'Kooltra'"""
        return pd.DataFrame(
            data="Kooltra",
            index=self.source_frame.index,
            columns=[OrderColumns.DATA_SOURCE_NAME],
        )

    def _date(self) -> pd.DataFrame:
        """Returns a data frame containing date.
        This is populated from SourceColumns.TRADE_EXECUTION_TIME for Fx Trade files
        and SourceColumns.OPTION_EXECUTION_TIME for Fx Option files"""
        if self.file_type == FileTypes.FX_TRADE:
            return ConvertDatetime.process(
                self.source_frame,
                params=ConvertDatetimeParams(
                    source_attribute=SourceColumns.TRADE_EXECUTION_TIME,
                    source_attribute_format="%Y-%m-%d %H:%M:%S",
                    target_attribute=OrderColumns.DATE,
                    convert_to="date",
                ),
            )
        else:
            # self.file_type == FileTypes.FX_OPTION
            return ConvertDatetime.process(
                self.source_frame,
                params=ConvertDatetimeParams(
                    source_attribute=SourceColumns.OPTION_EXECUTION_TIME,
                    source_attribute_format="%Y-%m-%d %H:%M:%S",
                    target_attribute=OrderColumns.DATE,
                    convert_to="date",
                ),
            )

    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        pass

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.buySellIndicator.
        This is populated from SourceColumns.ACTION"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.ACTION,
                target_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                case_insensitive=True,
                value_map={
                    "buy": BuySellIndicator.BUYI.value,
                    "sell": BuySellIndicator.SELL.value,
                },
            ),
            auditor=self.auditor,
        )

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        pass

    def _execution_details_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        pass

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        pass

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        pass

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        pass

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        pass

    def _execution_details_order_status(self) -> pd.DataFrame:
        """Returns a data frame containing _order.executionDetails.orderStatus and
        _orderState.executionDetails.orderStatus.
        This is populated with the static values NEWO and FILL respectively"""
        return pd.DataFrame(
            data=[[OrderStatus.NEWO.value, OrderStatus.FILL.value]],
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                ),
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                ),
            ],
        )

    def _execution_details_order_type(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.orderType.
        This is populated with the static value 'Market'"""
        return pd.DataFrame(
            data="Market",
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_ORDER_TYPE],
        )

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.outgoingOrderAddlInfo.
        For Fx Trade files, this is populated from SourceColumns.CURRENCY_1, SourceColumns.CURRENCY_2,
        SourceColumns.FIXING_DATE and SourceColumns.FIXING_SOURCE.
        For Fx Option files, this is populated from SourceColumns.PREMIUM_DATE, SourceColumns.TYPE,
        SourceColumns.OPTION_STRATEGY and SourceColumns.EXPIRATION_CONVENTION"""
        if self.file_type == FileTypes.FX_TRADE:
            # Create temp dfs with the required columns
            fx_trade_currency_pair_df = ConcatAttributes.process(
                source_frame=self.source_frame,
                params=ParamsConcatAttributes(
                    target_attribute=TempColumns.FX_TRADES_CURRENCY_PAIR,
                    source_attributes=[
                        SourceColumns.CURRENCY_1,
                        SourceColumns.CURRENCY_2,
                    ],
                ),
            )
            fx_trade_additional_info_df = pd.concat(
                [
                    MapAttribute.process(
                        source_frame=fx_trade_currency_pair_df,
                        params=ParamsMapAttribute(
                            source_attribute=TempColumns.FX_TRADES_CURRENCY_PAIR,
                            target_attribute=TempColumns.FX_TRADES_CURRENCY_PAIR_WITH_PREFIX,
                            prefix="Currency Pair: ",
                        ),
                        auditor=self.auditor,
                    ),
                    MapAttribute.process(
                        source_frame=self.source_frame,
                        params=ParamsMapAttribute(
                            source_attribute=SourceColumns.FIXING_DATE,
                            target_attribute=TempColumns.FIXING_DATE_WITH_PREFIX,
                            prefix="Fixing Date: ",
                        ),
                        auditor=self.auditor,
                    ),
                    MapAttribute.process(
                        source_frame=self.source_frame,
                        params=ParamsMapAttribute(
                            source_attribute=SourceColumns.FIXING_SOURCE,
                            target_attribute=TempColumns.FIXING_SOURCE_WITH_PREFIX,
                            prefix="Fixing Source: ",
                        ),
                        auditor=self.auditor,
                    ),
                ],
                axis=1,
            )
            return ConcatAttributes.process(
                source_frame=fx_trade_additional_info_df,
                params=ParamsConcatAttributes(
                    target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
                    source_attributes=[
                        TempColumns.FX_TRADES_CURRENCY_PAIR_WITH_PREFIX,
                        TempColumns.FIXING_DATE_WITH_PREFIX,
                        TempColumns.FIXING_SOURCE_WITH_PREFIX,
                    ],
                    delimiter=", ",
                ),
            )
        else:
            # self.file_type == FileTypes.FX_OPTION
            fx_option_additional_info_df = pd.concat(
                [
                    MapAttribute.process(
                        source_frame=self.source_frame,
                        params=ParamsMapAttribute(
                            source_attribute=SourceColumns.PREMIUM_DATE,
                            target_attribute=TempColumns.PREMIUM_DATE_WITH_PREFIX,
                            prefix="Premium Date: ",
                        ),
                        auditor=self.auditor,
                    ),
                    MapAttribute.process(
                        source_frame=self.source_frame,
                        params=ParamsMapAttribute(
                            source_attribute=SourceColumns.TYPE,
                            target_attribute=TempColumns.TYPE_WITH_PREFIX,
                            prefix="Type: ",
                        ),
                        auditor=self.auditor,
                    ),
                    MapAttribute.process(
                        source_frame=self.source_frame,
                        params=ParamsMapAttribute(
                            source_attribute=SourceColumns.OPTION_STRATEGY,
                            target_attribute=TempColumns.OPTION_STRATEGY_WITH_PREFIX,
                            prefix="Strategy: ",
                        ),
                        auditor=self.auditor,
                    ),
                    MapAttribute.process(
                        source_frame=self.source_frame,
                        params=ParamsMapAttribute(
                            source_attribute=SourceColumns.EXPIRATION_CONVENTION,
                            target_attribute=TempColumns.EXPIRATION_CONVENTION_WITH_PREFIX,
                            prefix="Expiration Convention: ",
                        ),
                        auditor=self.auditor,
                    ),
                ],
                axis=1,
            )
            return ConcatAttributes.process(
                source_frame=fx_option_additional_info_df,
                params=ParamsConcatAttributes(
                    target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
                    source_attributes=[
                        TempColumns.PREMIUM_DATE_WITH_PREFIX,
                        TempColumns.TYPE_WITH_PREFIX,
                        TempColumns.OPTION_STRATEGY_WITH_PREFIX,
                        TempColumns.EXPIRATION_CONVENTION_WITH_PREFIX,
                    ],
                    delimiter=", ",
                ),
            )

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.passiveAggressiveIndicator.
        This is populated with the static value 'PASV'"""
        return pd.DataFrame(
            data=PassiveAggressiveStatus.PASV.value,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_PASSIVE_AGGRESSIVE_INDICATOR],
        )

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        pass

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        pass

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        pass

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_stop_price(self) -> pd.DataFrame:
        pass

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.tradingCapacity.
        This is populated with the static value 'AOTC'"""
        return pd.DataFrame(
            data=TradingCapacity.AOTC.value,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY],
        )

    def _execution_details_validity_period(self) -> pd.DataFrame:
        pass

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        pass

    def _hierarchy(self) -> pd.DataFrame:
        """Returns a data frame containing hierarchy.
        This is populated with the static value 'Standalone'"""
        return pd.DataFrame(
            data=HierarchyEnum.STANDALONE.value,
            index=self.source_frame.index,
            columns=[OrderColumns.HIERARCHY],
        )

    def _financing_type(self) -> pd.DataFrame:
        pass

    def _id(self) -> pd.DataFrame:
        """Returns a data frame containing _order.id and _orderState.id.
        This is populated from SourceColumns.TRADE_NAME for Fx trade files, and
        SourceColumns.FX_OPTION_FX_OPTION_NAME for Fx Option files"""
        if self.file_type == FileTypes.FX_TRADE:
            return pd.concat(
                [
                    MapAttribute.process(
                        source_frame=self.source_frame,
                        params=ParamsMapAttribute(
                            source_attribute=SourceColumns.TRADE_NAME,
                            target_attribute=add_prefix(
                                prefix=ModelPrefix.ORDER, attribute=OrderColumns.ID
                            ),
                        ),
                        auditor=self.auditor,
                    ),
                    MapAttribute.process(
                        source_frame=self.source_frame,
                        params=ParamsMapAttribute(
                            source_attribute=SourceColumns.TRADE_NAME,
                            target_attribute=add_prefix(
                                prefix=ModelPrefix.ORDER_STATE,
                                attribute=OrderColumns.ID,
                            ),
                        ),
                        auditor=self.auditor,
                    ),
                ],
                axis=1,
            )
        else:
            # self.file_type == FileTypes.FX_OPTION
            return pd.concat(
                [
                    MapAttribute.process(
                        source_frame=self.source_frame,
                        params=ParamsMapAttribute(
                            source_attribute=SourceColumns.FX_OPTION_FX_OPTION_NAME,
                            target_attribute=add_prefix(
                                prefix=ModelPrefix.ORDER, attribute=OrderColumns.ID
                            ),
                        ),
                        auditor=self.auditor,
                    ),
                    MapAttribute.process(
                        source_frame=self.source_frame,
                        params=ParamsMapAttribute(
                            source_attribute=SourceColumns.FX_OPTION_FX_OPTION_NAME,
                            target_attribute=add_prefix(
                                prefix=ModelPrefix.ORDER_STATE,
                                attribute=OrderColumns.ID,
                            ),
                        ),
                        auditor=self.auditor,
                    ),
                ],
                axis=1,
            )

    def _is_discretionary(self) -> pd.DataFrame:
        pass

    def _is_iceberg(self):
        pass

    def _is_repo(self):
        pass

    def _is_synthetic(self):
        pass

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        pass

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        pass

    def _jurisdiction_country(self) -> pd.DataFrame:
        pass

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        pass

    def _market_identifiers(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers by calling MergeMarketIdentifiers.
        Assumes _market_identifiers_instrument() and _market_identifiers_parties() have been
        called earlier"""
        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=ParamsMergeMarketIdentifiers(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                instrument_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.instrument by calling InstrumentIdentifiers.
        Assumes that _transaction_details_quantity_currency() and _transaction_details_venue() have been
        called earlier"""
        instrument_source_df = pd.concat(
            [
                self.source_frame,
                self._get_asset_class(),
                self.target_df.loc[
                    :,
                    [
                        OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
                        OrderColumns.TRANSACTION_DETAILS_VENUE,
                    ],
                ],
                self.pre_process_df.loc[
                    :,
                    [TempColumns.EXPIRY_DATE, TempColumns.OPTION_STRIKE_PRICE],
                ],
                self._get_option_type(),
                self._get_notional_currency_2(),
            ],
            axis=1,
        )
        return InstrumentIdentifiers.process(
            source_frame=instrument_source_df,
            params=ParamsInstrumentIdentifiers(
                asset_class_attribute=TempColumns.ASSET_CLASS,
                currency_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
                expiry_date_attribute=TempColumns.EXPIRY_DATE,
                notional_currency_2_attribute=TempColumns.NOTIONAL_CURRENCY_2,
                option_strike_price_attribute=TempColumns.OPTION_STRIKE_PRICE,
                option_type_attribute=TempColumns.OPTION_TYPE,
                venue_attribute=OrderColumns.TRANSACTION_DETAILS_VENUE,
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
            ),
            auditor=self.auditor,
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.parties by calling PartyIdentifiers
        Assumes that _transaction_details_buy_sell_indicator() has been
        called earlier"""
        parties_source_frame = pd.concat(
            [
                self.source_frame,
                self._get_trader(),
                self._get_executing_entity(),
                self._get_counterparty(),
                self.target_df.loc[
                    :, [OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR]
                ],
            ],
            axis=1,
        )
        return GenericOrderPartyIdentifiers.process(
            source_frame=parties_source_frame,
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                counterparty_identifier=TempColumns.COUNTERPARTY_WITH_LEI,
                executing_entity_identifier=TempColumns.EXECUTING_ENTITY_WITH_LEI,
                buyer_identifier=TempColumns.EXECUTING_ENTITY_WITH_LEI,
                seller_identifier=TempColumns.COUNTERPARTY_WITH_LEI,
                trader_identifier=TempColumns.TRADER_ID,
                execution_within_firm_identifier=TempColumns.TRADER_ID,
                buyer_decision_maker_identifier=TempColumns.TRADER_ID,
                buy_sell_side_attribute=OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
                use_buy_mask_for_buyer_seller=True,
                use_buy_mask_for_buyer_seller_decision_maker=True,
            ),
        )

    def _meta_model(self) -> pd.DataFrame:
        """Returns a data frame containing _order.__meta_model__ and _orderState.__meta_model__.
        The columns are populated with the static values Order and OrderState respectively"""
        return pd.DataFrame(
            data=[["Order", "OrderState"]],
            index=self.source_frame.index,
            columns=[
                add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.META_MODEL),
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.META_MODEL,
                ),
            ],
        )

    def _order_class(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_aggregated_order_id_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        pass

    def _order_identifiers_internal_order_id_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.orderIdCode.
        It assumes that _id() has been called earlier"""
        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.ID)]
            ],
            params=ParamsMapAttribute(
                source_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER, attribute=OrderColumns.ID
                ),
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE,
            ),
            auditor=self.auditor,
        )

    def _order_identifiers_parent_order_id(self):
        pass

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        pass

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.transactionRefNo.
        For Fx Trade files, the source column is SourceColumns.TRADE_NAME. For Fx Option
        files, the source column is SourceColumns.FX_OPTION_FX_OPTION_NAME"""
        if self.file_type == FileTypes.FX_TRADE:
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.TRADE_NAME,
                    target_attribute=OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
                    end_index=52,
                ),
                auditor=self.auditor,
            )
        else:
            # self.file_type == FileTypes.FX_OPTION
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.FX_OPTION_FX_OPTION_NAME,
                    target_attribute=OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
                    end_index=52,
                ),
                auditor=self.auditor,
            )

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        pass

    def _price_forming_data_initial_quantity(self):
        """Returns a data frame containing priceFormingData.initialQuantity.
        For Fx Trade files, the source column is SourceColumns.AMOUNT_1. For Fx Option
        files, the source column is SourceColumns.STANDARD_PAIR_AMOUNT_1"""
        if self.file_type == FileTypes.FX_TRADE:
            return ConvertMinorToMajor.process(
                source_frame=self.source_frame,
                params=ParamsConvertMinorToMajor(
                    source_price_attribute=SourceColumns.AMOUNT_1,
                    source_ccy_attribute=SourceColumns.CURRENCY_1,
                    target_price_attribute=OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
                    cast_to="abs",
                ),
            )
        else:
            # self.file_type == FileTypes.FX_OPTION:
            return ConvertMinorToMajor.process(
                source_frame=pd.concat(
                    [self.source_frame, self.pre_process_df], axis=1
                ),
                params=ParamsConvertMinorToMajor(
                    source_price_attribute=SourceColumns.STANDARD_PAIR_AMOUNT_1,
                    source_ccy_attribute=TempColumns.FX_OPTIONS_CURRENCY_1,
                    target_price_attribute=OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
                    cast_to="abs",
                ),
            )

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price(self) -> pd.DataFrame:
        """Returns a data frame containing priceFormingData.price.
        For Fx Trade files, the source column is SourceColumns.RATE. For Fx Option
        files, the source column is SourceColumns.PREMIUM"""
        if self.file_type == FileTypes.FX_TRADE:
            return ConvertMinorToMajor.process(
                source_frame=self.source_frame,
                params=ParamsConvertMinorToMajor(
                    source_price_attribute=SourceColumns.RATE,
                    source_ccy_attribute=SourceColumns.CURRENCY_2,
                    target_price_attribute=add_prefix(
                        prefix=ModelPrefix.ORDER_STATE,
                        attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                    ),
                    cast_to="abs",
                ),
            )
        else:
            # self.file_type == FileTypes.FX_OPTION:
            return ConvertMinorToMajor.process(
                source_frame=self.source_frame,
                params=ParamsConvertMinorToMajor(
                    source_price_attribute=SourceColumns.PREMIUM,
                    source_ccy_attribute=SourceColumns.PREMIUM_CURRENCY,
                    target_price_attribute=add_prefix(
                        prefix=ModelPrefix.ORDER_STATE,
                        attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                    ),
                    cast_to="abs",
                ),
            )

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        pass

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        """Returns a data frame containing priceFormingData.tradedQuantity.
        It assumes that _price_forming_data_initial_quantity() has been called earlier"""
        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY]
            ],
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
                target_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                ),
            ),
            auditor=self.auditor,
        )

    def _price_forming_data_remaining_quantity(self):
        pass

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """Returns a data frame containing reportDetails.transactionRefNo.
        It assumes that _order_identifiers_transaction_ref_no() has been called earlier"""
        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO]
            ],
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
                target_attribute=OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO,
            ),
            auditor=self.auditor,
        )

    def _source_index(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceIndex column"""
        return pd.DataFrame(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_INDEX],
        )

    def _source_key(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceKey column from the SWARM_FILE_URL"""
        return pd.DataFrame(
            data=os.getenv("SWARM_FILE_URL"),
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_KEY],
        )

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        pass

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        pass

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        pass

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        pass

    def _timestamps_order_received(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.orderReceived.
        For Fx Trade files, the source column is SourceColumns.TRADE_EXECUTION_TIME. For
        Fx Fx Option files, the source column is SourceColumns.OPTION_EXECUTION_TIME"""
        if self.file_type == FileTypes.FX_TRADE:
            return ConvertDatetime.process(
                self.source_frame,
                params=ConvertDatetimeParams(
                    source_attribute=SourceColumns.TRADE_EXECUTION_TIME,
                    source_attribute_format="%Y-%m-%d %H:%M:%S",
                    target_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                    convert_to="datetime",
                ),
            )
        else:
            # self.file_type == FileTypes.FX_OPTION:
            return ConvertDatetime.process(
                self.source_frame,
                params=ConvertDatetimeParams(
                    source_attribute=SourceColumns.OPTION_EXECUTION_TIME,
                    source_attribute_format="%Y-%m-%d %H:%M:%S",
                    target_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                    convert_to="datetime",
                ),
            )

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.orderSubmitted.
        Assumes that _timestamps_order_received() has been called earlier"""
        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [OrderColumns.TIMESTAMPS_ORDER_RECEIVED]
            ],
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
            ),
            auditor=self.auditor,
        )

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.orderStatusUpdated.
        Assumes that _timestamps_order_received() has been called earlier"""
        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [OrderColumns.TIMESTAMPS_ORDER_RECEIVED]
            ],
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED,
            ),
            auditor=self.auditor,
        )

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.tradingDateTime.
        It assumes that _timestamps_order_received() has been called earlier"""
        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [OrderColumns.TIMESTAMPS_ORDER_RECEIVED]
            ],
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                target_attribute=OrderColumns.TIMESTAMPS_TRADING_DATE_TIME,
            ),
            auditor=self.auditor,
        )

    def _timestamps_validity_period(self):
        pass

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(self):
        pass

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(self):
        pass

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_short_selling_indicator(self):
        pass

    def _traders_algos_waivers_indicators_waiver_indicator(self) -> pd.DataFrame:
        pass

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        pass

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.buySellIndicator.
        Assumes that _execution_details_buy_sell_indicator() has been called earlier"""
        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR]
            ],
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_commission_amount(self):
        pass

    def _transaction_details_commission_amount_currency(self):
        pass

    def _transaction_details_commission_amount_type(self):
        pass

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.complexTradeComponentId.
        For Fx Trade files, pd.NA is returned.
        Fx Fx Option files, the source column is SourceColumns.OPTION_NAME"""
        if self.file_type == FileTypes.FX_OPTION:
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.OPTION_NAME,
                    target_attribute=OrderColumns.TRANSACTION_DETAILS_COMPLEX_TRADE_COMPONENT_ID,
                ),
                auditor=self.auditor,
            )
        else:
            return pd.DataFrame(
                data=pd.NA,
                index=self.source_frame.index,
                columns=[OrderColumns.TRANSACTION_DETAILS_COMPLEX_TRADE_COMPONENT_ID],
            )

    def _transaction_details_cross_indicator(self):
        pass

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        pass

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        pass

    def _transaction_details_net_amount(self):
        pass

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        pass

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        pass

    def _transaction_details_position_effect(self):
        pass

    def _transaction_details_position_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_price(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.price.
        Assumes that _price_forming_data_price() has been called earlier"""
        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :,
                [
                    add_prefix(
                        prefix=ModelPrefix.ORDER_STATE,
                        attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                    )
                ],
            ],
            params=ParamsMapAttribute(
                source_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                ),
                target_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.TRANSACTION_DETAILS_PRICE,
                ),
            ),
            auditor=self.auditor,
        )

    def _transaction_details_price_average(self):
        pass

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.priceCurrency.
        For Fx Trade files, the source column is SourceColumns.CURRENCY_2.
        For Fx Fx Option files, the source column is SourceColumns.PREMIUM_CURRENCY"""
        if self.file_type == FileTypes.FX_TRADE:
            return ConvertMinorToMajor.process(
                source_frame=self.source_frame,
                params=ParamsConvertMinorToMajor(
                    source_ccy_attribute=SourceColumns.CURRENCY_2,
                    target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                ),
            )
        else:
            # self.file_type == FileTypes.FX_OPTION:
            return ConvertMinorToMajor.process(
                source_frame=self.source_frame,
                params=ParamsConvertMinorToMajor(
                    source_ccy_attribute=SourceColumns.PREMIUM_CURRENCY,
                    target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                ),
            )

    def _transaction_details_price_notation(self):
        """Returns a data frame containing transactionDetails.priceNotation.
        This is populated with the static value 'MONE'"""
        return pd.DataFrame(
            data=PriceNotation.MONE.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION],
        )

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        pass

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        pass

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.quantity.
        Assumes that _price_forming_data_initial_quantity() has been called earlier"""

        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY]
            ],
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
                target_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY,
                ),
            ),
            auditor=self.auditor,
        )

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.quantityCurrency.
        For Fx Trade files, the source column is SourceColumns.CURRENCY_1.
        For Fx Fx Option files, the source column is SourceColumns.CURRENCY_PAIR"""
        if self.file_type == FileTypes.FX_TRADE:
            return ConvertMinorToMajor.process(
                source_frame=self.source_frame,
                params=ParamsConvertMinorToMajor(
                    source_ccy_attribute=SourceColumns.CURRENCY_1,
                    target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
                ),
            )
        else:
            # self.file_type == FileTypes.FX_OPTION:
            return ConvertMinorToMajor.process(
                source_frame=self.pre_process_df,
                params=ParamsConvertMinorToMajor(
                    source_ccy_attribute=TempColumns.FX_OPTIONS_CURRENCY_1,
                    target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
                ),
            )

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.quantityNotation.
        This is populated with the static value 'MONE'"""
        return pd.DataFrame(
            data=QuantityNotation.MONE.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION],
        )

    def _transaction_details_record_type(self):
        """Returns a data frame containing transactionDetails.recordType.
        This is populated with the static value 'Market Side'"""
        return pd.DataFrame(
            data=OrderRecordType.MARKET_SIDE.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE],
        )

    def _transaction_details_settlement_amount(self):
        pass

    def _transaction_details_settlement_amount_currency(self):
        pass

    def _transaction_details_settlement_date(self):
        pass

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        pass

    def _transaction_details_swap_directionalities(self):
        pass

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.tradingCapacity.
        This is populated with the static value 'AOTC'"""
        return pd.DataFrame(
            data=TradingCapacity.AOTC.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY],
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.tradingDateTime.
        Assumes that _timestamps_order_received() has been called earlier"""
        return MapAttribute.process(
            source_frame=self.target_df.loc[
                :, [OrderColumns.TIMESTAMPS_ORDER_RECEIVED]
            ],
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_trail_id(self):
        pass

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.ultimateVenue.
        This is populated with the static value 'XOFF'"""
        return pd.DataFrame(
            data="XOFF",
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE],
        )

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        pass

    def _transaction_details_upfront_payment_currency(self):
        pass

    def _transaction_details_venue(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.venue.
        This is populated with the static value 'XOFF'"""
        return pd.DataFrame(
            data="XOFF",
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_VENUE],
        )

    # The following functions are used to populate temporary columns
    def _get_asset_class(self):
        """Gets the asset class for an order based on SourceColumns.TYPE and
        SourceColumns.LEG_CLASSIFICATION for FxTrades. If the file is an Fx option
        file, the asset class is Fx Options"""
        if self.file_type == FileTypes.FX_OPTION:
            return pd.DataFrame(
                data=AssetClass.FX_OPTION,
                index=self.source_frame.index,
                columns=[TempColumns.ASSET_CLASS],
            )
        fx_fwd_or_ndf_cond = f"`{SourceColumns.TYPE}`.str.fullmatch('FXFORWARD|FXNDF', case=False, na=False)"
        fx_swap_cond = (
            f"(`{SourceColumns.TYPE}`.str.fullmatch('FXSWAP', case=False, na=False)"
        )
        fx_spot_cond = (
            f"`{SourceColumns.TYPE}`.str.fullmatch('FXSPOT', case=False, na=False)"
        )
        leg_class_forward_cond = f"`{SourceColumns.LEG_CLASSIFICATION}`.str.fullmatch('Forward', case=False, na=False))"
        cases = [
            {"query": fx_spot_cond, "value": AssetClass.FX_SPOT},
            {
                "query": f"{fx_fwd_or_ndf_cond} | ({fx_swap_cond} & {leg_class_forward_cond})",
                "value": AssetClass.FX_FORWARD,
            },
            {
                "query": f"{fx_swap_cond} & (~{leg_class_forward_cond})",
                "value": AssetClass.FX_FORWARD,
            },
        ]
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.ASSET_CLASS, cases=cases
            ),
        )

    def _get_option_type(self):
        """Gets the option type for an order"""
        if self.file_type == FileTypes.FX_OPTION:
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.OPTION_STRATEGY,
                    target_attribute=TempColumns.OPTION_TYPE,
                    end_index=1,
                ),
                auditor=self.auditor,
            )
        else:
            return pd.DataFrame(
                data=pd.NA,
                index=self.source_frame.index,
                columns=[TempColumns.OPTION_TYPE],
            )

    def _get_strike_price(self):
        """Gets the option strike price for an order from SourceColumns.STRIKE"""
        if self.file_type == FileTypes.FX_OPTION:
            return ConvertMinorToMajor.process(
                source_frame=self.source_frame,
                params=ParamsConvertMinorToMajor(
                    source_price_attribute=SourceColumns.STRIKE,
                    source_ccy_attribute=TempColumns.FX_OPTIONS_CURRENCY_1,
                    target_price_attribute=TempColumns.OPTION_STRIKE_PRICE,
                    cast_to="abs",
                ),
            )
        else:
            return pd.DataFrame(
                data=pd.NA,
                index=self.source_frame.index,
                columns=[TempColumns.OPTION_STRIKE_PRICE],
            )

    def _get_expiry_date(self):
        """Gets the expiry date for an order from SourceColumns.VALUE_DATE for Fx Trade files
        and SourceColumns.EXPIRATION_DATE for Fx Option files"""
        if self.file_type == FileTypes.FX_TRADE:
            return ConvertDatetime.process(
                self.source_frame,
                params=ConvertDatetimeParams(
                    source_attribute=SourceColumns.VALUE_DATE,
                    source_attribute_format="%d/%m/%Y",
                    target_attribute=TempColumns.EXPIRY_DATE,
                    convert_to="date",
                ),
            )
        else:
            # self.file_type == FileTypes.FX_OPTION:
            return ConvertDatetime.process(
                self.source_frame,
                params=ConvertDatetimeParams(
                    source_attribute=SourceColumns.EXPIRATION_DATE,
                    source_attribute_format="%d/%m/%Y",
                    target_attribute=TempColumns.EXPIRY_DATE,
                    convert_to="date",
                ),
            )

    def _get_notional_currency_2(self):
        """Gets the notional currency 2 value to be used in PartyIdentifiers
        For Fx Trade files, the source column is SourceColumns.CURRENCY_2
        For Fx Option files, the source column is SourceColumns.CURRENCY_PAIR"""
        if self.file_type == FileTypes.FX_TRADE:
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.CURRENCY_2,
                    target_attribute=TempColumns.NOTIONAL_CURRENCY_2,
                ),
                auditor=self.auditor,
            )
        else:
            # FX Option
            return MapAttribute.process(
                source_frame=self.pre_process_df.loc[
                    :, [TempColumns.FX_OPTIONS_CURRENCY_2]
                ],
                params=ParamsMapAttribute(
                    source_attribute=TempColumns.FX_OPTIONS_CURRENCY_2,
                    target_attribute=TempColumns.NOTIONAL_CURRENCY_2,
                ),
                auditor=self.auditor,
            )

    def _get_trader(self):
        """Gets the trader value to be used in PartyIdentifiers
        For Fx Trade files, the source column is SourceColumns.BROKER_BROKER_NAME
        For Fx Option files, the source column is SourceColumns.FX_OPTION_CREATED_BY"""
        if self.file_type == FileTypes.FX_TRADE:
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.BROKER_BROKER_NAME,
                    target_attribute=TempColumns.TRADER_ID,
                    prefix=PartyPrefix.ID,
                ),
                auditor=self.auditor,
            )
        else:
            return MapAttribute.process(
                source_frame=self.source_frame,
                params=ParamsMapAttribute(
                    source_attribute=SourceColumns.FX_OPTION_CREATED_BY,
                    target_attribute=TempColumns.TRADER_ID,
                    prefix=PartyPrefix.ID,
                ),
                auditor=self.auditor,
            )

    def _get_executing_entity(self):
        """Gets the executing entity value to be used in PartyIdentifiers from the
        AccountFirm record"""
        return GetTenantLEI.process(
            source_frame=self.source_frame,
            params=ParamsGetTenantLEI(
                target_column_prefix=PartyPrefix.LEI,
                target_lei_column=TempColumns.EXECUTING_ENTITY_WITH_LEI,
            ),
        )

    def _get_counterparty(self):
        """Gets the counterparty value to be used in PartyIdentifiers from SourceColumns.LEI"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceColumns.LEI,
                target_attribute=TempColumns.COUNTERPARTY_WITH_LEI,
                prefix=PartyPrefix.LEI,
            ),
            auditor=self.auditor,
        )

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        """Not implemented"""
