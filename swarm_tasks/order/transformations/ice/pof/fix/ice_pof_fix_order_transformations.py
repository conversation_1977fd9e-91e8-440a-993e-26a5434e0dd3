from typing import NoReturn

import pandas as pd
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_elastic_schema.static.mifid2 import MultiLegReportingType
from se_elastic_schema.static.mifid2 import OrderStatus
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order.static import OrderTempColumns
from se_trades_tasks.order_and_tr.fix.fix_parser_result_to_frame import StaticFields
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.order.transformations.ice.pof.fix.static import DATA_SOURCE
from swarm_tasks.order.transformations.ice.pof.fix.static import MARKET_SIDE
from swarm_tasks.order.transformations.ice.pof.fix.static import SourceColumns
from swarm_tasks.order.transformations.ice.pof.fix.static import TempColumns
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ParamsConcatAttributes,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ParamsConvertDatetime,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as ParamsMapAttribute
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as ParamsMapValue
from swarm_tasks.utilities.static import Delimiters


class IcePofFixOrderTransformations(AbstractOrderTransformations):
    def __init__(
        self,
        es_client,
        tenant: str,
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.es_client = es_client
        self.tenant: str = tenant

    def _pre_process(self) -> NoReturn:
        self.pre_process_df.loc[:, TempColumns.ORDER_ID] = ConcatAttributes.process(
            source_frame=self.source_frame,
            params=ParamsConcatAttributes(
                target_attribute=TempColumns.ORDER_ID,
                source_attributes=[
                    SourceColumns.CL_ORD_ID,
                    SourceColumns.SYMBOL,
                ],
                delimiter=Delimiters.PIPE,
            ),
        )
        self.pre_process_df.loc[:, TempColumns.BUY_SELL] = self._get_buy_sell()
        self.pre_process_df.loc[
            :, OrderTempColumns.NEWO_IN_FILE
        ] = self._get_newo_in_file()

    def process(self) -> pd.DataFrame:
        self.pre_process()
        self.buy_sell()
        self.data_source_name()
        self.date()
        self.execution_details_buy_sell_indicator()
        self.execution_details_order_status()
        self.execution_details_order_type()
        self.execution_details_outgoing_order_addl_info()
        self.execution_details_routing_strategy()
        self.execution_details_trading_capacity()
        self.execution_details_validity_period()
        self.hierarchy()
        self.id()
        self.is_synthetic()
        self.meta_model()
        self.order_class()
        self.order_identifiers_aggregated_order_id_code()
        self.order_identifiers_internal_order_id_code()
        self.order_identifiers_order_id_code()
        self.order_identifiers_trading_venue_transaction_id_code()
        self.order_identifiers_transaction_ref_no()
        self.price_forming_data_initial_quantity()
        self.price_forming_data_remaining_quantity()
        self.price_forming_data_traded_quantity()
        self.report_details_transaction_ref_no()
        self.source_index()
        self.source_key()
        self.timestamps_order_received()
        self.timestamps_order_status_updated()
        self.timestamps_order_submitted()
        self.timestamps_trading_date_time()
        self.transaction_details_buy_sell_indicator()
        self.transaction_details_complex_trade_component_id()
        self.transaction_details_position_effect()
        self.transaction_details_quantity()
        self.transaction_details_record_type()
        self.transaction_details_trading_capacity()
        self.transaction_details_trading_date_time()
        self.multi_leg_reporting_type()
        self.post_process()
        return self.target_df

    def _post_process(self) -> NoReturn:
        """Populates few temp cols which are required by downstream tasks"""
        self.target_df.loc[:, TempColumns.SYMBOL] = self.source_frame.loc[
            :, SourceColumns.SYMBOL
        ]
        self.target_df.loc[:, TempColumns.PRICE] = self.source_frame.loc[
            :, [SourceColumns.PRICE, SourceColumns.ORD_TYPE]
        ].apply(
            lambda x: x[SourceColumns.PRICE]
            if not pd.isna(x[SourceColumns.ORD_TYPE])
            and x[SourceColumns.ORD_TYPE] == "2"
            else pd.NA,
            axis=1,
        )
        self.target_df.loc[:, TempColumns.LAST_PX] = self.source_frame.loc[
            :, SourceColumns.LAST_PX
        ]
        self.target_df.loc[:, TempColumns.STOP_PX] = self.source_frame.loc[
            :, [SourceColumns.STOP_PX, SourceColumns.ORD_TYPE]
        ].apply(
            lambda x: x[SourceColumns.STOP_PX]
            if not pd.isna(x[SourceColumns.ORD_TYPE])
            and x[SourceColumns.ORD_TYPE] in ["3", "4"]
            else pd.NA,
            axis=1,
        )
        self.target_df.loc[:, OrderTempColumns.NEWO_IN_FILE] = self._get_newo_in_file()
        self.target_df.loc[:, OrderTempColumns.NEWO_IN_FILE] = self.pre_process_df.loc[
            :, OrderTempColumns.NEWO_IN_FILE
        ]

        # Party identifiers need to be called after multi-legs are split into
        # single-legs (done downstream) as we derive the buySell for each leg
        # of a multi-leg
        self.target_df = pd.concat(
            [self.target_df, self._get_party_identifiers_source_fields()], axis=1
        )

    def _buy_sell(self) -> pd.DataFrame:
        """Returns a data frame containing _order.buySell and _orderState.buySell.
        This is populated from SourceColumns.SIDE"""
        return pd.concat(
            [
                MapAttribute.process(
                    source_frame=self.pre_process_df,
                    params=ParamsMapAttribute(
                        source_attribute=TempColumns.BUY_SELL,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER, attribute=OrderColumns.BUY_SELL
                        ),
                    ),
                    auditor=self.auditor,
                ),
                MapAttribute.process(
                    source_frame=self.pre_process_df,
                    params=ParamsMapAttribute(
                        source_attribute=TempColumns.BUY_SELL,
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.BUY_SELL,
                        ),
                    ),
                    auditor=self.auditor,
                ),
            ],
            axis=1,
        )

    def _data_source_name(self) -> pd.DataFrame:
        """Returns a data frame containing dataSourceName.
        This is populated with the static value 'ICE POF Exchange'"""
        return pd.DataFrame(
            data=DATA_SOURCE,
            index=self.source_frame.index,
            columns=[OrderColumns.DATA_SOURCE_NAME],
        )

    def _date(self) -> pd.DataFrame:
        """Returns a data frame containing date as the target column"""
        return ConvertDatetime.process(
            self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.TRANSACT_TIME,
                target_attribute=OrderColumns.DATE,
                convert_to=ConvertTo.DATE,
            ),
        )

    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Populates and returns executionDetails.buySellIndicator column"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.BUY_SELL].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR],
        )

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_limit_price(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_order_status(self) -> pd.DataFrame:
        """Returns a data frame containing _order.executionDetails.orderStatus and
        _orderState.executionDetails.orderStatus"""
        order_execution_details_order_status = pd.DataFrame(
            data=OrderStatus.NEWO.value,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                )
            ],
        )
        cases = [
            {
                "query": "`ExecType`.astype('string').str.fullmatch('1', case=False)",
                "value": "PARF",
            },
            {
                "query": "`ExecType`.astype('string').str.fullmatch('2', case=False)",
                "value": "FILL",
            },
            {
                "query": "`ExecType`.astype('string').str.fullmatch('3', case=False)",
                "value": "DNFD",
            },
            {
                "query": "`ExecType`.astype('string').str.fullmatch('4', case=False)",
                "value": "CAME",
            },
            {
                "query": "`ExecType`.astype('string').str.fullmatch('5|D', case=False)",
                "value": "REME",
            },
            {
                "query": "`ExecType`.astype('string').str.fullmatch('6', case=False)",
                "value": "PNDC",
            },
            {
                "query": "`ExecType`.astype('string').str.fullmatch('7|9', case=False)",
                "value": "REMA",
            },
            {
                "query": "`ExecType`.astype('string').str.fullmatch('8', case=False)",
                "value": "REMO",
            },
            {
                "query": "`ExecType`.astype('string').str.fullmatch('C', case=False)",
                "value": "EXPI",
            },
            {
                "query": "`ExecType`.astype('string').str.fullmatch('H', case=False)",
                "value": "CAMO",
            },
            {
                "query": "`ExecType`.astype('string').str.fullmatch('L', case=False)",
                "value": "TRIG",
            },
        ]
        order_state_execution_details_order_status = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                ),
                cases=cases,
            ),
        )
        return pd.concat(
            [
                order_execution_details_order_status,
                order_state_execution_details_order_status,
            ],
            axis=1,
        )

    def _execution_details_order_type(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.orderType.
        This is populated with the static value mapping."""
        ord_type_value_map = {
            "1": "Market",
            "2": "Limit",
            "3": "Stop / Stop Loss",
            "4": "Stop Limit",
            "5": "Market On Close",
            "6": "With Or Without",
            "7": "Limit Or Better",
            "8": "Limit With Or Without",
            "9": "On Basis",
            "D": "Previously Quoted",
            "E": "Previously Indicated",
            "G": "Forex Swap",
            "I": "Funari",
            "J": "Market If Touched",
            "K": "Market With Left Over as Limit",
            "L": "Previous Fund Valuation Point",
            "M": "Next Fund Valuation Point",
            "P": "Pegged",
            "Q": "Counter-order selection",
        }
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.ORD_TYPE,
                target_attribute=OrderColumns.EXECUTION_DETAILS_ORDER_TYPE,
                case_insensitive=True,
                value_map=ord_type_value_map,
            ),
            auditor=self.auditor,
        )

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.outgoingOrderAddlInfo."""
        ff_9520 = "DeliveryStartDate:" + self.source_frame.loc[
            :, SourceColumns.FF_9520
        ].astype("string")
        ff_9521 = "DeliveryEndDate:" + self.source_frame.loc[
            :, SourceColumns.FF_9521
        ].astype("string")
        ff_9522 = "LocationCode:" + self.source_frame.loc[
            :, SourceColumns.FF_9522
        ].astype("string")
        ff_9523 = "MeterNumber:" + self.source_frame.loc[
            :, SourceColumns.FF_9523
        ].astype("string")
        ff_9524 = "LeadTime:" + self.source_frame.loc[:, SourceColumns.FF_9524].astype(
            "string"
        )
        ff_9525 = "ReasonCode:" + self.source_frame.loc[
            :, SourceColumns.FF_9525
        ].astype("string")

        additional_info_df = pd.concat(
            [
                self.source_frame.loc[
                    :,
                    [
                        SourceColumns.FF_439,
                        SourceColumns.FF_440,
                        SourceColumns.TRD_TYPE,
                        SourceColumns.TEXT,
                    ],
                ],
                ff_9520,
                ff_9521,
                ff_9522,
                ff_9523,
                ff_9524,
                ff_9525,
            ],
            axis=1,
        )

        return ConcatAttributes.process(
            source_frame=additional_info_df,
            params=ParamsConcatAttributes(
                target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
                source_attributes=[
                    SourceColumns.TRD_TYPE,
                    SourceColumns.TEXT,
                    SourceColumns.FF_439,
                    SourceColumns.FF_440,
                    SourceColumns.FF_9520,
                    SourceColumns.FF_9521,
                    SourceColumns.FF_9522,
                    SourceColumns.FF_9523,
                    SourceColumns.FF_9524,
                    SourceColumns.FF_9525,
                ],
                delimiter=Delimiters.PIPE,
            ),
        )

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.EXECUTION_DETAILS_ROUTING_STRATEGY"""
        return pd.DataFrame(
            data=self.source_frame.loc[
                :, SourceColumns.ON_BEHALF_OF_LOCATION_ID
            ].values,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_ROUTING_STRATEGY],
        )

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_stop_price(self) -> pd.DataFrame:
        """Not implemented"""

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.tradingCapacity"""
        value_map = {"0": "DEAL", "1": "MTCH", "2": "AOTC"}
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.FF_9701,
                target_attribute=OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY,
                value_map=value_map,
            ),
            auditor=self.auditor,
        )

    def _execution_details_validity_period(self) -> pd.DataFrame:
        """Returns a df with the col executionDetails.validityPeriod"""
        time_in_force_mappings = {
            "0": ["DAVY"],
            "1": ["GTCV"],
            "3": ["IOCV"],
            "4": ["FOKV"],
            "5": ["GTXV"],
            "6": ["GTDV"],
        }
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.TIME_IN_FORCE]
            .astype("str")
            .map(time_in_force_mappings)
            .values,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_VALIDITY_PERIOD],
        )

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        """Not implemented"""

    def _financing_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _hierarchy(self) -> pd.DataFrame:
        """Returns a data frame containing column hierarchy, value is hardcoded to Standalone"""
        return pd.DataFrame(
            index=self.target_df.index,
            data="Standalone",
            columns=[OrderColumns.HIERARCHY],
        )

    def _id(self) -> pd.DataFrame:
        """Returns a data frame containing _order.id and _orderState.id.
        This is populated from SourceColumns.CL_ORD_ID and SECURITY_ID"""
        return pd.concat(
            [
                pd.DataFrame(
                    data=self.pre_process_df.loc[:, TempColumns.ORDER_ID].values,
                    index=self.pre_process_df.index,
                    columns=[
                        add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.ID)
                    ],
                ),
                pd.DataFrame(
                    data=self.pre_process_df.loc[:, TempColumns.ORDER_ID].values,
                    index=self.pre_process_df.index,
                    columns=[
                        add_prefix(
                            prefix=ModelPrefix.ORDER_STATE, attribute=OrderColumns.ID
                        )
                    ],
                ),
            ],
            axis=1,
        )

    def _is_discretionary(self) -> pd.DataFrame:
        """Not Implemented"""

    def _is_iceberg(self):
        """Not Implemented"""

    def _is_repo(self):
        """Not Implemented"""

    def _is_synthetic(self):
        """Populates is_synthetic"""
        return pd.DataFrame(
            data=~self.pre_process_df.loc[:, OrderTempColumns.NEWO_IN_FILE]
            .astype(bool)
            .values,
            index=self.pre_process_df.index,
            columns=[
                add_prefix(
                    ModelPrefix.ORDER,
                    OrderColumns.IS_SYNTHETIC,
                )
            ],
        )

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        """Not Implemented"""

    def _market_identifiers(self) -> pd.DataFrame:
        """Populated in a downstream task after splitting multi-legs into
        single legs"""

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        """Not implemented"""

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """Populated in a downstream task after splitting multi-legs into
        single legs"""

    def _get_party_client(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=PartyPrefix.ID
            + self.source_frame.loc[:, SourceColumns.CLIENT_ID]
            .apply(
                lambda client_id: client_id[0]
                if isinstance(client_id, list)
                else client_id
            )
            .values,
            index=self.source_frame.index,
            columns=[TempColumns.CLIENT],
        )

    def _get_party_identifiers_source_fields(self):
        """Gets the fields to be used for PartyIdentifiers (with prefix).
        PartyIdentifiers is called downstream after splitting multi-legs into
        single legs in the FetchVDIAndSplitMultiLegTrades task. The reason for
        this is that multi-legs are split and the final multi-leg buySellIndicator
        is obtained from the Venue Direct instrument in this task.
        This buySellIndicator is then used in PartyIdentifiers to flip the buyer
        and seller in Sell trades.
        """
        buyer = pd.DataFrame(
            data=PartyPrefix.ID
            + self.source_frame.loc[:, SourceColumns.TARGET_SUB_ID].values,
            index=self.source_frame.index,
            columns=[TempColumns.BUYER],
        )
        seller = pd.DataFrame(
            data=PartyPrefix.ID
            + self.source_frame.loc[:, SourceColumns.SENDER_COMP_ID].values,
            index=self.source_frame.index,
            columns=[TempColumns.SELLER],
        )

        client = self._get_party_client()

        counterparty = pd.DataFrame(
            data=PartyPrefix.ID
            + self.source_frame.loc[:, SourceColumns.SENDER_COMP_ID].values,
            index=self.source_frame.index,
            columns=[TempColumns.COUNTERPARTY],
        )
        executing_entity = pd.DataFrame(
            data=PartyPrefix.ID
            + self.source_frame.loc[:, SourceColumns.TARGET_SUB_ID].values,
            index=self.source_frame.index,
            columns=[TempColumns.EXECUTING_ENTITY],
        )
        execution_within_firm = pd.DataFrame(
            data=PartyPrefix.ID
            + self.source_frame.loc[:, SourceColumns.FF_9705].values,
            index=self.source_frame.index,
            columns=[TempColumns.EXECUTION_WITHIN_FIRM],
        )
        investment_decision = pd.DataFrame(
            data=PartyPrefix.ID
            + self.source_frame.loc[:, SourceColumns.FF_9704].values,
            index=self.source_frame.index,
            columns=[TempColumns.INVESTMENT_DECISION],
        )
        trader = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.TRADER,
                cases=[
                    {
                        "query": f"`{SourceColumns.ON_BEHALF_OF_SUB_ID}`.notnull()",
                        "attribute": SourceColumns.ON_BEHALF_OF_SUB_ID,
                        "attribute_prefix": PartyPrefix.ID,
                    },
                    {
                        "query": f"`{SourceColumns.ON_BEHALF_OF_SUB_ID}`.isnull()",
                        "attribute": SourceColumns.FF_9139,
                        "attribute_prefix": PartyPrefix.ID,
                    },
                ],
            ),
        )
        parties_source_frame = pd.concat(
            [
                buyer,
                seller,
                client,
                counterparty,
                executing_entity,
                execution_within_firm,
                investment_decision,
                trader,
            ],
            axis=1,
        )
        return parties_source_frame

    def _meta_model(self) -> pd.DataFrame:
        """Returns a data frame containing _order.__meta_model__ and _orderState.__meta_model__.
        The columns are populated with the static values Order and OrderState respectively"""
        return pd.DataFrame(
            data=[["Order", "OrderState"]],
            index=self.source_frame.index,
            columns=[
                add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.META_MODEL),
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.META_MODEL,
                ),
            ],
        )

    def _order_class(self) -> pd.DataFrame:
        trd_type_value_map = {
            "0": "Regular Trade",
            "2": "ICE EFRP",
            "3": "ICEBLK",
            "4": "Basis Trade",
            "5": "Guaranteed Cross",
            "6": "Volatility Contingent Trade",
            "7": "Stock Contingent Trade",
            "9": "CCX EFP Trade",
            "A": "Other Clearing Venue",
            "D": "NN2EX",
            "E": "EFP Trade/Against Actual",
            "G": "EEX",
            "F": "EFS/EFP Contra Trade",
            "I": "EFM Trade",
            "J": "EFR Trade",
            "K": "Block Trade",
            "O": "NG EFP/EFS Trade",
            "Q": "EOO Trade",
            "S": "EFS Trade",
            "T": "Contra Trade",
            "U": "CPBLK",
            "V": "Bilateral Off-Exchange Trade",
            "Y": "Cross Contra Trade",
            "AA": "Asset Allocation",
        }
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.TRD_TYPE,
                target_attribute=OrderColumns.ORDER_CLASS,
                case_insensitive=True,
                value_map=trd_type_value_map,
            ),
            auditor=self.auditor,
        )

    def _order_identifiers_aggregated_order_id_code(self) -> pd.DataFrame:
        """Returns a data frame containing column orderIdentifiers.aggregatedOrderId"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.SECONDARY_EXEC_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID],
        )

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        """Not implemented"""

    def _order_identifiers_internal_order_id_code(self) -> pd.DataFrame:
        """Returns a dataframe with the col orderIdentifiers.internalOrderIdCode"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.ORDER_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE],
        )

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        """Returns a data frame containing orderIdentifiers.orderIdCode"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.ORDER_ID].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE],
        )

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        """Not implemented"""

    def _order_identifiers_parent_order_id(self) -> pd.DataFrame:
        """Not implemented"""

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        """Not implemented"""

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        """Not implemented"""

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        """Returns a dataframe with the col orderIdentifiers.tradingVenueTransactionIdCode"""
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.ORDER_ID].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_TRADING_VENUE_TRANSACTION_ID_CODE],
        )

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        """Returns a df with the col orderIdentifiers.transactionRefNo"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.EXEC_ID].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO],
        )

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        """Not implemented"""

    def _price_forming_data_initial_quantity(self) -> pd.DataFrame:
        """Returns a df with the col priceFormingData.initialQuantity"""
        cases = [
            {
                "query": f"`{SourceColumns.MULTI_LEG_REPORTING_TYPE}` == '2'",
                "attribute": SourceColumns.FF_9018,
            },
            {
                "query": f"`{SourceColumns.MULTI_LEG_REPORTING_TYPE}` != '2'",
                "attribute": SourceColumns.ORDER_QTY,
            },
        ]
        temp_df = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
                cases=cases,
            ),
        )
        return pd.to_numeric(
            temp_df.loc[:, OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY],
            errors="coerce",
        ).astype("Float64")

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        """Not implemented"""

    def _price_forming_data_price(self) -> pd.DataFrame:
        """Not implemented"""

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        """Not implemented"""

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        """Not implemented"""

    def _price_forming_data_remaining_quantity(self) -> pd.DataFrame:
        """Returns a df with the col priceFormingData.remainingQuantity"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.LEAVES_QTY].values,
            index=self.source_frame.index,
            columns=[OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY],
        )

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        """Returns a df with the col priceFormingData.tradedQuantity"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.LAST_QTY].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    ModelPrefix.ORDER_STATE,
                    OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                )
            ],
        )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """Returns a df with the col reportDetails.transactionRefNo"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.EXEC_ID].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO],
        )

    def _source_index(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceIndex column"""
        return pd.DataFrame(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_INDEX],
        )

    def _source_key(self) -> pd.DataFrame:
        """Returns a df with the col sourceKey"""
        return MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=StaticFields.FILE_URL,
                target_attribute=OrderColumns.SOURCE_KEY,
            ),
            auditor=self.auditor,
        )

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        """Not implemented"""

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        """Not implemented"""

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        """Not implemented"""

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        """Not implemented"""

    def _timestamps_order_received(self) -> pd.DataFrame:
        """Returns a df with col timestamps.orderReceived"""
        return ConvertDatetime.process(
            self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.TRANSACT_TIME,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                convert_to=ConvertTo.DATETIME,
            ),
        )

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        """Returns a df with col timestamps.orderStatusUpdated"""
        return ConvertDatetime.process(
            self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.TRANSACT_TIME,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED,
                convert_to=ConvertTo.DATETIME,
            ),
        )

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        """Returns a df with col timestamps.orderSubmitted"""
        return ConvertDatetime.process(
            self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.TRANSACT_TIME,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
                convert_to=ConvertTo.DATETIME,
            ),
        )

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        """Returns a df with col timestamps.tradingDateTime"""
        return pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.TIMESTAMPS_ORDER_RECEIVED].values,
            index=self.target_df.index,
            columns=[OrderColumns.TIMESTAMPS_TRADING_DATE_TIME],
        )

    def _timestamps_validity_period(self) -> pd.DataFrame:
        """Not implemented"""

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(
        self,
    ) -> pd.DataFrame:
        """Not implemented"""

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        """Not implemented"""

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        """Not implemented"""

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(
        self,
    ) -> pd.DataFrame:
        """Not implemented"""

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        """Not implemented"""

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        """Not implemented"""

    def _traders_algos_waivers_indicators_waiver_indicator(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.BUY_SELL].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR],
        )

    def _transaction_details_commission_amount(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_commission_amount_currency(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_commission_amount_type(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_COMPLEX_TRADE_COMPONENT_ID],
        )

    def _transaction_details_cross_indicator(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_position_effect(self) -> pd.DataFrame:
        """Returns a df with the col transactionDetails.positionEffect"""
        value_map = {"O": "Open", "C": "Close", "R": "Rolled", "F": "FIFO"}
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.POSITION_EFFECT,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_POSITION_EFFECT,
                value_map=value_map,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_position_id(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_price(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_price_average(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.AVG_PX],
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_AVERAGE],
        )

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """Returns a df with the col transactionDetails.quantity"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.LAST_QTY].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY],
        )

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_record_type(self) -> pd.DataFrame:
        """Returns a df with col transactionDetails.recordType with a static value
        'Market Side'"""
        return pd.DataFrame(
            data=MARKET_SIDE,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE,
                )
            ],
        )

    def _transaction_details_settlement_amount(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_settlement_amount_currency(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_settlement_date(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_swap_directionalities(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        """Returns a df with the col transactionDetails.tradingCapacity"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY],
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """Returns a df with the col transactionDetails.transactionDetailsTradingDateTime"""
        return MapAttribute.process(
            source_frame=self.target_df,
            params=ParamsMapAttribute(
                source_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME,
            ),
            auditor=self.auditor,
        )

    def _transaction_details_trail_id(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        """Not implemented"""

    def _transaction_details_venue(self) -> pd.DataFrame:
        """Not implemented"""

    def _get_buy_sell(self) -> pd.Series:
        """Populates a temp col __buy_sell__ to be used to populate other buy_sell fields"""
        value_map = {
            "1": "BUYI",
            "2": "SELL",
        }
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.SIDE,
                target_attribute=TempColumns.BUY_SELL,
                value_map=value_map,
            ),
            auditor=self.auditor,
        )[TempColumns.BUY_SELL]

    def _get_newo_in_file(self) -> pd.DataFrame:
        """Returns a df with col __newo_in_file__ in post-process"""
        cases = [
            {"query": "`ExecType` == '0'", "value": True},
            {"query": "`ExecType` != '0'", "value": False},
        ]
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderTempColumns.NEWO_IN_FILE, cases=cases
            ),
        )

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        """
        Populates multi-leg reporting type as per this ticket's comment:
        https://steeleye.atlassian.net/browse/ON-4794?focusedCommentId=190087
        """

        cases = [
            {
                "query": f"`{SourceColumns.MULTI_LEG_REPORTING_TYPE}` == '1'",
                "value": MultiLegReportingType.OUTRIGHT,
            },
            {
                "query": f"`{SourceColumns.MULTI_LEG_REPORTING_TYPE}` == '2'",
                "value": MultiLegReportingType.SINGLE_LEG_OF_MULTI_LEG,
            },
            {
                "query": f"`{SourceColumns.MULTI_LEG_REPORTING_TYPE}` == '3'",
                "value": MultiLegReportingType.MULTI_LEG_SECURITY,
            },
            {
                "query": f"`{SourceColumns.MULTI_LEG_REPORTING_TYPE}`.isnull() & `{SourceColumns.FF_9092}`.isnull()",
                "value": MultiLegReportingType.OUTRIGHT,
            },
            {
                "query": f"`{SourceColumns.MULTI_LEG_REPORTING_TYPE}`.isnull() & `{SourceColumns.FF_9092}`.notnull()",
                "value": MultiLegReportingType.MULTI_LEG_SECURITY,
            },
        ]
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.MULTI_LEG_REPORTING_TYPE,
                cases=cases,
            ),
        )
