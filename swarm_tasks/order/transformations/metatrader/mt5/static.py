import pandas as pd
from se_elastic_schema.static.mifid2 import BuySellIndicator

from swarm_tasks.utilities.task_utils import BaseColumns


class SourceOrderColumns:
    COMMENT = "Comment"
    CONTRACT_SIZE = "ContractSize"
    EXTERNAL_ID = "ExternalID"
    LOGIN = "Login"
    MODIFY_FLAGS = "ModifyFlags"
    OANDA_USER_ID = "OANDA_USER_ID"
    ORDER = "Order"
    POSITION_ID = "PositionID"
    PRICE_ORDER = "PriceOrder"
    SYMBOL = "Symbol"
    TIMESTAMP = "Timestamp"
    TIME_SETUP_MSC = "TimeSetupMsc"
    TYPE = "Type"
    TYPE_FILL = "TypeFill"
    VOLUME_INITIAL = "VolumeInitial"
    VOLUME_INITIAL_EXT = "VolumeInitialExt"


class SourceInstrumentColumns(BaseColumns):
    C_BASE_CCY = "c_base_ccy"
    C_CFI = "c_CFI"
    C_MT5_ID = "c_MT5_id"
    C_NAME = "c_name"
    C_PRODUCT = "c_product"
    C_QUOTED_CCY = "c_quoted_ccy"
    M_ASSET = "m_asset"
    M_COMMODITY_BASE = "m_commodity_base"
    M_COMMODITY_DETAILS = "m_commodity_details"
    M_ISIN = "m_ISIN"
    M_NAME = "m_name"
    M_PRICE_CURRENCY = "m_price_currency"
    M_TR_ASSET = "m_tr_asset"
    T_ID_PRIM_EXCH_MIC = "t_id_prim_exch_mic"
    T_ISIN = "t_isin"


class SourceDealsColumns:
    ACTION = "ACTION"
    COMMISSION = "COMMISSION"
    CONTRACT_SIZE = "CONTRACT_SIZE"
    DEAL = "DEAL"
    ENTRY = "ENTRY"
    EXTERNAL_ID = "EXTERNAL_ID"
    FAIR_VALUE_ADJUSTMENT = "FAIR_VALUE_ADJUSTMENT"
    GATEWAY = "GATEWAY"
    LOGIN = "LOGIN"
    OANDA_USER_ID = "OANDA_USER_ID"
    ORDER = "ORDER"
    POSITION_ID = "POSITION_ID"
    PRICE = "PRICE"
    PRICE_GATEWAY = "PRICE_GATEWAY"
    SYMBOL = "SYMBOL"
    TIME_MSC = "TIME_MSC"
    VOLUME = "VOLUME"
    VOLUME_EXT = "VOLUME_EXT"


class TempColumns:
    TEMP_ASSET_CLASS = "TEMP_ASSET_CLASS"
    TEMP_BUY_SELL_INDICATOR = "__TEMP_BUY_SELL_INDICATOR__"
    TEMP_DATE = "TEMP_DATE"
    TEMP_DELIMITER = "TEMP_DELIMITER"
    TEMP_INST_ASSET_CLASS = "__TEMP_INST_ASSET_CLASS__"
    TEMP_INST_ID_BESTEX_ASSET_CLASS_MAIN = "__TEMP_INST_ID_BESTEX_ASSET_CLASS_MAIN__"
    TEMP_INST_ID_BESTEX_ASSET_CLASS_SUB = "__TEMP_INST_ID_BESTEX_ASSET_CLASS_SUB__"
    TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_BASE_PRODUCT = (
        "__TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_BASE_PRODUCT__"
    )
    TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_FURTHER_SUB_PRODUCT = (
        "__TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_FURTHER_SUB_PRODUCT__"
    )
    TEMP_INST_ID_DELIVERY_TYPE = "__TEMP_INST_ID_DELIVERY_TYPE__"
    TEMP_INST_ID_EXT_EXCHANGE_SYMBOL = "__TEMP_INST_ID_EXT_EXCHANGE_SYMBOL__"
    TEMP_INST_ID_INSTRUMENT_CLASSIFICATION = (
        "__TEMP_INST_ID_INSTRUMENT_CLASSIFICATION__"
    )
    TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_ASSET_CLASS = (
        "__TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_ASSET_CLASS__"
    )
    TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_CONTRACT_TYPE = (
        "__TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_CONTRACT_TYPE__"
    )
    TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_PRODUCT_TYPE = (
        "__TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_PRODUCT_TYPE__"
    )
    TEMP_INST_ID_INSTRUMENT_FULL_NAME = "__TEMP_INST_ID_INSTRUMENT_FULL_NAME__"
    TEMP_INST_ID_INSTRUMENT_ID_CODE = "__TEMP_INST_ID_INSTRUMENT_ID_CODE__"
    TEMP_INST_ID_INSTRUMENT_ID_CODE_TYPE = "__TEMP_INST_ID_INSTRUMENT_ID_CODE_TYPE__"
    TEMP_INST_ID_INSTRUMENT_UNIQUE_ID = "__TEMP_INST_ID_INSTRUMENT_UNIQUE_ID__"
    TEMP_INST_ID_PRICE_MULTIPLIER = "__TEMP_INST_ID_PRICE_MULTIPLIER__"
    TEMP_INST_ID_PRICE_NOTATION = "__TEMP_INST_ID_PRICE_NOTATION__"
    TEMP_INST_ID_QUANTITY_NOTATION = "__TEMP_INST_ID_QUANTITY_NOTATION__"
    TEMP_INST_ID_SYMBOL = "__TEMP_INST_ID_SYMBOL__"
    TEMP_INST_ID_UNDERLYING_INDEX_NAME = "__TEMP_INST_ID_UNDERLYING_INDEX_NAME__"
    TEMP_INST_ID_UNDERLYING_INSTRUMENT_ID = "__TEMP_INST_ID_UNDERLYING_INSTRUMENT_ID__"
    TEMP_INST_UNIQUE_ID_CASH_EQUITY = "__TEMP_INST_UNIQUE_ID_CASH_EQUITY__"
    TEMP_INST_UNIQUE_ID_ISIN_NOT_POPULATED = (
        "__TEMP_INST_UNIQUE_ID_ISIN_NOT_POPULATED__"
    )
    TEMP_INST_UNIQUE_ID_ISIN_POPULATED = "__TEMP_INST_UNIQUE_ID_ISIN_POPULATED__"
    TEMP_INST_UNIQUE_ID_M_ISIN_POPULATED = "__TEMP_INST_UNIQUE_ID_M_ISIN_POPULATED__"
    TEMP_INST_UNIQUE_ID_T_ISIN_POPULATED = "__TEMP_INST_UNIQUE_ID_T_ISIN_POPULATED__"
    TEMP_IS_CREATED_THROUGH_FALLBACK = "__TEMP_IS_CREATED_THROUGH_FALLBACK__"
    TEMP_OANDA_EXEC_ENTITY = "TEMP_OANDA_EXEC_ENTITY"
    TEMP_ONE_FINANCIAL_EXEC_ENTITY = "TEMP_ONE_FINANCIAL_EXEC_ENTITY"
    TEMP_ORDER_ID = "TEMP_ORDER_ID"
    TEMP_ORDER_STATUS = "TEMP_ORDER_STATUS"
    TEMP_PARENT_META_MODEL = "__TEMP_PARENT_META_MODEL__"
    TEMP_QTY = "TEMP_QTY"
    TEMP_SOURCE_TIMESTAMP = "TEMP_SOURCE_TIMESTAMP"
    TEMP_STATIC_TEXT_ACTION = "TEMP_ACTION"
    TEMP_STATIC_TEXT_LOGIN = "TEMP_LOGIN"
    TEMP_STATIC_TEXT_MODIFY_FLAGS = "TEMP_STATIC_TEXT_MODIFY_FLAGS"
    TEMP_STATIC_TEXT_MT5_SYMBOL = "TEMP_MT5_SYMBOL"
    TEMP_TENANT = "TEMP_TENANT"
    TEMP_TIME_SETUP_MSC = "TEMP_TIME_SETUP_MSC"
    TEMP_TIMESTAMP = "TEMP_TIMESTAMP"
    TEMP_XXXX_VENUE = "TEMP_XXXX_VENUE"


class TempPartyIDs:
    BUYER = "__BUYER__"
    BUYER_DECISION_MAKER = "__BUYER_DECISION_MAKER__"
    CLIENT = "__CLIENT__"
    COUNTERPARTY = "__COUNTERPARTY__"
    EXECUTING_ENTITY = "__EXECUTING_ENTITY__"
    EXECUTION_WITHIN_FIRM = "__EXECUTION_WITHIN_FIRM__"
    INVESTMENT_DEC_WITHIN_FIRM = "__INVESTMENT_DEC_WITHIN_FIRM__"
    SELLER = "__SELLER__"
    SELLER_DECISION_MAKER = "__SELLER_DECISION_MAKER__"
    TRADER = "__TRADER__"


class AssetClassIdentifier:
    FX = "FX"


ONE_FINANCIAL = "onefinancial"

query_m_asset_fx_currency = "`m_asset`.str.lower().isin(['fx', 'currency'])"
query_m_asset_not_fx_currency = "~(`m_asset`.str.lower().isin(['fx', 'currency']))"


def calculate_qty(
    df: pd.DataFrame, qty_attribute: str, volume_attribute: str
) -> pd.Series:
    """
    :param df: pd.DataFrame
    :param qty_attribute: str
    :param volume_attribute: str
    :return: pd.Series
    """
    result = pd.Series(data=pd.NA, index=df.index)
    required_fields_not_na_mask = (
        df[qty_attribute].notnull()
        & df[volume_attribute].notnull()
        & df[volume_attribute]
        != 0
    )
    result.loc[required_fields_not_na_mask] = (
        df.loc[required_fields_not_na_mask, volume_attribute]
        / df.loc[required_fields_not_na_mask, qty_attribute]
    )
    return result


def populate_buyer_seller_decision_maker(df: pd.DataFrame) -> pd.DataFrame:
    """
    :param df: pd.DataFrame
    :return: pd.DataFrame
    Adds TempPartyIDs.BUYER_DECISION_MAKER and  TempPartyIDs.SELLER_DECISION_MAKER
    fields to the input dataframe. Logic is as below:
    if buy:
        SELLER_DECISION_MAKER = EXECUTING_ENTITY
        BUYER_DECISION_MAKER = NA(Do not populate)
    else:
        BUYER_DECISION_MAKER = EXECUTING_ENTITY
        SELLER_DECISION_MAKER = NA(Do not populate)
    """
    buy_mask = df[TempColumns.TEMP_BUY_SELL_INDICATOR] == BuySellIndicator.BUYI.value
    if TempPartyIDs.EXECUTING_ENTITY not in df.columns:
        df[TempPartyIDs.EXECUTING_ENTITY] = pd.NA

    if buy_mask.any():
        df.loc[buy_mask, TempPartyIDs.SELLER_DECISION_MAKER] = df.loc[
            buy_mask, TempPartyIDs.EXECUTING_ENTITY
        ]

    if (~buy_mask).any():
        df.loc[~buy_mask, TempPartyIDs.BUYER_DECISION_MAKER] = df.loc[
            ~buy_mask, TempPartyIDs.EXECUTING_ENTITY
        ]
    return df
