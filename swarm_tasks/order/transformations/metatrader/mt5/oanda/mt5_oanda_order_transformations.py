import logging
import os

import pandas as pd
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.order.transformations.metatrader.mt5.mt5_order_transformations import (
    MT5OrderTransformations,
)
from swarm_tasks.order.transformations.metatrader.mt5.static import calculate_qty
from swarm_tasks.order.transformations.metatrader.mt5.static import SourceOrderColumns
from swarm_tasks.order.transformations.metatrader.mt5.static import TempPartyIDs
from swarm_tasks.utilities.static import Delimiters


logger_ = logging.getLogger(__name__)


class MT5OandaOrderTransformations(MT5OrderTransformations):
    def _price_forming_data_initial_quantity(self) -> None:
        """
        :return: None
        Populates PRICE_FORMING_DATA_INITIAL_QUANTITY
        """
        ten_thousand = "__ten_thousand__"
        self.source_frame.loc[:, ten_thousand] = 10000
        self.target_df.loc[
            :, OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY
        ] = calculate_qty(
            df=self.source_frame,
            qty_attribute=ten_thousand,
            volume_attribute=SourceOrderColumns.VOLUME_INITIAL,
        )

    def populate_party_identifiers(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        :param df: pd.Dataframe
        :return: pd.Dataframe
        Overrides populate_party_identifiers() method from
        MT5OrderTransformations to add EXECUTING_ENTITY
        """
        df.loc[:, TempPartyIDs.CLIENT] = (
            PartyPrefix.ID + df.loc[:, SourceOrderColumns.OANDA_USER_ID]
        )
        exec_entity = self.executing_entity()
        df.loc[:, TempPartyIDs.EXECUTING_ENTITY] = f"{PartyPrefix.ID}{exec_entity}"
        return df

    @staticmethod
    def executing_entity() -> str:
        """
        :return: str
        Take the first subfield of the input file name delimieted by '-'
        (i.e., mt5-ogm-deals-2022-02-14.csv will be “ogm”)
        """
        source_file_name = os.getenv("SWARM_FILE_URL").split("/")[-1]
        first_name_sub_field = source_file_name.split("-")
        if len(first_name_sub_field) > 2:
            return first_name_sub_field[1]
        else:
            logger_.warning(
                f"Unable to fetch executing_entity from file_name. "
                f"Filename:{source_file_name}"
            )
        return ""

    def _execution_details_outgoing_order_addl_info(self) -> None:
        """
        :return: None
        Populates EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO
        """
        remove_regex = ""
        static_strings_list = [
            "Modify Flags - ",
            "Action - ",
            "Login - ",
            "Internal Client ID - ",
            "mt5_symbol - ",
        ]
        for static_string in static_strings_list:
            remove_regex += static_string + r"\|\?|"

        self.target_df.loc[
            :, OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO
        ] = (
            (
                "Modify Flags - "
                + self.source_frame.loc[:, SourceOrderColumns.MODIFY_FLAGS].fillna("")
                + "|?"
                + "Action - "
                + self.source_frame.loc[:, SourceOrderColumns.COMMENT].fillna("")
                + "|?"
                + "Login - "
                + self.source_frame.loc[:, SourceOrderColumns.LOGIN].fillna("")
                + "|?"
                + "Internal Client ID - "
                + self.source_frame.loc[:, SourceOrderColumns.OANDA_USER_ID].fillna("")
                + "|?"
                + "mt5_symbol - "
                + self.source_frame.loc[:, SourceOrderColumns.SYMBOL].fillna("")
                + "|?"
            )
            .str.replace(remove_regex.rstrip("|"), "")
            .str.replace(r"\|\?", Delimiters.COMMA_SPACE)
            .str.rstrip(Delimiters.COMMA_SPACE)
            .replace("", pd.NA)
        )
