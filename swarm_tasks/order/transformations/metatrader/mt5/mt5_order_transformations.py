import logging
import os

import pandas as pd
from prefect.engine.signals import SKIP
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_attribute import CastTo
from se_core_tasks.map.map_conditional import Case
from se_elastic_schema.components.market.identifier import Identifier
from se_elastic_schema.static.market import IdentifierType
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import DeliveryType
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_elastic_schema.static.reference import InstrumentIdCodeType
from se_elastic_schema.static.reference import OrderRecordType
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order.static import OrderType
from se_trades_tasks.order.static import Venue
from se_trades_tasks.order_and_tr.instrument.identifiers.identifiers import (
    InstrumentIdentifiers,
)
from se_trades_tasks.order_and_tr.static import (
    INSTRUMENT_PATH,
)
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ParamsConvertMinorToMajor,
)
from swarm_tasks.io.read.aws.df_from_s3_csv import DfFromS3Csv
from swarm_tasks.io.read.aws.df_from_s3_csv import Params as ParamsDfFromS3Csv
from swarm_tasks.order.transformations.metatrader.mt5.static import AssetClassIdentifier
from swarm_tasks.order.transformations.metatrader.mt5.static import calculate_qty
from swarm_tasks.order.transformations.metatrader.mt5.static import (
    populate_buyer_seller_decision_maker,
)
from swarm_tasks.order.transformations.metatrader.mt5.static import (
    query_m_asset_fx_currency,
)
from swarm_tasks.order.transformations.metatrader.mt5.static import (
    query_m_asset_not_fx_currency,
)
from swarm_tasks.order.transformations.metatrader.mt5.static import (
    SourceInstrumentColumns,
)
from swarm_tasks.order.transformations.metatrader.mt5.static import SourceOrderColumns
from swarm_tasks.order.transformations.metatrader.mt5.static import TempColumns
from swarm_tasks.order.transformations.metatrader.mt5.static import TempPartyIDs
from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI
from swarm_tasks.steeleye.generic.get_tenant_lei import Params as ParamsGetTenantLEI
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ParamsConcatAttributes,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ParamsConvertDatetime,
)
from swarm_tasks.transform.frame.get_rows_by_condition import GetRowsByCondition
from swarm_tasks.transform.frame.get_rows_by_condition import (
    Params as ParamsGetRowsByCondition,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as ParamsMapAttribute
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.transform.map.map_static import MapStatic
from swarm_tasks.transform.map.map_static import Params as ParamsMapStatic
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as ParamsMapValue
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as ParamsMergeMarketIdentifers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)
from swarm_tasks.utilities.static import Delimiters
from swarm_tasks.utilities.static import MetaModel
from swarm_tasks.utilities.static import SWARM_FILE_URL

DATA_SOURCE_NAME = "MetaTrader5 - Orders"
logger = logging.getLogger(__name__)

logger_ = logging.getLogger(__name__)


class MT5OrderTransformations(AbstractOrderTransformations):
    def _pre_process(self):

        # fetch instrument data
        self._fetch_instrument_data_from_s3()

        # create additional rows for orderStates
        self._add_rows_for_order_states()

        # populate columns for pre_process_df
        self.pre_process_df.loc[:, TempColumns.TEMP_DATE] = ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceOrderColumns.TIME_SETUP_MSC,
                target_attribute=TempColumns.TEMP_TIME_SETUP_MSC,
                convert_to=ConvertTo.DATE.value,
            ),
        )[TempColumns.TEMP_TIME_SETUP_MSC]

        self.pre_process_df.loc[
            :, TempColumns.TEMP_TIME_SETUP_MSC
        ] = ConvertDatetime.process(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceOrderColumns.TIME_SETUP_MSC,
                target_attribute=TempColumns.TEMP_TIME_SETUP_MSC,
                convert_to=ConvertTo.DATETIME.value,
            ),
        )[
            TempColumns.TEMP_TIME_SETUP_MSC
        ]

        self.pre_process_df.loc[
            :, TempColumns.TEMP_ORDER_ID
        ] = ConcatAttributes.process(
            source_frame=self.source_frame,
            params=ParamsConcatAttributes(
                source_attributes=[
                    SourceOrderColumns.POSITION_ID,
                    SourceOrderColumns.ORDER,
                    SourceOrderColumns.OANDA_USER_ID,
                ],
                target_attribute=TempColumns.TEMP_ORDER_ID,
                delimiter=Delimiters.PIPE,
            ),
        )[
            TempColumns.TEMP_ORDER_ID
        ]

        self.pre_process_df.loc[
            :, TempColumns.TEMP_STATIC_TEXT_MODIFY_FLAGS
        ] = MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_attribute=TempColumns.TEMP_STATIC_TEXT_MODIFY_FLAGS,
                target_value="Modify Flags - ",
            ),
        )[
            TempColumns.TEMP_STATIC_TEXT_MODIFY_FLAGS
        ]

        self.pre_process_df.loc[
            :, TempColumns.TEMP_STATIC_TEXT_ACTION
        ] = MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_attribute=TempColumns.TEMP_STATIC_TEXT_ACTION,
                target_value="Action - ",
            ),
        )[
            TempColumns.TEMP_STATIC_TEXT_ACTION
        ]

        self.pre_process_df.loc[
            :, TempColumns.TEMP_STATIC_TEXT_LOGIN
        ] = MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_attribute=TempColumns.TEMP_STATIC_TEXT_LOGIN,
                target_value="Login - ",
            ),
        )[
            TempColumns.TEMP_STATIC_TEXT_LOGIN
        ]

        self.pre_process_df.loc[
            :, TempColumns.TEMP_STATIC_TEXT_MT5_SYMBOL
        ] = MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_attribute=TempColumns.TEMP_STATIC_TEXT_MT5_SYMBOL,
                target_value="mt5_symbol - ",
            ),
        )[
            TempColumns.TEMP_STATIC_TEXT_MT5_SYMBOL
        ]

        self.pre_process_df.loc[:, TempColumns.TEMP_DELIMITER] = MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_attribute=TempColumns.TEMP_DELIMITER,
                target_value="|?",
            ),
        )[TempColumns.TEMP_DELIMITER]

        self.pre_process_df.loc[
            :, TempColumns.TEMP_BUY_SELL_INDICATOR
        ] = MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceOrderColumns.TYPE,
                target_attribute=TempColumns.TEMP_BUY_SELL_INDICATOR,
                case_insensitive=True,
                value_map={
                    "0": BuySellIndicator.BUYI.value,
                    "1": BuySellIndicator.SELL.value,
                    "2": BuySellIndicator.BUYI.value,
                    "3": BuySellIndicator.SELL.value,
                    "4": BuySellIndicator.BUYI.value,
                    "5": BuySellIndicator.SELL.value,
                    "6": BuySellIndicator.BUYI.value,
                    "7": BuySellIndicator.SELL.value,
                    "8": BuySellIndicator.BUYI.value,
                },
            ),
            auditor=self.auditor,
        )[
            TempColumns.TEMP_BUY_SELL_INDICATOR
        ]

        self.pre_process_df.loc[:, SourceOrderColumns.LOGIN] = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceOrderColumns.LOGIN,
                target_attribute=SourceOrderColumns.LOGIN,
            ),
            auditor=self.auditor,
        )[SourceOrderColumns.LOGIN]

        self.pre_process_df.loc[
            :, SourceOrderColumns.OANDA_USER_ID
        ] = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceOrderColumns.OANDA_USER_ID,
                target_attribute=SourceOrderColumns.OANDA_USER_ID,
            ),
            auditor=self.auditor,
        )[
            SourceOrderColumns.OANDA_USER_ID
        ]

        # add party identifier fields to pre_process_df
        self.pre_process_df = self.populate_party_identifiers(df=self.pre_process_df)

        self.pre_process_df.loc[
            :, TempColumns.TEMP_INST_ASSET_CLASS
        ] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.TEMP_INST_ASSET_CLASS,
                cases=[
                    Case(
                        query="`c_product`.str.fullmatch('cfd', case=False, na=False)",
                        value="CFD",
                    ),
                    Case(
                        query="`c_product`.str.fullmatch('spreadbet', case=False, na=False)",
                        value="SB",
                    ),
                ],
            ),
        )[
            TempColumns.TEMP_INST_ASSET_CLASS
        ]

        self.pre_process_df.loc[
            :, TempColumns.TEMP_INST_ID_INSTRUMENT_FULL_NAME
        ] = ConcatAttributes.process(
            source_frame=self.source_frame,
            params=ParamsConcatAttributes(
                source_attributes=[
                    SourceInstrumentColumns.C_NAME,
                    SourceInstrumentColumns.M_ASSET,
                    SourceInstrumentColumns.C_PRODUCT,
                ],
                delimiter="-",
                target_attribute=TempColumns.TEMP_INST_ID_INSTRUMENT_FULL_NAME,
            ),
        )[
            TempColumns.TEMP_INST_ID_INSTRUMENT_FULL_NAME
        ]

    def _post_process(self):
        for col in [
            TempPartyIDs.EXECUTING_ENTITY,
            TempPartyIDs.CLIENT,
            TempPartyIDs.INVESTMENT_DEC_WITHIN_FIRM,
        ]:
            if col not in self.pre_process_df:
                self.pre_process_df.loc[:, col] = pd.NA

            self.target_df.loc[:, col] = self.pre_process_df.loc[:, col]

        # Temp colum used in the ConcatAttributes Task downstream
        self.target_df.loc[:, TempColumns.TEMP_XXXX_VENUE] = Venue.XXXX

        self.target_df.loc[
            :, TempColumns.TEMP_BUY_SELL_INDICATOR
        ] = self.pre_process_df.loc[:, TempColumns.TEMP_BUY_SELL_INDICATOR]

        # fields for instrument identifiers downstream
        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_SYMBOL
        ] = InstrumentIdentifiers.format_symbol(
            df=self.source_frame,
            src_col=SourceOrderColumns.SYMBOL,
            dest_col=TempColumns.TEMP_INST_ID_SYMBOL,
        )

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_CONTRACT_TYPE
        ] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_CONTRACT_TYPE,
                cases=[
                    Case(
                        query="`c_product`.str.fullmatch('cfd', case=False, na=False)",
                        value="CD",
                    ),
                    Case(
                        query="~(`c_product`.str.fullmatch('cfd', case=False, na=False))",
                        value="OT",
                    ),
                ],
            ),
        )[
            TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_CONTRACT_TYPE
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_PRODUCT_TYPE
        ] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_PRODUCT_TYPE,
                cases=[
                    Case(
                        query="`c_product`.str.fullmatch('cfd', case=False, na=False)",
                        value="CFD",
                    )
                ],
            ),
        )[
            TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_PRODUCT_TYPE
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_ASSET_CLASS
        ] = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceInstrumentColumns.M_TR_ASSET,
                target_attribute=TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_ASSET_CLASS,
                end_index=2,
            ),
            auditor=self.auditor,
        )[
            TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_ASSET_CLASS
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_BASE_PRODUCT
        ] = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceInstrumentColumns.M_COMMODITY_BASE,
                target_attribute=TempColumns.TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_BASE_PRODUCT,
            ),
            auditor=self.auditor,
        )[
            TempColumns.TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_BASE_PRODUCT
        ]

        self.target_df.loc[
            :,
            TempColumns.TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_FURTHER_SUB_PRODUCT,
        ] = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceInstrumentColumns.M_COMMODITY_DETAILS,
                target_attribute=TempColumns.TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_FURTHER_SUB_PRODUCT,
            ),
            auditor=self.auditor,
        )[
            TempColumns.TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_FURTHER_SUB_PRODUCT
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_DELIVERY_TYPE
        ] = MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_attribute=TempColumns.TEMP_INST_ID_DELIVERY_TYPE,
                target_value=DeliveryType.CASH.value,
            ),
        )[
            TempColumns.TEMP_INST_ID_DELIVERY_TYPE
        ]

        self.target_df.loc[:, TempColumns.TEMP_INST_ID_PRICE_MULTIPLIER] = 1

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_EXT_EXCHANGE_SYMBOL
        ] = self.source_frame.loc[:, SourceInstrumentColumns.M_NAME]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_INSTRUMENT_ID_CODE_TYPE
        ] = MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_attribute=TempColumns.TEMP_INST_ID_INSTRUMENT_ID_CODE_TYPE,
                target_value=InstrumentIdCodeType.OTHR.value,
            ),
        )[
            TempColumns.TEMP_INST_ID_INSTRUMENT_ID_CODE_TYPE
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_PRICE_NOTATION
        ] = MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_attribute=TempColumns.TEMP_INST_ID_PRICE_NOTATION,
                target_value=PriceNotation.MONE.value,
            ),
        )[
            TempColumns.TEMP_INST_ID_PRICE_NOTATION
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION
        ] = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceInstrumentColumns.C_CFI,
                target_attribute=TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION,
            ),
            auditor=self.auditor,
        )[
            TempColumns.TEMP_INST_ID_INSTRUMENT_CLASSIFICATION
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_INSTRUMENT_FULL_NAME
        ] = self.pre_process_df.loc[:, TempColumns.TEMP_INST_ID_INSTRUMENT_FULL_NAME]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_UNDERLYING_INSTRUMENT_ID
        ] = MapConditional.process(
            source_frame=pd.concat([self.source_frame, self.target_df], axis=1),
            params=ParamsMapConditional(
                target_attribute=TempColumns.TEMP_INST_ID_UNDERLYING_INSTRUMENT_ID,
                cases=[
                    Case(
                        query="`m_ISIN`.str.len() == 12",
                        attribute=SourceInstrumentColumns.M_ISIN,
                    ),
                    Case(
                        query="`t_isin`.str.len() == 12",
                        attribute=SourceInstrumentColumns.T_ISIN,
                    ),
                ],
            ),
        )[
            TempColumns.TEMP_INST_ID_UNDERLYING_INSTRUMENT_ID
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_UNDERLYING_INDEX_NAME
        ] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.TEMP_INST_ID_UNDERLYING_INDEX_NAME,
                cases=[
                    Case(
                        query="`c_CFI`.str.upper().str.startswith('JEI', na=False)",
                        attribute=SourceOrderColumns.SYMBOL,
                    )
                ],
            ),
        )[
            TempColumns.TEMP_INST_ID_UNDERLYING_INDEX_NAME
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ASSET_CLASS
        ] = self.pre_process_df.loc[:, TempColumns.TEMP_INST_ASSET_CLASS]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_UNIQUE_ID_M_ISIN_POPULATED
        ] = ConcatAttributes.process(
            source_frame=pd.concat([self.target_df, self.source_frame], axis=1),
            params=ParamsConcatAttributes(
                source_attributes=[
                    TempColumns.TEMP_XXXX_VENUE,
                    SourceInstrumentColumns.M_ISIN,
                    OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                    TempColumns.TEMP_INST_ASSET_CLASS,
                ],
                target_attribute=TempColumns.TEMP_INST_UNIQUE_ID_M_ISIN_POPULATED,
            ),
        )[
            TempColumns.TEMP_INST_UNIQUE_ID_M_ISIN_POPULATED
        ]

        self.target_df.loc[:, TempColumns.TEMP_ASSET_CLASS] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.TEMP_ASSET_CLASS,
                cases=[
                    Case(
                        query=query_m_asset_fx_currency,
                        value=AssetClassIdentifier.FX,
                    ),
                    Case(query=query_m_asset_not_fx_currency, value=""),
                ],
            ),
        )[TempColumns.TEMP_ASSET_CLASS]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_UNIQUE_ID_T_ISIN_POPULATED
        ] = ConcatAttributes.process(
            source_frame=pd.concat([self.target_df, self.source_frame], axis=1),
            params=ParamsConcatAttributes(
                source_attributes=[
                    TempColumns.TEMP_XXXX_VENUE,
                    SourceInstrumentColumns.T_ISIN,
                    OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                    TempColumns.TEMP_ASSET_CLASS,
                    TempColumns.TEMP_INST_ASSET_CLASS,
                ],
                target_attribute=TempColumns.TEMP_INST_UNIQUE_ID_T_ISIN_POPULATED,
            ),
        )[
            TempColumns.TEMP_INST_UNIQUE_ID_T_ISIN_POPULATED
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_UNIQUE_ID_ISIN_NOT_POPULATED
        ] = ConcatAttributes.process(
            source_frame=self.target_df,
            params=ParamsConcatAttributes(
                source_attributes=[
                    TempColumns.TEMP_XXXX_VENUE,
                    TempColumns.TEMP_INST_ID_SYMBOL,
                    TempColumns.TEMP_ASSET_CLASS,
                    TempColumns.TEMP_INST_ASSET_CLASS,
                ],
                target_attribute=TempColumns.TEMP_INST_UNIQUE_ID_ISIN_NOT_POPULATED,
            ),
        )[
            TempColumns.TEMP_INST_UNIQUE_ID_ISIN_NOT_POPULATED
        ]

        # Temp column for Cash Equity instruments
        self.target_df.loc[
            :, TempColumns.TEMP_INST_UNIQUE_ID_CASH_EQUITY
        ] = ConcatAttributes.process(
            source_frame=pd.concat(
                [
                    self.source_frame.loc[:, SourceInstrumentColumns.T_ISIN],
                    self.target_df.loc[
                        :,
                        [
                            OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                            OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                        ],
                    ],
                ],
                axis=1,
            ),
            params=ParamsConcatAttributes(
                source_attributes=[
                    SourceInstrumentColumns.T_ISIN,
                    OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                    OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                ],
                target_attribute=TempColumns.TEMP_INST_UNIQUE_ID_CASH_EQUITY,
            ),
        )[
            TempColumns.TEMP_INST_UNIQUE_ID_CASH_EQUITY
        ]

        self.target_df.loc[
            :, TempColumns.TEMP_INST_ID_INSTRUMENT_UNIQUE_ID
        ] = MapConditional.process(
            source_frame=pd.concat([self.source_frame, self.target_df], axis=1),
            params=ParamsMapConditional(
                target_attribute=TempColumns.TEMP_INST_ID_INSTRUMENT_UNIQUE_ID,
                cases=[
                    Case(
                        query="index == index",
                        attribute=TempColumns.TEMP_INST_UNIQUE_ID_ISIN_NOT_POPULATED,
                    ),
                    Case(
                        query="`m_ISIN`.str.len() == 12",
                        attribute=TempColumns.TEMP_INST_UNIQUE_ID_M_ISIN_POPULATED,
                    ),
                    Case(
                        query="`t_isin`.str.len() == 12",
                        attribute=TempColumns.TEMP_INST_UNIQUE_ID_T_ISIN_POPULATED,
                    ),
                    Case(
                        query="`c_product`.str.lower().str.fullmatch('cash/spot', na=False) & `m_asset`.str.lower().str.fullmatch('equity shares', na=False)",
                        attribute=TempColumns.TEMP_INST_UNIQUE_ID_CASH_EQUITY,
                    ),
                ],
            ),
        )[
            TempColumns.TEMP_INST_ID_INSTRUMENT_UNIQUE_ID
        ]

        # Note From specs: Although this mechanism is not ‘strictly’ fallback,
        # it does help to differentiate SRP vs. Non SRP by setting to “true”
        # which will be useful when assessing any market data issues stemming
        # from Instrument Reference Data integrity
        self.target_df.loc[:, TempColumns.TEMP_IS_CREATED_THROUGH_FALLBACK] = True

        self.target_df.loc[:, TempColumns.TEMP_ORDER_STATUS] = self.source_frame.loc[
            :, TempColumns.TEMP_ORDER_STATUS
        ]

        # Passing along SourceOrderColumns.CONTRACT_SIZE which is to be used downstream
        self.target_df.loc[:, SourceOrderColumns.CONTRACT_SIZE] = self.source_frame.loc[
            :, SourceOrderColumns.CONTRACT_SIZE
        ]

    def _buy_sell(self) -> None:
        """
        :return: None
        Populates BUY_SELL
        """
        self.target_df.loc[:, OrderColumns.BUY_SELL] = MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceOrderColumns.TYPE,
                target_attribute=OrderColumns.BUY_SELL,
                case_insensitive=True,
                value_map={
                    "0": "1",
                    "1": "2",
                    "2": "1",
                    "3": "2",
                    "4": "1",
                    "5": "2",
                    "6": "1",
                    "7": "2",
                    "8": "1",
                },
            ),
            auditor=self.auditor,
        )[OrderColumns.BUY_SELL]

    def _data_source_name(self) -> None:
        """
        :return: None
        Populates DATA_SOURCE_NAME
        """
        self.target_df.loc[:, OrderColumns.DATA_SOURCE_NAME] = DATA_SOURCE_NAME

    def _date(self) -> None:
        """
        :return: None
        Populate DATE
        """
        self.target_df.loc[:, OrderColumns.DATE] = self.pre_process_df.loc[
            :, TempColumns.TEMP_DATE
        ]

    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        pass

    def _execution_details_buy_sell_indicator(self) -> None:
        """
        :return: None
        Populates EXECUTION_DETAILS_BUY_SELL_INDICATOR
        """
        self.target_df.loc[
            :, OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
        ] = self.pre_process_df.loc[:, TempColumns.TEMP_BUY_SELL_INDICATOR]

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        pass

    def _execution_details_limit_price(self) -> None:
        """
        :return: None
        Populates EXECUTION_DETAILS_LIMIT_PRICE
        """
        self.target_df[
            OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE
        ] = ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_price_attribute=SourceOrderColumns.PRICE_ORDER,
                source_ccy_attribute=SourceInstrumentColumns.C_BASE_CCY,
                target_price_attribute=OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE,
                cast_to=CastTo.FLOAT,
            ),
        )[
            OrderColumns.EXECUTION_DETAILS_LIMIT_PRICE
        ]

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        pass

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        pass

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        pass

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        pass

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        pass

    def _execution_details_order_status(self) -> None:
        """
        :return: None
        Populates EXECUTION_DETAILS_ORDER_STATUS
        """
        self.target_df.loc[
            :, OrderColumns.EXECUTION_DETAILS_ORDER_STATUS
        ] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                cases=[
                    Case(
                        query="`TEMP_ORDER_STATUS`.isnull()",
                        value=OrderStatus.NEWO.value,
                    ),
                    Case(
                        query="`TEMP_ORDER_STATUS`.notnull()",
                        attribute=TempColumns.TEMP_ORDER_STATUS,
                    ),
                ],
            ),
        )[
            OrderColumns.EXECUTION_DETAILS_ORDER_STATUS
        ]

    def _execution_details_order_type(self) -> None:
        """
        :return: None
        Populate EXECUTION_DETAILS_ORDER_TYPE
        """
        self.target_df.loc[
            :, OrderColumns.EXECUTION_DETAILS_ORDER_TYPE
        ] = MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceOrderColumns.TYPE,
                target_attribute=OrderColumns.EXECUTION_DETAILS_ORDER_TYPE,
                value_map={
                    "0": OrderType.MARKET,
                    "1": OrderType.MARKET,
                    "2": OrderType.LIMIT,
                    "3": OrderType.LIMIT,
                    "4": OrderType.STOP,
                    "5": OrderType.STOP,
                    "6": OrderType.LIMIT,
                    "7": OrderType.LIMIT,
                    "8": OrderType.MARKET,
                },
            ),
            auditor=self.auditor,
        )[
            OrderColumns.EXECUTION_DETAILS_ORDER_TYPE
        ]

    def _execution_details_outgoing_order_addl_info(self) -> None:
        """
        Populates EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO skipping columns which are not present.
        :return: None
        """
        static_cols = [
            TempColumns.TEMP_STATIC_TEXT_MODIFY_FLAGS,
            TempColumns.TEMP_STATIC_TEXT_ACTION,
            TempColumns.TEMP_STATIC_TEXT_LOGIN,
            TempColumns.TEMP_STATIC_TEXT_MT5_SYMBOL,
        ]
        outgoing_order_addl_info_frame = pd.concat(
            [
                self.source_frame,
                self.pre_process_df.loc[:, static_cols + [TempColumns.TEMP_DELIMITER]],
            ],
            axis=1,
        )

        # Replace regex
        remove_regex = ""
        for col in static_cols:
            remove_regex += outgoing_order_addl_info_frame[col].iloc[0] + r"\|\?|"

        self.target_df.loc[
            :, OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO
        ] = (
            ConcatAttributes.process(
                source_frame=outgoing_order_addl_info_frame,
                params=ParamsConcatAttributes(
                    source_attributes=[
                        TempColumns.TEMP_STATIC_TEXT_MODIFY_FLAGS,
                        SourceOrderColumns.MODIFY_FLAGS,
                        TempColumns.TEMP_DELIMITER,
                        TempColumns.TEMP_STATIC_TEXT_ACTION,
                        SourceOrderColumns.COMMENT,
                        TempColumns.TEMP_DELIMITER,
                        TempColumns.TEMP_STATIC_TEXT_LOGIN,
                        SourceOrderColumns.LOGIN,
                        TempColumns.TEMP_DELIMITER,
                        TempColumns.TEMP_STATIC_TEXT_MT5_SYMBOL,
                        SourceOrderColumns.SYMBOL,
                        TempColumns.TEMP_DELIMITER,
                    ],
                    target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
                ),
            )[OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO]
            .str.replace(remove_regex.rstrip("|"), "")
            .str.replace(r"\|\?", Delimiters.COMMA_SPACE)
            .replace("", pd.NA)
        )

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        pass

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        pass

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        pass

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_stop_price(self) -> pd.DataFrame:
        pass

    def _execution_details_trading_capacity(self) -> None:
        """
        :return: None
        Populates EXECUTION_DETAILS_TRADING_CAPACITY
        """
        self.target_df.loc[
            :, OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY
        ] = MapStatic.process(
            source_frame=self.source_frame,
            params=ParamsMapStatic(
                target_attribute=OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY,
                target_value=TradingCapacity.DEAL.value,
            ),
        )[
            OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY
        ]

    def _execution_details_validity_period(self) -> pd.DataFrame:
        pass

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        pass

    def _financing_type(self) -> pd.DataFrame:
        pass

    def _hierarchy(self) -> pd.DataFrame:
        pass

    def _id(self) -> None:
        """
        :return: None
        Populate ID
        """
        self.target_df.loc[:, OrderColumns.ID] = self.pre_process_df.loc[
            :, TempColumns.TEMP_ORDER_ID
        ]

    def _is_discretionary(self) -> pd.DataFrame:
        pass

    def _is_iceberg(self):
        pass

    def _is_repo(self):
        pass

    def _is_synthetic(self) -> None:
        self.target_df.loc[:, OrderColumns.IS_SYNTHETIC] = False

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        pass

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        pass

    def _jurisdiction_country(self) -> pd.DataFrame:
        pass

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        pass

    def _market_identifiers(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers by calling MergeMarketIdentifiers.
        Assumes _market_identifiers_instrument() and _market_identifiers_parties() have been
        called earlier"""
        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=ParamsMergeMarketIdentifers(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                instrument_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _market_identifiers_instrument(self) -> None:
        """
        :return: None
        Populates MARKET_IDENTIFIERS_INSTRUMENT
        Note: Although this field is not used in this flow by
        instrument_identifiers task downstream we need value in
        market_identifiers for the instrument_fullname so that it is
        displayed on IRIS on the Orders dashboard.
        """
        self.target_df.loc[
            :, OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT
        ] = self.pre_process_df.loc[:, TempColumns.TEMP_INST_ID_INSTRUMENT_FULL_NAME]
        self.target_df.loc[:, OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT] = (
            self.target_df[OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT]
            .dropna()
            .apply(
                lambda x: [
                    Identifier(
                        labelId=x,
                        path=INSTRUMENT_PATH,
                        type=IdentifierType.OBJECT,
                    ).dict()
                ]
            )
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """
        :return: None
        Populate MARKET_IDENTIFIERS_PARTIES
        """
        self.pre_process_df = populate_buyer_seller_decision_maker(
            df=self.pre_process_df
        )
        return GenericOrderPartyIdentifiers.process(
            source_frame=self.pre_process_df,
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                client_identifier=TempPartyIDs.CLIENT,
                counterparty_identifier=TempPartyIDs.EXECUTING_ENTITY,
                executing_entity_identifier=TempPartyIDs.EXECUTING_ENTITY,
                trader_identifier=TempPartyIDs.CLIENT,
                buyer_identifier=TempPartyIDs.CLIENT,
                seller_identifier=TempPartyIDs.EXECUTING_ENTITY,
                buyer_decision_maker_identifier=TempPartyIDs.BUYER_DECISION_MAKER,
                seller_decision_maker_identifier=TempPartyIDs.SELLER_DECISION_MAKER,
                execution_within_firm_identifier=TempPartyIDs.INVESTMENT_DEC_WITHIN_FIRM,
                investment_decision_within_firm_identifier=TempPartyIDs.INVESTMENT_DEC_WITHIN_FIRM,
                buy_sell_side_attribute=TempColumns.TEMP_BUY_SELL_INDICATOR,
                use_buy_mask_for_buyer_seller=True,
            ),
        )

    def _meta_model(self) -> None:
        """
        :return: None
        Populates META_MODEL
        """
        self.target_df.loc[:, OrderColumns.META_MODEL] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.META_MODEL,
                cases=[
                    Case(
                        query="`TEMP_ORDER_STATUS`.isnull()",
                        value=MetaModel.ORDER,
                    ),
                    Case(
                        query="`TEMP_ORDER_STATUS`.notnull()",
                        value=MetaModel.ORDER_STATE,
                    ),
                ],
            ),
        )[OrderColumns.META_MODEL]

    def _order_class(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_aggregated_order_id_code(self) -> None:
        """
        :return: None
        Populates ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID
        """
        self.target_df.loc[
            :, OrderColumns.ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID
        ] = self.source_frame.loc[:, SourceOrderColumns.POSITION_ID]

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        pass

    def _order_identifiers_internal_order_id_code(self) -> None:
        """
        :return: None
        Populates ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE
        """
        self.target_df.loc[
            :, OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE
        ] = self.source_frame.loc[:, SourceOrderColumns.EXTERNAL_ID]

    def _order_identifiers_order_id_code(self) -> None:
        """
        :return: None
        Populates ORDER_IDENTIFIERS_ORDER_ID_CODE
        """
        self.target_df.loc[
            :, OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE
        ] = self.pre_process_df.loc[:, TempColumns.TEMP_ORDER_ID]

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_parent_order_id(self) -> pd.DataFrame:
        pass

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        pass

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        pass

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        pass

    def _price_forming_data_initial_quantity(self) -> None:
        """
        :return: None
        Populates PRICE_FORMING_DATA_INITIAL_QUANTITY
        """
        self.target_df.loc[
            :, OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY
        ] = calculate_qty(
            df=self.source_frame,
            qty_attribute=SourceOrderColumns.CONTRACT_SIZE,
            volume_attribute=SourceOrderColumns.VOLUME_INITIAL,
        )

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        pass

    def _price_forming_data_remaining_quantity(self) -> None:
        """
        :return: None
        Populates PRICE_FORMING_DATA_REMAINING_QUANTITY
        """
        self.target_df.loc[
            :, OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY
        ] = calculate_qty(
            df=self.source_frame,
            qty_attribute=SourceOrderColumns.CONTRACT_SIZE,
            volume_attribute=SourceOrderColumns.VOLUME_INITIAL,
        )

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        pass

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        pass

    def _source_index(self) -> None:
        """
        :return: None
        Populates SOURCE_INDEX
        """
        self.target_df.loc[
            :, OrderColumns.SOURCE_INDEX
        ] = self.source_frame.index.values

    def _source_key(self) -> None:
        """
        :return: None
        Populates SOURCE_KEY
        """
        self.target_df.loc[:, OrderColumns.SOURCE_KEY] = os.getenv(SWARM_FILE_URL)

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        pass

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        pass

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        pass

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        pass

    def _timestamps_order_received(self) -> None:
        """
        :return: None
        Populates TIMESTAMPS_ORDER_RECEIVED
        """
        self.target_df.loc[
            :, OrderColumns.TIMESTAMPS_ORDER_RECEIVED
        ] = self.pre_process_df.loc[:, TempColumns.TEMP_TIME_SETUP_MSC]

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        """
        :return: None
        Populates TIMESTAMPS_ORDER_STATUS_UPDATED
        """
        self.target_df.loc[
            :, OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED
        ] = self.pre_process_df.loc[:, TempColumns.TEMP_TIME_SETUP_MSC]

    def _timestamps_order_submitted(self) -> None:
        """
        :return: None
        Populates TIMESTAMPS_ORDER_SUBMITTED
        """
        self.target_df.loc[
            :, OrderColumns.TIMESTAMPS_ORDER_SUBMITTED
        ] = self.pre_process_df.loc[:, TempColumns.TEMP_TIME_SETUP_MSC]

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        pass

    def _timestamps_validity_period(self) -> None:
        """
        :return: None
        Populates TIMESTAMPS_VALIDITY_PERIOD
        """
        self.target_df.loc[
            :, OrderColumns.TIMESTAMPS_VALIDITY_PERIOD
        ] = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceOrderColumns.TYPE_FILL,
                target_attribute=OrderColumns.TIMESTAMPS_VALIDITY_PERIOD,
                cast_to=CastTo.STRING_LIST.value,
                list_delimiter=Delimiters.SEMI_COLON,
            ),
            auditor=self.auditor,
        )[
            OrderColumns.TIMESTAMPS_VALIDITY_PERIOD
        ]

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_waiver_indicator(self) -> pd.DataFrame:
        pass

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        pass

    def _transaction_details_buy_sell_indicator(self) -> None:
        """
        :return: None
        Populates TRANSACTION_DETAILS_BUY_SELL_INDICATOR
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR
        ] = self.pre_process_df.loc[:, TempColumns.TEMP_BUY_SELL_INDICATOR]

    def _transaction_details_commission_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_commission_amount_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_commission_amount_type(self) -> pd.DataFrame:
        pass

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_cross_indicator(self) -> pd.DataFrame:
        pass

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        pass

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        pass

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        pass

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        pass

    def _transaction_details_position_effect(self) -> pd.DataFrame:
        pass

    def _transaction_details_position_id(self) -> None:
        """
        :return: None
        Populate TRANSACTION_DETAILS_POSITION_ID
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_POSITION_ID
        ] = MapAttribute.process(
            source_frame=self.source_frame,
            params=ParamsMapAttribute(
                source_attribute=SourceOrderColumns.POSITION_ID,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_POSITION_ID,
            ),
            auditor=self.auditor,
        )[
            OrderColumns.TRANSACTION_DETAILS_POSITION_ID
        ]

    def _transaction_details_price(self) -> pd.DataFrame:
        pass

    def _transaction_details_price_average(self) -> pd.DataFrame:
        pass

    def _transaction_details_price_currency(self) -> None:
        """
        Map to field, in order of priority, that contains a valid currency:

        instruments.[c_quoted_ccy]
        instruments.[m_price_currency]
        instruments.[c_base_ccy]

        only take a value from the field where it is a currency (i.e., f it’s ‘NULL’ then don’t take it)
        major/minor currency conversion must be applied
        changes raised in ON-3395, ON-4232
        """
        # SPI-1582 --> only take a value from the field where it is a currency
        for col in [
            SourceInstrumentColumns.C_QUOTED_CCY,
            SourceInstrumentColumns.M_PRICE_CURRENCY,
            SourceInstrumentColumns.C_BASE_CCY,
        ]:
            self.source_frame[col] = (
                self.source_frame[col]
                .fillna("")
                .apply(lambda x: x if isinstance(x, str) and len(x) == 3 else None)
            )
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY
        ] = ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attributes=[
                    SourceInstrumentColumns.C_QUOTED_CCY,
                    SourceInstrumentColumns.M_PRICE_CURRENCY,
                    SourceInstrumentColumns.C_BASE_CCY,
                ],
                target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
            ),
        )[
            OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY
        ]

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        pass

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        pass

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        pass

    def _transaction_details_quantity(self) -> pd.DataFrame:
        pass

    def _transaction_details_quantity_currency(self) -> None:
        """
        if instruments.[m_asset].lower() in (fx, currency):
            instruments.[c_base_ccy]
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY
        ] = ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceInstrumentColumns.C_BASE_CCY,
                target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
            ),
        )[
            OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY
        ]

        m_asset_mask = (
            self.source_frame.loc[:, SourceInstrumentColumns.M_ASSET]
            .str.lower()
            .isin(["fx", "currency"])
        )
        self.target_df.loc[
            ~m_asset_mask, OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY
        ] = pd.NA

    def _transaction_details_quantity_notation(self) -> None:
        """
        :return: None
        Populates TRANSACTION_DETAILS_QUANTITY_NOTATION
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION
        ] = MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION,
                cases=[
                    Case(
                        query="`m_asset`.str.lower().isin(['fx', 'currency'])",
                        value=QuantityNotation.MONE.value,
                    ),
                    Case(
                        query="~(`m_asset`.str.lower().isin(['fx', 'currency']))",
                        value=QuantityNotation.UNIT.value,
                    ),
                ],
            ),
        )[
            OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION
        ]

    def _transaction_details_record_type(self) -> None:
        """
        :return: None
        Populates TRANSACTION_DETAILS_RECORD_TYPE
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE
        ] = OrderRecordType.CLIENT_SIDE.value

    def _transaction_details_settlement_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_amount_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_date(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        pass

    def _transaction_details_swap_directionalities(self) -> pd.DataFrame:
        pass

    def _transaction_details_trading_capacity(self) -> None:
        """
        :return: None
        Populates TRANSACTION_DETAILS_TRADING_DATE_TIME
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY
        ] = TradingCapacity.DEAL.value

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        pass

    def _transaction_details_trail_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_ultimate_venue(self) -> None:
        """
        Use instrument.[t_id_prim_exch_mic]
        note: if value isn't valid (i.e. not 4 characters then map to “XXXX”)

        if instrument.[t_id_prim_exch_mic] = “NULL” or “OOTC” then: 'XXXX'
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE
        ] = Venue.XXXX

        four_characters_mask = (
            self.source_frame.loc[
                :, SourceInstrumentColumns.T_ID_PRIM_EXCH_MIC
            ].str.len()
            == 4
        )
        not_ootc_mask = (
            ~self.source_frame.loc[:, SourceInstrumentColumns.T_ID_PRIM_EXCH_MIC]
            .str.upper()
            .str.fullmatch("OOTC", na=False)
        )
        combined_mask = four_characters_mask & not_ootc_mask

        if combined_mask.any():
            self.target_df.loc[
                combined_mask, OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE
            ] = self.source_frame.loc[
                combined_mask, SourceInstrumentColumns.T_ID_PRIM_EXCH_MIC
            ]

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        pass

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_venue(self) -> None:
        """
        Populates TRANSACTION_DETAILS_VENUE from TRANSACTION_DETAILS_ULTIMATE_VENUE
        """
        self.target_df.loc[
            :, OrderColumns.TRANSACTION_DETAILS_VENUE
        ] = self.target_df.loc[:, OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE]

    @staticmethod
    def populate_party_identifiers(df: pd.DataFrame) -> pd.DataFrame:
        """
        :param df: pd.Dataframe
        :return: pd.Dataframe
        Add required fields for party identifiers
        """
        # client
        df.loc[:, TempPartyIDs.CLIENT] = (
            PartyPrefix.ID + df.loc[:, SourceOrderColumns.LOGIN]
        )

        # executing entity
        df.loc[:, TempPartyIDs.EXECUTING_ENTITY] = GetTenantLEI.process(
            source_frame=df,
            params=ParamsGetTenantLEI(
                target_column_prefix=PartyPrefix.LEI,
                target_lei_column=TempPartyIDs.EXECUTING_ENTITY,
            ),
        )

        return df

    def _fetch_instrument_data_from_s3(self) -> None:
        """
        :return: None
        Adds instrument data from s3 file to source_frame and removes rows
        where instrument is not found.
        """
        # instrument_data from s3 file
        instrument_df = DfFromS3Csv.process(
            params=ParamsDfFromS3Csv(
                s3_key="resource/metatrader/mt5/instrument/InstrumentData.csv"
            ),
        )
        # check for required columns in instrument_data df
        for col in SourceInstrumentColumns().get_columns():
            if col not in instrument_df.columns:
                instrument_df.loc[:, col] = pd.NA

        # merge source_frame with instrument data
        self.source_frame = self.source_frame.merge(
            instrument_df.drop_duplicates(subset=[SourceInstrumentColumns.C_MT5_ID]),
            how="left",
            left_on=SourceOrderColumns.SYMBOL,
            right_on=SourceInstrumentColumns.C_MT5_ID,
        )

        # remove rows where instrument not found
        instrument_na_mask = self.source_frame[
            SourceInstrumentColumns.C_MT5_ID
        ].isnull()

        # Raise SKIP if instruments not found for all rows
        if instrument_na_mask.all():
            msg = "No rows can be linked to instruments. So skipping all rows."
            logger_.error(msg)
            self.auditor.add(msg)
            raise SKIP(msg)

        # Instruments not found for some rows
        if instrument_na_mask.any():
            instrument_na_symbol_list = self.source_frame.loc[
                instrument_na_mask, SourceOrderColumns.SYMBOL
            ].tolist()
            instrument_na_indices = self.source_frame.loc[
                instrument_na_mask
            ].index.tolist()
            msg = (
                f"[!] Skipping rows where instruments not found...\n"
                f"[!] Count of rows skipped: "
                f"[!] {len(self.source_frame.loc[instrument_na_mask])}.\n"
                f"[!] Source Symbols: {set(instrument_na_symbol_list)}.\n"
                f"[!] Source Indices: {instrument_na_indices}"
            )
            self.auditor.add(msg)
            logger_.warning(msg)
            self.source_frame = self.source_frame.loc[~instrument_na_mask]

    def _add_rows_for_order_states(self) -> None:
        """
        :return: None
        Updates source_frame by adding additional rows if
        `State`.isin(['2', '5', '6', '8', '9'])
        """
        temp_df = self.source_frame.copy()

        self.query_order_states = "`State`.isin(['2', '5', '6', '8', '9'])"
        order_states_df = GetRowsByCondition.process(
            source_frame=temp_df,
            params=ParamsGetRowsByCondition(query=self.query_order_states),
        )
        if not order_states_df.empty:
            order_states_df.loc[
                :, TempColumns.TEMP_ORDER_STATUS
            ] = MapConditional.process(
                source_frame=order_states_df,
                params=ParamsMapConditional(
                    target_attribute=TempColumns.TEMP_ORDER_STATUS,
                    cases=[
                        Case(
                            query="`State` == '2'",
                            value=OrderStatus.CAME.value,
                        ),
                        Case(
                            query="`State` == '5'",
                            value=OrderStatus.REMO.value,
                        ),
                        Case(
                            query="`State` == '6'",
                            value=OrderStatus.EXPI.value,
                        ),
                        Case(
                            query="`State`.isin(['8', '9'])",
                            value=OrderStatus.CHME.value,
                        ),
                    ],
                ),
            )[
                TempColumns.TEMP_ORDER_STATUS
            ]

        self.source_frame = pd.concat([self.source_frame, order_states_df], axis=0)
        self.source_frame = self.source_frame.reset_index()

        # safety check for cases where order_states_df is empty
        if TempColumns.TEMP_ORDER_STATUS not in self.source_frame:
            self.source_frame.loc[:, TempColumns.TEMP_ORDER_STATUS] = pd.NA

        # re-align target and pre-process_df to match source_frame
        self.pre_process_df = pd.DataFrame(index=self.source_frame.index)
        self.target_df = pd.DataFrame(index=self.source_frame.index)

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        """Not implemented"""
