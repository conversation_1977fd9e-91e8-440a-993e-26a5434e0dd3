class SourceTradesColumns:
    CLOSE_PRICE = "CLOSE_PRICE"
    CLOSE_TIME = "CLOSE_TIME"
    CMD = "CMD"
    LOGIN = "LOGIN"
    MAGIC = "MAGIC"
    MARGIN_RATE = "MARGIN_RATE"
    OANDA_USER_ID = "OANDA_USER_ID"
    OPEN_PRICE = "OPEN_PRICE"
    OPEN_TIME = "OPEN_TIME"
    SL = "SL"
    SYMBOL = "SYMBOL"
    TICKET = "TICKET"
    TP = "TP"
    VOLUME = "VOLUME"


class TempColumns:
    BUY_SELL_INDICATOR = "__BUY_SELL_INDICATOR__"
    CLOSE_DATE = "__CLOSE_DATE__"
    CLOSE_TIMESTAMP = "__CLOSE_TIMESTAMP__"
    CLOSE_TIMESTAMP_STR = "__CLOSE_TIMESTAMP_STR__"
    OPEN_DATE = "__OPEN_DATE__"
    OPEN_TIMESTAMP = "__OPEN_TIMESTAMP__"
    OPEN_TIMESTAMP_STR = "__OPEN_TIMESTAMP_STR__"
    POSITION_TYPE = "__POSITION_TYPE__"
    TEMP_ASSET_CLASS = "__TEMP_ASSET_CLASS__"
    TEMP_INST_ASSET_CLASS = "__TEMP_INST_ASSET_CLASS__"
    TEMP_INST_ID_BESTEX_ASSET_CLASS_MAIN = "__TEMP_INST_ID_BESTEX_ASSET_CLASS_MAIN__"
    TEMP_INST_ID_BESTEX_ASSET_CLASS_SUB = "__TEMP_INST_ID_BESTEX_ASSET_CLASS_SUB__"
    TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_BASE_PRODUCT = (
        "__TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_BASE_PRODUCT__"
    )
    TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_FURTHER_SUB_PRODUCT = (
        "__TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_FURTHER_SUB_PRODUCT__"
    )
    TEMP_INST_ID_DELIVERY_TYPE = "__TEMP_INST_ID_DELIVERY_TYPE__"
    TEMP_INST_ID_EXT_EXCHANGE_SYMBOL = "__TEMP_INST_ID_EXT_EXCHANGE_SYMBOL__"
    TEMP_INST_ID_INSTRUMENT_CLASSIFICATION = (
        "__TEMP_INST_ID_INSTRUMENT_CLASSIFICATION__"
    )
    TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_ASSET_CLASS = (
        "__TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_ASSET_CLASS__"
    )
    TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_CONTRACT_TYPE = (
        "__TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_CONTRACT_TYPE__"
    )
    TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_PRODUCT_TYPE = (
        "__TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_PRODUCT_TYPE__"
    )
    TEMP_INST_ID_INSTRUMENT_FULL_NAME = "__TEMP_INST_ID_INSTRUMENT_FULL_NAME__"
    TEMP_INST_ID_INSTRUMENT_ID_CODE = "__TEMP_INST_ID_INSTRUMENT_ID_CODE__"
    TEMP_INST_ID_INSTRUMENT_ID_CODE_TYPE = "__TEMP_INST_ID_INSTRUMENT_ID_CODE_TYPE__"
    TEMP_INST_ID_INSTRUMENT_UNIQUE_ID = "__TEMP_INST_ID_INSTRUMENT_UNIQUE_ID__"
    TEMP_INST_ID_PRICE_MULTIPLIER = "__TEMP_INST_ID_PRICE_MULTIPLIER__"
    TEMP_INST_ID_PRICE_NOTATION = "__TEMP_INST_ID_PRICE_NOTATION__"
    TEMP_INST_ID_QUANTITY_NOTATION = "__TEMP_INST_ID_QUANTITY_NOTATION__"
    TEMP_INST_ID_UNDERLYING_INDEX_NAME = "__TEMP_INST_ID_UNDERLYING_INDEX_NAME__"
    TEMP_INST_ID_UNDERLYING_INSTRUMENT_ID = "__TEMP_INST_ID_UNDERLYING_INSTRUMENT_ID__"
    TEMP_INST_UNIQUE_ID_ISIN_NOT_POPULATED = (
        "__TEMP_INST_UNIQUE_ID_ISIN_NOT_POPULATED__"
    )
    TEMP_INST_UNIQUE_ID_ISIN_POPULATED = "__TEMP_INST_UNIQUE_ID_ISIN_POPULATED__"
    TEMP_INST_UNIQUE_ID_M_ISIN_POPULATED = "__TEMP_INST_UNIQUE_ID_M_ISIN_POPULATED__"
    TEMP_INST_UNIQUE_ID_T_ISIN_POPULATED = "__TEMP_INST_UNIQUE_ID_T_ISIN_POPULATED__"
    TEMP_IS_CREATED_THROUGH_FALLBACK = "__TEMP_IS_CREATED_THROUGH_FALLBACK__"
    TEMP_PARENT_META_MODEL = "__TEMP_PARENT_META_MODEL__"
    TIMESTAMP_ORDER_RECEIVED = "__TIMESTAMP_ORDER_RECEIVED__"
