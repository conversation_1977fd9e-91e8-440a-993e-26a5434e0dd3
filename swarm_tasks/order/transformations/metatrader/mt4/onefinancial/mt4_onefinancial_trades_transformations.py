import pandas as pd
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.order.transformations.metatrader.mt4.mt4_trades_transformations import (
    MT4TradesTransformations,
)
from swarm_tasks.order.transformations.metatrader.mt5.static import ONE_FINANCIAL
from swarm_tasks.order.transformations.metatrader.mt5.static import TempPartyIDs


class MT4OneFinancialTradesTransformations(MT4TradesTransformations):
    def populate_party_identifiers(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        :param df: pd.Dataframe
        :return: pd.Dataframe
        Overrides populate_party_identifiers() method from
        MT4OrderTransformations to add EXECUTING_ENTITY and
        INVESTMENT_DEC_WITHIN_FIRM
        """
        df = super().populate_party_identifiers(df=self.pre_process_df)
        df.loc[:, TempPartyIDs.EXECUTING_ENTITY] = f"{PartyPrefix.ID}{ONE_FINANCIAL}"
        df.loc[:, TempPartyIDs.INVESTMENT_DEC_WITHIN_FIRM] = PartyPrefix.ALGO_1
        return df
