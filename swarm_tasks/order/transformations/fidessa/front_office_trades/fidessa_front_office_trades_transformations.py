import os

import pandas as pd
from se_core_tasks.currency.convert_minor_to_major import CastTo
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_conditional import Case
from se_elastic_schema.static.fix import OrderClass
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_elastic_schema.static.mifid2 import HierarchyEnum
from se_elastic_schema.static.mifid2 import OptionType
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.mifid2 import PassiveAggressiveStatus
from se_elastic_schema.static.mifid2 import PriceNotation
from se_elastic_schema.static.mifid2 import QuantityNotation
from se_elastic_schema.static.mifid2 import TradingCapacity
from se_elastic_schema.static.reference import OrderRecordType
from se_trades_tasks.abstractions.abstract_order_transformations import (
    AbstractOrderTransformations,
)
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order_and_tr.static import AssetClass
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ParamsConvertMinorToMajor,
)
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ParamsConcatAttributes,
)
from swarm_tasks.transform.datetime.convert_datetime import ConvertDatetime
from swarm_tasks.transform.datetime.convert_datetime import (
    Params as ParamsConvertDatetime,
)
from swarm_tasks.transform.datetime.join_date_and_time import JoinDateAndTimeFormat
from swarm_tasks.transform.datetime.join_date_and_time import (
    Params as ParamsJoinDateAndTimeFormat,
)
from swarm_tasks.transform.map.map_attribute import MapAttribute
from swarm_tasks.transform.map.map_attribute import Params as ParamsMapAttribute
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as ParamsMapValue
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    MergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.link.merge_market_identifiers import (
    Params as ParamsMergeMarketIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    InstrumentIdentifiers,
)
from swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers import (
    Params as ParamsInstrumentIdentifiers,
)
from swarm_tasks.utilities.static import Delimiters
from swarm_tasks.utilities.static import SWARM_FILE_URL

DEFAULT_TIMEZONE = "Europe/London"


class DateTimeFormats:
    SOURCE_DATE_FORMAT = "%Y%m%d"
    SOURCE_DATE_FORMATS = ["%Y%m%d"]
    SOURCE_DATETIME_FORMATS = ["%Y%m%d %H:%M:%S.%f %zs", "%Y%m%d %H:%M:%S.%f %z"]
    TARGET_DATE_FORMAT = "%Y-%m-%d"
    TARGET_DATETIME_FORMAT = "%Y-%m-%dT%H:%M:%S.%fZ"


class SourceColumns:
    AGGREGATION_ID = "Aggregation_Id"
    AMENDED_DATETIME = "Amended_Datetime"
    BOOK_ID = "Book_Id"
    BUSINESS_TRANSACTION_DESCRIPTION = "Business_Transaction_Description"
    BUY_SELL = "Buy_Sell"
    CFI_CODE = "CFI_Code"
    CLIENT_LEI = "Client_LEI"
    COUNTERPARTY = "Counterparty"
    COUNTERPARTY_DESCRIPTION = "Counterparty_Description"
    DEALING_CAPACITY = "Dealing_Capacity"
    DEALT_CCY = "Dealt_Ccy"
    DISCRETIONARY_ORDER = "Discretionary_Order"
    ENTERED_BY = "Entered_By"
    ENTERED_BY_USER_GROUP = "Entered_By_User_Group"
    ENTERED_DATE = "Entered_Date"
    EPIC_CODE = "Epic_Code"
    EXCHANGE_CONTRACT_CODE = "Exchange_Contract_Code"
    EXCHANGE_TRADE_CODE = "Exchange_Trade_Code"
    EXECUTING_BOOK_ID = "Executing_Book_Id"
    EXECUTION_DECISION_VALUE = "Execution_Decision_Value"
    EXECUTION_VENUE = "Execution_Venue"
    EXPIRY_DATE = "Expiry_Date"
    EXPIRY_TYPE = "Expiry_Type"
    GROSS_PRICE = "Gross_Price"
    INSTRUMENT_CODE = "Instrument_Code"
    INSTRUMENT_DESCRIPTION = "Instrument_Description"
    INSTRUMENT_TYPE = "Instrument_Type"
    INVESTMENT_DECISION_VALUE = "Investment_Decision_Value"
    ISIN_CODE = "Isin_Code"
    OFFICIAL_PLACE_OF_LISTING = "Official_Place_Of_Listing"
    OPTION_TYPE = "Option_Type"
    ORDER_ID = "Order_Id"
    ORDER_PRICE_TYPE = "Order_Price_Type"
    ORIGINATOR_ORDER_ID = "Originator_Order_Id"
    QUANTITY = "Quantity"
    REPORT_TO_EXCHANGE = "Report_To_Exchange"
    SEDOL_CODE = "Sedol_Code"
    SETTLEMENT_CCY = "Settlement_Ccy"
    SETTLEMENT_CONSIDERATION = "Settlement_Consideration"
    SETTLEMENT_DATE = "Settlement_Date"
    STATE = "State"
    STRIKE_PRICE = "Strike_Price"
    TRADE_DATETIME = "Trade_Datetime"
    TRADE_ID = "Trade_Id"
    TRADE_TYPE = "Trade_Type"
    TRADER = "Trader"
    TRADING_ENTITY_ID = "Trading_Entity_Id"
    TRADING_VENUE_TRANSACTION_ID = "Trading_Venue_Transaction_Id"
    UNDERLYING_ISIN = "Underlying_ISIN"
    VERSION_NUMBER = "Version_Number"


class CommonQueries:
    DISCRETIONARY_ORDER_QUERY = f"`{SourceColumns.DISCRETIONARY_ORDER}`.str.fullmatch('Y', case=False, na=False)"
    ENTERED_BY_RSPQ_QUERY = (
        f"`{SourceColumns.ENTERED_BY}`.str.fullmatch('RSPQ', case=False, na=False)"
    )
    INSTRUMENT_TYPE_OP_QUERY = (
        f"`{SourceColumns.INSTRUMENT_TYPE}`.str.fullmatch('OP', case=False, na=False)"
    )
    RECORD_TYPE_QUERY = (
        f"{SourceColumns.COUNTERPARTY}.str.fullmatch('C', case=False, na=False)"
        f" and "
        f"{SourceColumns.EXECUTING_BOOK_ID}.str.fullmatch('OMAR', case=False, na=False)"
    )


class TempColumns:
    ASSET_CLASS = "__asset_class__"
    BUY_SELL = "__buy_sell__"
    BUY_SELL_INDICATOR = "__buy_sell_indicator__"
    CLIENT_ID = "__client_id__"
    CONDITIONAL_BUY_SELL = "__conditional_buy_sell__"
    CONDITIONAL_BUY_SELL_INDICATOR = "__conditional_buy_sell_indicator__"
    DEALING_CAPACITY = "__dealing_capacity__"
    EXPIRY_DATE = "__expiry_date__"
    INVERSE_BUY_SELL = "__inverse_buy_sell__"
    INVERSE_BUY_SELL_INDICATOR = "__inverse_buy_sell_indicator__"
    STRIKE_PRICE = "__strike_price__"
    TIMESTAMPS_VALIDITY_PERIOD = "__timestamps_validity_period__"


class PostProcessTempColumns:
    """Temp columns which are inserted into the target_df and need to be dropped in
    the bundle"""

    # Instrument Fallback columns
    FALLBACK_EXERCISE_STYLE = "__fallback_exercise_style__"
    FALLBACK_ID_CODE = "__fallback_id_code__"
    FALLBACK_INSTR_CLASS = "__fallback_instr_class__"
    FALLBACK_INSTR_FULL_NAME = "__fallback_instr_full_name__"
    FALLBACK_INSTR_ID_CODE = "__fallback_instr_id_code__"
    FALLBACK_IS_CREATED_THROUGH_FALLBACK = "__fallback_is_created_through_fb__"
    FALLBACK_OPTION_TYPE = "__fallback_option_type__"
    FALLBACK_PRICE_MULTIPLIER = "__fallback_price_multiplier__"
    FALLBACK_STRIKE_PRICE = "__fallback_strike_price__"
    FALLBACK_STRIKE_PRICE_CCY = "__fallback_strike_price_ccy__"
    FALLBACK_STRIKE_PRICE_TYPE = "__fallback_strike_price_type__"

    # Instrument Override columns
    OVERRIDE_INSTRUMENT_DESCRIPTION = "__override_instrument_description__"

    # Parties fallback columns
    CLIENT_WITHOUT_PREFIX = "__client_without_prefix__"
    EXECUTION_DECISION_WITHOUT_PREFIX = "__execution_decision_without_prefix__"
    INVESTMENT_DECISION_WITHOUT_PREFIX = "__investment_decision_without_prefix__"
    TRADER_WITHOUT_PREFIX = "__trader_without_prefix__"
    TRADING_ENTITY_WITHOUT_PREFIX = "__trading_entity_without_prefix__"

    # Newo In File
    NEWO_IN_FILE = "__newo_in_file__"


class FidessaFrontOfficeTradesTransformations(AbstractOrderTransformations):
    """Transformations class for Fidessa Front Office Trades."""

    def _pre_process(self):
        """This pre-processing method is used to populate columns in pre_process_df."""
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._get_expiry_date(),
                self._get_option_strike_price(),
                self._get_buy_sell_and_inverse(),
            ],
            axis=1,
        )

    def process(self):
        """Populates all the required columns"""
        self.pre_process()
        self.buy_sell()
        self.data_source_name()
        self.date()
        self.execution_details_buy_sell_indicator()
        self.execution_details_order_status()
        self.execution_details_order_type()
        self.execution_details_passive_aggressive_indicator()
        self.execution_details_routing_strategy()
        self.execution_details_settlement_amount()
        self.execution_details_trading_capacity()
        self.execution_details_outgoing_order_addl_info()
        self.execution_details_validity_period()
        self.meta_model()
        self.order_class()
        self.order_identifiers_aggregated_order_id_code()
        self.order_identifiers_initial_order_designation()
        self.order_identifiers_internal_order_id_code()
        self.order_identifiers_order_routing_code()
        self.order_identifiers_sequence_number()
        self.order_identifiers_trading_venue_transaction_id_code()
        self.order_identifiers_transaction_ref_no()
        self.order_identifiers_order_id_code()
        self.hierarchy()
        self.id()
        self.price_forming_data_initial_quantity()
        self.price_forming_data_price()
        self.price_forming_data_traded_quantity()
        self.report_details_transaction_ref_no()
        self.source_index()
        self.source_key()
        self.timestamps_trading_date_time()
        self.timestamps_order_received()
        self.timestamps_order_submitted()
        self.timestamps_order_status_updated()
        self.timestamps_validity_period()
        self.transaction_details_basket_id()
        self.transaction_details_buy_sell_indicator()
        self.transaction_details_complex_trade_component_id()
        self.transaction_details_price()
        self.transaction_details_price_average()
        self.transaction_details_price_currency()
        self.transaction_details_price_notation()
        self.transaction_details_quantity()
        self.transaction_details_quantity_notation()
        self.transaction_details_record_type()
        self.transaction_details_settlement_amount()
        self.transaction_details_settlement_date()
        self.transaction_details_trading_capacity()
        self.transaction_details_trading_date_time()
        self.transaction_details_ultimate_venue()
        self.transaction_details_venue()
        self.market_identifiers_instrument()
        self.market_identifiers_parties()
        self.market_identifiers()
        self.post_process()
        return self.target_df

    def _post_process(self):
        """Populates columns which are needed downstream in target_df."""
        self.target_df = pd.concat(
            [
                self.target_df,
                self._get_instrument_fallback_columns(),
                self._get_parties_fallback_columns(),
                self._get_newo_in_file(),
            ],
            axis=1,
        )

    def _buy_sell(self) -> pd.DataFrame:
        """Returns a data frame containing _order.buySell and _orderState.buySell.
        This is populated from SourceColumns.BUY_SELL conditionally based on SourceColumns.ENTERED_BY
        """

        conditional_df = MapConditional.process(
            source_frame=pd.concat(
                [
                    self.pre_process_df,
                    self.source_frame.loc[:, [SourceColumns.ENTERED_BY]],
                ],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=TempColumns.CONDITIONAL_BUY_SELL,
                cases=[
                    Case(
                        query=CommonQueries.ENTERED_BY_RSPQ_QUERY,
                        attribute=TempColumns.INVERSE_BUY_SELL,
                    ),
                    Case(
                        query=f"~{CommonQueries.ENTERED_BY_RSPQ_QUERY}",
                        attribute=TempColumns.BUY_SELL,
                    ),
                ],
            ),
        )
        return pd.concat(
            [
                pd.DataFrame(
                    data=conditional_df.loc[:, TempColumns.CONDITIONAL_BUY_SELL].values,
                    index=conditional_df.index,
                    columns=[
                        add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.BUY_SELL,
                        )
                    ],
                ),
                pd.DataFrame(
                    data=conditional_df.loc[:, TempColumns.CONDITIONAL_BUY_SELL].values,
                    index=conditional_df.index,
                    columns=[
                        add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.BUY_SELL,
                        )
                    ],
                ),
            ],
            axis=1,
        )

    def _data_source_name(self) -> pd.DataFrame:
        """Returns a df containing dataSourceName"""
        return pd.DataFrame(
            data="Fidessa Front Office Trades",
            index=self.source_frame.index,
            columns=[OrderColumns.DATA_SOURCE_NAME],
        )

    def _date(self) -> pd.DataFrame:
        """Populates date from Trade_Datetime"""
        return JoinDateAndTimeFormat.process(
            source_frame=self.source_frame,
            params=ParamsJoinDateAndTimeFormat(
                source_date_attribute=SourceColumns.TRADE_DATETIME,
                skip_time_attribute=True,
                target_attribute=OrderColumns.DATE,
                source_formats=DateTimeFormats.SOURCE_DATETIME_FORMATS,
                target_format=DateTimeFormats.TARGET_DATE_FORMAT,
            ),
        )

    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.buySellIndicator.
        This is populated from SourceColumns.BUY_SELL conditionally based on SourceColumns.ENTERED_BY
        """

        return MapConditional.process(
            source_frame=pd.concat(
                [
                    self.pre_process_df,
                    self.source_frame.loc[:, [SourceColumns.ENTERED_BY]],
                ],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                cases=[
                    Case(
                        query=CommonQueries.ENTERED_BY_RSPQ_QUERY,
                        attribute=TempColumns.INVERSE_BUY_SELL_INDICATOR,
                    ),
                    Case(
                        query=f"~{CommonQueries.ENTERED_BY_RSPQ_QUERY}",
                        attribute=TempColumns.BUY_SELL_INDICATOR,
                    ),
                ],
            ),
        )

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_limit_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_order_status(self) -> pd.DataFrame:
        """Returns a data frame containing _order.executionDetails.orderStatus and
        _orderState.executionDetails.orderStatus."""
        return pd.concat(
            [
                pd.DataFrame(
                    data=OrderStatus.NEWO.value,
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                        )
                    ],
                ),
                MapConditional.process(
                    source_frame=self.source_frame,
                    params=ParamsMapConditional(
                        target_attribute=add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                        ),
                        cases=[
                            Case(
                                query=CommonQueries.DISCRETIONARY_ORDER_QUERY,
                                value=OrderStatus.PARF.value,
                            ),
                            Case(
                                query=f"~{CommonQueries.DISCRETIONARY_ORDER_QUERY}",
                                value=OrderStatus.FILL.value,
                            ),
                        ],
                    ),
                ),
            ],
            axis=1,
        )

    def _execution_details_order_type(self) -> pd.DataFrame:
        """Populates OrderColumns.EXECUTION_DETAILS_ORDER_TYPE for NEWO based
        on multiple conditions"""
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.EXECUTION_DETAILS_ORDER_TYPE,
                ),
                cases=[
                    Case(
                        query="index == index",
                        value="MARKET",
                    ),
                    Case(
                        query=f"{CommonQueries.DISCRETIONARY_ORDER_QUERY} & (`{SourceColumns.ORDER_PRICE_TYPE}`).str.fullmatch('LIM', case=False, na=False)",
                        # noqa: E501
                        value="LIMIT",
                    ),
                    Case(
                        query=f"{CommonQueries.DISCRETIONARY_ORDER_QUERY} & (`{SourceColumns.ORDER_PRICE_TYPE}`).str.fullmatch('STP', case=False, na=False)",
                        # noqa: E501
                        value="STOP",
                    ),
                    Case(
                        query=f"{CommonQueries.ENTERED_BY_RSPQ_QUERY}",
                        value="QUOTE",
                    ),
                ],
            ),
        )

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Returns a df containing OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO"""

        with_prefix_df = pd.concat(
            [
                "Instrument Code: "
                + self.source_frame.loc[:, SourceColumns.INSTRUMENT_CODE],
                "Sedol: " + self.source_frame.loc[:, SourceColumns.SEDOL_CODE],
                "EPIC: " + self.source_frame.loc[:, SourceColumns.EPIC_CODE],
                "Client LEI: " + self.source_frame.loc[:, SourceColumns.CLIENT_LEI],
                "Business_Transaction: "
                + self.source_frame.loc[
                    :, SourceColumns.BUSINESS_TRANSACTION_DESCRIPTION
                ],
                "Entered By User Group: "
                + self.source_frame.loc[:, SourceColumns.ENTERED_BY_USER_GROUP],
                "Order Type: "
                + self.source_frame.loc[:, SourceColumns.ORDER_PRICE_TYPE],
            ],
            axis=1,
        )

        # Note: concatenating Series as above retains the original column names in the
        # resulting df
        return ConcatAttributes.process(
            source_frame=with_prefix_df,
            params=ParamsConcatAttributes(
                source_attributes=[
                    SourceColumns.INSTRUMENT_CODE,
                    SourceColumns.SEDOL_CODE,
                    SourceColumns.EPIC_CODE,
                    SourceColumns.CLIENT_LEI,
                    SourceColumns.BUSINESS_TRANSACTION_DESCRIPTION,
                    SourceColumns.ENTERED_BY_USER_GROUP,
                    SourceColumns.ORDER_PRICE_TYPE,
                ],
                target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
                delimiter=",\n",
            ),
        )

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        """Returns a df containing OrderColumns.EXECUTION_DETAILS_PASSIVE_AGGRESSIVE_INDICATOR"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.REPORT_TO_EXCHANGE,
                target_attribute=OrderColumns.EXECUTION_DETAILS_PASSIVE_AGGRESSIVE_INDICATOR,
                case_insensitive=True,
                value_map={
                    "N": PassiveAggressiveStatus.PASV.value,
                    "Y": PassiveAggressiveStatus.AGRE.value,
                },
            ),
            auditor=self.auditor,
        )

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.EXECUTION_DETAILS_ROUTING_STRATEGY.
        This is populated from SourceColumns.EXECUTING_BOOK_ID"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.EXECUTING_BOOK_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_ROUTING_STRATEGY],
        )

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        """Returns a df containing OrderColumns.EXECUTION_DETAILS_SETTLEMENT_AMOUNT. This
        is populated from SourceColumns.SETTLEMENT_CONSIDERATION"""
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_price_attribute=SourceColumns.SETTLEMENT_CONSIDERATION,
                source_ccy_attribute=SourceColumns.SETTLEMENT_CCY,
                target_price_attribute=OrderColumns.EXECUTION_DETAILS_SETTLEMENT_AMOUNT,
                cast_to=CastTo.ABS,
            ),
        )

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_stop_price(self) -> pd.DataFrame:
        """Not Implemented"""

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        """Returns a DataFrame containing OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY.
        This is populated from SourceColumns.DEALING_CAPACITY"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.DEALING_CAPACITY,
                target_attribute=OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY,
                case_insensitive=True,
                value_map={
                    "A": TradingCapacity.AOTC.value,
                    "P": TradingCapacity.DEAL.value,
                    "R": TradingCapacity.DEAL.value,
                },
            ),
            auditor=self.auditor,
        )

    def _execution_details_validity_period(self) -> pd.DataFrame:
        """Returns a df containing OrderColumns.EXECUTION_DETAILS_VALIDITY_PERIOD"""
        expiry_type_mappings = {
            "GTD": ["GTDV"],
            "GFD": ["DAVY"],
            "IOC": ["IOCV"],
            "FOK": ["FOKV"],
            "GTC": ["GTCV"],
            "GTT": ["GTTV"],
            "GTS": ["GTSV"],
            "GTX": ["GTXV"],
            "GAT": ["GATV"],
            "GAD": ["GADV"],
            "GAS": ["GASV"],
        }
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.EXPIRY_TYPE]
            .astype("str")
            .str.upper()
            .map(expiry_type_mappings)
            .values,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_VALIDITY_PERIOD],
        )

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _financing_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _hierarchy(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.HIERARCHY."""

        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.HIERARCHY,
                cases=[
                    Case(
                        query=CommonQueries.DISCRETIONARY_ORDER_QUERY,
                        value=HierarchyEnum.CHILD.value,
                    ),
                    Case(
                        query=f"~{CommonQueries.DISCRETIONARY_ORDER_QUERY}",
                        value=HierarchyEnum.STANDALONE.value,
                    ),
                ],
            ),
        )

    def _id(self) -> pd.DataFrame:
        """Returns a data frame containing _order.id and _orderState.id.
        Assumes that order_identifiers_transaction_ref_no() and order_identifiers_order_id_code
        have been called earlier"""
        return pd.concat(
            [
                pd.DataFrame(
                    data=self.target_df.loc[
                        :, OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE
                    ].values,
                    index=self.target_df.index,
                    columns=[
                        add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.ID,
                        )
                    ],
                ),
                pd.DataFrame(
                    data=self.target_df.loc[
                        :, OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE
                    ].values,
                    index=self.target_df.index,
                    columns=[
                        add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.ID,
                        )
                    ],
                ),
            ],
            axis=1,
        )

    def _is_discretionary(self) -> pd.DataFrame:
        """Not Implemented"""

    def _is_iceberg(self):
        """Not Implemented"""

    def _is_repo(self):
        """Not Implemented"""

    def _is_synthetic(self):
        """Not Implemented"""

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _jurisdiction_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        """Not Implemented"""

    def _market_identifiers(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers by calling MergeMarketIdentifiers.
        Assumes _market_identifiers_instrument() and _market_identifiers_parties() have been
        called earlier"""
        return MergeMarketIdentifiers.process(
            source_frame=self.target_df,
            params=ParamsMergeMarketIdentifiers(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                instrument_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
        )

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.instrument by calling InstrumentIdentifiers.
        Assumes that _transaction_details_quantity_currency() and _transaction_details_venue() have been
        called earlier"""
        instrument_source_df = pd.concat(
            [
                self.source_frame.loc[
                    :,
                    [
                        SourceColumns.ISIN_CODE,
                        SourceColumns.OPTION_TYPE,
                        SourceColumns.EXCHANGE_CONTRACT_CODE,
                        SourceColumns.UNDERLYING_ISIN,
                    ],
                ],
                self._get_asset_class(),
                self.target_df.loc[
                    :,
                    [
                        OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                        OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                    ],
                ],
                self.pre_process_df.loc[
                    :,
                    [TempColumns.EXPIRY_DATE, TempColumns.STRIKE_PRICE],
                ],
            ],
            axis=1,
        )
        return InstrumentIdentifiers.process(
            source_frame=instrument_source_df,
            params=ParamsInstrumentIdentifiers(
                asset_class_attribute=TempColumns.ASSET_CLASS,
                currency_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                expiry_date_attribute=TempColumns.EXPIRY_DATE,
                isin_attribute=SourceColumns.ISIN_CODE,
                option_strike_price_attribute=TempColumns.STRIKE_PRICE,
                option_type_attribute=SourceColumns.OPTION_TYPE,
                underlying_symbol_attribute=SourceColumns.EXCHANGE_CONTRACT_CODE,
                underlying_isin_attribute=SourceColumns.UNDERLYING_ISIN,
                venue_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                retain_task_inputs=True,
            ),
            auditor=self.auditor,
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.parties by calling PartyIdentifiers"""

        # Note: adding prefixes and concatenating Series results in the column names
        # being retained (from the source frame) in the resulting df
        with_prefix_df = pd.concat(
            [
                PartyPrefix.ID
                + self.source_frame.loc[:, SourceColumns.TRADING_ENTITY_ID],
                PartyPrefix.ID + self.source_frame.loc[:, SourceColumns.TRADER],
                PartyPrefix.ID
                + self.source_frame.loc[:, SourceColumns.COUNTERPARTY_DESCRIPTION],
                PartyPrefix.ID
                + self.source_frame.loc[:, SourceColumns.INVESTMENT_DECISION_VALUE],
                PartyPrefix.ID
                + self.source_frame.loc[:, SourceColumns.EXECUTION_DECISION_VALUE],
                "(" + self.source_frame.loc[:, SourceColumns.COUNTERPARTY] + ")",
            ],
            axis=1,
        )

        concat_df = ConcatAttributes.process(
            source_frame=with_prefix_df,
            params=ParamsConcatAttributes(
                source_attributes=[
                    SourceColumns.COUNTERPARTY_DESCRIPTION,
                    SourceColumns.COUNTERPARTY,
                ],
                target_attribute=TempColumns.CLIENT_ID,
                delimiter=" ",
            ),
        )

        return GenericOrderPartyIdentifiers.process(
            source_frame=pd.concat(
                [
                    with_prefix_df,
                    concat_df,
                    self.target_df.loc[
                        :, [OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR]
                    ],
                ],
                axis=1,
            ),
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                executing_entity_identifier=SourceColumns.TRADING_ENTITY_ID,
                trader_identifier=SourceColumns.TRADER,
                client_identifier=TempColumns.CLIENT_ID,
                counterparty_identifier=TempColumns.CLIENT_ID,
                execution_within_firm_identifier=SourceColumns.EXECUTION_DECISION_VALUE,
                investment_decision_within_firm_identifier=SourceColumns.INVESTMENT_DECISION_VALUE,
                buy_sell_side_attribute=OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
                buyer_identifier=SourceColumns.TRADING_ENTITY_ID,
                seller_identifier=TempColumns.CLIENT_ID,
                use_buy_mask_for_buyer_seller=True,
            ),
        )

    def _meta_model(self) -> pd.DataFrame:
        """Returns a data frame containing _order.__meta_model__ and _orderState.__meta_model__.
        The columns are populated with the static values Order and OrderState respectively"""
        return pd.DataFrame(
            data=[["Order", "OrderState"]],
            index=self.source_frame.index,
            columns=[
                add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.META_MODEL),
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.META_MODEL,
                ),
            ],
        )

    def _order_class(self) -> pd.DataFrame:
        """Populates orderClass from Business_Transaction_Description.
        MapValue is used to make sure the values are case-insensitive
        while mapping to the OrderClass Enum"""
        return MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.BUSINESS_TRANSACTION_DESCRIPTION,
                target_attribute=OrderColumns.ORDER_CLASS,
                case_insensitive=True,
                value_map={
                    "Against Actual": OrderClass.AGAINST_ACTUAL.value,
                    "Block Trade": OrderClass.BLOCK_TRADE.value,
                    "Booking": OrderClass.BOOKING.value,
                    "Broker fill": OrderClass.BROKER_FILL.value,
                    "Broker trade": OrderClass.BROKER_TRADE.value,
                    "Client trade": OrderClass.CLIENT_TRADE.value,
                    "Cross": OrderClass.CROSS.value,
                    "Dark trade": OrderClass.DARK_TRADE.value,
                    "Exchange for Physical": OrderClass.EXCHANGE_FOR_PHYSICAL.value,
                    "Exchange for Swap": OrderClass.EXCHANGE_FOR_SWAP.value,
                    "Exchange of Futures for Futures": OrderClass.EXCHANGE_OF_FUTURES_FOR_FUTURES.value,
                    "Exchange of Options": OrderClass.EXCHANGE_OF_OPTIONS.value,
                    "House fill": OrderClass.HOUSE_FILL.value,
                    "Inter entity book transfer": OrderClass.INTER_ENTITY_BOOK_TRANSFER.value,
                    "Intra entity book transfer": OrderClass.INTRA_ENTITY_BOOK_TRANSFER.value,
                    "Order warehousing transfer": OrderClass.ORDER_WAREHOUSING_TRANSFER.value,
                    "Package trade": OrderClass.PACKAGE_TRADE.value,
                    "Position flip": OrderClass.POSITION_FLIP.value,
                    "Vola Trade": OrderClass.VOLA_TRADE.value,
                },
            ),
            auditor=self.auditor,
        )

    def _order_identifiers_aggregated_order_id_code(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID.
        This is populated from SourceColumns.AGGREGATION_ID"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.AGGREGATION_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID],
        )

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.ORDER_IDENTIFIERS_INITIAL_ORDER_DESIGNATION.
        This is populated from SourceColumns.EXCHANGE_TRADE_CODE"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.EXCHANGE_TRADE_CODE].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_INITIAL_ORDER_DESIGNATION],
        )

    def _order_identifiers_internal_order_id_code(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE.
        This is populated from SourceColumns.ORIGINATOR_ORDER_ID"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.ORIGINATOR_ORDER_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_INTERNAL_ORDER_ID_CODE],
        )

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE.
        Assumes that order_identifiers_transaction_ref_no() has been called earlier"""

        return MapConditional.process(
            source_frame=pd.concat(
                [
                    self.source_frame,
                    self.target_df.loc[
                        :, [OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO]
                    ],
                ],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE,
                cases=[
                    Case(
                        query=CommonQueries.DISCRETIONARY_ORDER_QUERY,
                        attribute=SourceColumns.ORDER_ID,
                    ),
                    Case(
                        query=f"~{CommonQueries.DISCRETIONARY_ORDER_QUERY}",
                        attribute=OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
                    ),
                ],
            ),
        )

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.ORDER_IDENTIFIERS_ORDER_ROUTING_CODE.
        This is populated from SourceColumns.BOOK_ID"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.BOOK_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_ORDER_ROUTING_CODE],
        )

    def _order_identifiers_parent_order_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.ORDER_IDENTIFIERS_SEQUENCE_NUMBER.
        This is populated from SourceColumns.VERSION_NUMBER"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.VERSION_NUMBER].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_SEQUENCE_NUMBER],
        )

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.ORDER_IDENTIFIERS_TRADING_VENUE_TRANSACTION_ID_CODE.
        This is populated from SourceColumns.TRADING_VENUE_TRANSACTION_ID"""
        return pd.DataFrame(
            data=self.source_frame.loc[
                :, SourceColumns.TRADING_VENUE_TRANSACTION_ID
            ].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_TRADING_VENUE_TRANSACTION_ID_CODE],
        )

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        """Populates OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO from 3 source
        columns"""
        return ConcatAttributes.process(
            source_frame=self.source_frame,
            params=ParamsConcatAttributes(
                source_attributes=[
                    SourceColumns.TRADE_ID,
                    SourceColumns.VERSION_NUMBER,
                    SourceColumns.ENTERED_DATE,
                ],
                target_attribute=OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
                delimiter=Delimiters.PIPE,
            ),
        )

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_initial_quantity(self) -> pd.DataFrame:
        """Returns a data frame containing _order.priceFormingData.initialQuantity.
        This is populated from SourceColumns.QUANTITY"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.QUANTITY].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
                )
            ],
        )

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_price(self) -> pd.DataFrame:
        """Returns a df containing OrderColumns.PRICE_FORMING_DATA_PRICE. This
        is populated from SourceColumns.GROSS_PRICE"""
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_price_attribute=SourceColumns.GROSS_PRICE,
                source_ccy_attribute=SourceColumns.DEALT_CCY,
                target_price_attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
                cast_to=CastTo.ABS,
            ),
        )

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_remaining_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        """Returns a data frame containing Order_Sate.OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY.
        Assumes that price_forming_data_initial_quantity() has been called earlier"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :,
                add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
                ),
            ].values,
            index=self.target_df.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                )
            ],
        )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO.
        Assumes that order_identifiers_transaction_ref_no() has been called earlier"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO],
        )

    def _source_index(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceIndex column"""
        return pd.DataFrame(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_INDEX],
        )

    def _source_key(self) -> pd.DataFrame:
        """Returns a data frame containing the sourceKey column from the SWARM_FILE_URL"""
        return pd.DataFrame(
            data=os.getenv(SWARM_FILE_URL),
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_KEY],
        )

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        """Not Implemented"""

    def _timestamps_order_received(self) -> pd.DataFrame:
        """Returns a data frame containing _order.timestamps.orderReceived.
        Assumes that _timestamps_trading_date_time() has been called earlier"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.TIMESTAMPS_TRADING_DATE_TIME
            ].values,
            index=self.target_df.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                )
            ],
        )

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        """Returns a data frame containing timestamps.orderStatusUpdated.
        This is populated from SourceColumns.AMENDED_DATETIME"""
        return JoinDateAndTimeFormat.process(
            source_frame=self.source_frame,
            params=ParamsJoinDateAndTimeFormat(
                source_date_attribute=SourceColumns.AMENDED_DATETIME,
                skip_time_attribute=True,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED,
                source_formats=DateTimeFormats.SOURCE_DATETIME_FORMATS,
                target_format=DateTimeFormats.TARGET_DATETIME_FORMAT,
            ),
        )

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        """Returns a data frame containing _order.timestamps.orderSubmitted.
        Assumes that _timestamps_trading_date_time() has been called earlier"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.TIMESTAMPS_TRADING_DATE_TIME
            ].values,
            index=self.target_df.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.TIMESTAMPS_ORDER_SUBMITTED,
                )
            ],
        )

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        """Populates OrderColumns.TIMESTAMPS_TRADING_DATE_TIME from SourceColumns.TRADE_DATETIME"""
        return JoinDateAndTimeFormat.process(
            source_frame=self.source_frame,
            params=ParamsJoinDateAndTimeFormat(
                source_date_attribute=SourceColumns.TRADE_DATETIME,
                skip_time_attribute=True,
                target_attribute=OrderColumns.TIMESTAMPS_TRADING_DATE_TIME,
                source_formats=DateTimeFormats.SOURCE_DATETIME_FORMATS,
                target_format=DateTimeFormats.TARGET_DATETIME_FORMAT,
            ),
        )

    def _timestamps_validity_period(self) -> pd.DataFrame:
        """Returns a df containing OrderColumns.TIMESTAMPS_VALIDITY_PERIOD. This
        is populated from SourceColumns.EXPIRY_DATE"""
        datetime_df = ConvertDatetime.process(
            self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.EXPIRY_DATE,
                source_attribute_format=DateTimeFormats.SOURCE_DATE_FORMAT,
                target_attribute=TempColumns.TIMESTAMPS_VALIDITY_PERIOD,
                convert_to=ConvertTo.DATETIME.value,
            ),
        )
        return MapAttribute.process(
            source_frame=datetime_df,
            params=ParamsMapAttribute(
                source_attribute=TempColumns.TIMESTAMPS_VALIDITY_PERIOD,
                target_attribute=OrderColumns.TIMESTAMPS_VALIDITY_PERIOD,
                cast_to="string.list",
                list_delimiter=";",
            ),
            auditor=self.auditor,
        )

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(
        self,
    ) -> pd.DataFrame:
        """Not Implemented"""

    def _traders_algos_waivers_indicators_waiver_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.TRANSACTION_DETAILS_BASKET_ID.
        This is populated from SourceColumns.BOOK_ID"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.BOOK_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_BASKET_ID],
        )

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR.
        Assumes that execution_details_buy_sell_indicator() has been called earlier"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR],
        )

    def _transaction_details_commission_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_commission_amount_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_commission_amount_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.TRANSACTION_DETAILS_COMPLEX_TRADE_COMPONENT_ID.
        This is populated from SourceColumns.INSTRUMENT_CODE"""
        return pd.DataFrame(
            data=self.source_frame.loc[:, SourceColumns.INSTRUMENT_CODE].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_COMPLEX_TRADE_COMPONENT_ID],
        )

    def _transaction_details_cross_indicator(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_position_effect(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_position_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_price(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.TRANSACTION_DETAILS_PRICE.
        Assumes that price_forming_data_price() has been called earlier"""
        return pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.PRICE_FORMING_DATA_PRICE].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE],
        )

    def _transaction_details_price_average(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.TRANSACTION_DETAILS_PRICE_AVERAGE.
        Assumes that price_forming_data_price() has been called earlier"""
        return pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.PRICE_FORMING_DATA_PRICE].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_AVERAGE],
        )

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        """Populates OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY from
        SourceColumns.DEALT_CCY"""
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=SourceColumns.DEALT_CCY,
                target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
            ),
        )

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.priceNotation (MONE)"""
        return pd.DataFrame(
            data=PriceNotation.MONE.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION],
        )

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_quantity(self) -> pd.DataFrame:
        """Returns a data frame containing Order_State.Order.OrderColumns.TRANSACTION_DETAILS_QUANTITY.
        Assumes that price_forming_data_initial_quantity() has been called earlier"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :,
                add_prefix(
                    prefix=ModelPrefix.ORDER,
                    attribute=OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY,
                ),
            ].values,
            index=self.target_df.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY,
                )
            ],
        )

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.quantityNotation (UNIT)"""
        return pd.DataFrame(
            data=QuantityNotation.UNIT.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION],
        )

    def _transaction_details_record_type(self) -> pd.DataFrame:
        """Returns a data frame containing transactionDetails.recordType.
        If the Book_Id is 'OMAR' and the Counterparty code is 'C', it is populated with 'Client Side' value.
        It is populated with 'Market Side' value otherwise."""
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE,
                cases=[
                    Case(
                        query=CommonQueries.RECORD_TYPE_QUERY,
                        value=OrderRecordType.CLIENT_SIDE.value,
                    ),
                    Case(
                        query=f"~({CommonQueries.RECORD_TYPE_QUERY})",
                        value=OrderRecordType.MARKET_SIDE.value,
                    ),
                ],
            ),
        )

    def _transaction_details_settlement_amount(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.TRANSACTION_DETAILS_SETTLEMENT_AMOUNT.
        Assumes that execution_details_settlement_amount() has been called earlier"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.EXECUTION_DETAILS_SETTLEMENT_AMOUNT
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_SETTLEMENT_AMOUNT],
        )

    def _transaction_details_settlement_amount_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_settlement_date(self) -> pd.DataFrame:
        """Populates OrderColumns.TIMESTAMPS_TRADING_DATE_TIME from SourceColumns.SETTLEMENT_DATE"""
        return JoinDateAndTimeFormat.process(
            source_frame=self.source_frame,
            params=ParamsJoinDateAndTimeFormat(
                source_date_attribute=SourceColumns.SETTLEMENT_DATE,
                skip_time_attribute=True,
                target_attribute=OrderColumns.TRANSACTION_DETAILS_SETTLEMENT_DATE,
                source_formats=DateTimeFormats.SOURCE_DATE_FORMATS,
                target_format=DateTimeFormats.TARGET_DATE_FORMAT,
            ),
        )

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_swap_directionalities(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY.
        Assumes that execution_details_trading_capacity() has been called earlier"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_CAPACITY],
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME.
        Assumes that _timestamps_trading_date_time() has been called earlier"""
        return pd.DataFrame(
            data=self.target_df.loc[
                :, OrderColumns.TIMESTAMPS_TRADING_DATE_TIME
            ].values,
            index=self.target_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME],
        )

    def _transaction_details_trail_id(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        """Returns a df containing OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE"""
        execution_venue_query = f"`{SourceColumns.EXECUTION_VENUE}`.isnull() | (`{SourceColumns.EXECUTION_VENUE}`.str.fullmatch('XOFF|XXXX', case=False, na=False))"  # noqa: E501
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                cases=[
                    Case(
                        query=execution_venue_query,
                        attribute=SourceColumns.OFFICIAL_PLACE_OF_LISTING,
                    ),
                    Case(
                        query=f"~{execution_venue_query}",
                        attribute=SourceColumns.EXECUTION_VENUE,
                    ),
                ],
            ),
        )

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        """Not Implemented"""

    def _transaction_details_venue(self) -> pd.DataFrame:
        """Returns a data frame containing OrderColumns.TRANSACTION_DETAILS_VENUE"""
        return pd.DataFrame(
            data="XOFF",
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_VENUE],
        )

    # ----------------------Functions called from pre_process()----------------------
    def _get_buy_sell_and_inverse(self):
        """Returns a df containing 4 columns: __buy_sell, __inverse_buy_sell__,
        __buy_sell_indicator__ and __inverse_buy_sell_indicator__"""
        return pd.concat(
            [
                MapValue.process(
                    source_frame=self.source_frame,
                    params=ParamsMapValue(
                        source_attribute=SourceColumns.BUY_SELL,
                        target_attribute=TempColumns.BUY_SELL,
                        case_insensitive=True,
                        value_map={"B": "1", "S": "2"},
                    ),
                    auditor=self.auditor,
                ),
                MapValue.process(
                    source_frame=self.source_frame,
                    params=ParamsMapValue(
                        source_attribute=SourceColumns.BUY_SELL,
                        target_attribute=TempColumns.INVERSE_BUY_SELL,
                        case_insensitive=True,
                        value_map={"B": "2", "S": "1"},
                    ),
                    auditor=self.auditor,
                ),
                MapValue.process(
                    source_frame=self.source_frame,
                    params=ParamsMapValue(
                        source_attribute=SourceColumns.BUY_SELL,
                        target_attribute=TempColumns.BUY_SELL_INDICATOR,
                        case_insensitive=True,
                        value_map={
                            "B": BuySellIndicator.BUYI.value,
                            "S": BuySellIndicator.SELL.value,
                        },
                    ),
                    auditor=self.auditor,
                ),
                MapValue.process(
                    source_frame=self.source_frame,
                    params=ParamsMapValue(
                        source_attribute=SourceColumns.BUY_SELL,
                        target_attribute=TempColumns.INVERSE_BUY_SELL_INDICATOR,
                        case_insensitive=True,
                        value_map={
                            "B": BuySellIndicator.SELL.value,
                            "S": BuySellIndicator.BUYI.value,
                        },
                    ),
                    auditor=self.auditor,
                ),
            ],
            axis=1,
        )

    def _get_expiry_date(self):
        """Returns a df containing __expiry_date__"""
        return ConvertDatetime.process(
            self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=SourceColumns.EXPIRY_DATE,
                source_attribute_format=DateTimeFormats.SOURCE_DATE_FORMAT,
                target_attribute=TempColumns.EXPIRY_DATE,
                convert_to=ConvertTo.DATE.value,
            ),
        )

    # ----------------------Functions called from market_identifiers_instrument()----------------------
    def _get_asset_class(self):
        """Returns a df containing __asset_class__"""
        instrument_type_ad_gd_fi_query = f"`{SourceColumns.INSTRUMENT_TYPE}`.str.fullmatch('AD|GD|FI', case=False, na=False)"  # noqa E501
        instrument_type_fu_query = f"`{SourceColumns.INSTRUMENT_TYPE}`.str.fullmatch('FU', case=False, na=False)"
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=TempColumns.ASSET_CLASS,
                cases=[
                    Case(
                        query=instrument_type_ad_gd_fi_query,
                        value=AssetClass.BOND,
                    ),
                    Case(
                        query=instrument_type_fu_query,
                        value=AssetClass.FUTURE,
                    ),
                    Case(
                        query=CommonQueries.INSTRUMENT_TYPE_OP_QUERY,
                        value=AssetClass.OPTION,
                    ),
                ],
            ),
        )

    def _get_option_strike_price(self):
        """Returns a df containing the option strike price"""
        return ConvertMinorToMajor.process(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_price_attribute=SourceColumns.STRIKE_PRICE,
                source_ccy_attribute=SourceColumns.DEALT_CCY,
                target_price_attribute=TempColumns.STRIKE_PRICE,
                cast_to=CastTo.ABS,
            ),
        )

    # -------------------------Functions called from post_process()-------------------------
    def _get_newo_in_file(self) -> pd.DataFrame:
        """Returns a data frame containing a column __newo_in_file__ which indicates
        whether the record corresponds to a NEWO in the input file. This is required for
        the RemoveDuplicateNewo task to filter out synthetic NEWOs when the 'real' NEWO
        is present"""
        return MapConditional.process(
            source_frame=self.source_frame,
            params=ParamsMapConditional(
                target_attribute=PostProcessTempColumns.NEWO_IN_FILE,
                cases=[
                    Case(
                        query=CommonQueries.DISCRETIONARY_ORDER_QUERY,
                        value=False,
                    ),
                    Case(
                        query=f"~{CommonQueries.DISCRETIONARY_ORDER_QUERY}",
                        value=True,
                    ),
                ],
            ),
        )

    def _get_instrument_fallback_columns(self) -> pd.DataFrame:
        """Returns a df containing all the columns required by the InstrumentFallback
        task downstream"""
        return pd.concat(
            [
                pd.DataFrame(
                    data=self.source_frame.loc[:, SourceColumns.CFI_CODE].values,
                    index=self.source_frame.index,
                    columns=[PostProcessTempColumns.FALLBACK_INSTR_CLASS],
                ),
                pd.DataFrame(
                    data=self.source_frame.loc[:, SourceColumns.ISIN_CODE].values,
                    index=self.source_frame.index,
                    columns=[PostProcessTempColumns.FALLBACK_INSTR_ID_CODE],
                ),
                pd.DataFrame(
                    data="1",
                    index=self.source_frame.index,
                    columns=[PostProcessTempColumns.FALLBACK_PRICE_MULTIPLIER],
                ),
                pd.DataFrame(
                    data=True,
                    index=self.source_frame.index,
                    columns=[
                        PostProcessTempColumns.FALLBACK_IS_CREATED_THROUGH_FALLBACK
                    ],
                ),
                pd.DataFrame(
                    data="ID",
                    index=self.source_frame.index,
                    columns=[PostProcessTempColumns.FALLBACK_ID_CODE],
                ),
                ConcatAttributes.process(
                    source_frame=self.source_frame,
                    params=ParamsConcatAttributes(
                        source_attributes=[
                            SourceColumns.INSTRUMENT_CODE,
                            SourceColumns.INSTRUMENT_DESCRIPTION,
                        ],
                        target_attribute=PostProcessTempColumns.FALLBACK_INSTR_FULL_NAME,
                        delimiter=" ",
                    ),
                ),
                MapConditional.process(
                    source_frame=self.source_frame,
                    params=ParamsMapConditional(
                        target_attribute=PostProcessTempColumns.FALLBACK_STRIKE_PRICE_TYPE,
                        cases=[
                            Case(
                                query=CommonQueries.INSTRUMENT_TYPE_OP_QUERY,
                                value="MntryVal",
                            )
                        ],
                    ),
                ),
                MapConditional.process(
                    source_frame=pd.concat(
                        [
                            self.source_frame.loc[:, [SourceColumns.INSTRUMENT_TYPE]],
                            self.target_df.loc[
                                :, [OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY]
                            ],
                        ],
                        axis=1,
                    ),
                    params=ParamsMapConditional(
                        target_attribute=PostProcessTempColumns.FALLBACK_STRIKE_PRICE_CCY,
                        cases=[
                            Case(
                                query=CommonQueries.INSTRUMENT_TYPE_OP_QUERY,
                                attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                            )
                        ],
                    ),
                ),
                MapConditional.process(
                    source_frame=pd.concat(
                        [
                            self.source_frame.loc[:, [SourceColumns.INSTRUMENT_TYPE]],
                            self.pre_process_df.loc[:, [TempColumns.STRIKE_PRICE]],
                        ],
                        axis=1,
                    ),
                    params=ParamsMapConditional(
                        target_attribute=PostProcessTempColumns.FALLBACK_STRIKE_PRICE,
                        cases=[
                            Case(
                                query=CommonQueries.INSTRUMENT_TYPE_OP_QUERY,
                                attribute=TempColumns.STRIKE_PRICE,
                            )
                        ],
                    ),
                ),
                MapConditional.process(
                    source_frame=self.source_frame,
                    params=ParamsMapConditional(
                        target_attribute=PostProcessTempColumns.FALLBACK_EXERCISE_STYLE,
                        cases=[
                            Case(
                                query=CommonQueries.INSTRUMENT_TYPE_OP_QUERY,
                                value="EURO",
                            )
                        ],
                    ),
                ),
                MapConditional.process(
                    source_frame=self.source_frame,
                    params=ParamsMapConditional(
                        target_attribute=PostProcessTempColumns.FALLBACK_OPTION_TYPE,
                        cases=[
                            Case(
                                query=f"{CommonQueries.INSTRUMENT_TYPE_OP_QUERY} & (`{SourceColumns.OPTION_TYPE}`.str.fullmatch('P', case=False, na=False))",
                                # noqa E501
                                value=OptionType.PUTO.value,
                            ),
                            Case(
                                query=f"{CommonQueries.INSTRUMENT_TYPE_OP_QUERY} & (`{SourceColumns.OPTION_TYPE}`.str.fullmatch('C', case=False, na=False))",
                                # noqa E501
                                value=OptionType.CALL.value,
                            ),
                        ],
                    ),
                ),
                pd.DataFrame(
                    data=self.source_frame.loc[
                        :, SourceColumns.INSTRUMENT_DESCRIPTION
                    ].values,
                    index=self.source_frame.index,
                    columns=[PostProcessTempColumns.OVERRIDE_INSTRUMENT_DESCRIPTION],
                ),
            ],
            axis=1,
        )

    def _get_parties_fallback_columns(self):
        """Returns a df containing all columns required for PartiesFallback"""

        parens_df = "(" + self.source_frame.loc[:, [SourceColumns.COUNTERPARTY]] + ")"
        concat_df = ConcatAttributes.process(
            source_frame=pd.concat(
                [
                    parens_df,
                    self.source_frame.loc[:, [SourceColumns.COUNTERPARTY_DESCRIPTION]],
                ],
                axis=1,
            ),
            params=ParamsConcatAttributes(
                source_attributes=[
                    SourceColumns.COUNTERPARTY_DESCRIPTION,
                    SourceColumns.COUNTERPARTY,
                ],
                target_attribute=TempColumns.CLIENT_ID,
                delimiter=" ",
            ),
        )

        return pd.concat(
            [
                pd.DataFrame(
                    data=concat_df.loc[:, TempColumns.CLIENT_ID].values,
                    index=self.source_frame.index,
                    columns=[PostProcessTempColumns.CLIENT_WITHOUT_PREFIX],
                ),
                pd.DataFrame(
                    data=self.source_frame.loc[
                        :, SourceColumns.TRADING_ENTITY_ID
                    ].values,
                    index=self.source_frame.index,
                    columns=[PostProcessTempColumns.TRADING_ENTITY_WITHOUT_PREFIX],
                ),
                pd.DataFrame(
                    data=self.source_frame.loc[:, SourceColumns.TRADER].values,
                    index=self.source_frame.index,
                    columns=[PostProcessTempColumns.TRADER_WITHOUT_PREFIX],
                ),
                pd.DataFrame(
                    data=self.source_frame.loc[
                        :, SourceColumns.INVESTMENT_DECISION_VALUE
                    ].values,
                    index=self.source_frame.index,
                    columns=[PostProcessTempColumns.INVESTMENT_DECISION_WITHOUT_PREFIX],
                ),
                pd.DataFrame(
                    data=self.source_frame.loc[
                        :, SourceColumns.EXECUTION_DECISION_VALUE
                    ].values,
                    index=self.source_frame.index,
                    columns=[PostProcessTempColumns.EXECUTION_DECISION_WITHOUT_PREFIX],
                ),
            ],
            axis=1,
        )

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        """Not implemented"""
