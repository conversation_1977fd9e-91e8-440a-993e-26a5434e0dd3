import pandas as pd
from se_elastic_schema.static.mifid2 import Trading<PERSON>apacity
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.order.transformations.fidessa.front_office_trades.fidessa_front_office_trades_transformations import (
    FidessaFrontOfficeTradesTransformations,
)
from swarm_tasks.order.transformations.fidessa.front_office_trades.fidessa_front_office_trades_transformations import (
    SourceColumns,
)
from swarm_tasks.order.transformations.fidessa.front_office_trades.fidessa_front_office_trades_transformations import (
    TempColumns,
)
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ParamsConcatAttributes,
)
from swarm_tasks.transform.map.map_value import MapValue
from swarm_tasks.transform.map.map_value import Params as ParamsMapValue
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    GenericOrderPartyIdentifiers,
)
from swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)


class SingerFidessaFrontOfficeTradesTransformations(
    FidessaFrontOfficeTradesTransformations
):
    def _buy_sell(self) -> pd.DataFrame:
        """Returns a data frame containing _order.buySell and _orderState.buySell.
        This is populated from SourceColumns.BUY_SELL conditionally based on SourceColumns.ENTERED_BY
        """
        return pd.concat(
            [
                pd.DataFrame(
                    data=self.pre_process_df.loc[:, TempColumns.BUY_SELL].values,
                    index=self.pre_process_df.index,
                    columns=[
                        add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.BUY_SELL,
                        )
                    ],
                ),
                pd.DataFrame(
                    data=self.pre_process_df.loc[:, TempColumns.BUY_SELL].values,
                    index=self.pre_process_df.index,
                    columns=[
                        add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.BUY_SELL,
                        )
                    ],
                ),
            ],
            axis=1,
        )

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.buySellIndicator.
        This is populated from SourceColumns.BUY_SELL conditionally based on SourceColumns.ENTERED_BY
        """
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, TempColumns.BUY_SELL_INDICATOR].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR],
        )

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        """Returns a DataFrame containing OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY.
        This is populated with the static value 'DEAL'"""
        return pd.DataFrame(
            data=TradingCapacity.DEAL.value,
            index=self.source_frame.index,
            columns=[OrderColumns.EXECUTION_DETAILS_TRADING_CAPACITY],
        )

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        """Returns a df containing OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO.
        Expects execution_details_trading_capacity() to have been called earlier"""

        dealing_capacity_df = MapValue.process(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.DEALING_CAPACITY,
                target_attribute=TempColumns.DEALING_CAPACITY,
                case_insensitive=True,
                value_map={
                    "A": "Agency",
                    "P": "Principal",
                    "R": "Deal",
                    "RP": "Riskless Principal",
                    "W": "Worked",
                },
            ),
            auditor=self.auditor,
        )
        with_prefix_df = pd.concat(
            [
                "Instrument Code: "
                + self.source_frame.loc[:, SourceColumns.INSTRUMENT_CODE],
                "Sedol: " + self.source_frame.loc[:, SourceColumns.SEDOL_CODE],
                "EPIC: " + self.source_frame.loc[:, SourceColumns.EPIC_CODE],
                "Client LEI: " + self.source_frame.loc[:, SourceColumns.CLIENT_LEI],
                "Business_Transaction: "
                + self.source_frame.loc[
                    :, SourceColumns.BUSINESS_TRANSACTION_DESCRIPTION
                ],
                "Entered By User Group: "
                + self.source_frame.loc[:, SourceColumns.ENTERED_BY_USER_GROUP],
                "Order Type: "
                + self.source_frame.loc[:, SourceColumns.ORDER_PRICE_TYPE],
                "Dealing Capacity: "
                + dealing_capacity_df.loc[:, TempColumns.DEALING_CAPACITY],
            ],
            axis=1,
        )

        # Note: concatenating Series as above retains the original column names in the
        # resulting df
        return ConcatAttributes.process(
            source_frame=with_prefix_df,
            params=ParamsConcatAttributes(
                source_attributes=[
                    SourceColumns.INSTRUMENT_CODE,
                    SourceColumns.SEDOL_CODE,
                    SourceColumns.EPIC_CODE,
                    SourceColumns.CLIENT_LEI,
                    SourceColumns.BUSINESS_TRANSACTION_DESCRIPTION,
                    SourceColumns.ENTERED_BY_USER_GROUP,
                    SourceColumns.ORDER_PRICE_TYPE,
                    TempColumns.DEALING_CAPACITY,
                ],
                target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
                delimiter=",\n",
            ),
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        """Returns a data frame containing marketIdentifiers.parties by calling PartyIdentifiers"""

        # Note: adding prefixes and concatenating Series results in the column names
        # being retained (from the source frame) in the resulting df
        with_prefix_df = pd.concat(
            [
                PartyPrefix.ID
                + self.source_frame.loc[:, SourceColumns.TRADING_ENTITY_ID],
                PartyPrefix.ID + self.source_frame.loc[:, SourceColumns.TRADER],
                PartyPrefix.ID
                + self.source_frame.loc[:, SourceColumns.COUNTERPARTY_DESCRIPTION],
                PartyPrefix.ID
                + self.source_frame.loc[:, SourceColumns.INVESTMENT_DECISION_VALUE],
                PartyPrefix.ID
                + self.source_frame.loc[:, SourceColumns.EXECUTION_DECISION_VALUE],
                "(" + self.source_frame.loc[:, SourceColumns.COUNTERPARTY] + ")",
            ],
            axis=1,
        )

        concat_df = ConcatAttributes.process(
            source_frame=with_prefix_df,
            params=ParamsConcatAttributes(
                source_attributes=[
                    SourceColumns.COUNTERPARTY_DESCRIPTION,
                    SourceColumns.COUNTERPARTY,
                ],
                target_attribute=TempColumns.CLIENT_ID,
                delimiter=" ",
            ),
        )

        return GenericOrderPartyIdentifiers.process(
            source_frame=pd.concat(
                [
                    with_prefix_df,
                    concat_df,
                    self.target_df.loc[
                        :, [OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR]
                    ],
                ],
                axis=1,
            ),
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                executing_entity_identifier=SourceColumns.TRADING_ENTITY_ID,
                trader_identifier=SourceColumns.TRADER,
                client_identifier=TempColumns.CLIENT_ID,
                counterparty_identifier=TempColumns.CLIENT_ID,
                execution_within_firm_identifier=SourceColumns.EXECUTION_DECISION_VALUE,
                investment_decision_within_firm_identifier=SourceColumns.INVESTMENT_DECISION_VALUE,
                buy_sell_side_attribute=OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR,
                buyer_identifier=SourceColumns.TRADING_ENTITY_ID,
                buyer_decision_maker_identifier=SourceColumns.EXECUTION_DECISION_VALUE,
                seller_identifier=TempColumns.CLIENT_ID,
                use_buy_mask_for_buyer_seller=True,
                use_buy_mask_for_buyer_seller_decision_maker=True,
            ),
        )
