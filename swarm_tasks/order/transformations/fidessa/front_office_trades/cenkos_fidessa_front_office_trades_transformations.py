import pandas as pd
from se_core_tasks.map.map_conditional import Case
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns

from swarm_tasks.order.transformations.fidessa.front_office_trades.fidessa_front_office_trades_transformations import (
    CommonQueries,
)
from swarm_tasks.order.transformations.fidessa.front_office_trades.fidessa_front_office_trades_transformations import (
    FidessaFrontOfficeTradesTransformations,
)
from swarm_tasks.order.transformations.fidessa.front_office_trades.fidessa_front_office_trades_transformations import (
    SourceColumns,
)
from swarm_tasks.order.transformations.fidessa.front_office_trades.fidessa_front_office_trades_transformations import (
    TempColumns,
)
from swarm_tasks.transform.map.map_conditional import MapConditional
from swarm_tasks.transform.map.map_conditional import Params as ParamsMapConditional


FLIP_BUY_SELL_QUERY = f"{CommonQueries.ENTERED_BY_RSPQ_QUERY} | ~`{SourceColumns.COUNTERPARTY}`.str.fullmatch('LCH|ITG_ALGO|CNKS|LCHGB2E', case=False, na=False)"  # noqa E501


class CenkosFidessaFrontOfficeTradesTransformations(
    FidessaFrontOfficeTradesTransformations
):
    def _buy_sell(self) -> pd.DataFrame:
        """Returns a data frame containing _order.buySell and _orderState.buySell.
        This is populated from SourceColumns.BUY_SELL conditionally based on SourceColumns.ENTERED_BY
        """

        conditional_df = MapConditional.process(
            source_frame=pd.concat(
                [
                    self.pre_process_df,
                    self.source_frame.loc[
                        :, [SourceColumns.ENTERED_BY, SourceColumns.COUNTERPARTY]
                    ],
                ],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=TempColumns.CONDITIONAL_BUY_SELL,
                cases=[
                    Case(
                        query=FLIP_BUY_SELL_QUERY,
                        attribute=TempColumns.INVERSE_BUY_SELL,
                    ),
                    Case(
                        query=f"~({FLIP_BUY_SELL_QUERY})",
                        attribute=TempColumns.BUY_SELL,
                    ),
                ],
            ),
        )
        return pd.concat(
            [
                pd.DataFrame(
                    data=conditional_df.loc[:, TempColumns.CONDITIONAL_BUY_SELL].values,
                    index=conditional_df.index,
                    columns=[
                        add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.BUY_SELL,
                        )
                    ],
                ),
                pd.DataFrame(
                    data=conditional_df.loc[:, TempColumns.CONDITIONAL_BUY_SELL].values,
                    index=conditional_df.index,
                    columns=[
                        add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.BUY_SELL,
                        )
                    ],
                ),
            ],
            axis=1,
        )

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        """Returns a data frame containing executionDetails.buySellIndicator.
        This is populated from SourceColumns.BUY_SELL conditionally based on SourceColumns.ENTERED_BY
        and SourceColumns.COUNTERPARTY
        """
        return MapConditional.process(
            source_frame=pd.concat(
                [
                    self.pre_process_df,
                    self.source_frame.loc[
                        :, [SourceColumns.ENTERED_BY, SourceColumns.COUNTERPARTY]
                    ],
                ],
                axis=1,
            ),
            params=ParamsMapConditional(
                target_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                cases=[
                    Case(
                        query=FLIP_BUY_SELL_QUERY,
                        attribute=TempColumns.INVERSE_BUY_SELL_INDICATOR,
                    ),
                    Case(
                        query=f"~({FLIP_BUY_SELL_QUERY})",
                        attribute=TempColumns.BUY_SELL_INDICATOR,
                    ),
                ],
            ),
        )
