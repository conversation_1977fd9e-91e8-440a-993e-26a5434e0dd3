from typing import Optional

import pandas as pd
from prefect.engine.signals import FAIL
from prefect.engine.signals import SKIP
from se_core_tasks.controllers.multiple_files_input_controller import (
    FailIfInvalidPattern,
)
from se_core_tasks.controllers.multiple_files_input_controller import (
    FailIfNoUniqueIdentifierPattern,
)
from se_core_tasks.controllers.multiple_files_input_controller import (
    Params as MultipleFilesInputControllerParams,
)
from se_core_tasks.controllers.multiple_files_input_controller import (
    run_multiple_files_input_controller,
)
from se_core_tasks.controllers.multiple_files_input_controller import SkipIfMissingFiles
from se_core_tasks.controllers.multiple_files_input_controller import (
    SkipIfSourceTimestampLessThanPair,
)
from se_core_tasks.controllers.multiple_files_input_controller import (
    SkipIfSourceTimestampSameAlphaLess,
)
from swarm.conf import Settings
from swarm.task.base import BaseParams
from swarm.task.transform.base import BaseTask


class Params(BaseParams, MultipleFilesInputControllerParams):
    pass


class MultipleFilesInputController(BaseTask):
    """
    This task creates 2 different types of CSV files which can be used to trigger a flow. The use cases
    are as below:

    1. For a given source `file_url`, checks whether its associated files are already present in the cloud
    and then creates a CSV file which contains the data of all files and places it in the target cloud path
    (can be uploaded to multiple prefixes) which can be used to trigger a separate flow.

    2. For a given source `file_url`, checks whether its associated files are already present in the cloud
    and then creates a CSV file which contains the cloud links to all files and places it in the target cloud path.
    The columns names in this case are fetched from the first capturing group in each of the regex expressions
    in list_of_files_regex. In this case, the output file's name will be the <current timestamp>.csv

    # TODO for 1. Supports CSV files only for the time being (to add a param for file extension that will change which
        Pandas method to use to read/write the input files, nothing else needs to be changed on the code)

    If all the required files are not present, the task raises a FAIL signal. However, the task can also
    optionally ignore certain 'valid' files (files for which a FAIL signal shouldn't be raised). This may
    be required if another task in the flow handles files of a different type, and as a FAIL signal for these
    files will not handle these file types.
    """

    params_class = Params

    def execute(
        self,
        params: Optional[Params] = None,
        file_url: str = None,
        **kwargs,
    ) -> Optional[pd.DataFrame]:
        try:
            return run_multiple_files_input_controller(
                file_url=file_url,
                params=params,
                realm=Settings.realm,
            )
        except (FailIfNoUniqueIdentifierPattern, FailIfInvalidPattern) as fe:
            raise FAIL(f"Flow failed. Exception: {fe}")
        except (
            SkipIfMissingFiles,
            SkipIfSourceTimestampLessThanPair,
            SkipIfSourceTimestampSameAlphaLess,
        ) as se:
            raise SKIP(f"Flow skipped. Reason: {se}")
