site_name: Swarm flows
theme:
  name: material
  features:
    - navigation.top
    - navigation.indexes

# Repository
repo_name: steeleye/swarm-flows
repo_url: https://github.com/steeleye/swarm-flows
# To show the pencil button in each page
# and redirects into the edit page in GitHub
edit_uri: edit/master/docs/


nav:
  - index.md
  - getting-started.md
  - Development:
      - development/index.md
      - development/deployment.md
      - development/faq.md
      - development/setup.md
      - development/task.md
      - development/testing.md
      - development/workflow.md
  - Flows:
      - bundles/index.md
      - Comms Flows:
        - Comms Home: bundles/comms/index.md
        - Chat:
          - Refinitiv FXT: bundles/comms/chat/refinitiv_fxt.md
        - Controller:
          - Truphone Controller: bundles/comms/controller/comms-truphone-controller.md
        - Text:
          - Truphone Flow: bundles/comms/text/comms-text-truphone.md
        - Transcription:
          - Deepgram API: bundles/comms/voice/transcription/deepgram/api/deepgram-api.md
          - Deepgram Feed: bundles/comms/voice/transcription/deepgram/feed/deepgram-feed.md
          - DeepGram Transcription - How to setup: bundles/comms/voice/transcription/deepgram/deepgram.md
          - Transcription Router: bundles/comms/voice/transcription/transcription-router.md
        - Voice:
          - 8x8 Voice API Flow: bundles/comms/voice/8x8/api/comms-voice-8x8-api.md
          - 8x8 Voice Feed Flow: bundles/comms/voice/8x8/feed/comms-voice-8x8-feed.md
          - Avaya Voice Flow: bundles/comms/voice/comms-voice-avaya.md
          - Focus Horizon Voice Flow: bundles/comms/voice/comms-voice-focus-horizon.md
          - Redbox Voice Flow: bundles/comms/voice/comms-voice-redbox.md
          - Teleware Voice Flow: bundles/comms/voice/comms-voice-teleware.md
          - Truphone Flow: bundles/comms/voice/comms-voice-truphone.md
          - Wavenet Voice Flow: bundles/comms/voice/comms-voice-feed-wavenet.md
          - Xima Voice API Flow: bundles/comms/voice/xima/api/comms-voice-xima-api.md
          - Xima Voice Feed Flow: bundles/comms/voice/xima/feed/comms-voice-xima-feed.md
          - Zoom Trium Feed: bundles/comms/voice/zoom/trium/comms-voice-zoom-trium.md
      - MyMarket Flows:
        - MyMarket Home: bundles/mymarket/index.md
        - BP Market Person Controller: bundles/mymarket/bp/mymarket-bp-person-controller.md
        - BP Market Person Processor: bundles/mymarket/bp/mymarket-bp-person-processor.md
        - MyMarket Firm: bundles/mymarket/universal/mymarket-universal-steeleye-firm.md
        - MyMarket Person: bundles/mymarket/universal/mymarket-universal-steeleye-person.md
      - Order Flows:
        - Order Home: bundles/order/index.md
        - Aladdin: bundles/order/feed/order-feed-aladdin.md
        - BBG EMSI Controller: bundles/order/feed/order-bbg-emsi-controller.md
        - BBG EMSI Processor: bundles/order/feed/order-bbg-emsi-processor.md
        - Charles River Controller: bundles/order/feed/order-feed-charles-river-controller.md
        - Charles River Processor: bundles/order/feed/order-feed-charles-river-processor.md
        - CME STP Fix: bundles/order/feed/order-feed-cme-stp-fix.md
        - EMSX Fix: bundles/order/feed/order-feed-emsx-fix.md
        - Enfusion V2: bundles/order/feed/order-feed-enfusion-v2.md
        - Expersoft UBP: bundles/order/feed/order-feed-expersoft-ubp.md
        - Fidessa Front Office Trades: bundles/order/feed/order-feed-fidessa-front-office-trades.md
        - Fidessa Order Progress: bundles/order/feed/order-feed-fidessa-order-progress.md
        - Flextrade Controller and PRIM-RPL Processor:  bundles/order/feed/order-feed-flextrade-controller-prim-rpl-processor.md
        - Flextrade CXL Processor: bundles/order/feed/order-feed-flextrade-cxl-processor.md
        - Flextrade EXE Processor: bundles/order/feed/order-feed-flextrade-exe-processor.md
        - IBP TSOX Bloomberg Fix: bundles/order/feed/order-feed-tsox-fix.md
        - ICE POF Fix: bundles/order/feed/order-feed-ice-pof-fix.md
        - Kooltra Monsas: bundles/order/feed/order-feed-kooltra-monsas.md
        - LME Fix: bundles/order/feed/order-feed-lme-fix.md
        - LME EOD: bundles/order/feed/order-feed-lme-eod.md
        - Metatrader MT4 Trades: bundles/order/feed/order-metatrader-mt4-trades.md
        - Metatrader MT5 Deals: bundles/order/feed/order-metatrader-mt5-deals.md
        - Metatrader MT5 Orders: bundles/order/feed/order-metatrader-mt5-orders.md
        - Oanda V20: bundles/order/feed/order-feed-oanda-v20.md
        - Pershing UBP: bundles/order/feed/order-feed-pershing-ubp.md
        - Red Deer EMSO Controller: bundles/order/feed/order-feed-reddeer-controller.md
        - Red Deer EMSO Processor: bundles/order/feed/order-feed-reddeer-processor.md
        - Saxo Bank Orders: bundles/order/feed/order-feed-saxo-bank.md
        - Shell Controller: bundles/order/feed/order-feed-samco-bbg-audt-controller.md
        - Shell Processor: bundles/order/feed/order-feed-samco-bbg-audt-processor.md
      - Ops Flows:
        - Ops Home: bundles/ops/index.md
        - Csv2Model: bundles/ops/csv2record/csv-to-record.md
        - Re-drive Call Transcription: bundles/ops/redrive-call-transcription/redrive-call-transcription.md
        - TR Eligibility Assessor Get Metrics: bundles/ops/eligibility-assessor/tr-eligibility-assessor-get-metrics.md
        - TR Eligibility Assessor Update Records: bundles/ops/eligibility-assessor/tr-eligibility-assessor-update-records.md
      - MyMarket Flows:
        - MyMarket Home: bundles/mymarket/index.md
        - MyMarket Firm: bundles/mymarket/universal/mymarket-universal-steeleye-firm.md
        - MyMarket Kytebroking: bundles/mymarket/kyte/mymarket-kytebroking-firm.md
        - MyMarket Person: bundles/mymarket/universal/mymarket-universal-steeleye-person.md
        - Mymarket Slack Suggestion and Person Updater: bundles/mymarket/slack/mymarket-slack-suggestions-person-updater.md
        - BP Market Person Controller: bundles/mymarket/bp/mymarket-bp-person-controller.md
        - BP Market Person Processor: bundles/mymarket/bp/mymarket-bp-person-processor.md
      - Reference Flows:
        - Reference Home: bundles/reference/index.md
        - SE Instrument CFD/SB-Commodity Futures Transformer: bundles/reference/instrument/steeleye/cfd_sb_transformer/commodity_futures/reference-instrument-se-cfdsb-commodity-transformer.md
        - SE Instrument CFD/SB-Index Futures Transformer: bundles/reference/instrument/steeleye/cfd_sb_transformer/index_futures/reference-instrument-se-cfdsb-index-transformer.md
      - TR (RTS22) Flows:
        - TR Home: bundles/tr/index.md
        - Enfusion V2 Controller: bundles/tr/feed/tr-feed-enfusion-v2-controller.md
        - Enfusion V2 Processor: bundles/tr/feed/tr-feed-enfusion-v2-processor.md
        - Julius Baer: bundles/tr/feed/tr-feed-julius-baer.md
        - LME Controller: bundles/tr/feed/tr-feed-lme-controller.md
        - LME Processor: bundles/tr/feed/tr-feed-lme-processor.md
        - Saxo Bank: bundles/tr/feed/tr-feed-saxo-bank.md
        - UnaVista: bundles/tr/feed/tr-feed-unavista.md
        - XML to UnaVista Converter: bundles/tr/feed/tr-feed-xml-to-unavista-converter.md
plugins:
  - search
  - git-revision-date-localized # to show last update date
  - mermaid2
  - section-index

markdown_extensions:
  # To render correctly nested lists
  - def_list
  # To support emojis
  - pymdownx.emoji:
      emoji_index: !!python/name:materialx.emoji.twemoji
      emoji_generator: !!python/name:materialx.emoji.to_svg
  # To support note blocks
  - admonition
  - pymdownx.details
  # To support code syntax highlighting
  - pymdownx.highlight
  - pymdownx.inlinehilite
  - pymdownx.superfences
  - pymdownx.snippets
  # More extensions to be added under here


extra_javascript:
  # To allow table sorting
  - https://cdnjs.cloudflare.com/ajax/libs/tablesort/5.2.1/tablesort.min.js
  - extra/javascript/tablesort.js

extra_css:
  - extra/css/mkdocstrings.css