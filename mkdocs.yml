site_name: Swarm SDK
theme:
  name: material

# Repository
repo_name: steeleye/swarm-sdk
repo_url: https://github.com/steeleye/swarm-sdk
# To show the pencil button in each page
# and redirects into the edit page in GitHub
edit_uri: edit/master/docs/


nav:
  - index.md
  - Managing records:
      - records/rh-sync.md
      - records/rh-async.md
  - Code Reference: reference.md

plugins:
  - search
  - git-revision-date-localized # to show last update date
  - mermaid2
  - section-index
  # Python docstrings
  - mkdocstrings:
      handlers:
        python:
          selection:
            docstring_style: restructured-text
      watch:
        - swarm

markdown_extensions:
  # To render correctly nested lists
  - def_list
  # To support emojis
  - pymdownx.emoji:
      emoji_index: !!python/name:materialx.emoji.twemoji
      emoji_generator: !!python/name:materialx.emoji.to_svg
  # To support note blocks
  - admonition
  - pymdownx.details
  # To support code syntax highlighting
  - pymdownx.highlight
  - pymdownx.inlinehilite
  - pymdownx.superfences
  - pymdownx.snippets
  # More extensions to be added under here


extra_javascript:
  # To allow table sorting
  - https://cdnjs.cloudflare.com/ajax/libs/tablesort/5.2.1/tablesort.min.js
  - extra/javascript/tablesort.js

extra_css:
  - extra/css/mkdocstrings.css
