version: 2.1
orbs:
  slack: circleci/slack@3.4.2
#  codecov: codecov/codecov@3.0.0

commands:
  setup-and-validate:
    description: "Checkout Code, Validate, and Setup Docker Image"
    steps:
      - setup_remote_docker
      - restore_cache:
          keys:
            - v2-poetry-deps-{{ checksum "poetry.lock" }}
            - v2-poetry-deps-
      - run:
          name: Install poetry
          command: |
            curl -sSL https://install.python-poetry.org | python3 - --version 1.5.1
      - run:
          name: Install Validation Dependencies
          command: |
            poetry config repositories.steeleye-pypi ${PYPI_HOST}
            poetry config http-basic.steeleye-pypi ${PYPI_USERNAME} ${PYPI_PASSWORD}
            poetry config virtualenvs.in-project true
            poetry install --sync --no-cache --no-ansi --no-interaction
      - save_cache:
          key: v2-poetry-deps-{{ checksum "poetry.lock" }}
          paths: .venv
      - run:
          name: Validate
          command: |
            poetry run pre-commit run --all --show-diff-on-failure
      - run:
          name: Run Tests
          command: |
            poetry run pytest tests
#            --cov=swarm --cov-report=xml
#      - codecov/upload
      
  update-version:
    description: "Update Version in pkg_info.json and pyproject.toml"
    steps:
      - run:
          name: Update Version
          command: |
            package_name=$(sed -n 's/name = "\(.*\)"/\1/p' pyproject.toml | head -1)
            sed -i -e 's/latest/'${CIRCLE_TAG:1}'/g' $package_name/pkg_info.json
            poetry version ${CIRCLE_TAG:1}
            cat $package_name/pkg_info.json
  publish-package:
    description: "Publish the package to artifactory"
    steps:
      - run:
          name: Publish Package
          command: |
            poetry publish --build -r steeleye-pypi
jobs:
  build:
    docker:
      - image: cimg/python:3.9
      - image: steeleye/esdocker:5.6.16
        auth:
          username: $DOCKER_USER
          password: $DOCKER_PASS
        environment:
          ES_JAVA_OPTS: "-Xms512m -Xmx512m"
          bootstrap.memory_lock: false
          transport.host: "0.0.0.0"
    steps:
      - checkout
      - setup-and-validate
      - slack/status

  deploy:
    docker:
      - image: cimg/python:3.9
      - image: steeleye/esdocker:5.6.16
        auth:
          username: $DOCKER_USER
          password: $DOCKER_PASS
        environment:
          ES_JAVA_OPTS: "-Xms512m -Xmx512m"
          bootstrap.memory_lock: false
          transport.host: "0.0.0.0"
    steps:
      - checkout
      - update-version
      - setup-and-validate
      - publish-package
      - slack/status

workflows:
  version: 2
  build:
    jobs:
      - build:
          context: swarm-global
          filters:
            branches:
              ignore:
                - develop
                - master
  build_n_deploy:
    jobs:
      - build:
          context: swarm-global
          filters:
            branches:
              only:
                - develop
                - master

      - deploy:
          context: swarm-global
          filters:
            tags:
              only: /v[0-9]+\.[0-9]+\.[0-9]+(-(post|dev|rc)\.[0-9]+)*/
            branches:
              ignore: /.*/
