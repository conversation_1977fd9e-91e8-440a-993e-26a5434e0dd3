# Focus Horizon Voice Flow

Build for Capindex, this flow ingests Focus Horizon Call records into Elasticsearch.
The input file is a zip file which contains multiple mp3 files. 

### Specs
[Focus Horizon Call Confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/2460844033/Call+Focus+Horizon)

### Input File
ZIP file containing mp3 files. The metadata is derived from the file name. The duration
is fetched by using the mutagen library on each individual audio file

## Output
`Call` and `Attachment` records in ElasticSearch. 

## Tasks
- #### ParametersFlowController
This flow can be executed for both a local file or one on S3

- #### S3DownloadFile
Download CSV file from S3

- #### LocalFile
Read local CSV file

- #### Unzip
Used to unzip the source zip file. The extracted files are stored in a temp directory

- #### S3FileListFromExtractPathResultList
Create an 'S3 File' list from the temp results list. After this, we have the recordings in
the right format to upload to S3.

- #### S3UploadRecordings
Actually uploads the recordings to S3.

- #### DataFrameFromS3FileList
Convert the S3 file list to a data frame containing 2 columns -- the local file path
and the S3 file url

- #### CallDurationFromAttachment
Get the call duration directly from the local MP3 files (not from
S3 urls).

- #### S3MetadataAttachmentBatch
Creates Attachment model fields from the S3 recording files

- #### AttachmentStatic
Used to populate the meta model field for assigning meta fields to the Attachment records

- #### AttachmentConcatenator
Combine data frames from the previous 2 steps

- #### AssignAttachmentMeta
Adds meta fields for the Attachment model

- #### AttachmentWithMetaConcatenator
Concatenates data frames from the previous 2 steps

- #### ElasticBulkTransformerAttachment
Creates a file containing all the Attachment records to be ingested into ElasticSearch

- ### ElasticBulkWriterAttachment
Uses the file created by ElasticBulkTransformer to ingest Attachment records into Elasticsearch 

- #### RegexGetColumnsFromGroups
Uses a regular expression to extract the different metadata columns from the file names

- #### FocusHorizonTransformations
Creates all the Call fields other than the voiceFile fields (which come directly from the attachment)

- #### AssignCallMeta
Adds meta fields for the Call model

- #### FormatVoiceFile
Adds the prefix 'voiceFile.' to the Attachment columns so that it can be integrated into the
Call data frame.

- #### FinalFrameConcatenator
Concatenates the Call meta fields with the Call data fields and the Attachment (voice file) fields

- #### ElasticBulkTransformerCall
Creates a file containing all the Call records to be ingested into ElasticSearch

- ### ElasticBulkWriterCall
Uses the file created by ElasticBulkTransformer to ingest Call records into Elasticsearch 

- #### FetchTenantConfiguration
Fetches the tenant configuration for the client to get the deepgram usage details

- #### TranscriptionRouter
Trigger the Deepgram API flow

- #### TriggerWaveformFlow
Trigger the waveform flow to add the waveform fields to the Call records