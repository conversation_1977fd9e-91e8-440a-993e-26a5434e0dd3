# XIMA Voice API Flow

This flow fetches calls from XIMA API.

### Specs
[XIMA Call Confluence page](https://steeleye.atlassian.net/wiki/spaces/IN/pages/2392457217/Call+Xima+IPSolutions+VoIP)

### Input
flow arguments `tenant_realm` (tenant bucket)

## Output
Uploads json `metadata` files in (ingress/raw/comms/voice/xima/metadata) and wav `recording` files in (ingress/raw/comms/voice/xima/recordings) on S3. 
Also uploads the batch csv file containing the links for s3 metadata files and s3 recording file on `flows/comms-voice-xima-feed` on S3.

## Tasks
1. #### FlowArgsValidator
Validates the flow argument sent by EventBridge rule.

2. #### GetCallMetadata
It fetches all the metadata from XIMA API.

3. #### GetCallRecordings
Fetch the call recordings from XIMA API against each call id

4. #### MetaDataAsS3FileObj
Convert metadata response to S3File object

5. #### CreateBatchCSV
Creates the batch CSV file containing the links of metafile and recording file for S3

6. #### S3UploadCallMetadata
Uploads the metadata files on s3

7. #### S3UploadRecordings
Uploads the recording files on s3

8. #### S3UploadBatchCSV
Uploads the batch csv file on s3 which triggers the feed flow
