# Order Feed Lme Eod

Specs: [Order LME EOD](https://steeleye.atlassian.net/wiki/spaces/IN/pages/2545778689/Order+LME+EOD)

This flow is triggered by when a file is dropped to `flows/order-feed-lme-eod` directory. There are 4 file types which are sent through daily. Each file is processed via the same flow with the same logic:
- End of Day Matched Trades Report (EODM)
- End of Day Unmatched Trades Report (EODU)
- End of Day Alleged Trades Report (EODA)
- End of Day Error Trades Report (EODE)

## Input file
One of 4 types of `.csv` files mentioned above. 

## Output
`Order`, `OrderState` and `QuarantinedOrder` records in ElasticSearch

## Skip logic
No skip logic

## Tasks

-  ### ParametersFlowController
This flow can be executed for both a local file or one on S3

- ### S3DownloadFile
Download CSV file from S3

- ### LocalFile
Read local CSV file

- ### CsvFileSplitter
Split CSV file in Batches if the input file exceeds a parametrized threshold

- ### BatchProducer
Reads each CSV chunk and produces Batches of DataFrames to downstream tasks

- ### PrimaryTransformation
Maps all the required columns to the required target columns

- ### LinkParties
Links the Party Identifiers to existing records

- ### LinkInstrument
Links the Instrument Identifiers to existing records in SRP

- ### AssignMetaParent
Links each OrderState record to the appropriate parent Order record

- ### AuxiliaryFrameConcatenator
Concatenates all columns from the `PrimaryTransformations`, `LinkParties`, `LinkInstrument`,
and `AssignMetaParent` tasks

- ### OrderRecords
Filters only Order records into a data frame

- ### OrderStateRecords
Filters only OrderState records into a data frame

- ### StripPrefixOrder
Removes the prefix '_order' from all Order records

- ### StripPrefixOrderState
Removes the prefix '_orderState' from all OrderState records

- ### VerticalConcatenator
Concatenates Order and OrderState records into a single data frame

- ### RemoveInvalidOrderStates
Removes invalid orderstates (orderState records where `executionDetails.orderStatus` is null()

- ### BestExecution
Populates Best Execution fields

- ### BestExecutionConcatenator
Concatenates the data frames from `VerticalConcatenator` and `BestExecution`

- ### AssignMeta
Creates the meta fields of the records after record validation

- ### PostMetaConcatenator
Concatenates the data frames from `BestExecutionConcatenator` and `AssignMeta`

- ### ElasticBulkTransformer
Creates a file containing all the Order and OrderState records to be ingested into ElasticSearch

- ### PutIfAbsent
Uses the file created by ElasticBulkTransformer to ingest Order and OrderState records into Elasticsearch 

- ### GenerateOrderKafkaMessages
Creates Order messages for Kafka producer    

- ### KafkaMessageProducer
Kafka Producer which writes messages into the OrderAnalyticsTopic Kafka topic

- ### QuarantineCondition
Assesses if any record should be quarantined

- ### QuarantinedElasticBulkTransformer
Creates a file containing all the QuarantiedOrder records to be ingested into ElasticSearch

- ### QuarantinedElasticBulkWriter
Uses the file created by QuarantinedElasticBulkTransformer to ingest QuarantiedOrder records into Elasticsearch

- ### Noop
Dummy task, used when there is no need to quarantine.
