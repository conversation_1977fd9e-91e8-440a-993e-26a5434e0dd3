# Swarm Tasks

Merging release branches should be done with a merge commit

## Release Swarm-task version

First, we need to make sure that our local master branch up to date.

> git fetch

> git pull origin master


1. Create a release branch (`release/<tag>`) to update the version (to be tagged) in `pyproject.toml` (you can just run locally `poetry version <tag (without the prefix v)` and create a PR.
2. After the PR is approved and merged, from your local master branch, do `git pull origin master` and create the tag (must follow these standards `v[0-9]+.[0-9]+.[0-9]+`; example: `git tag v3.4.0`).
3. [OPTIONAL] If issues are detected with the swarm-tasks release, please update the `readme.md` to provide more detail on those issues, to prevent developers from deploying that version. Once a stable swarm-tasks release is made, please return to the `readme.md` and update the stable version.
