#!/usr/bin/env python3
"""
Test script to verify the abs_or_none function fix.
This script tests the behavior of abs_or_none with various input types.
"""

import sys
import os

# Add the swarm_tasks directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'swarm-tasks'))

from swarm_tasks.utilities.data_util import abs_or_none
from swarm_tasks.utilities.dict_util import cast_to_type

def test_abs_or_none():
    """Test the abs_or_none function with various inputs."""
    print("Testing abs_or_none function:")
    
    # Test cases
    test_cases = [
        # (input, expected_output, description)
        (None, None, "None input should return None"),
        (5.5, 5.5, "Positive float should return same value"),
        (-3.2, 3.2, "Negative float should return absolute value"),
        (0, 0.0, "Zero should return 0.0"),
        ("", 0.0, "Empty string should return 0.0"),
        ("  ", 0.0, "Whitespace string should return 0.0"),
        ([], 0.0, "Empty list should return 0.0"),
        ("nan", 0.0, "String 'nan' should return 0.0"),
        ("5.5", 5.5, "String number should be converted"),
        ("-3.2", 3.2, "String negative number should return absolute value"),
    ]
    
    all_passed = True
    
    for input_val, expected, description in test_cases:
        try:
            result = abs_or_none(input_val)
            if result == expected:
                print(f"✓ PASS: {description}")
                print(f"  Input: {repr(input_val)} -> Output: {result}")
            else:
                print(f"✗ FAIL: {description}")
                print(f"  Input: {repr(input_val)} -> Expected: {expected}, Got: {result}")
                all_passed = False
        except Exception as e:
            print(f"✗ ERROR: {description}")
            print(f"  Input: {repr(input_val)} -> Exception: {e}")
            all_passed = False
        print()
    
    return all_passed

def test_cast_to_type_abs():
    """Test the cast_to_type function with 'abs' type."""
    print("Testing cast_to_type with 'abs' type:")
    
    test_cases = [
        # (input, expected_output, description)
        (5.5, 5.5, "Positive float should return same value"),
        (-3.2, 3.2, "Negative float should return absolute value"),
        (0, 0.0, "Zero should return 0.0"),
        ("", 0.0, "Empty string should return 0.0"),
        ("-5.5", 5.5, "String negative number should return absolute value"),
    ]
    
    all_passed = True
    
    for input_val, expected, description in test_cases:
        try:
            result = cast_to_type(input_val, "abs")
            if result == expected:
                print(f"✓ PASS: {description}")
                print(f"  Input: {repr(input_val)} -> Output: {result}")
            else:
                print(f"✗ FAIL: {description}")
                print(f"  Input: {repr(input_val)} -> Expected: {expected}, Got: {result}")
                all_passed = False
        except Exception as e:
            print(f"✗ ERROR: {description}")
            print(f"  Input: {repr(input_val)} -> Exception: {e}")
            all_passed = False
        print()
    
    return all_passed

if __name__ == "__main__":
    print("=" * 60)
    print("Testing the abs_or_none function fix")
    print("=" * 60)
    
    test1_passed = test_abs_or_none()
    print("-" * 60)
    test2_passed = test_cast_to_type_abs()
    
    print("=" * 60)
    if test1_passed and test2_passed:
        print("✓ ALL TESTS PASSED! The fix is working correctly.")
    else:
        print("✗ SOME TESTS FAILED! Please review the implementation.")
    print("=" * 60)
