repos:
  - repo: https://github.com/asottile/reorder_python_imports
    rev: v1.9.0
    hooks:
    - id: reorder-python-imports

  - repo: https://github.com/psf/black
    rev: 22.3.0
    hooks:
      - id: black
        language_version: python3.9
        exclude: \.git|\.hg|\.mypy_cache|\.tox|\.venv|venv|_build|buck-out|build|dist
        args: ["--line-length=88",
               "--quiet",
               '--exclude="/(\.git|\.hg|\.mypy_cache|\.tox|\.venv|venv|_build|buck-out|build|dist)/"']

  - repo: **************:humitos/mirrors-autoflake.git
    rev: v1.3
    hooks:
      - id: autoflake
        exclude: \.git|\.hg|\.mypy_cache|\.tox|\.venv|venv|_build|buck-out|build|dist
        language_version: python3.9
        args: ["--in-place",
               "--remove-all-unused-imports",
               "--remove-unused-variable",
               "--exclude=.venv,venv"]

  - repo: https://github.com/PyCQA/flake8
    rev: 3.7.9
    hooks:
      - id: flake8
        exclude: \.git|\.hg|\.mypy_cache|\.tox|\.venv|venv|_build|buck-out|build|dist
        language_version: python3.9
        args: ["--ignore=E203,E266,E501,W503,F403,F401",
               "--max-line-length=88",
               "--max-complexity=18",
               "--select=B,C,E,F,W,T4,B9",
               "--exclude=.venv,venv"]
